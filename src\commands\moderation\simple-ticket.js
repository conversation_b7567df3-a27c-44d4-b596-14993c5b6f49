const { <PERSON>lash<PERSON>ommandBuilder, PermissionFlagsBits, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('simple-ticket')
        .setDescription('Create a simple ticket system with one button')
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageGuild)
        .addStringOption(option =>
            option.setName('roles')
                .setDescription('The roles that can see tickets (mention multiple roles separated by spaces)')
                .setRequired(true)),

    async execute(interaction) {
        const rolesInput = interaction.options.getString('roles');
        
        // Extract role mentions and IDs
        const roleMentions = rolesInput.match(/<@&(\d+)>/g);
        const validRoles = [];

        if (roleMentions) {
            for (const mention of roleMentions) {
                const roleId = mention.match(/\d+/)[0];
                const role = interaction.guild.roles.cache.get(roleId);
                if (role) {
                    validRoles.push(role);
                }
            }
        }

        if (validRoles.length === 0) {
            return interaction.reply({
                content: '❌ No valid roles found. Please mention valid roles using @role format.',
                ephemeral: true
            });
        }

        const rolesData = validRoles.map(role => role.id).join(',');

        const ticketEmbed = new EmbedBuilder()
            .setColor('#61607e')
            .setTitle('🎫 Support Ticket System')
            .setDescription(`
**Need Help?**

If you need assistance, have a question, or encounter any issues, click the button below to create a support ticket.

**How it works:**
🎫 **1** Click the "Create Ticket" button below
🎫 **2** Describe your issue or question clearly
🎫 **3** Wait for our support team to assist you

Our team will respond as soon as possible!
            `)
            .setFooter({ text: 'Click the button below to get started' })
            .setTimestamp();

        const ticketButton = new ActionRowBuilder()
            .addComponents(
                new ButtonBuilder()
                    .setCustomId(`create_simple_ticket_${rolesData}`)
                    .setLabel('Create Ticket')
                    .setEmoji('🎫')
                    .setStyle(ButtonStyle.Primary)
            );

        await interaction.channel.send({
            embeds: [ticketEmbed],
            components: [ticketButton]
        });

        await interaction.reply({
            content: '✅ Simple ticket system has been set up successfully!',
            ephemeral: true
        });
    }
};
