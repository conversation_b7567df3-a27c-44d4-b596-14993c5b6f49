{"_id": "@jest/expect", "_rev": "72-989f475d16520bcef9dd461f442a5c27", "name": "@jest/expect", "dist-tags": {"next": "30.0.0-rc.1", "latest": "30.0.2"}, "versions": {"28.0.0-alpha.2": {"name": "@jest/expect", "version": "28.0.0-alpha.2", "license": "MIT", "_id": "@jest/expect@28.0.0-alpha.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a780675a3b8d94fb4ece818017dcda5325223c77", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-28.0.0-alpha.2.tgz", "fileCount": 7, "integrity": "sha512-S1TL0LXtNv78LLvl7Yx26I+IXZGOHVN5dPsmQ9Q+G0UJyEETQLNInKvwHoAWiyB9t+1jhpVCaSaTCZXT2rhYRw==", "signatures": [{"sig": "MEUCIBsPPwHaJnk6ej97kp0AEGoE4bQsbxg3Mq6quFTZ85tdAiEA2dQdY0ad3ypbVpuIreGNyrQm+/hiPqMkkOTxWgMBO5s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5831, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDT6GCRA9TVsSAnZWagAAqCQP/AuE+HOAUzBZnKAcW41x\nHgfkqbl0N/ApMsH5mYJNsmrGJS9xdVIplvO662tWkEw8dvwcN1sI7JznlEqw\nW9gaOJki0UUke/UtBhM5KIIlLQ5hJnwtAsKBBqhblGVGewTW2VkRTHCJwh9L\naF0UnFNFxohToUxvYIEjpfriKqyRX5Y5IOdOY3QaATPt8JjpWAGcHizwCwJK\nt0bPhWKxi8iAx8E5ECNpnOjPIJWIRGyoIjDGDfbyx6OxMXRaNXlGjG3BsX7o\nsKMn9hJ/ke1gPo78dmwuMJTmyxUUKlqCXfjnMwHrAuTrLpd7lSnodPrM69OM\nqHLlNdpYvaVQkHMNIr0b3b32O+W/O3CB/qFZ7CwSss8jOVcSirARicedXclm\n/hRhIc74zlvRAr3ufDrk14kCcAXKVhJNfhafJTrIa8KXFPDtYUOuegaTFdVg\ndcpA0zTVkzm68x2799IrFse7gr6k/CTpKHMUs9IX65+4LgWrGFD7y4kucARQ\ntP6u28+/6pk9kZGNcldgBPM8EYS4QMZUUkoD0/aLgVLWGk3/oempvyQ/9hQb\nX8fKlISK+TlWO9JkOIu7j1xgr1SIGdFKk4Kq+l1dCsyelXuiMef71Y0JksWE\ni2ewuAZNj5zAPtbo8byRU5UaVFnb7Pbis3TGoajnKpop2ykFMoNd5kn+lS+x\n06wC\r\n=Z/1M\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "694d6bfea56f9cb49d0c7309cdbfff032da198c2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"expect": "^28.0.0-alpha.2", "jest-snapshot": "^28.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.5.1", "@tsd/typescript": "~4.5.5"}, "_npmOperationalInternal": {"tmp": "tmp/expect_28.0.0-alpha.2_1645035141875_0.32030115120861846", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.3": {"name": "@jest/expect", "version": "28.0.0-alpha.3", "license": "MIT", "_id": "@jest/expect@28.0.0-alpha.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c32fc882b1a69f75dc8900460f2a8c5efb54d21d", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-28.0.0-alpha.3.tgz", "fileCount": 6, "integrity": "sha512-wXubMF9LSZjZAWdSMaL+IFApt0r8Cc3iFmvOupO8XcsApLAuMVOCXLYbYOATteToW/F//ZjHO4Ug1G/C0/NCHw==", "signatures": [{"sig": "MEUCIQCx4bTyq7XR3Dq7TVoTJb5kAg4hB8SfUb5GagOuiRNmPQIgK/lAJXLK3X3u6t/fUHIWM1GCZFdYBM8F0Bkdicjzny4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5175, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDmzmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp3bw/8CBrKNy+8EfL2vKKKum70kMNPT75Ku5PWKZFHHvR4xir4Dpby\r\n98OWbUNMPmf/9MNjcnCn43ZtqdDmRL2JSJmXuvudAY11xogeGhMEVNso2S+u\r\nIvGSu6ixRdfAB9GHSPYeY0h39N7ivlc/tql6V07Z+S4yaUJ6el9UusgQD64Z\r\n2v4Gl3jBetCh2OpTPUL1kS9K4VZJNo6FSrqb0yuBceKpwz5ux84qv8szOQDb\r\nHvmdlQ9kqVrcQCmIeWHxT2a8xvMBQk8WNsVz15eb5mNDtzCJmA2cxEjE8mH8\r\nRU6LCqoXAKz3KMIHWd5nnf1Utvu81ZKzpt7L5D4HFGZPSNfsWNVij+vdf939\r\nrboJqsR9QnDpap9MRvD1teOz4lTIoXzl0MSE8agjdqQaoJ7WgCaR8E+PPsRn\r\nEjtikPGagARf0Bcl074LyIJ3QUKZd2RT7QJRK2qVkURDwGe+G5quW/uB6q5u\r\n9JUmvB91gKDisdaTfE12q+7Y1TrYrufC15DtV51HNRrJXJNuq8Al5ie85U75\r\nIx7x8N5NfMMUMwmkTcGbhJAJy/Dplh33Ru1Q/ZDna3+hOaXTz/9yVXtJ2b1m\r\nZFGV9honcolDAMdMzM7TkO/nVlnubODyHRCj+Z/mE7WxPJODYn7QehP26nbJ\r\nHguRiJ3xh05EsE5HTkhLTybMo7f1q9uxqRE=\r\n=REKt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fc30b27bd94bb7ebeaadc72626ebbdba535150d2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"expect": "^28.0.0-alpha.3", "jest-snapshot": "^28.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.5.1", "@tsd/typescript": "~4.5.5"}, "_npmOperationalInternal": {"tmp": "tmp/expect_28.0.0-alpha.3_1645112549951_0.16422580327118697", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.4": {"name": "@jest/expect", "version": "28.0.0-alpha.4", "license": "MIT", "_id": "@jest/expect@28.0.0-alpha.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "562eee135be991c064ca79d32a94029f50c30eb1", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-28.0.0-alpha.4.tgz", "fileCount": 6, "integrity": "sha512-USt0oK//zQx7/zywam7NGN282IuAuXS+VRo2ClPAFxxm+nJymxU8KfJtJLwyNemd1h4OFeKkpVz8R3YRJ3XF2w==", "signatures": [{"sig": "MEUCIQDgWhw6mDBN+hQrcfuh97BbOpTPCSb4M05rqNm4Nvby8QIgBUc0Om0MfU1EJj5miEj3oMSl01Gt+imS6zd59v+X5Nc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5175, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFNOJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqo/hAAgvTTM2/kc5/4jtMDEBMWqlmV9Io+KUnxqwaH+GfPxS+jILDc\r\nN6MeKuAxw5cdaubq6sCxJsSYio8J4wFCQ1dg0EEwKcLlR4OhrMJ1TdyjF6SH\r\nMIAilhC8BHednEw4hhw703acMaAafE1z1ka2Ki3VCV3xovWDRxJoV3DrZwrO\r\nUPi4U7O2qJcNN05EUZ5HQqxWjFOueJl+k5frWJAd8auMHZw2mU/aVWSk4O2E\r\nB+8bVjx6j9xqINk+Wc9GEuL1xk/KAJeoZ1zCCkgmcr3SEeF13wMWlXXfURXk\r\nGspz1GRFdg9XzEBbM2B4hxzo3q2TKFQ5xT6i8OhdcfAXFN81SSZB3jPlon4l\r\nl760IZgkUCsslowZiRYV2IWgNYuVVw+z5IdNZD7gPPoYQDt/pwh4u2xEDU9C\r\nTc8QU4/8CUxjMxPt2CxD6QE+OXElWNFSzjhzJQfeJ1G9EP+IZK5Tyi0l0v8F\r\niXJtJ3ly9gNoYlErJK6HGsFSBfoy1iOeuEPYJjLYB0v1vw5MeFzg7p5yFqGO\r\nIUG87p4UCpKRt1NxCkLc9Ieb2r5eEo+Dr4LWsVSpq6fb5Crhvd0IM2jChdvT\r\nOXn1jaGkUPwsXhipTk/YfurLW/9d8TFTDvRHyV48DhpKx+t79gJa1nGClttH\r\nI/2ax67T+EZD1h9dYSKBb08nU5HJ7T+C5AA=\r\n=TFQn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c13dab19491ba6b57c2d703e7d7c4b20189e1e17", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"expect": "^28.0.0-alpha.4", "jest-snapshot": "^28.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.5.1", "@tsd/typescript": "~4.5.5"}, "_npmOperationalInternal": {"tmp": "tmp/expect_28.0.0-alpha.4_1645532041484_0.34389874363467343", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.5": {"name": "@jest/expect", "version": "28.0.0-alpha.5", "license": "MIT", "_id": "@jest/expect@28.0.0-alpha.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6b0e24078cecab4cb1fbf4da71aa9f684382fff1", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-28.0.0-alpha.5.tgz", "fileCount": 6, "integrity": "sha512-U1bj/SNmQwYZSMpah2br9Jr+Y5+6IWzs9WWN9lNM2kj5RNPtONFBYvg2c9iDXNbkoiv0GkQVSNieLe7eYeiOdw==", "signatures": [{"sig": "MEQCIHeOurEIFXlyrh92mfw0TubmU5BxdmNEUWZUn3SeRCLeAiA9rKZJXgg/CQDWN6y/FCBcbfz2zJMYmzjaAe4JgEXHnA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5175, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF/E3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpzaA/+K0DC8VBVb6Eo5oDvEf+FcRcDDA9ZvaMjJCBf0bGclsg0t0RA\r\nFRshEOBB6M2q2ZO+UHUTWiZAlpW//CAsAYdq6qjhQtgV5aWPal1kRIl03bRj\r\nTsJ7iMRw9W3+3j8w31qHlpqoDQXVo6rAYk8cGLcNr+ALU0G7E8oL91tfD3du\r\ny0MVnCk8uwIkwtGHGuVbMQLJ8AJrVKypk31wkwtJTYTrzIb0Of2ui+X0r3g+\r\neX709b2qJbb/dSqtvBpjRKLvr21Xskcg4jd/88yMuWLbhLwrznAQ8RtTeG0H\r\nm2WE/Nz3R3b+lMzdq30RBkX2esNscAn3nJBQJcT+7NLpVaDal4KFiruPfc7V\r\nE8YD86BC6swVVmCbTN563KSOEW09FgJbmHW3zF2RLpy852pbP07sDU8CTyI0\r\nrGcTlyRtEvY6Si+sg8wyIf94aGlEU7by87zgrQU+nSVsq8IaMHA6szLJNlYb\r\nCyuwVJzMWC0It07Azm7Zsfavv2m4X2dnZn8Jc27+mg1zUThjkCu2eUcyK/dL\r\ntuwbHrJ0Y5LFPaKgYDKiLA8ySnwVFw4gm6gx3goeBQt/wqCSSt316Q77BZG+\r\nx/v/zJispzsinI8wIhdtRu+dORZ3LgZ+9EBF0A1sguoG6OybfwHj8pDGkEBO\r\nt7RhGTovAyLSa37crWV/Y6k/I3uTG7JKsDU=\r\n=U/xk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "46fb19b2628bd87676c10730ba19592c30b05478", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"expect": "^28.0.0-alpha.5", "jest-snapshot": "^28.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.5.1", "@tsd/typescript": "~4.5.5"}, "_npmOperationalInternal": {"tmp": "tmp/expect_28.0.0-alpha.5_1645736246974_0.19320832360605267", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.6": {"name": "@jest/expect", "version": "28.0.0-alpha.6", "license": "MIT", "_id": "@jest/expect@28.0.0-alpha.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "acec33b8241c68c8a4ecc0a2a60d3be11c6f1aaa", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-28.0.0-alpha.6.tgz", "fileCount": 6, "integrity": "sha512-7hFvXopsn53Q+ClsUJZ2+Dz1p4UCIy/ewkgY6Wys5XkLI4LrjVHCQfvtWKyvTRW04s+6X2QJN162C0CDyC49uA==", "signatures": [{"sig": "MEYCIQCQem3Q1H4CqA4kfkrswlcPlBbztUSiIp12WoGooD7kBgIhAKhcbVGAUXLjHBpN5+G5zTWaS6lkG2/KxoguRkwuxNzC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5175, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHdofACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmprlBAAnEL9i8eJscrrNB/41TdgSF7u6wAx8tV/LtNN9kLCJmNm6C0W\r\nOlBpIWBnWdRAY4caXkC6CfsQq4HnmDhlc3cmih06CBSGRgFPZvjdODP1+hyR\r\nXUk4oxd8ZvDXg7VS7+ZrrGlQ1U7oYcg3qg8UKYVr0V9X0GBrMnLkLP5/4N+8\r\nWfQJOneK4UUtfkcr+bo2C/r3M94Ah6SFZUedh7ZZDy3l51DWgAvCBp3iKlTG\r\nHqZWGs/yY9QqT0zuzXnksNOljNu0epRhMYzqm65rDytKltOlnNzksfW+MQ0V\r\nOd2pzgxY5FB3RiAX5mlpnozXfzO0foVGu7qIoGz5HTmMd4+oheyyUQuUpXZs\r\nrOVVDzfw1y8FDaLWvRkQbzd7LZ1Yb10d/RETq9MMVAtqPSCkaWzIGWlpAYPD\r\nbETKx1bOmkLJ0+UxH3Lq1txZYaEUSu4Pph+d7krlAb3CViHLyarn5yaxLFcf\r\n3LSkEyywtoLtIf9aBhTy7MUy5k6LO2W6sVweL83oJIFPoCqIX5fN4fksLYNL\r\nu37Zv0ObUMi2gEA0NITAynB0I+/kH5b9RqXTuhx9FYxFi00pjcA3yqbGFn7+\r\nSaJW6WM34Je32U0Pe3Tgf4e20ui9jheqP0elxfckO0n6aMHVAwybtaPq8Dc3\r\nZokFjopZAjFrRW9wM6XZ2NAaPeva02TMlF4=\r\n=V0IC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6284ada4adb7008f5f8673b1a7b1c789d2e508fb", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"expect": "^28.0.0-alpha.6", "jest-snapshot": "^28.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.5.1", "@tsd/typescript": "~4.5.5"}, "_npmOperationalInternal": {"tmp": "tmp/expect_28.0.0-alpha.6_1646123551780_0.5439920594807159", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.7": {"name": "@jest/expect", "version": "28.0.0-alpha.7", "license": "MIT", "_id": "@jest/expect@28.0.0-alpha.7", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "719f5942279f0de7a5b12790b7560aafe527e452", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-28.0.0-alpha.7.tgz", "fileCount": 6, "integrity": "sha512-CHDuuCwdsyABEpx9Ce4SnGov/68A0sAi5p767h1/woxBj8ma0B+5Bqh6qnsxpTC7nVgi10+b9yUBr6pyd3+d8Q==", "signatures": [{"sig": "MEUCIEdTKlVAC1m674+2gKhzRvpCW68s8HRYjYUhoqnnX0uwAiEA4P04jPuVKk9v+HWlwiRZwEwPxP4rV1pk+bm7YbD3IGE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5175, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiJIbJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrLwg//fumst2uJpe8Dy2cDTsm7Sg91EcPRonlrOf4N52Q2oEhBY7Xa\r\nF6jXD7TOossfBRSrM15KSRMBBXdjDv9Au84156M9PDNUypz7hB3Pewwrdjje\r\n3dG6Kvrdgbck1cW3ZmnQ/7NZyjFzuqi9wRhaHq6st3xWxsP+PpFgGS64IIpD\r\nMcSaN/LHJgVvrnpCcDf4Rzxacbh6s3NabLWdF3dOG1YTgc+dYMjKhkguNiyO\r\nHW9zz9jPkk2I7mj6Gxxw+/VH8Uks5V4n5CK66ipAVZcB4EuXbIBfB0Tb1lim\r\n2HsIXXO4v1S1Ps9zenWJPM0RrBAWthakuCO+a3hBBBWDm/4g3u658bEWox2g\r\nh7S4+O5IxRYXpPmrO+V8qQU4TS7aMQc+hJ0V/QIYq/tpHIMIF8o4T4ha9lmL\r\n53MpEn3DM3Ym2Bm0INsour30fpluYd7mku6eYiUkfOnXllSI4COzZ13/tZKm\r\nvPPNiOeZheJZlh5uhPesUFZTOV/lsKXgbsfAOdHt9x3HzmxojaCw6tb8EoU3\r\nTTDk4RKUradwd0ChsKu/dw/9DEQpd/nHNtxM5ZyU1ZO+emJfnDM32WLahWyy\r\nY3I4FZtNue1Z6gDNMY2khPK80kkFtbDLzdgnekClWaFH/xz+X90RqUxxoUur\r\nez+oFPYxXOl5eSlloHKxJljnoBRvp302oSw=\r\n=KciH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "06f58f8ca70abc9c09d554967935b58ce85c48d6", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"expect": "^28.0.0-alpha.7", "jest-snapshot": "^28.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.5.1", "@tsd/typescript": "~4.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/expect_28.0.0-alpha.7_1646560968833_0.510234683333801", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.8": {"name": "@jest/expect", "version": "28.0.0-alpha.8", "license": "MIT", "_id": "@jest/expect@28.0.0-alpha.8", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6e2b1f3b417d60e97ba353a00e431f3b9cc2a428", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-28.0.0-alpha.8.tgz", "fileCount": 6, "integrity": "sha512-usUSBK+dDg+rFsq9kVRJDrCUSa/Guuu0IPiZfOYrKI71qHPbCHO05L1InW0zmyS6BjCdaUGrqGS8jOMndBLUMw==", "signatures": [{"sig": "MEUCIC7Ypkt62JjZN1Kr4EIHb3guDR+NgsQsr2kfVVEPVlahAiEA1u9eKHq5t1/R4GbQmtW2Kg2kxjcMKtlj6OKZKk+N8Po=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5175, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTFmAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoeUxAAnwb/f7ipklSuKocltvBD+pSrPkXO3znOz6tvh3v9WjaWeKUU\r\nvnsn71peQFvZJZWZNaqxW5+3YP7jEV9Sx6a0PBl4g7/ZF/2VJm+yV3nLDTPQ\r\nR9hCQEv9ucTjfxs6Lu3tdFyPT0uktE/o4XKTAOwMTRKUK2wmNLF1NAe4bCdr\r\nr8DinL356iY7xWzV9mnTuZqv5wdIwaViWQ7jiBdpn2sDSfKMH/JP0Ic67FiI\r\nUzFuKNoq6dSeQltfwEI+X/TIUEDMgeM7ICyRJJ3pTJ3W6s2wdm66guQzsVA+\r\nBtTG/oUp3awtlDieEjywJn/6qf31kSdyCxuYrLJVXvgLNGQo9QiAs4bfrjQ7\r\nrbWCkBvdgGj3ylkEnW02a6zFb68NBHGh7kZVC6qs/qQSTskGM13Cjes2B70J\r\n/QWiaJY3BsKA50y7T5mXRG4UPsr09yU+qiBhF7b6H/lhRc4csFojvQiqiK2z\r\noZJGCB6XqaWTWqrNSno129s5aAiB4hVS3+5XTF1spWB8318GkaDRnTOp+pgz\r\nqtt6mxenN2kxJxqkde6S4oCAvncIaKj2+OcYKc192lxTMM/xByjjuWx4sdSq\r\nJMNY8V2JTe3pqRkjmV1y1O+k/14APrbfq5OdLJ1l+8SkATtaJmDYVlX9yvtQ\r\n5Js7c0jkh9PwWcyFqzWpYuvjvHwQsEHR5Aw=\r\n=ObTN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d915e7df92b220dbe6e124585ba6459838a6c41c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"expect": "^28.0.0-alpha.8", "jest-snapshot": "^28.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.5.1", "@tsd/typescript": "~4.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/expect_28.0.0-alpha.8_1649170816178_0.9709566005856249", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.9": {"name": "@jest/expect", "version": "28.0.0-alpha.9", "license": "MIT", "_id": "@jest/expect@28.0.0-alpha.9", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ce3bf6bc2bf225ef456f014e4d3c28d4b2a3e275", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-28.0.0-alpha.9.tgz", "fileCount": 6, "integrity": "sha512-F6DINcJwLhfIHzs+VDF4+cjoNFaTEYWv8MBg09f1y3Uo3xVCE633i+0g3CbTWlvhMokhMk2Q44p4tOzvoIskzQ==", "signatures": [{"sig": "MEQCIDhPo6hIM7B2dJjDihqNSP/CsIEMlEyr16wWQQAD/xR1AiAaygGRMLwQPE9fRw+ZRhnBw+veCTnis6KRooezAj6mGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5175, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiXpYIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqP+g//VMRC3iUevyDVcZNytGjvDNWiDLzsRFqhcJFrAphv0HnHja/3\r\nt4wf9qh3/yjrla5saxZhGn92rBgvO9wwPYhxIWJZK2PVI9WG97osJro5j2Tn\r\nqcSMCrwiO9LTeOZvq8uAvfh5kdAXcdATr+51hyFkTwZrZ1BQsPO851E09Zh5\r\n0sgP2c8+wC6CidGEtrzReM9LoDw/pT2sPnKiak9OH6W1ECL1DEQF1MCOUNLG\r\nO942AUNqbRUcK5U2vnB2IAvblfqh1AZNWHhTCdxOPL4fPNOTVMx1LXsK3+Mp\r\npj0IQsqQ7lu0LxTV1SRWxmR6qZoPAGpLNrrOC0mpmjrm95bY2teJLSXDdLiF\r\nqSYZx6/KAbNIrUuSZHPxMNKNI329++kQOx0thFJJJNYv82sKtJ8YAZ3+gl/r\r\nfdhAWbDzkWLqHk0HR/jDcfhuOiVrDcFhU6Er0vNg2GNp9n5ITtEBbxEK2jXy\r\nqhyxU8LOH45E22wGgqQn8WU61xS1iP1J8lD2XGfkkBWOnbOsyQO9jh1PffS5\r\ndZFdobAp7O5diJl44seZTaPhInhmOj4jn6kbd1LqOyXfrxosxiLKh6IyDYtD\r\no15J9FhVm+EWnmj0oiah55E9BcGztaIQzlQEiFeJ3tTRKwUd6IalfrNxWfGt\r\naPkRH6tGZZhPHrtDWgyA5LxKjDV+ImtvudA=\r\n=AS99\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "7c63f5981eb20d4b89a4c04f3675e0050d8d7887", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"expect": "^28.0.0-alpha.9", "jest-snapshot": "^28.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.5.1", "@tsd/typescript": "~4.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/expect_28.0.0-alpha.9_1650365960646_0.6198185560214906", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.10": {"name": "@jest/expect", "version": "28.0.0-alpha.10", "license": "MIT", "_id": "@jest/expect@28.0.0-alpha.10", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "39db51ba1fd81ba8ad0ae79b1b15f4cb34014625", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-28.0.0-alpha.10.tgz", "fileCount": 6, "integrity": "sha512-zC8gsLZns3CcgkdObAuIfko+iKzAQ2QezhSgg4/GldtJv6UjRZim+c/WJAo+NFIBHohcHkUbbpYLuQwNL5V4bA==", "signatures": [{"sig": "MEUCIQC/+RtIn3AgX/Spe5dnA93kv26FGkNZfY9Evlm9XaGViQIgKJkBuX0sIK2VKDRVMJWiqG7n51c/jbIQqwxZYtS/7HI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5177, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiX7g9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmroDQ/8D60D4kYmxa6PBKrd/MobmeFY6rO1vwHT029PGSFyQB8/xANR\r\nJms/v5givR4bhUeQR2r/Ju0bReRZHzeNXGWJbBe6OY9mVhIIrP/ukt0YioRl\r\nnrSkP21rGDR5REK4WG0RFTC6kvX3HY+l7RZ+R8wRSqOyZE1XMppQlbKSCrIk\r\nbS7vdsYKDMJJ87su1UGSJIgXZzpJQsM9C1jBJYh0C+iG3REfcATev1GKaCJj\r\ny2OshcHDppUVPb7uv/k+t/hq1Hj6JMXLhq40ctwTM3y/xgaweZfdGdUWLsEu\r\nfb9mEU8iXBZqdv78exwaU+bFRt34M209vOm8oXbbH4PCh6m/Nk567kMFRmE/\r\nG4RK+lkCVxbykVTbjFl+6QUuGv6D41NqPmS67v4qeuFoyKpIkBEQ4vZg5ZUd\r\n42xsbrbmg/pLUiEBCMG8LDyQD7YCD/93yjx4QFlRmLTY4ZnGuIov87fjRt75\r\nC1n5UU8AMMllct9RUoBFo1ND13nYCHEkCUO/Nc0oe8FIYaYIVQqLIvJlH+Xs\r\nBZSPSsgH2lVd5JPXoyFdd22HgLkNph9zLMCoZwCY+/wGktJgDlr5HbbD+8o1\r\npmpRC8/lHDW2Nj/tKPpBPVxRmLQXTUUoD3IMd8oIQeNFckJkoARpZKI3mnws\r\npFnX0NCKwuh3GdNO5TG5RyKPfGPrsSYD/Wc=\r\n=OAxD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d5eda7f23571081cea24760261d209ff3e01a084", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"expect": "^28.0.0-alpha.9", "jest-snapshot": "^28.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.5.1", "@tsd/typescript": "~4.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/expect_28.0.0-alpha.10_1650440253481_0.7670769676577045", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.11": {"name": "@jest/expect", "version": "28.0.0-alpha.11", "license": "MIT", "_id": "@jest/expect@28.0.0-alpha.11", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2bf7b06ee62876ea63b4c6f319ece4bbaaba02a4", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-28.0.0-alpha.11.tgz", "fileCount": 6, "integrity": "sha512-RR3c81HlWpnx1N6+xF9xlxwkaAohUxgYYKg0TaX8O1oduUQh54Fifoh1bWEA82E45U23FPhVzt24swiMEk/ydw==", "signatures": [{"sig": "MEUCIQDCZB3sl4drcbHRrWQ5vq2/dL/nDmyCRvN/81SowOtZJAIgc8926CnQs2X6zSZDzvr7tn8o61ywl25GyGiSlgNqaPY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5177, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiYAsYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmofEQ//c3x3vOusqSHTHV+3hVFxA/UhMJTMlPJEFJVaafbH9inRs+uA\r\n4h4PChq/xRPB5wk818YwNP1sgQxiJvCWFNpMsxb4ZiKNT7G5luOg1jkvF0dN\r\nyRJ9wFFmA/VTtwZ/i1jy7ogv4MkYR/cKJiLXsDhhovczbgEdyBGdrrez/SgP\r\nNMyAeSZH7OojGGtP1f1oknkDywA88lM6DyRgRrNNeJfE0Jdwv1HS4tjAuk8t\r\nN/MXDgLhw/w6mO9RLZkuYqopIwrYq8tOr+b+PrXhMbEcx52cTqSYuAHGObgo\r\nuVoTPsrZob0RuieHGaBzeqaY+/dwUOAnnI/gpHGcEuIlMx1mOqfoOXkZHBJn\r\nJ9Z6PtiMsFMzdvt2a2f/AJv+tmamVcxFQSpLAJTGke9CkLwovJ7s+Xaj5xSI\r\n++wlUIDcwyBuue8zlWNRBoVF2KP6UQt6tBaeJ/62kc0KeYKouQzDm90Uhkr+\r\n0RFXfQOxVuT2SgDlgCnpi4/n262Y1bedNA+nqzEjEvaFEwXUeC/azcYzX/8N\r\nasUIFuSo6lVjoWxEnqjhkXvIfiuBBopbfrq7JHvEMr3GlSTCGz5TsXW7ttY+\r\nXs2xS5uLSm1fWFJz8yivF1HPO/EXu68D8sXMis+w/kBs7hFIbfRmSerYJDB0\r\ntPhLofyCI/FEDAxJBO+Tdi+rIS0KitPyfTw=\r\n=BLLu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "8b4b78759d255746f11e396efe7d06ac93dbd05b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"expect": "^28.0.0-alpha.9", "jest-snapshot": "^28.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.5.1", "@tsd/typescript": "~4.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/expect_28.0.0-alpha.11_1650461464241_0.22521971290844034", "host": "s3://npm-registry-packages"}}, "28.0.0": {"name": "@jest/expect", "version": "28.0.0", "license": "MIT", "_id": "@jest/expect@28.0.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6aa6f195043fb06728f4ca34384f0d96bc3e46aa", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-28.0.0.tgz", "fileCount": 6, "integrity": "sha512-usQreaafoLj25kgt8YpcWLZlveqdV5/Upa8ouFrtlbLa0jcxlfG9eXvJHfd061/kUinMUmg5umaribPlIZnO9A==", "signatures": [{"sig": "MEUCIHkY73BAJEviQCi3Tx+Kz1yvh4CVjlN0lb061gfptwocAiEArLO7kLEX2QxN4RegFLtSohyS/TUmMAtCBv3KKv4eS+E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5151, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZo8wACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmreRQ/8DKY3B+wNr4xW/saINuTGFdt1LQJWJlBMcPaUVBg65rBodiKf\r\nt1B4SkGMJB89zadFUXSeH1Qm5blfAqQ7rH7DIPNIJAOU7sDdvOHLwJwUWDjh\r\nw9/IbM5wW0RI070mYhUolMbGTjsXjEYHa9JE6mTnAZhQ2mEFe4nZmO108uO+\r\nU8PTYdptSb6x8w1GTwEe8zzVWJWIvHWg1OieG+gOCBGsgfdIiP2s7N0ktB7y\r\nElIkiBeYI+nN82BevsIyaDiTERjJeo+lj+jc8VxMKhGkcUiXLjEqbs+kss8G\r\nEai1KQ86Si4Z1kwH+CFamp2COOzw0INkEaNTxw8PO1aVazxkLjRBrNMDcGuE\r\nG3Djip1nk/ocSe+iSkn5tpTWbpjcbk4W52Oilybf8ZP1KanWed0FldJUfLRY\r\nxtNN+ARD852eTgxZBDNDeiGcEunjXnDmw7oeog7lyJG60arwh17gfEHn6ANB\r\nU+C2PYUvwf/4q/fDzzWeuGaYnndSK4+p1GTyPHYrRXt18punDIcLob6eyswO\r\ny+HDAjCKF4wtRlr+vU/uk93F9l3vhemTdSTtkN9PP3hpO2TLgvdvmyaM6nFI\r\nPubw1IstmQas+A3LguT9BpHDtYcthB8Xf8gKLo6Mi5WZFOFxtCMbHuiiSUPZ\r\ncxzVmxW4H4uUudlBQe+e4pVLBgHBcEGVqHs=\r\n=wyuw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "8f9b812faf8e4d241d560a8574f0c6ed20a89365", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"expect": "^28.0.0", "jest-snapshot": "^28.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.5.1", "@tsd/typescript": "~4.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/expect_28.0.0_1650888496020_0.6872047434109085", "host": "s3://npm-registry-packages"}}, "28.0.1": {"name": "@jest/expect", "version": "28.0.1", "license": "MIT", "_id": "@jest/expect@28.0.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "eb836dc95686e724de3419bf08e43f12466a9fb2", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-28.0.1.tgz", "fileCount": 6, "integrity": "sha512-qRAiC7/gJ/1z2O+TnGCVUTJ/HkqXhDCSm4R7ydfY/rOMryvFzccpALmHdI8joovGRQvkHStM/wwHRHRQTc8+zQ==", "signatures": [{"sig": "MEUCIDwexAuYhvFt29YIkS//DcvSLqE0gJ9yul/SVU+ojHVUAiEAgMAqsgxWCr9ia4c28viQDzREFDtpY1gIWmsc3dIEc7o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5151, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZ8NFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpxoQ//d49M1/AWrMn6NWRQ22lcYhZQy4haj1OuXByUSelREVb++Vyp\r\nUKYilQUXdLpXje3ni4VH+Cme2CJn9yZV2PJZNPnc8/in89z7FHvb6MEe1lpa\r\nSoYZKi4TkdxrIZRz/E1f/iYvAT06ogNqYiJiJVLOSdyXKI9Lfsl51qjFLx4s\r\nw6xNRUQxU22PWRXyW8xY6TR5Jo9y+eOOnFsa4NPDoxWNrc28sbNgS6Q4uZ17\r\ncjstALzQd3fpMla728hbqQxh9gln4zU3JBbZNKE7yhpQm6XKCbid028b6Oy3\r\nAnBKsySkgMzmrumv0UfH1lfyv1JiXHU7KSifhMQzm8/Q8ScofnrdpbQpggHm\r\n99K/iINXJ9ElR5GTBVGwT4whKeJZUx32ht/ThFr2t14+07hC2eBf++aw5tv7\r\nQt0DZpDnzQhvZ7lpSUp4/2nfo5oYGAdcKyNaKcRhh+agzQUugrqclgbEWgP3\r\nOPACyL9V0y0eDyO6dxypy1kB+MYhzaolTA6vpNHa85QFLGPVcnybm0bCbiq5\r\nf8R8/z41RSyof7PQMgT8Nrh4UjD9r7PupJGbsb8CtCVhsb7gmaLyZ1+ZSeE/\r\nEr5H0Eq2EyOeimECoZvr3UqcZxMJjO7C6U2AW236wU6qS+PFr9nH0AsBlp/f\r\ngVFnoHsP8CgEu0fiLnPY3aspJ0tASQy4UZU=\r\n=qr10\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0a08639e4299f07becf1020a761adfec83536018", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"expect": "^28.0.1", "jest-snapshot": "^28.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.5.1", "@tsd/typescript": "~4.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/expect_28.0.1_1650967364867_0.0982575465441271", "host": "s3://npm-registry-packages"}}, "28.0.2": {"name": "@jest/expect", "version": "28.0.2", "license": "MIT", "_id": "@jest/expect@28.0.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ca94154c63a69027da0932b712b0104618cb98b1", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-28.0.2.tgz", "fileCount": 6, "integrity": "sha512-rxgWG/updGoQtHFw/duImu5gPN48+kHvhVjLJ0fFk2mYQ+3dp7/zLiNTjSQxc92Bq4VOk+b6ln0gSgKM4etOtQ==", "signatures": [{"sig": "MEQCIAChxYxrnN5rOYov2RFUdprQxsfjaQhAHa/7+4oFXMzfAiARbUGzVSvysYRSGB0M4oFPYtDFRbKraH1dSRBPybBudA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5151, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaPRKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoLJhAApQDIWb8QTaS6zPTVmj8+mJU8Nlrx9afG9vuSyQ9BSDbalCOR\r\nXfZDRKTCho6IhJqWFze2wKpW5vOeIR02NVq2mph51PELiKuxwQ+PAGpTQyfU\r\nCtUGndNUuG/CkA7VKLFfzH2qjXUkf6CMRz973guhLHfJHFuAo2RwiGbDxykM\r\nI+9DCTVMcolHDn8eyl5Gqk70MaFib+86rzPKzPVqRmUtK1UDeN41sg9CWuT7\r\nQP7kh/jZ1nmjSwTVMiify3WjCYaG4LtyJguiByHjrw/mvkzibCwiN9za7J8o\r\nUFgBkNReBdF5lu7KwbH9MFQTkpWRoaQVH3NzMme6QvokgV6DfjoXfoInO+CW\r\nY5pcYNQsGQnI59Fsv7iDO8l3ljQlPQ9JAo8m7wZDKZ20lATnycn8dWZlrAoW\r\nFQE+6XdeZKe4i7Mn57F8FFYMctf8gA3T2++U9OnaW4LBm9kJkEsUyDrzyVjh\r\ncF254Zhi4pSYysqXBwFFaf6TKVZ0mFOaO839XvU1uYFYdzqMNjhANb8yMQ+u\r\nX6jd1JIvT28xlLOErI7mb3jLWzy/2muZFXukNGToSRvJxhO7D85mbmkMq57E\r\nKVUqqOL3cifA1up4VNUuE67DWEDPqCj9pXDpz6NImNrFFZkYUVtmZ7sfIpzB\r\n8lAE2zAwFn5qKW7bSr/25tJ5WVIjecyIpEM=\r\n=ik0z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "279ee6658d763f024d51f340fab6a37c17d94502", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"expect": "^28.0.2", "jest-snapshot": "^28.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.5.1", "@tsd/typescript": "~4.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/expect_28.0.2_1651045450407_0.8606068035672565", "host": "s3://npm-registry-packages"}}, "28.0.3": {"name": "@jest/expect", "version": "28.0.3", "license": "MIT", "_id": "@jest/expect@28.0.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "80e0233bee62586e1112f904d28b904dd1143ef2", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-28.0.3.tgz", "fileCount": 6, "integrity": "sha512-VEzZr85bqNomgayQkR7hWG5HnbZYWYWagQriZsixhLmOzU6PCpMP61aeVhkCoRrg7ri5f7JDpeTPzDAajIwFHw==", "signatures": [{"sig": "MEUCIQCWVJL5B9ZacyqMZuVlDUTRkoVNMddEJ8846TDMg5jJFQIgJ9z9se5iLp7THZHnxRu+KJoeDucaQD/NeWvWkTiVcJY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5151, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia8GMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp7bA/9GqO0gT5yqJgXNGpPgoeJPEav+dsSyLhbRecmp4GMhHe77Trk\r\ng3ux6tAFp/92mkaN3QHH6xpRx+hdMPRnvqjlrNE3d7Gg9JDxDN/xfy1QVTn9\r\nBn7XCEjkvc2Gw+UhQFgxKhL/pSQw4dlPiWloMpQag5Mccbx2zU1tB5pGL4zX\r\n2yBHyGdRqP0aFmB5j2gcAOEWGS+YHrwXtmukITKoWY7CW8F97YQI+waVdjMp\r\nQNtMdTrnojqh34sALRC8zfkLix2Feu2A44F+8qYiUh2FITnkbo9Pgcs6/+lI\r\nNjkpa2xPx20PThSRDiMutAZxHaIWe9FTBWes2sRYHKD2a2l534LKxGtTpnyh\r\nVw26tPeJ3/H4hrxAl09x5DOtKqYsNFkuOCjL8CMPum6JrK6KlSOcf7Vi2vYu\r\nO/OuuyTjy+NEE5BewEGj7ah9kSc3qI7Cm3VNfK8y+wBqdR9D2XJ2xREsBJ57\r\nN/W2jteVU64ikeALoR//ddg5xL4buN7vvsNKAz+gJBnGoJ0vkIXv4dzumTXp\r\nCRgYH2UkeY7oQf8kjGBXrgEG9N6WDFRvGzigMMQ8xstqlBRwqNKuZi45xY7b\r\n/ZuhmzktvzXB+SUR7O+7ut1wjbfQ6K1GznGrYFCWheK6XcujJdKINBaUuASX\r\nLFUz9xE/lJrxwmTVKIi5RjcuZRfMohpjcQQ=\r\n=DbVY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "1bea778422dc049b0fb3d7da809ffec39be549ff", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"expect": "^28.0.2", "jest-snapshot": "^28.0.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.5.1", "@tsd/typescript": "~4.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/expect_28.0.3_1651229067874_0.02665531221398254", "host": "s3://npm-registry-packages"}}, "28.1.0": {"name": "@jest/expect", "version": "28.1.0", "license": "MIT", "_id": "@jest/expect@28.1.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2e5a31db692597070932366a1602b5157f0f217c", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-28.1.0.tgz", "fileCount": 6, "integrity": "sha512-be9ETznPLaHOmeJqzYNIXv1ADEzENuQonIoobzThOYPuK/6GhrWNIJDVTgBLCrz3Am73PyEU2urQClZp0hLTtA==", "signatures": [{"sig": "MEQCIHLvW5SWD+ITHOGhtRI7AVjV9vVJEw9OnjNyqJlFFQ+hAiBI3QsOJzTDKQiy3gtDqgONbW46otsIL47vPCnzdLRd4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5151, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidP0cACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrqkw//RMSDgTEl11sCepsvu9Zthnh8Y4R9LS9cLzyiRVclgNFMyq5Y\r\nyFK8xeFxrwQyhvd7HNeTBm9lZAyGCkAhd1QXVK/uNYiBQGkGlSAmguFCaHmo\r\n8GZhSlfd9jzYcHhOg4pQcWMYnmw9+p3phRQQLhEVy4xzqQ4Mr2KNB0xCKnh2\r\nWNSf0CsRpMNOwsLbUJNaObQh57HeJStfX/eQc2Ll7pxUVZCv0FgPCaYluc3g\r\nOAgL7XUi17X8yZGnrr1z5iF23DDFMYDJvK3Hy4f1W3FML16T7cl6G0CnEcP4\r\nna2m2WOYQKwr3X+SmPzcUNisCjLxG6F6oBOcMnf2AWPdWHAvsi0Qp4XKJWXk\r\nyfbIbWX68bd/4bpiU5uP93bBJ3VKYpMhWzlkuA4gql/nKjOep5v+OGcnpOnN\r\nrjyUrKjhzgmUDGrLEp8ho060m9lsBuVXG8cjt7C1T/+kVOYaxiiM2vMRGmOA\r\nJj12/W+VD9AigksHRQ2q2ZfdkvQwvp3dmq0+3u9HdshZJfQ95+POSmicNlAU\r\n2KnnHuow9AzfS1m/YsT+RvKCTZOT5r5tzT3p71cVD3WXTb5ExrLZxkJaW4Bx\r\nyYZ+DvvhUk1fC0G0JDdtxj+wXrDK2JdsEQq7uBombJGGEVtxTzWVvgM4QO70\r\n/KjB77OzsDjMpVa0RZRHkbZPjLfAXQOnC1Q=\r\n=zYac\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f5db241312f46528389e55c38221e6b6968622cf", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"expect": "^28.1.0", "jest-snapshot": "^28.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.5.1", "@tsd/typescript": "~4.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/expect_28.1.0_1651834140009_0.11751122636293299", "host": "s3://npm-registry-packages"}}, "28.1.1": {"name": "@jest/expect", "version": "28.1.1", "license": "MIT", "_id": "@jest/expect@28.1.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ea4fcc8504b45835029221c0dc357c622a761326", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-28.1.1.tgz", "fileCount": 6, "integrity": "sha512-/+tQprrFoT6lfkMj4mW/mUIfAmmk/+iQPmg7mLDIFOf2lyf7EBHaS+x3RbeR0VZVMe55IvX7QRoT/2aK3AuUXg==", "signatures": [{"sig": "MEUCIBOJaywUxq1odQNf9enucBXziT40/yljOQNJD5G83k74AiEA33PnQmj0weSdXLbqpjz7DiHPavG8ViVLJCscTc1Y4Og=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5151, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJinuumACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo39w/9Er9UlS7jhmVv5upIceWpHdYOB10Ngu3jBDufFdAMBxzVfjw6\r\nelgvI9ZsidxDqostmUrgTPHFehDaB5XXWu5SHfbvpZEa9p1NZphzLDtJ7I7U\r\nTKBS9yB6XA8P4PRBGXFwkDv8lcvEkkDq4CI4l9+pz60/eOb++ERo2zNBz5HQ\r\nzlQCcDOUHA2aAu4aPNfX+73VmNzScXWUqZmcOBImrzqt48a2DDK5yGoOmLJk\r\neA5uovBwD/CsTtCI6nxKvl3Pc4uXOxMGKwUECNjG2wtybm+rhhksj75fUYET\r\nhRTeubOSIoQ/X80ShqEJ+YHbtj//+qNGD9q9ZeYBFMeqFp8hUBtCv7IHT+aS\r\nqMXoqQoq9Dvc3QrVF5vpu9XKfFrfWftuMxVnbZisz33hQShDLvWyvhHUMZe2\r\ntSzjOp9PCjERxzQCfKX8yhHw5SfyCsoVUPrzoPiNJQddsfHYMOk2J//e1KR1\r\nO7eT0mgpJuRJhTfEWdOQ+x7IQxtc3JY54uMSL2POrmJr5qLfB7gZPyYF5sqM\r\njcVc8A/fphjXo2WIsftLfAi62OYw9v0Fs/1mfo055wRCLOjm9TJTeit+uE7J\r\nV3pmyVQEP60qeKNHclpZ7xuCTVwsTL8l8y1oPjdj6pcVQ4q98SR7REMTo7X2\r\nAgvom/2QWv5WDCU+Qr8Axols7cNNnfIMpd4=\r\n=52e2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "eb954f8874960920ac50a8f976bb333fbb06ada9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"expect": "^28.1.1", "jest-snapshot": "^28.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.5.1", "@tsd/typescript": "~4.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/expect_28.1.1_1654582181750_0.06896900795300565", "host": "s3://npm-registry-packages"}}, "28.1.2": {"name": "@jest/expect", "version": "28.1.2", "license": "MIT", "_id": "@jest/expect@28.1.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0b25acedff46e1e1e5606285306c8a399c12534f", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-28.1.2.tgz", "fileCount": 6, "integrity": "sha512-HBzyZBeFBiOelNbBKN0pilWbbrGvwDUwAqMC46NVJmWm8AVkuE58NbG1s7DR4cxFt4U5cVLxofAoHxgvC5MyOw==", "signatures": [{"sig": "MEQCIBWQuYKBzjqFxQ2HeDkm1zZuL3drEEDL/DZs3xgoIQq+AiBJHh3uFtb6tJ4rGmwomJ8cNLqFH8wSBMACKWzufvwBLw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5151, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivCqYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq1hg/+Or9/8avEwTPPF7pYvbhPespnWiFSj28p5wnq03nPfQU4A/GU\r\npKCc9141p3TIuPVKIGfJSRX52163Dnb0szZgHP6I+DGI/97ceS+bq3F2tS14\r\nAh+Hlp34V5tBhVD7jXUwIdovhIUhJfJUOLRYO3dMBQ16v8WofNNAGDCmFszN\r\nHdQ73mNdWkdYkzXCFw4rczRqSbdHw0pRnmCd+k91QHeRHHXT/s/3+js/UoEL\r\nF5grEOcC0Q3zfBZojl+MOlWAF+PXGLqig71A0fv8Bi+bZB/uFRGja3+LMU4z\r\naMXYfaCCDRm5lRe/Se85/2f5FfhvlbEwGm+s8QwocG9iqL4/onNNhfo7y2nX\r\nQZK52akU9+H89qGPyROSpLApYAslAaJS8u9Sm7mp/wWOcpPbd9Wm/wVx8j4l\r\nJcrh+z0LYkmMkCtobbln03jeOMDudnj7pj8js486VerYt/2B6CS+FQAr2TYA\r\nphXuZ864/9rUvoxInvT/yKDBjxMJMI1T8mOC0dqtGuoyAMCkxhSDwZ3XtiMT\r\ndWZkBPEJJvL3sybpJETQGG64BukIZ77lD/Pgp+DKSPlmfP5fVj7D+9jM2DBi\r\nSGcHT/iSbwDe1YnByQVVLc+QIAn2MmouEf42bxQRlDCzrCLmy+95Gjemdekv\r\nmTFPU1yhrk71uIJTfKp4L+OHa3yf1UsNl2o=\r\n=zlrc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "789965efec4253fc54ceb3539711b3a3a6604d94", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"expect": "^28.1.1", "jest-snapshot": "^28.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.5.1", "@tsd/typescript": "~4.7.3"}, "_npmOperationalInternal": {"tmp": "tmp/expect_28.1.2_1656498840625_0.6750122473511497", "host": "s3://npm-registry-packages"}}, "28.1.3": {"name": "@jest/expect", "version": "28.1.3", "license": "MIT", "_id": "@jest/expect@28.1.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9ac57e1d4491baca550f6bdbd232487177ad6a72", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-28.1.3.tgz", "fileCount": 6, "integrity": "sha512-lzc8CpUbSoE4dqT0U+g1qODQjBRHPpCPXissXD4mS9+sWQdmmpeJ9zSH1rS1HEkrsMN0fb7nKrJ9giAR1d3wBw==", "signatures": [{"sig": "MEYCIQCLRKYmmNZ5sv4m8jzVsrMz+gzZMluZ9gh7JGmm7GAH+QIhAPT2IV5UeH/3Q3e/Jpp7t0v+uVp4XJ7Mn2av01rbP06w", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5151, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiztLUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqGMw/+MnSUsSxxAAafgQbyzHZoU2r86oscemh5uh7Gnlb0uu2GJGT8\r\nNlAJxXeQ5oSUqG20aXa2ewWkQJcipnAavSDkhuQiq+tgpo+baW72qCa68HA5\r\nJ78dzF2d4YdkWU/BBDAED8uZ2psqULL5mCpsUHmLuKKVDkd/C9LhzSXQNnH+\r\n57Y/1fiivAsqhJIdIEFGJon7KN/QL345xZ644GkuR2tiglBMtTiaYDscQvKc\r\nRHCU6m/KSosvOfb4TlpauJSY+MKSvvq6j6vhLiuXZkvZBGxEYsqW8575op08\r\nnRygjaL7wcEzVNH+YLb8vIc+IyxpfvOsWn0Enjf2UWQymCZTPV5EZPYcN5N4\r\n1+54R86C+eGVNuPijb7y4uH/AMEojSkJq1jy0yJAQ7BI4h2uKhlRvAFznW6w\r\n9PM2QwkhZQksbho3RLGxtn1+q+MTMiNvZdKRpHEL2SZ7Vj2RHPCP+VgKZHgS\r\nBbFp6Wc1cUyywg0tu405ZCY/7wuAIRj+zcfICOBP/8KVy3Qwfm4ZCTqXuMqb\r\nekAHUL4kk+/7w7puuS6sdkaas2u8dzXUPXPT5lk42yyaQ2tryWVbO8dkr6SS\r\na3s9U3RLFyvLUJQQXqDYTBEfPmwVVQOeVvDOP9EHIeIYlcGU/uCPYjW8dB1J\r\naAmSVpQPcBBneTHzOfIzYQFbZqg4KtEz5Ko=\r\n=yzuM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "2cce069800dab3fc8ca7c469b32d2e2b2f7e2bb1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"expect": "^28.1.3", "jest-snapshot": "^28.1.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.5.6", "@tsd/typescript": "~4.7.4"}, "_npmOperationalInternal": {"tmp": "tmp/expect_28.1.3_1657721555984_0.46702452104834613", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.0": {"name": "@jest/expect", "version": "29.0.0-alpha.0", "license": "MIT", "_id": "@jest/expect@29.0.0-alpha.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "cc66c67f4c005c33da5d1ea7331d3d0500fb17ba", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-29.0.0-alpha.0.tgz", "fileCount": 6, "integrity": "sha512-6hdCxFogkn/UJguM7imO7uYM8KHQUIK4uk1r2FnvbBbfPHd1SG2aBD8lVVfumQq1pr0SDyPBiQ0+RXF6TitlZA==", "signatures": [{"sig": "MEYCIQDxzx73qmnJj8enlAtFgqcOklN/ro568faKV7izuPgsEQIhAIo4RW+oJnYXGIEhKgNgwNLdaDPg4X8yASlq/nHHvt3p", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5163, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1IgRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrCYg//dmX7ES4dg2W5qdRZ3BDGtp7OLljkA3maJdR/gZ9Wjmu214si\r\nx1Yth8LDEYXNn2Ok5gkSgp626IwgiEHlVdinGgl4dVV6zu/d1pJTaKqNNO8a\r\n2fM4Ax+YVokI0cwV3zjfXXcJVrZ2Br6s1C6Aay+nFv2jESpu6NzzOyPPyvja\r\nJpbbhXgnC4dNvl5A62XZAfS06ddry4gPY2xJiZczZG7bYbnTm75GcoPDptHl\r\nzfjPwcixdT1DDqzOYX34ukd4bqBV0kjSKReIjvpbfmri6233w7qx4KTaKbLk\r\n3QoqHWQ1XedchvFwWofQz04D0rrAHoQ6ULknxoC4z1yoUHW4tMc2CZjWOLot\r\naeB1TN5qZSImWl2fR/dRURmLqa/KQBN8dKzqRpjjxIDRYkgQRTxj8XoV2zmH\r\n0IFWOAEH1E5ZVZbecczQNzkySFpIAnMCbQb0NSfF1LQgQrSnFl46lrCV7o1v\r\nVeKwy42O7iJL6MapH2wxmUN+2SxnSEU6V+/GGUv+0+LZkqjB0lFeTmojnUwG\r\n2n8lu0GjQ/XvCOOB0UTh2tZmz6XjWpgfR63ROc7YTPD8wgv1sFTQkaU9uydQ\r\ngm/YvFhOCF/Ud7vc+dwurcwYHhltv30CtxFP2Il6dQrl31Yreu5oWj1MgCs7\r\nu+cV9pbxeZZIpxLA0ONFdqHURIozOOPKIWg=\r\n=cI83\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6862afb00307b52f32eedee977a9b3041355f184", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"expect": "^29.0.0-alpha.0", "jest-snapshot": "^29.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.5.6", "@tsd/typescript": "~4.7.4"}, "_npmOperationalInternal": {"tmp": "tmp/expect_29.0.0-alpha.0_1658095633648_0.4360387670380974", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.1": {"name": "@jest/expect", "version": "29.0.0-alpha.1", "license": "MIT", "_id": "@jest/expect@29.0.0-alpha.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e02c9b80ef63cbd22c0be9bfdba906b6546c75a1", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-29.0.0-alpha.1.tgz", "fileCount": 6, "integrity": "sha512-7/5rjCIx/Uf9gNZ1hiv/GPOHJtY2HzUt5iNo0m+P8bF4wQPNPFauBAfpPFFwhy1hDGGrJqdDHLuUYUZ9xqA21Q==", "signatures": [{"sig": "MEUCIBZ5X3T3SXvWGClGuXFs7j/8RYnu6UuCwD0mL1lwHb2QAiEAkaPsOFAQ/ZZOjZhlfgKBYNDbSQmtoZanbaxJUZOI+YM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5163, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi64IHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq2VA//b7itS35sm/8c9jfz0IdIJwkYwd9LXoXRYEWQJ931nSRshrO6\r\nsl/lTlFRqteT7VGbV3JgIv83ZbW4vdddPdtXPUj2JiYftlwzXsrGUX/u0P0+\r\nze6cVdwc5M6oyIgD/owNRemsr9dRnpQkcDnCsvj0OTop165rSb8FfOafURhR\r\nLiqtnqQ5KFYJz7594LRBy2FC9p+b5oY9fQk55Et4La3phP5bbyEmVbc3wQEm\r\nKRS6XKyorjJia5RqO6THoRd6yrW+hxeBGakVQs7lR6sIi7yvI6RQdaLwT2jG\r\nXRL38nquhaCcQVBiFuQ3uUgG3tTdiJV9NTvixGqiXsVEmzjzaTg9dFg/9NOW\r\npThK+o+hTPM5Hj+uYxVRZnvJsUSY4vmiEvx6khsJSChf0k1y17ohdNoSGlzW\r\n00x2yIudDf8MIe4dGVLiUKLLt0wG1y6/3qIBtDkTpqdmI/B1rIhMk40oF5sD\r\nCgTsBV2kxp122rNgukRzSox5V7bTW9zYdKWC8MQNAkywkxOtFpG/PJxueBR4\r\nMAlU4mze8R6aTC7r/SjIfRjZ+PrL7bWrbvfFoOtES3KgB1O4MApX/LduMj9b\r\nZpvTEGPJB2/eu0xWd7gH6er/qmmqJ5d0zrLblAAF7208AKNcuPCVmvzlLldZ\r\nWz6ShP4DfQ9ksM2uANidvX6yUTsiglKEdHI=\r\n=OVel\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "10f1e7f52d9f876e6fb7f20c1903fdcddd8db8b1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"expect": "^29.0.0-alpha.1", "jest-snapshot": "^29.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.5.6", "@tsd/typescript": "~4.7.4"}, "_npmOperationalInternal": {"tmp": "tmp/expect_29.0.0-alpha.1_1659601415308_0.060747060020011734", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.3": {"name": "@jest/expect", "version": "29.0.0-alpha.3", "license": "MIT", "_id": "@jest/expect@29.0.0-alpha.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "cf0318b66040b359d6035d4b67f3984d5c23972e", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-29.0.0-alpha.3.tgz", "fileCount": 6, "integrity": "sha512-loMfgOXmPSSwXu3E5oA04pqFs1sRVZ3K2ksh9V9mLoJKnELhEovopDBo3eIKsDHYYdHfQ+SMdfBrzy+bDycQsw==", "signatures": [{"sig": "MEQCICawTsNMc3XU2Kpz3s9NO81v/5C+5KPtlfJNlv2kfW73AiByOdYqfl+Rgma9AsHmOtQPEEpSaJppl7YwJxXkhpk8FA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5163, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi78EYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpMEw//WApUDCeWL5lE65oo/GUZG6MJ36Cv4Tw4PqEkxqhcpM036gv/\r\nCUA2gr7a+rpd8f1HugheCT3BEQKozeXtxG9uT99nV9dSHCJPiibxskA95USG\r\nEKjjsCm4tS5qjpR+UrVrYxJgynTNoJAVWjZoaqzpZNSb2wvhV+ovQwKYUOHN\r\ngVNLa4GkKfFLblI/w3OKr3d9RhxCMOJVSNlavLneSgmsSXdZVHMAO7Kw4Sn2\r\n8aoM5wf2oMpVoBYa9tjPRESmV77mpVXNW9nJgHpHjVrbzZXD8CHvwaBtvC8H\r\n8ljwOJFMjBluyA2pYP3/aKVUVZiYJn/KfCU7vSWedrf3PI44xnUrsCE/Gthc\r\nwb50LiIRTM3SjFkZoH0W5cvTTXba4QCnymo2dKUkt0jDrbnZJDJhVrRU2LvR\r\nQYi5I3wPm0a+FpuTH/O/+CFuV6iBxd8JwQlhrO3boLo8xbmlJlK6aVjntTDL\r\nbFCzukE1EL8f9ITqU7A/CCFkSx+TE9DyKcVCfjFdeWU5cf8zIg9LUc/45nJF\r\npdjF/IyJ4aoBCcXvjpCKz/2Y64B5Aw2eea5eDy2+207KRhzdvxt/w7uiVcUu\r\nnx1N1ucn42D8lRm/OqF3k6atTGg9FBrCilgb3GTRrpjV6DjR2ByCg1r6IoYj\r\nX1Ur4XGNAA0wuawCPXNxQ2fgj8gBChyo1Qg=\r\n=ODhW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "09981873c55442e5e494d42012f518b7d3d41fbd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"expect": "^29.0.0-alpha.3", "jest-snapshot": "^29.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.5.6", "@tsd/typescript": "~4.7.4"}, "_npmOperationalInternal": {"tmp": "tmp/expect_29.0.0-alpha.3_1659879704722_0.7522610655081645", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.4": {"name": "@jest/expect", "version": "29.0.0-alpha.4", "license": "MIT", "_id": "@jest/expect@29.0.0-alpha.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6beceb6a323310575ad4f77204ef7f2a68b7f769", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-29.0.0-alpha.4.tgz", "fileCount": 6, "integrity": "sha512-kOXHATWUjNGcjiekl5uYokfU7F9AuOC2h7ZGpCFz26dr79queeovRe/TcPkWdJ9qNyB0lZ9CfxqWd5k6VDrjkw==", "signatures": [{"sig": "MEUCIQDVJUaaTm39az2ucGKX4ggbFJU2rCJUlali8KWja21tuQIgJSlcsuDwkacP60pXxo2sYXVtlbMsi1e6nUrOSiB860o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5163, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi8QooACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpLFQ/+NvVrS2IORc5NPzB9jGcqwjPOGqrsme7FFutDXOWB8qKWKDfW\r\nFHOdiV0CSWXibKT9QBkvYwzDZPNCl1enDKnYA3F5m0fq3mQHR5h8bo6LQMYu\r\n85jdpLVrf6xpmx7FYPiYyR6mUdAI3IcfZkHc/rzTIB87VzIZ7UZjLrOYanm6\r\nhUkfa/IuabbDH6ua2DpN1I2AINr4iSWPIpJzRtZ7EaXcpg66DsO1BvCigK/8\r\n41ZKz1hg3Hnc<PERSON>cqLiKGZIKRAbtNEx8z3l2ujyK4kJH4HG9ptEqsFBDAzZuy\r\n280eFhMw8Rhfp7ohE7M3oQ3DdVXf2Uk+QpQT9PywcMC1OeqKsGCBpD2q+9KV\r\nlhzZa22phsfBKY5So3EB9Ya4Ff0xkgaxf+AhgfXCcyVQvJ9SQI9kl/kvmJ7l\r\nkWt09gIyUUPQ9y4yrGkLvmTztSGuyQkaA5IS5leUQyiOonUwX0+OEPiR1SjY\r\ncUO5mpvHyIIUQRsuIoCHeVdQ1+3kX7A04e/dyQCXvkCmdgOJ5jQYfzupP5KR\r\nS6WlvadZGkEkCau59B3ASJ7mKXy5+01G626DzPvPyUMfw7DYQq03vtvYl5fI\r\nZ3/iM8YLHqOpgmpq2XcOAOoori+xzscIRZWcYFvBw0mbsWmmnYhhAlLuH6w1\r\nWoN/lUbhUusGnpyjydxN+A594mDV5ymQeXg=\r\n=OYef\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "98a833bd4bc0bdcfcee5d4f04c2833400c4e2933", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"expect": "^29.0.0-alpha.4", "jest-snapshot": "^29.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.5.6", "@tsd/typescript": "~4.7.4"}, "_npmOperationalInternal": {"tmp": "tmp/expect_29.0.0-alpha.4_1659963944485_0.014128480867757087", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.5": {"name": "@jest/expect", "version": "29.0.0-alpha.5", "license": "MIT", "_id": "@jest/expect@29.0.0-alpha.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fa89aaf8d120b371d6deb62e2cc65d15d117d7cf", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-29.0.0-alpha.5.tgz", "fileCount": 6, "integrity": "sha512-tbN8bAgUNQbGuGkFiszi0joja5Ftl1Px/BFudnkE6G8DUk4sTA+7fMjRRu/Uz/fHNf+HyDIRGkJZf4JoOSQntg==", "signatures": [{"sig": "MEQCIHUkkJBmqinmMlzMQ67segPyQMh1rSbhOMQysJKy7Lz1AiA9GuxQV2OzDCcAlVFtNSmsp0q+mU+SHKEjc3kdDG9C4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5163, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi9QbuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo8jBAAlXyN2gpont8OS0dkRVk8ccktnNSWo4/moJnStr8HW0Lu/Sx2\r\nTLjoV1HLD4PMyUIxEl/PZJS0mecgibubbGkWWaTi7SbaqcXAasSq5k/wShxh\r\ncIoVjZQXIBTI6ldCfYsg7nF9PGVY42jZvJuJsC0s3rWK9akwAV8JkGMFXw6b\r\nIslbPzwY98amNPY1Onwo1xCIaOb/pGWj1JdES6554sExd82YnkTq1KZXyWGq\r\nkWB+2hCSq/4DASZgW+sJNaVa2qC7lBhXFr+wpPRTxdbFRKn354PRi5fhQBk0\r\nF9oeNirm7n+TsG5wp7Oru58+IcwKitW4YnoDWGT4mn91i8XC5oby44S7DZMz\r\nQxPrzrDBXVEOkeWL61S/5yxrK4XVxbnbKoYxE5UXkvO+2fagxy1gYIsqkNsH\r\neQess2bizMPrTkIege1VGBo2mFyE0NYtRwKZPOpd8CzZKPzhRDP6QbxD11Kv\r\noiPv55LUV+FLR9B59OQQouPvehxUXkOy3/ocDCnQ2DNuGyAk/pfa02sgmFz1\r\n9SlH0yVduQsMWW671ZHB1eNh7pIdh58/h6JsbC9Hbg96lhkIy0aJbFd3xgH5\r\n7fXYZYAEDkCg5+bMFwADyGiSnTXavVJ50UW3E229T1s/AqGrZYcE5lC42B93\r\n/B7E74/UeOpLGa3dzRzrqIWaS9yLXZfI+e0=\r\n=3rl0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "63e506b5d1558a9132a8fa65151407b0a40be3a5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"expect": "^29.0.0-alpha.4", "jest-snapshot": "^29.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.5.6", "@tsd/typescript": "~4.7.4"}, "_npmOperationalInternal": {"tmp": "tmp/expect_29.0.0-alpha.5_1660225262569_0.48829734294256344", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.6": {"name": "@jest/expect", "version": "29.0.0-alpha.6", "license": "MIT", "_id": "@jest/expect@29.0.0-alpha.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ebae3a262851561aee9768d1c7735ae50f9fd5a2", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-29.0.0-alpha.6.tgz", "fileCount": 7, "integrity": "sha512-9O/k5Sfu8yCaHGgdnHq+Zo7W0YeIDLEcvA+nGGl81DI3M6YRpJVtklsSyIxfTdLiMan692YqJkddlMweQD4ZvQ==", "signatures": [{"sig": "MEUCIQC3LmYa/RePJukLQR55zsWDKAvqqYB9yV1Qyp5Exh3loQIgdOTWM9DnmQFf9FTYFjnQ3dycHikvXfkKQJnLJSczUME=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6952, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/5boACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq3VA/+JLuyP1a87dFhnaj5WWxMwaS+3YSLRrtK6bjEyAlYLmz/iFb4\r\nW6lXf0+l5FAbNm0hBqYiCDDsoaMTZcc7cxCF9m27F0yNXYKt+8t/SvHB4LqS\r\nhgJwff3TF+8SQOtYO52YSdTlV9iU4f69JGeoFQUCHUpbVvRDbvbAcfK4YbO7\r\nS2+JMEClaOEtBELCZHIesSPRyOg3vTa3Lmh1s1qe8Vnza67YnW02E1AqeWhd\r\n5ZLhPoH3Q29UMr/n7Ls4ZrYjmQCHd2dRm0q+oSOKlP8mboPt7xmcyLR96OXe\r\n8+8HEwtWg/4w32g9ZyOHznhUtdg5L75G5QDCkVCfgwKAw5Ern6O2rYaXZaYZ\r\nIBDAH3zIN08U2H0ICJzMxueRnZQB4rrdVaBRbJ6vLsXWeny/QENHLPqcOuav\r\n+s+6478jDPCv5sMlP9hR6rS+m6aDEyM/ctRL1T7NDqy9/0CC+pWkb1yChIgt\r\nHN3/9/DTdVkJHQY4ACa/mfyHZdkP3s85MqfrcxKGji4Rz1n82TrJM5g5lEXO\r\nUPPmusxbwzberaQaeMXO9uyutxz7NG3tjHteeVSegLCtgGnTcfmBQlglUwLT\r\nTKGGo12f9IrkKpcWBG4UNfuweyZuDems7P0N7gxyoqT7U/4nEy8OHD1xClZd\r\nPgx4Ox6SmNpUUICbC7vCnvOzVwuh8ghs5jU=\r\n=okTY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4def94b073cad300e99de378ba900e6ba9b7032f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"expect": "^29.0.0-alpha.6", "jest-snapshot": "^29.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.5.6", "@tsd/typescript": "~4.7.4"}, "_npmOperationalInternal": {"tmp": "tmp/expect_29.0.0-alpha.6_1660917480178_0.24852617205914895", "host": "s3://npm-registry-packages"}}, "29.0.0": {"name": "@jest/expect", "version": "29.0.0", "license": "MIT", "_id": "@jest/expect@29.0.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e4586136c27aa460afa427db2cd494d890d07b48", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-29.0.0.tgz", "fileCount": 7, "integrity": "sha512-X2S5NpZOeXXDGBLvU/4K1nAD5iIz6/9Gs041wToI0FiX3glh/aEGGsVv3+SxKddYIb6Ei+ZbqzJmfRzQ7nwPlQ==", "signatures": [{"sig": "MEUCIQDN5731c+et8GgbY9te2448/IdTcYq3riqOSIj/jSZnlAIgAQuAtj/RsriefjefGyMoXQ9SlSdWUhXwOkX1FzI4C2Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6928, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjB2wgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrg4w/6Apx5RZ++VNu6/CGd1JLn9S+ulNSF7z6QHV718w36X7TMhY9i\r\n/I/5flR9/P/3ZIjxcfzkbPUCkGYuCl/Uzz5jTf4+krm4cge70CKl/TFTS0HW\r\nZ/G4TMtOBjrh3S9YASyGMAIWmAUM/0aCe3dZo2v+EaQqlHa93QpCRRESkxuf\r\nxBqigQZHrodqxpH77n91jjea6lGeSNLBg4Wkglmrz7JoVoZakCGwP0JCFn2B\r\nhjuRpiEfzbCcgNPzTAPnobuROvvTkSn6ltI4Tr8rwsMMfsqM2IgPze/nOjWY\r\niQ3teSd43fHAU0mxD29D00igB2ypf5EmQGPjdOwiqAr4JUzxH+/NVYKaC7+t\r\n3Zhb6wibcm+Qx/lFu5910Pf6t8RSj8GSwwGVAKwQxHMaRKU5o2esjYejCD9K\r\ns+O829BMSkEHlFzBLG4fpvkWxivVfdRPG5WZevuTmTe/6eXcJI3kf/9vJvIy\r\nTkhNjr51HoqDeQBV2P3EkJCsrzjGiXm3aSROkQjHaDoP1HZiAt6s+edZ4vXf\r\nLx5B+bo/ZwygtyLhoT22o7xRwEaM/j+oP3NHpPN8nZ04GNRpJOaRHKV4Ma+9\r\n82+bC+dr3fZgnz/gqoFL8qDasTiPrDzsIRiRuVWtRtr2gGU+xJnvliSML8GS\r\nkpgjA3A1DZgJXZPbyjUaqtu8WYH2Ke6Hkso=\r\n=3Sys\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "75006e46c76f6fda14bbc0548f86edb2ba087cd2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"expect": "^29.0.0", "jest-snapshot": "^29.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.5.6", "@tsd/typescript": "~4.7.4"}, "_npmOperationalInternal": {"tmp": "tmp/expect_29.0.0_1661430816719_0.6465541644185557", "host": "s3://npm-registry-packages"}}, "29.0.1": {"name": "@jest/expect", "version": "29.0.1", "license": "MIT", "_id": "@jest/expect@29.0.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0ffde7f5b4c87f1dd6f8664726bd53f6cd1f7014", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-29.0.1.tgz", "fileCount": 6, "integrity": "sha512-qKB3q52XDV8VUEiqKKLgLrJx7puQ8sYVqIDlul6n7SIXWS97DOK3KqbR2rDDaMtmenRHqEUl2fI+aFzx0oSemA==", "signatures": [{"sig": "MEYCIQCpn+brD10ORz/n5NlbjdaRCrAepGZIDqHAulIp2dB/CAIhAKiC5FO/9Qezr4hpyYc/1qmKShwIXy7JaD4vTgMPWVoa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5269, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjCMv6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrQhA//fKVEj+TgHXvyHc5PqbrPhwU3YsxUJu4WHOGCeI7U7Rbj4ukV\r\nYTnmpdB/DdBcp32vKryGUH8Rlu0/Z5p15kPVAFs8jkbUhtBr3r2MXU5I5CRL\r\nD7RXvi2Jv4jdvI4QMsVwPjHRk0t0g3+BJ1fMLS+h/xJh/czo5DR6I4FpYE/L\r\neAwLknvKvojxEmt1ORakuufFxiRzbQ/JHwABJ4w2DqNea4XVsxXon4b5hDbu\r\n8cMPJBkdq2cCh8SZKX4FyaciJOiOr5NXh6UQpcIhulFG2YNSI1mWlrSTrYCf\r\nicIIcS2gd4reYHmWIcEBqK59/o3cVK1K5Vp7P5PD5QV5zQfAqi72GcGMobur\r\nn70zYw/87fRvi1wKpYpuCq9C1Uqqwj9tQksBwchllJzozYTHJihmbPyq9VVQ\r\n6eNcGUUzl95RBehHx05tCPiadAzVvbYv9CQOKmrAtqqqChJ+QK/bhsDyUbPn\r\n/1FMQXxOPBHwLr91tu9zvXfknqacC/lcmtY14zsStLH2dGqW1iWS49/LAJ0C\r\n5f4OuJNK3W+CqiOmr2tBQHqHol2LPOnuVtlpAQvxv3DXypBzBub8CdKB6e6j\r\n35oyhLV0elosLIgLF3ByXLf2Ul6ab31BdWyhlk8un/+nhQZdpbusRljOZv46\r\nTldCAUdOiQcx/X89Im7ydXPHEkWmdv6e4lY=\r\n=54dV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "b959a3d3bdf324ed1c7358f76ab238a8b0b0cf93", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"expect": "^29.0.1", "jest-snapshot": "^29.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "@tsd/typescript": "~4.7.4"}, "_npmOperationalInternal": {"tmp": "tmp/expect_29.0.1_1661520890302_0.05073874578867388", "host": "s3://npm-registry-packages"}}, "29.0.2": {"name": "@jest/expect", "version": "29.0.2", "license": "MIT", "_id": "@jest/expect@29.0.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "641d151e1062ceb976c5ad1c23eba3bb1e188896", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-29.0.2.tgz", "fileCount": 6, "integrity": "sha512-y/3geZ92p2/zovBm/F+ZjXUJ3thvT9IRzD6igqaWskFE2aR0idD+N/p5Lj/ZautEox/9RwEc6nqergebeh72uQ==", "signatures": [{"sig": "MEYCIQCg01787XIq0fTaAZb/NvGkYBuI1kkiCn7w9YuDXVKjUAIhAIV23XI1Z3jssDKhIDRyImobJtP99Ad9QbF1ShCg9VMq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5269, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjEzD8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrCzQ/+KsCL4gFgLX34z6DRJXN9/uuUKHuqVaf7kXlWWl0rhNBqT+tt\r\n8xqFfbbQaSvEiz98N9Qac2aVooVCri6PC1vGjpO55MfI661vRPOF2g98QGFB\r\n6wfKIbvvZtWTnpQJB78Qo9M6ZJsyHWfv8teiZk78fjQcVYq8w1XWmaLdOROo\r\n/zLi2l2tN+y59JAMLIw9DLn4iSng8O7cOUhvIO5KvQiEH+VXPPe6B+uHXvoM\r\nbsYuqk66R1ANkwEMxYMriEF88EV7c3vrIm/ZqL6qNPeoKD6IiM+Wc0Y8jecE\r\ne8ZuacV2E07xUTDwGbDrK2W8s0V/LIA+FuBDWzmVcmfBzgUUng+uxdR/gaPb\r\ndGqhSzN6I+qAZLYJ8UmGNHF9pV6FLDz5YhtoMGUXoYz14POsbfTE5Z7kOnQm\r\nAKMCl1uq5m8WQ2b0f5b2ijGitE0//PUsw4zQqz67Da03vT5rqdtuprLcDWeq\r\nbcIgyc96jGXv/DAGqecN8GOODzl+iWheBmd/WzPehdKh3tw2Syj+g2PgEzgP\r\nhZ4uPC+YEnvjMlC2s1sT8arLb6+RZDSUGbEwHaBlWs0ebi8c37212JS/ka7k\r\n5WzhhRVe1RuMpU1eYcF3WNOTUNTa5XKY00yEa1NalzWEpqnsQf+ZMq5t4JQZ\r\nLPsyhnU5SvpXsZKIBFJ3kdZa6KArWOWr2q0=\r\n=M+X7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "616fcf56bb8481d29ba29cc34be32a92b1cf85e5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"expect": "^29.0.2", "jest-snapshot": "^29.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "@tsd/typescript": "~4.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/expect_29.0.2_1662202108013_0.8090801090616668", "host": "s3://npm-registry-packages"}}, "29.0.3": {"name": "@jest/expect", "version": "29.0.3", "license": "MIT", "_id": "@jest/expect@29.0.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9dc7c46354eeb7a348d73881fba6402f5fdb2c30", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-29.0.3.tgz", "fileCount": 6, "integrity": "sha512-6W7K+fsI23FQ01H/BWccPyDZFrnU9QlzDcKOjrNVU5L8yUORFAJJIpmyxWPW70+X624KUNqzZwPThPMX28aXEQ==", "signatures": [{"sig": "MEQCICoN7x8GArI9CyVnY7xZdU8JwYvJwTLAuGaRPY3tILddAiArfi8i5N2RyfqAfu4n4q7d2jK8xpZVQV1ctokczMT5TA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5269, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjHKI5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrrgQ//dr63IMNtV+rF4xCglXFyeuzHo/382l6vKyhu4YMN/9v6CEE9\r\nyZsilH8LVhp8ajDbk28m7fY4pwGI3/peHIRp4HSrW7kkL2JGnoMTTyDA1VBn\r\n8IZMWRWGPlRUE1oQHEAeUetWcAaMVOd8wcJLLLSK1EydZ6DIuAatdfrHeXiE\r\n1tin1iPDuCLbKwaZ5ie3PWytId27sRl0fyTb+J90e0McjEW+efA3LwtRSPcl\r\nD44Nzg36GKfqDBWh//H2nJsHgodjMcERZBvBwwlb1C/uFnc+kgESA+v9VXTE\r\nGo8ZHiWfQPbcpbRmC44iJHCXU9TVg3gnqDofPAN9lT1ZJrXlWUFjzGQFlr+X\r\nx1n6qjuf5HTD6OGTxtYn6P0j5usX/JVq9LwA6SAP5zyAAjHiGc1KuMBnG02+\r\nqrhCD8HyREZVuhS1g/DEAYFLFUrq+yZZGhr9uhz9j4F/XTkcGCvkCf3pCCCB\r\nvPJcwr9pBG+BDc1hxNVH6W3hlG0i6qEWDX75j2yU1Ji6YatOIjGM2C9U391l\r\nK/tf0EjMxCghUuFJ6YbLbTHemf71Px7lQVGgInSO8cT+/StW9Y84DuTLZQPI\r\nJQuzSlDXo5fQzsAYDYqrZTssqj1ssUP5BdXWay2c96LQgTlZ6Ja/cqODiATD\r\nOZiy5DmpFCCMs61s2VkQzBvf+Gu9yEB4j0U=\r\n=88Bh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "77f865da39af5b3e1c114dc347e49257eb3dcfd1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"expect": "^29.0.3", "jest-snapshot": "^29.0.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "@tsd/typescript": "~4.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/expect_29.0.3_1662820920876_0.0882556653858102", "host": "s3://npm-registry-packages"}}, "29.1.0": {"name": "@jest/expect", "version": "29.1.0", "license": "MIT", "_id": "@jest/expect@29.1.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "58956d38c22f42a2ec7ecf7adb9e0ddd9a9082a3", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-29.1.0.tgz", "fileCount": 6, "integrity": "sha512-qWQttxE5rEwzvDW9G3f0o8chu1EKvIfsMQDeRlXMLCtsDS94ckcqEMNgbKKz0NYlZ45xrIoy+/pngt3ZFr/2zw==", "signatures": [{"sig": "MEYCIQDf9vSf8cUFCJ2+bOfGnFQxvjhH2koh11+SVO5ollUdaQIhANqk+1Ehs3jexgwFljyPd3353m8+BAIM645TZXbd1uGL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5272, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjM/nNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoEKw//cyEzBdsY1JFh1EQjRYuTszIJtB0JXXcJJjXBdgNbO+7/8wai\r\nCXoJWfDLQteKoqHO4dIDfyGUbR7h1GkHpcl5mYmMHrBGGjTNe3ZPmm8nlTvu\r\n9PyoH2oLm7U2Dx1hu5pJWI5IK35/kg5jxPFlj3KfvVwmsUlX/TtWNVgAodsT\r\n0US7s/FNX57nlWY0kLTwUlVFEBg/o+c7vwYrFsWdIVhCxQKR1c8RPVv7mDRH\r\njoA5+QXQOaWxeJJe73BPYUz/070ejISYF+rJ7R6jrUF2YPnvMCD2bp62U5th\r\nKuE5JhCmXEWin6IIr11znm8i+dEudDTo38jSg1+IFahuLgP7c8Yr3VtmwTe2\r\nRPdSZ791f6QXweqi3RfNsguupJ8q3v9lg2x6OIiUgBupSlHFEG9py/1dh0in\r\nzEZQlUsUaSQ0aj4gHZXzpCQLpURyax9ZizEqrADpmn4BQsEyKAqaR95mdY5h\r\n603CDimxR/aLKreY5h12CHTZeeAVQjMjePqOtqiV+wTwq2A3/5CTPQBriqRG\r\n/wLWDjyLrQJSEGAM46HmbQaonpUiPgM3zoNBkyc8uQJIhb/cQuti1aQo2mlq\r\nx6lxvrcZQs/vBib6Hn3AFEqZPiAl9Rcik424qaF0PmF97R5iYYjEwHy2mKTW\r\niAHuq6I9w/+TXevRXBK8P7Of7poSV73NvIg=\r\n=GePw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "51f10300daf90db003a1749ceaed1084c4f74811", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"expect": "^29.1.0", "jest-snapshot": "^29.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "@tsd/typescript": "~4.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/expect_29.1.0_1664350669208_0.8102066698543766", "host": "s3://npm-registry-packages"}}, "29.1.2": {"name": "@jest/expect", "version": "29.1.2", "license": "MIT", "_id": "@jest/expect@29.1.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "334a86395f621f1ab63ad95b06a588b9114d7b7a", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-29.1.2.tgz", "fileCount": 6, "integrity": "sha512-FXw/UmaZsyfRyvZw3M6POgSNqwmuOXJuzdNiMWW9LCYo0GRoRDhg+R5iq5higmRTHQY7hx32+j7WHwinRmoILQ==", "signatures": [{"sig": "MEUCIDy/gylHFjS0Kfw3ReGHb3ec4JGxD7t5X2XemZe1FEklAiEA2+Praic+csxT0/tpdQnolkzijXuaqT+DhiGtYK9c5Fg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5269, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNplQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq9tg//TS+rot4cY0WfPp2cssQDr2+PD6yMi80oMpAOgFYgw56mPQDv\r\nokLvfC9hiqsxQmDXDxbgz3kDSXxnpdDQfbah0Fj675yT8R6XxvtOpxhW36SW\r\n4FRt4b+gkPssjtJAf5rTVmX/h74+OYto2K0qazUc4SlBKgTV/tfd0R3Uwhlq\r\nLUuf+MRVAjV8GOcF6Ya4grRJFPqk8HeRYTabrKRK203UEuS5zkvhCVXT0QMQ\r\niEx8VXYHaRwo95Lw0k1FPGXJl8fmiImolM6A0HSxSGH7esx/mZ8IrlOdhgq8\r\niu8IE/G/3GzAI/C9TRnpULwxvpeMAePrhs9eWpHi/WrpHbGF1DnbT+map79e\r\nt747TNy78o7R32FCvv/yMNHs6sXHpiRD8+n4xbh0KbHmDQpHN6Z5Ga7rTiHV\r\n4pSGvHGWY1/PSjah9LZPRp1C36Fx1Eg0V35OqZSvlQu2jG8HMBY+jL9++elv\r\nbgV7aySfFDbJpP3OuoI1SUwIcerVcteJsKlX/S7psGrcP7KOvxAZevvNkHnQ\r\nkamnN7Po8bHNmSylpVZJgbcJWvUi9Wh+oCZALs/6MdF+qnkFHDPcHf/rTk5b\r\nMUS2ppokEg4lJvt4bcQwNl58Z8pETitP9naCp7g+eR8xVs4exy/Vfiqu8X9S\r\ntxOOPCMuQ29nbx7T2JmIbSJy/YEbPOIjPGg=\r\n=PsG4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "3c31dd619e8c022cde53f40fa12ea2a67f4752ce", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"expect": "^29.1.2", "jest-snapshot": "^29.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "@tsd/typescript": "~4.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/expect_29.1.2_1664522576351_0.9759882089980985", "host": "s3://npm-registry-packages"}}, "29.2.0": {"name": "@jest/expect", "version": "29.2.0", "license": "MIT", "_id": "@jest/expect@29.2.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "25316d2ae930e7bb9df96cce7521053d377c4c0d", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-29.2.0.tgz", "fileCount": 6, "integrity": "sha512-+3lxcYL9e0xPJGOR33utxxejn+Mulz40kY0oy0FVsmIESW87NZDJ7B1ovaIqeX0xIgPX4laS5SGlqD2uSoBMcw==", "signatures": [{"sig": "MEUCIBeZAMAJsjqolQbbJZmbbZioI7az9DkzK4oEj3KBtIkkAiEArJ+1sRrT64fQOUwJV3imiluiD2FQZu6T2+7NOZjfEsg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5261, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSShdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrFgA//WTrycX+e9fD8vNl/Qp35xmUDpi7L66K6xKStLiTKlMrgvi0+\r\nfvMixEhXr6kDq2EmVfVcraLtFTQI19h1ru0S66i7q1SCrMpJko3Sq1YwItf8\r\nXEZhD3K1DTBvN0rQsuBDJ2v+BSWbmQJ2Bj3QYkiIU1FQ5sQ5tcUbCFobSnl7\r\nj9soRR6BB1shoOVLRaIMRlTZ3gxHPyLy7ZAYqecpNWukwhWwVXtCJIdCy11l\r\nUkBWZNSc3K8diMDCsTqG0ZJmJVuZW0Ni1vSZ8pgXaXa7ecAmWoAmTzuxQmNe\r\nsq4EneKBZ/6GsiiKLJaxETsGkPcn8Auv3diT64NFKrYabttrO0GzNfUx8/pR\r\n65b6VZgdlbZ64aOeOnG8DGbKtfsrvLDER3WbekCpRhHMmFZgxCW9/7tTPqXO\r\nWO8YynT9Rz2oKhJgIZnSoIX8dyLoO+teggIWnmIJGWxxMJAoLxFmoidNjM+j\r\niTJctoDuTWeoLeOzZreSt8btjN5M3rJ+vLDcON1RFgzfYH6PCQ8kdO2+kD9Z\r\nYc/6ExMZVEkPu3NlDasoaWvN9KEpeMVv8o2e/6CrHB6MI6rcFub9AOUKj+GZ\r\nQTNGtUfsHmY9gMvFKjYct2ZMZlLYVgPFGJ64ms8+qOmJKX8DHL/gEHTAJci9\r\nHtLQ6m1mkmbihHnru00vADLnAq+aoJFvNMs=\r\n=Nggy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ee5b37a4f4433afcfffb0356cea47739d8092287", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"expect": "^29.2.0", "jest-snapshot": "^29.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "@tsd/typescript": "~4.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/expect_29.2.0_1665738844860_0.8195816228435533", "host": "s3://npm-registry-packages"}}, "29.2.1": {"name": "@jest/expect", "version": "29.2.1", "license": "MIT", "_id": "@jest/expect@29.2.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8d99be3886ebfcffd6cabb2b46602a301b976ffe", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-29.2.1.tgz", "fileCount": 6, "integrity": "sha512-o14R2t2tHHHudwji43UKkzmmH49xfF5T++FQBK2tl88qwuBWQOcx7fNUYl+mA/9TPNAN0FkQ3usnpyS8FUwsvQ==", "signatures": [{"sig": "MEQCIDq5QJWMllRBbvHWK2obgvbfk/QQed3EEYBKfVqjSKf7AiAH5NwAy7mZLQkkK8jQ6Yg7GWtHqnTebI9KIK/acANB7Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5261, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTs2VACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqlSg//cKAwjYgWRD9QoLecnRrEUFXQAyOFLvLsxuUj9Zs5fld2uDc9\r\nBxpz5TuR70S1tcO+wkC0eFZy+VYawapjjIz+UQtJw+ykoK95kjUR6DAaGmUo\r\nwT1+wz+uOG1kvZD5WeY9KxdWT80itUKyNz3hftgikt47FHocXNOtx3Tl2aMh\r\nG7JE/5vsZJov7FjYESCdsFKuBm7jFMzt/1lRfc+mgmKB4+NLY0kcu+BBCeNc\r\nDCJIsV/dMSaCEVvHNaLaYCEToZfqAtU9X4M/hcRqNjrOYw9wKbb7oClhLv2p\r\nGH1WsHfl0BkIu0xW90kAmAnSyIgZ4V+QoaJwCyrUtDHOBrNyKPuKDbAk1fYn\r\nLN+hitjNvbXh6BghvhZTInLt/9NWeUjvHTvIPOih6nm40NqD1h7DVDK6HDhQ\r\n3hMRJuD5tUt/xE3nHvXI+WGDjQpCWq83FFlKhRCVncIA7LDRlMoO1S7uiflF\r\nig7YDabhDD9AkrTB3Vfa5bcYH3QhLV65U18r/6LZbzUOF1REp7Tx/aVcKb90\r\nKhvvVT5gIP1k9A0Ox7hIgJFejDSoOs38hjr5hOChlWwY9KeZ5HXvW4Sc8r2K\r\nv4UAPStT+l+OpGLWitqcSM7JwT1Dl3Y/L0byqqDx1vl7grPPQ1+G1a7CSDlM\r\nVbdL7AMb0JidAWXzHk4hEM5+SyHLLDphV9A=\r\n=BlJ2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4551c0fdd4d25b7206824957c7bcc6baf61e63bf", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"expect": "^29.2.1", "jest-snapshot": "^29.2.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "@tsd/typescript": "~4.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/expect_29.2.1_1666108821327_0.8827730209782936", "host": "s3://npm-registry-packages"}}, "29.2.2": {"name": "@jest/expect", "version": "29.2.2", "license": "MIT", "_id": "@jest/expect@29.2.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "81edbd33afbde7795ca07ff6b4753d15205032e4", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-29.2.2.tgz", "fileCount": 6, "integrity": "sha512-zwblIZnrIVt8z/SiEeJ7Q9wKKuB+/GS4yZe9zw7gMqfGf4C5hBLGrVyxu1SzDbVSqyMSlprKl3WL1r80cBNkgg==", "signatures": [{"sig": "MEUCIQClvUD7ONqW2X3kgQ0ycb61DjBEiLl1t9TxoGxy8yQQ+AIgB6y0GJbHZd2VjszdeIZgp/gygIdV+gQc0mkcwJURA9Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5261, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjVvRqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpVhw//ZuznDmznTZRQmefFhpgPBBYDuyDiYrL+aEwpO1PbutQv0AuE\r\nArlpCXm5Vx6A8K0YU3FQo34WQeKxUXfQKgCJip2TjKa07TnKx0Njp0O0JSVv\r\nXNWHzVLp92BKRiMnWUBGvbbUJCVO322CZsD4pnZXn0g8FE3N7AV7RCsZavqO\r\nbvuiPJ4/j2SqRjw2hU8c6blN80ioJlT8WJj/dl6N7mCY53HNumRlAtJkx/n1\r\n16ufikThSNS+MqH5hNUxIlKRW3DsjAzp+ClDD57oNWMZ9LVJdvR2X3e1LMJU\r\nrsydedWUML7bhLgHVcJXcnRTs+Zw5n6bZ0JAuyPbwzKn+UQE9SHiWIK12do9\r\n9BZ+bNXQseF2KMKVcswmnCDLaUfRH3XRiFuZy4ya5ofjyy55exhYIQj2FDgw\r\nWiXjKAltqmu0bwlsMmebFN+RINjeKGRUhSYnu94ysHl1H5f3ia/AB0/EcmXI\r\naDYeanMO9mxm3Ir23/sfcQe7BAGSEiVrVjwX6Q7Ihcrcuyr0XvCkLA9IWykr\r\neC3hfzDuCJwmd18Y0n0a64oYReGVftMjH/03qXP/LV3uc8v6wTXKn0W73weW\r\nXrMidCnCTOMy3r1UGb1GG6B2gupqH4fuR+8AMECOJj0yOfmQ0oOBxZWf6lJW\r\nBhbgX5LTnLbiaLxZ2L2ijBYxs4/UsohihwA=\r\n=dWNw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0a8edbe0ac434394a16cc173a03ff54a9cc50e41", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"expect": "^29.2.2", "jest-snapshot": "^29.2.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "@tsd/typescript": "~4.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/expect_29.2.2_1666643050702_0.1104587085909805", "host": "s3://npm-registry-packages"}}, "29.3.0": {"name": "@jest/expect", "version": "29.3.0", "license": "MIT", "_id": "@jest/expect@29.3.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "06907ffc02541c8d5186e8324765a72f391f3125", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-29.3.0.tgz", "fileCount": 6, "integrity": "sha512-Lz/3x4Se5g6nBuLjTO+xE8D4OXY9fFmosZPwkXXZUJUsp9r9seN81cJa54wOGr1QjCQnhngMqclblhM4X/hcCg==", "signatures": [{"sig": "MEYCIQDkC5aqXyZLSN7fkQFljfFFB/gMA59wJr2msJSdCz4JGwIhAK/bLX3LSM8vHyon1ahUwGfEEMyd2CoXkPZwZxc2szGa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5261, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjaUapACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmomoQ/6Az2TvCz89LaWEvzKqM8nI77x3ognRpysk/N308VXe3qN/T15\r\n5VmZ3S3VrhPpSjOvVCHqPNFvaOxevA5hfJoWJsQbBsSqNo0r8IOfUvMi7nWC\r\n/UVmXSBPkw2xMFBPAnt2HAl3N6dXCod3EDFtFlBcsGbCrVLa84ZiEQH6Dx/L\r\nb5DhPDgtVlq3UdNu8OCxGMTidUtwx6yvFCDB2edVEJ8UqPXX3AeC5Pa2yMkm\r\nQ/B2WCK5sQBaScCc1UCAPXJkoEbA9HoSUUZnKSLhmLgDQDq1Tm7AaGZXtPFC\r\nsfKOGeuN4qbkZ8U9JOFjS+rlme3AmU8JLcgll6swNcWNPKselrhm5xmAPp0N\r\nR7HRdLcTHBz0KwyGtMoZXajNpWKtJ/9VtNploYtqq7+gBJEzxyVqdgOsT9wI\r\neGB352/saIU51o57T/us8m7YrsBPFFmZeBe8M3uNdVagCbVF6nVALfsMj402\r\nM4ryArSN2s6c2ZY4oP2Rv5eSlpVhfCTugokimkuGeIW9UQucWzubhPz7ujOW\r\n2BJ+C9zT1qv6GwVk6aPnSp7IzC5zkuWcvCmuzOXr22q+qUYacfO/w7s81CUh\r\nfr9udlsrUHtgzdNwvpWPJtTDuZ6hVuoF4ddCmpLFDD9fVfLzz8mkX5RzXoXc\r\ntDrBBV7+AmQlIcGow57T4XccQA1NHU5zzfo=\r\n=dewl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "84b8de987b33e2da20dc833aeb65f23d72a673cd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"expect": "^29.3.0", "jest-snapshot": "^29.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "@tsd/typescript": "~4.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/expect_29.3.0_1667843753760_0.4105592841357002", "host": "s3://npm-registry-packages"}}, "29.3.1": {"name": "@jest/expect", "version": "29.3.1", "license": "MIT", "_id": "@jest/expect@29.3.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "456385b62894349c1d196f2d183e3716d4c6a6cd", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-29.3.1.tgz", "fileCount": 6, "integrity": "sha512-QivM7GlSHSsIAWzgfyP8dgeExPRZ9BIe2LsdPyEhCGkZkoyA+kGsoIzbKAfZCvvRzfZioKwPtCZIt5SaoxYCvg==", "signatures": [{"sig": "MEUCIB0ASWK6pHkOaXV176IqZoVoFHwCWUDF3vto9uU3IaopAiEA5jAtwHGFZAJ2gruuE4Yx/sHhaozdw8tud+HbCnrlRZ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5261, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjat6dACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqLFA//bxtZRq6bDl7I/bIpDp+3mmx2aUztyAaiIfK/86VlIQJRv9dg\r\n0uUpTECRPNIDljjxeCb6K5I56aswgjzzNsDpYFifXm/3rirQ+/ijYRfrz8EO\r\nsN6E+S95z6vEhhXm+8pqPajdLg8xIf3MZhZ+4i8UMevszcuQnt1fBY0crstf\r\nnuaLCyd6h4w8jOOyNveiNpf1bw6G/hY/lHPGuYXk35gjbTrhipmFpW7grRUR\r\nyAIn22uM6xEwG0i4MJiPHoIWG4dFUhOPVh2OG0EyNQPa6NrAg/ejUEvLMHNr\r\nv7kbt4Cs4YSHeu1cDcS3I0C8m40FIu8br7VOUm37+HmJ9XUKJf0emhahZiJQ\r\nm1/DnjUD0XW5e+d6tyt5cuXODbU532ORnMHWgcYfYGbd5aKGpfiB9p5u1i9h\r\nth3UzZp/Rz0geaPikjnHXX7ZMx5cN7ofzf8mw5fHYzHOhOrjNBL9Z27rI9Fq\r\njRUWlEuqdXDdpStzdjQMReavOeR5cjw5ino4IY/yATXW4C/KCnzdkyycjKBl\r\nTQjftHNQ+IRKCJ6Q6n9WH61kMeSLmPiewnAMi1Tex+bhpSlrFUuRI4CbdoxF\r\naq0EZvNL/fJhxigl/+EQTycBbP2yKZVsA/eLtl/UMHfakvCU4gIGasn+vWQc\r\noUV103Unu6GNvsKIjBh7WAdOSr+DSE4LewU=\r\n=eJ4a\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "05deb8393c4ad71e19be2567b704dfd3a2ab5fc9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"expect": "^29.3.1", "jest-snapshot": "^29.3.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "@tsd/typescript": "~4.8.2"}, "_npmOperationalInternal": {"tmp": "tmp/expect_29.3.1_1667948189563_0.24799795803116376", "host": "s3://npm-registry-packages"}}, "29.4.0": {"name": "@jest/expect", "version": "29.4.0", "license": "MIT", "_id": "@jest/expect@29.4.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3d47dbb1c650a0833b8263ca354818ca0665adbe", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-29.4.0.tgz", "fileCount": 7, "integrity": "sha512-IiDZYQ/Oi94aBT0nKKKRvNsB5JTyHoGb+G3SiGoDxz90JfL7SLx/z5IjB0fzBRzy7aLFQOCbVJlaC2fIgU6Y9Q==", "signatures": [{"sig": "MEYCIQDhbrfusJIAf6KR2J7F/QPfvufJBiztTnYJPKL4sMDLwgIhAKMX2aPXpzjNFmLVEMwBC5YgrtRPrISHQSxt5K2vYhKg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5982, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjz7lGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpStQ//TVm7q1swzl0c2+/OnTXROTck+1wKZwEknEeNT4/i4WQd+1YA\r\nN0RnN7/2cLR/gRDImc8O1OBrPr6oK8ZHbPapqidbigVIARw9f8Qzn60gPU3M\r\nUnOX1fdZgbzzhTPdtNFUp+URkUwxXKylayt0boM8caM7yJMLc3yaW6fs01VB\r\nQPHlJO5cz1wFCnu3prhNpnZdT3rIALRB523NMO2Ubwd9QzcaI75qfi9SP+tJ\r\nmIsyQWTEaBBB+qZdUo69SVTyaiO9LMZzFbo3aB780qYeMXFu3okGplwY9ewD\r\neH72bOiJSQLtSn/YVsoD11Ob77SrS6vJ15UttXoVdB4PRo55BV8EPcvwFsJ+\r\nTyUxeMehjFQkw1ceCEOhGNAT513wBnvFz30m4oPWSb6Yu0SliSJU1JCHpCpu\r\nGaZt7NRZWVaHsDeOob9gosm/hMbM4m6TjxBUkqpUKgU6gvFNYTvBwWh/TvaJ\r\nAghE/Ol9romXa47XMsq9AOnI2ahaIjWYhaFX22iEbzjEk0FVZoAuI2+d7wfY\r\nvGJhQSH8by0zoXFOBXdQVtMj9SK4K/ZpIdwNTu4aOHQs4KPpbeBTVKxUzPNU\r\no47scs8dsrHJZDY/pFQlMh6k86ImVHpEbLER0SuCBCnhYghN2EX87Wmy0267\r\nDk7q/vXGOWjP8edyABHDGHbb2XXhSjM9xpc=\r\n=1qQS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4bc0e8acaf990e6618a7bed1dca67760c20bb12a", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"expect": "^29.4.0", "jest-snapshot": "^29.4.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "@tsd/typescript": "^4.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_29.4.0_1674557765967_0.7117465388630524", "host": "s3://npm-registry-packages"}}, "29.4.1": {"name": "@jest/expect", "version": "29.4.1", "license": "MIT", "_id": "@jest/expect@29.4.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3338fa20f547bb6e550c4be37d6f82711cc13c38", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-29.4.1.tgz", "fileCount": 7, "integrity": "sha512-ZxKJP5DTUNF2XkpJeZIzvnzF1KkfrhEF6Rz0HGG69fHl6Bgx5/GoU3XyaeFYEjuuKSOOsbqD/k72wFvFxc3iTw==", "signatures": [{"sig": "MEUCICLjR+ubRNvy1Dg9Crr4kUH1DxeDMMWdiW+ZDo1ZiM5GAiEA6oxGeNqVdUf9YQzq3vKsetRZMObASdFOuP1ZpFANt5k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5982, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0peCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoMVw/9FeundsQlp/kEZQFU86BexYtLIiRFZq+F8NJE6RlIRYywpsMN\r\nqUBDwjIZaQpEpz+Yro/qqQ7BdGZkzpWJy/WdEXOgT6iwakZ4qhQLev4U+dt1\r\nC1/5y0VfiGbUo/b5THqJwN1mnqL9xepLMGZ07J5+Ns//kqIuwoMHXKmnY96j\r\nmAqHQpT4uz7/UKj10bMqC6lctn5Poex759+1wKOh/0rcjqbb0yUP0P1TptjH\r\nE1yvwi7x4ZnoplvPWI6YtgFoSAaO/Fk4kkhnIzJ0ZprfdiPSwCRqT8JO369J\r\nVeSuli798YfNyVHTYe82p6KAKjeDDXTkSEbp6p6Wvil/wuLKFSwHb9ESwdEm\r\nt5o7MlWjedqdIvXemYhkysIVn0w8iZPqa+3bduvSCgI8yQmRMTbgN+ITPb9H\r\nUFiMHudhgelPfnpXBdvK5tzYTLAqllsORyLSH3MEE90RYWzxvhLuVP1Fz/LK\r\nr4pM0s7hhIkwCwiBF9nrSLoSHp0t2gW5zaB0RQG2bLTQUNj11eZGIgs3x9Q1\r\nMp2fN/BkwFJegpM6t3iZEhIVKQMdDeNDBqDhfY9YHkGnSSKm+5OgAqRYAerj\r\n3xkTbRvn7J9NoEfOMz7s7KJEfDqYP+Ui7f9FB0Ohh5aWX/tS6+9FyytoXarK\r\noOWSomwE6y0GbvOCALWkLl+wlVYcKzU6sd8=\r\n=LgLe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "bc84c8a15649aaaefdd624dc83824518c17467ed", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"expect": "^29.4.1", "jest-snapshot": "^29.4.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "@tsd/typescript": "^4.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_29.4.1_1674745730753_0.269231578067874", "host": "s3://npm-registry-packages"}}, "29.4.2": {"name": "@jest/expect", "version": "29.4.2", "license": "MIT", "_id": "@jest/expect@29.4.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2d4a6a41b29380957c5094de19259f87f194578b", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-29.4.2.tgz", "fileCount": 6, "integrity": "sha512-NUAeZVApzyaeLjfWIV/64zXjA2SS+NuUPHpAlO7IwVMGd5Vf9szTl9KEDlxY3B4liwLO31os88tYNHl6cpjtKQ==", "signatures": [{"sig": "MEUCICUhjA4+j5C9+KO6HTlK7KndEfN840G6SWxWtAegxoAyAiEArW/xVQFM3Rl74mcM1/iQQHF2JDjRiTgI0JaSA2x0n9g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5264, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4lYIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrgJg/+PfOj40Pd5i9R7TNIjxNR9t45x05uRZ9x39+2kL1mxWuuwTrf\r\najopjQdj22hMHvJdxy8gMDRYZDvEHlsMNdxLTwQde+KroK7yfdj0wwgNQVXv\r\nv+dcvMBaX+jgUxmVpNq6OAqcCBRwzwfgA3jJzAKvP78DjRBjj3s8D9EiWMZJ\r\nInuuFkNSu52jaCtyqqNqJ4yLhwx6LJwebKu2N2KpdHG5ZilAYLCrmtGZPT6U\r\nFmREk84bxs4227rjd+jVnLXxvVXnWv4bO+/IynLyb+GOcSRSV7h/d+NLuMsY\r\nFsGvnXTyjxK97BmQUkkrSdv1i06Cq6SMWisrNQXHTaY1RT2LQCJ1kvAn/RNy\r\nvexCdkNnOF4vFl4Mt7+rUXKaMwPyzF/z2CD0ZPE9k1WJ9R1rcKxEdXSKCcPK\r\npOLaYABaWZa+Ww+32kIJtw2JHCiyyC1TCg8OUGhWyBchJAKDJISeWcirfQsm\r\n9aVzlF1AoP71pCFCgmh7Amc6qZo05NvyJBuZgXgJYiOT3pKagpV9q22EP8hT\r\nN5oOg2pFq65EuOhXKt/0t60YPygZyEoX9z20tTLk7kvsz5YrPMcmlAY5JgXs\r\nQ40i/KtsoPtHkoVIygM3EzxFof4LS8wW2PAJhMyvE8nlr72B/rU4Elh7tIfZ\r\nhAe+kU5OyJ7rGeAmyBrP7kjQNA1bJhoEXPA=\r\n=69nt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f0fc92e8443f09546c7ec0472bf9bce44fe5898f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"expect": "^29.4.2", "jest-snapshot": "^29.4.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "@tsd/typescript": "^4.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_29.4.2_1675777544657_0.17445328001772542", "host": "s3://npm-registry-packages"}}, "29.4.3": {"name": "@jest/expect", "version": "29.4.3", "license": "MIT", "_id": "@jest/expect@29.4.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d31a28492e45a6bcd0f204a81f783fe717045c6e", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-29.4.3.tgz", "fileCount": 6, "integrity": "sha512-iktRU/YsxEtumI9zsPctYUk7ptpC+AVLLk1Ax3AsA4g1C+8OOnKDkIQBDHtD5hA/+VtgMd5AWI5gNlcAlt2vxQ==", "signatures": [{"sig": "MEUCIBOzUIN/v0+C+9jwyYZ5pkQjdRzrcGKBWllgNs9CnfS8AiEAi4Tha70fcTf5Gw8Mt2a+1upM0A4nYdT0mkdDqW48SEI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5228, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7MitACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrjIQ//bTGPmhBcHrf0TazWcYJ8QZelcf8iOW/28tfA9AvJ1YtRWoNq\r\nU8x4p/0zeLnlX+UNf6ncbEJe1FsqQpDJSnXGVcTsctLFZcP/oQrYw+NBYQfU\r\nX0HCPKpfuGMcHpGgYtzkwY3gRHsGoC44bgwYjFx0MW52/5Z/qlvQkOyfkxPY\r\nG6HYTUVzUSvL+p2srrOurbcr4+ZePwefBGqagfSmswPD9fkbsGnp6+4dxdsi\r\n07QQYBmnD+NmeSFq0MmwNik4ByM9QuzoOYCrc9xPDxDmZFZjwTZUvCdq4bde\r\nQmofEFfr4Vca1+f2oKI4h9jHHRHcc9D0TNIwWtxPjS2BVe+7bj6adfdMJxuD\r\nirPOVFKKZuTLHF7amLHbIF01MEMKAIOskfHd6aRdFtSJfoFqrOd3YAG+aQwd\r\nEs8pWZxEaqsiRQZfLwrQ8YJUS71dX80bvSWZ7A5CR1eS4MtFzZePIjIToVpD\r\nsSxmXuPhavPch5/wvWTCOe0QKL8BmlLZLWaKC+Lk9SoUeS3sgM9IJMUDmNrQ\r\nSAFcGEez1mVO3QvD64Q9O2ESEZFvsuZuJmcEDRsHFkZK1Yk+UfVZirprKF1V\r\n4W0ZL/k1MFIrJWYJJTe2lszuwqggLYHPD+tNtEiKxzok12wg55Dr1hkmHP2A\r\nkHygPR66HW+1iDstamUWWp+1laqo6xL28lk=\r\n=fVIr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a49c88610e49a3242576160740a32a2fe11161e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/1.13.0/node@v18.14.0+arm64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"expect": "^29.4.3", "jest-snapshot": "^29.4.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "@tsd/typescript": "^4.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_29.4.3_1676462253028_0.03802495832997965", "host": "s3://npm-registry-packages"}}, "29.5.0": {"name": "@jest/expect", "version": "29.5.0", "license": "MIT", "_id": "@jest/expect@29.5.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "80952f5316b23c483fbca4363ce822af79c38fba", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-29.5.0.tgz", "fileCount": 6, "integrity": "sha512-PueDR2HGihN3ciUNGr4uelropW7rqUfTiOn+8u0leg/42UhblPxHkfoh0Ruu3I9Y1962P3u2DY4+h7GVTSVU6g==", "signatures": [{"sig": "MEUCICoINIsFESZmqoHdXPEEYGTUu/C+CEtz2y8JBHYITf3sAiEA5ciyKQ+CJLhJghJ/CAOeylVo7yjdIm+fofuAqNZO2L0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5228, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBeu1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpQvg/+LaB/UZLtr/4aw6awFUs04e5ttG7AJ2Q4vKqnUx6oObIzL7G1\r\npsR4A3BHBzu53yLt8Sam6GD+5OfkKcCn64JiZgC67r6rlz2eToXa9JLgIETz\r\nkQfwffRZ6hp6IsGkeiQc25jrJf+IYNNiN5CHGmpuEDSJY1/W8CD282/nf9Fj\r\naKO+hbNj3Lwe4CUw8bKXkI/ILK3Py1acO/RrmIuWdfP7vbOgmugdZwNvDHYt\r\nFVdeGg/LPJirHeNTmyHmzhvwnJWiDj+ynAynP757fMUATWcjGoQQsfWRA4GL\r\n23519x2OLP9v8ki+sdz0AaOVS0Wzm+MXxZsQa8iee3WI3wzpMBS03GuEhRgq\r\nmFlrB/5UwXLcWDe6f/F8KJJ1GUQzjHLdEDoqzjXO4GLkrpBeFQN1VDlc1Dxv\r\nB3k1nm82d/7D8KfZkzdTyOhIt3eU+Mb8X+IJ2D4IlO0U+EB2kP6GEY2ewHVy\r\n706SGg5ooPHirQRL9UaP8lIn/lb0BR7vpWIM69PeFxz34PyctjM/oxBx9t44\r\nUXUTKznc0vICVDzMNgVJy/vwfG6cbIcWpLsEfMxj9h969lijGic1vbnwfsMm\r\nyvnEdQtsA+ychtuoDIGpXzlje9ar1KUJjiU5F+0UaZwqMnMAx4KDiwZBhZRH\r\ne22FIchoBH4ViHUk5NE1EazFTSbxI6PxOvc=\r\n=f+s+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "39f3beda6b396665bebffab94e8d7c45be30454c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/1.13.0/node@v18.14.2+arm64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "18.14.2", "dependencies": {"expect": "^29.5.0", "jest-snapshot": "^29.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "@tsd/typescript": "^4.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/expect_29.5.0_1678109621449_0.8253247891515212", "host": "s3://npm-registry-packages"}}, "29.6.0": {"name": "@jest/expect", "version": "29.6.0", "license": "MIT", "_id": "@jest/expect@29.6.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2a25759ec696bc03d3e5cfeba5a26732431f844f", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-29.6.0.tgz", "fileCount": 6, "integrity": "sha512-a7pISPW28Q3c0/pLwz4mQ6tbAI+hc8/0CJp9ix6e9U4dQ6TiHQX82CT5DV5BMWaw8bFH4E6zsfZxXdn6Ka23Bw==", "signatures": [{"sig": "MEUCIFjn7f/+2Uwn+53lf9Nl04tlNiXtCIhGdcE0YdaQMFDKAiEA6ZatAFVUlrX8toPYoI0C+1K+njdP1zXqmDrA8eObMwE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5228}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c1e5b8a38ef54bb138409f89831942ebf6a7a67e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"expect": "^29.6.0", "jest-snapshot": "^29.6.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.7.0", "@tsd/typescript": "^5.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/expect_29.6.0_1688484354656_0.8631000288353288", "host": "s3://npm-registry-packages"}}, "29.6.1": {"name": "@jest/expect", "version": "29.6.1", "license": "MIT", "_id": "@jest/expect@29.6.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fef18265188f6a97601f1ea0a2912d81a85b4657", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-29.6.1.tgz", "fileCount": 6, "integrity": "sha512-N5xlPrAYaRNyFgVf2s9Uyyvr795jnB6rObuPx4QFvNJz8aAjpZUDfO4bh5G/xuplMID8PrnuF1+SfSyDxhsgYg==", "signatures": [{"sig": "MEUCIQDldUaNYr4iRzl8GyGOW8vOoywnWqCv2CtXCJiv1oC33gIgKQP1zZ6+5ZcChMU1w2mP/pZyjfxt7xJU9Yz/OWXHXhY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5228}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "1f019afdcdfc54a6664908bb45f343db4e3d0848", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"expect": "^29.6.1", "jest-snapshot": "^29.6.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.7.0", "@tsd/typescript": "^5.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/expect_29.6.1_1688653115908_0.141987381409854", "host": "s3://npm-registry-packages"}}, "29.6.2": {"name": "@jest/expect", "version": "29.6.2", "license": "MIT", "_id": "@jest/expect@29.6.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5a2ad58bb345165d9ce0a1845bbf873c480a4b28", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-29.6.2.tgz", "fileCount": 6, "integrity": "sha512-m6DrEJxVKjkELTVAztTLyS/7C92Y2b0VYqmDROYKLLALHn8T/04yPs70NADUYPrV3ruI+H3J0iUIuhkjp7vkfg==", "signatures": [{"sig": "MEUCIHgeKyfeCZQIggbntrmK4fLWknIlx7Jdo7w2xTgGAAUHAiEAxDIGvPkrZTfZ89AqUVuwWQLDr5ywoj6XhmOuMc6V7gk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5228}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0fd5b1c37555f485c56a6ad2d6b010a72204f9f6", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"expect": "^29.6.2", "jest-snapshot": "^29.6.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.7.0", "@tsd/typescript": "^5.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/expect_29.6.2_1690449699573_0.1969474685226822", "host": "s3://npm-registry-packages"}}, "29.6.3": {"name": "@jest/expect", "version": "29.6.3", "license": "MIT", "_id": "@jest/expect@29.6.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "d54e1e7134982166f62653add0d4b8262dd72db9", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-29.6.3.tgz", "fileCount": 6, "integrity": "sha512-Ic08XbI2jlg6rECy+CGwk/8NDa6VE7UmIG6++9OTPAMnQmNGY28hu69Nf629CWv6T7YMODLbONxDFKdmQeI9FA==", "signatures": [{"sig": "MEYCIQCrHLs1bseto036KTvft7Jh5JZj4RN7vuLKrYj4JgWJXwIhAPeYXcX1COX1IP7b3gDbauH/HV58dYsUMJ1MvihSObmY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5226}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fb7d95c8af6e0d65a8b65348433d8a0ea0725b5b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"expect": "^29.6.3", "jest-snapshot": "^29.6.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.7.0", "@tsd/typescript": "^5.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/expect_29.6.3_1692621587139_0.6753178477440074", "host": "s3://npm-registry-packages"}}, "29.6.4": {"name": "@jest/expect", "version": "29.6.4", "license": "MIT", "_id": "@jest/expect@29.6.4", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "1d6ae17dc68d906776198389427ab7ce6179dba6", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-29.6.4.tgz", "fileCount": 6, "integrity": "sha512-Warhsa7d23+3X5bLbrbYvaehcgX5TLYhI03JKoedTiI8uJU4IhqYBWF7OSSgUyz4IgLpUYPkK0AehA5/fRclAA==", "signatures": [{"sig": "MEYCIQC1tdzgzcVNxlsmJFuSHj+cHZKkFBUowpgvEn4JhmAopQIhAJHZPKRmh13TG0QMDak5x/pRjcUwIImEAazZ28InLKrb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5226}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "55cd6a0aaf6f9178199dfa7af7a00fcaa7c421fd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/1.13.0/node@v20.5.1+arm64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "20.5.1", "dependencies": {"expect": "^29.6.4", "jest-snapshot": "^29.6.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.7.0", "@tsd/typescript": "^5.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/expect_29.6.4_1692875475046_0.40884741557389015", "host": "s3://npm-registry-packages"}}, "29.7.0": {"name": "@jest/expect", "version": "29.7.0", "license": "MIT", "_id": "@jest/expect@29.7.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "76a3edb0cb753b70dfbfe23283510d3d45432bf2", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-29.7.0.tgz", "fileCount": 6, "integrity": "sha512-8uMeAMycttpva3P1lBHB8VciS9V0XAr3GymPpipdyQXbBcuhkLQOSe8E/p92RyAdToS6ZD1tFkX+CkhoECE0dQ==", "signatures": [{"sig": "MEUCIQCPRQbEeGnLYFQaqvoGlCeavDifOL3XOhvZgQMjmp4mdwIgQFxEtfywYUCNnkI3d1QQrlhE4/Z+rOhn5Cp5yDXXokU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5226}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4e56991693da7cd4c3730dc3579a1dd1403ee630", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"expect": "^29.7.0", "jest-snapshot": "^29.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.7.0", "@tsd/typescript": "^5.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/expect_29.7.0_1694501032536_0.909640062224961", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.1": {"name": "@jest/expect", "version": "30.0.0-alpha.1", "license": "MIT", "_id": "@jest/expect@30.0.0-alpha.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "bffd840053902735ccf4bf5a00259ded7fc58386", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-30.0.0-alpha.1.tgz", "fileCount": 6, "integrity": "sha512-z75LD9YDNx01dlUq9bFu2U4YdYQ70drcxHXN5J3Vb376NdH4gja8NA62qk0DN6Ta5V/SIZ7uXi+k1mhnaWWMFA==", "signatures": [{"sig": "MEQCIGRyRawxoNrFrDkCMSFjKcDbiHL81Vw6+RYtJWiexzUaAiBqC9kALHyfvUve9JZ+fwAiajgLNXfJdj7rgt/hmqIEhw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5894}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d005cb2505c041583e0c5636d006e08666a54b63", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/1.13.0/node@v20.9.0+arm64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"expect": "30.0.0-alpha.1", "jest-snapshot": "30.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.8.0", "@tsd/typescript": "^5.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/expect_30.0.0-alpha.1_1698672801996_0.014758010878125116", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.2": {"name": "@jest/expect", "version": "30.0.0-alpha.2", "license": "MIT", "_id": "@jest/expect@30.0.0-alpha.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "2617099bd033779d0cd9dc021c96cfac3d9cc461", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-30.0.0-alpha.2.tgz", "fileCount": 6, "integrity": "sha512-iXESBUhHh9JSg9sK7XiYSUYb09e7tcSY05fMHj1iGmwVJsGU/k0XL1bFVObzbyTDbuPHh4wv7nharEh3UwVlxA==", "signatures": [{"sig": "MEUCIH9vK014NCRz/FcfsVfzZMdBFHIX8NgEBbComYAjMEjXAiEAgW6yuBVbofAnowvz/axXMwrzqHlJse8CZ5lHiVnEpZc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5708}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c04d13d7abd22e47b0997f6027886aed225c9ce4", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/2.7.0/node@v20.9.0+arm64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"expect": "30.0.0-alpha.2", "jest-snapshot": "30.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.8.0", "@tsd/typescript": "^5.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/expect_30.0.0-alpha.2_1700126920249_0.7274157909648746", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.3": {"name": "@jest/expect", "version": "30.0.0-alpha.3", "license": "MIT", "_id": "@jest/expect@30.0.0-alpha.3", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "8e817b9150e2ada5a7e99b34a61a93a122438b26", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-30.0.0-alpha.3.tgz", "fileCount": 6, "integrity": "sha512-4O6/gzB6y6MvHVnLjQQNsMLOxN1B7ynaOEdOD7GSlDcMThm+ecIkQ1Rsh7hS3SOpssugRZSzQgWlN0+vWO+1cw==", "signatures": [{"sig": "MEYCIQDwfKVsKJX4Qnm/YcAYJUkMZhgZvcShrP8+SkHRXLXidwIhANnyaSoyrwXFEOfu8p6TquO3VBj4Zc7skPB3vQGmUNyQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5708}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "e267aff33d105399f2134bad7c8f82285104f3da", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/3.2.1/node@v20.11.1+arm64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"expect": "30.0.0-alpha.3", "jest-snapshot": "30.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.8.0", "@tsd/typescript": "^5.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/expect_30.0.0-alpha.3_1708427365421_0.6813625355377837", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.4": {"name": "@jest/expect", "version": "30.0.0-alpha.4", "license": "MIT", "_id": "@jest/expect@30.0.0-alpha.4", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "4a7601ac09d2af247367f1f9fc75c9d2f9a8f136", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-30.0.0-alpha.4.tgz", "fileCount": 6, "integrity": "sha512-lpFBk4BegBcJyYo1Jr8+hWeGoxu2yRAO9P+IcIgGlO77XOseWlwKP42vYpRezWS6IKp3QnG3s6AQTCa5y1DwVQ==", "signatures": [{"sig": "MEQCIDrs9UcC5yAPViis2Dnez1I+j6ljoj7MbeHYZrTmBJBQAiBgimRawgZIoSBW8xTb7DJvSQzr19+hUJEl1tpTT6+nIg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5752}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "32b966f988d47a7673d2ef4b92e834dab7d66f07", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"expect": "30.0.0-alpha.4", "jest-snapshot": "30.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.8.0", "@tsd/typescript": "^5.0.4"}, "_npmOperationalInternal": {"tmp": "tmp/expect_30.0.0-alpha.4_1715550219431_0.4971669136940151", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.5": {"name": "@jest/expect", "version": "30.0.0-alpha.5", "license": "MIT", "_id": "@jest/expect@30.0.0-alpha.5", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "cb9aee0f0c51794a0cf4f33fc9e6698f457940a3", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-30.0.0-alpha.5.tgz", "fileCount": 6, "integrity": "sha512-Gp4hyR6UUPyplIO9ipcXSSM19xMoWro3+W8VbPrqw9IuOpOs/rVfh3Tn62WP8ZfYYK7CAtUI+Fhx/jiqwTGPtw==", "signatures": [{"sig": "MEUCIQDQPOSK+VIZLuNUpvp//dusq4j3AfL4+lj6odJyTZdFlAIgOfphGkckZvqTx63E18mhV5r70fSJMCpyWVarSBxqYro=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5666}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fa24a3bdd6682978d76799265016fb9d5bff135e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "description": "This package extends `expect` library with `jest-snapshot` matchers. It exports `jestExpect` object, which can be used as standalone replacement of `expect`.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"expect": "30.0.0-alpha.5", "jest-snapshot": "30.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/expect_30.0.0-alpha.5_1717073058784_0.7163052703755206", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.6": {"name": "@jest/expect", "version": "30.0.0-alpha.6", "license": "MIT", "_id": "@jest/expect@30.0.0-alpha.6", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "dist": {"shasum": "7eb750ebb9b2a7d6559cb0f1a570407df574d162", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-30.0.0-alpha.6.tgz", "fileCount": 6, "integrity": "sha512-3O74pygTwUBzUjO958IgNwmp0WrjASbiWdMEfUMePVqtiGoyS4Nxj9hsx4uKsNVivNJSZiiayYoP6dLhWerJXQ==", "signatures": [{"sig": "MEUCIC+Ej9+vP+TbOScOrDLRHEi73vSFSVWDhM3NQqZKqezBAiEAv1T3lzLvRywGWEuObXF3xb/jZX6tvYGBMc8rrOq/NVo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5666}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ba74b7de1b9cca88daf33f9d1b46bfe2b7f485a5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/3.7.1/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"expect": "30.0.0-alpha.6", "jest-snapshot": "30.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect_30.0.0-alpha.6_1723102995265_0.5842194292466183", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.7": {"name": "@jest/expect", "version": "30.0.0-alpha.7", "license": "MIT", "_id": "@jest/expect@30.0.0-alpha.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "a1f2f370438ad1540f19520d4da25af1ab141a58", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-30.0.0-alpha.7.tgz", "fileCount": 6, "integrity": "sha512-YevyfPQaCyG1SPv+9Zu05Qfds73nxv/exKrfPj0oVzDGaJEBPXAuUYqt0uKu17q8OWCNYaYcAY0szTqa7GnVnQ==", "signatures": [{"sig": "MEQCIHDvB6Of8XnDQjuWfSmLaG4aftaLxJF+RbmkfM272lpVAiBkuT0EA7OIa2aK7XWJClKRdjxkdKYvB9/F+3wMmWCZhg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5667}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "bacb7de30d053cd87181294b0c8a8576632a8b02", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/3.11.0/node@v20.18.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"expect": "30.0.0-alpha.7", "jest-snapshot": "30.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect_30.0.0-alpha.7_1738225723375_0.776553022332058", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.2": {"name": "@jest/expect", "version": "30.0.0-beta.2", "license": "MIT", "_id": "@jest/expect@30.0.0-beta.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "646d19a11babd107438293e22c1b997d12bda224", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-30.0.0-beta.2.tgz", "fileCount": 6, "integrity": "sha512-8L+gwb2hUucSpnwta+m487KqsiK31QkSOXEMPqNL28/0nd2cFhEBXMMuTcUuXjPmsNAJapCUFhQODYC9JHCZ6Q==", "signatures": [{"sig": "MEYCIQC4rhehPRtELnWnFE5sx0YkU+wLC3UEvm+O4eonKG4yoAIhAI8CFrO6MTZbXvqmJ9n7MaNHdNiQ5Qcork0MbfqajJy0", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5663}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "53a5635ac9a43099033f6103e179b13a5465e017", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"expect": "30.0.0-beta.2", "jest-snapshot": "30.0.0-beta.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect_30.0.0-beta.2_1748309008154_0.9965466825839313", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.3": {"name": "@jest/expect", "version": "30.0.0-beta.3", "license": "MIT", "_id": "@jest/expect@30.0.0-beta.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "535df8623b97e3f3c6694842fb7db56cf4497e1e", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-30.0.0-beta.3.tgz", "fileCount": 6, "integrity": "sha512-ReYpERY84kEkYHbdi+bkp0rFaoYRNWslVDjDq9HQvBamA2U9kOo8ieUkTH3/V8viVGDN/tOQh7pHcBMP21uIcQ==", "signatures": [{"sig": "MEYCIQDmODhlhmAvol05z0aTKBgEa+UabI+Ed086kzhUEGFbRAIhANUWp7lS9frxIoZR/PxUyjSz60xIMGib3ZuPNm0jC5Hj", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5663}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a123a3b667a178fb988662aaa1bc6308af759017", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"expect": "30.0.0-beta.3", "jest-snapshot": "30.0.0-beta.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect_30.0.0-beta.3_1748309276801_0.8208437555018173", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.4": {"name": "@jest/expect", "version": "30.0.0-beta.4", "license": "MIT", "_id": "@jest/expect@30.0.0-beta.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "22307f3fa0838e69e759ba94e51d98c9d5fcb7e6", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-30.0.0-beta.4.tgz", "fileCount": 6, "integrity": "sha512-Rkvf/WZJSaB0JBFlPTLCmCFjGpIXGHUlIBmKWJXhsXkibzPzsarTR0SVm9Uu/PB4PRy+1joLh3ZeCs4f5slisA==", "signatures": [{"sig": "MEUCIQDw2MTp3+rWWclqWF5sNrsF5C+b2wQ4JF1CNSLYEmuyJQIgYsee9X2W544s7qS7+qWkdKpNzz0dEuuDOH6gp2NTbe4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5663}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "69f0c890c804e6e6b0822adb592cd00372a7c297", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"expect": "30.0.0-beta.4", "jest-snapshot": "30.0.0-beta.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect_30.0.0-beta.4_1748329474721_0.2587926325876291", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.5": {"name": "@jest/expect", "version": "30.0.0-beta.5", "license": "MIT", "_id": "@jest/expect@30.0.0-beta.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "aff0b077c63117c125a9dfdc5d3be3103bdf7c5a", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-30.0.0-beta.5.tgz", "fileCount": 6, "integrity": "sha512-WpsjE6EeBBuwDtsIG/G6UqEihXUH+Bsnx1DTbIqn9xtSyky1H6rW9c3cBypJe9zpzMzAuNSSmAXm1NVdCQ5JSQ==", "signatures": [{"sig": "MEUCIET4ZjQL7XFVkxo/SHg3WtmGP9ZFBKKJIGhF9j2VExEyAiEArbbeRB48K7YK5PK+tWGWyuNXzBfVwepqaaZM6BbtvRk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5663}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f2171bb4c6836d74ad2b32a48151d9e0fdfa20a2", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"expect": "30.0.0-beta.5", "jest-snapshot": "30.0.0-beta.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect_30.0.0-beta.5_1748478621909_0.5182097308058724", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.6": {"name": "@jest/expect", "version": "30.0.0-beta.6", "license": "MIT", "_id": "@jest/expect@30.0.0-beta.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "2f95f984371c41b0b5e8b877a39ab30728c5f632", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-30.0.0-beta.6.tgz", "fileCount": 6, "integrity": "sha512-+3cTXEq/clkfiPjNdDB61ZWTxJl5qTyxK0zMVWvGZiiP11NaRXE/MVFGcC0r05zxbI9cZzY21k96k0nEOkq8pA==", "signatures": [{"sig": "MEYCIQDaEdxasZthHflctLDGd2I+3/GsfWGZzoqjJ7BaoeM0pgIhAIULf4z1nbWv5g37wNEYmrlbqPsXURnoU6QbIJJAYI8l", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5674}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4f964497dc21c06ce4d54f1349e299a9f6773d52", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/3.12.3/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"expect": "30.0.0-beta.6", "jest-snapshot": "30.0.0-beta.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect_30.0.0-beta.6_1748994659262_0.5567122107573723", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.7": {"name": "@jest/expect", "version": "30.0.0-beta.7", "license": "MIT", "_id": "@jest/expect@30.0.0-beta.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "91cdb5108695a6635e9de460090ca014509e6798", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-30.0.0-beta.7.tgz", "fileCount": 6, "integrity": "sha512-iFZHS8oq5B9KpPtfEO6kBC1Hg5iGfJ+DAsrj6Khh0OpaUCgTCXSez7lJjCg0eqND0fzrNcm6bG6l5pnrVwcUcw==", "signatures": [{"sig": "MEUCIA+/u5IZjEjp899kIPmtrPQNjGq8j5aZZ+Z69Tnv49doAiEA3hUKeszCX/+yYm7i4iTtozYlvZCA23mIkC+dj/dGQEo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5674}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "48de6a91368727d853d491df16e7d00c1f323676", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"expect": "30.0.0-beta.7", "jest-snapshot": "30.0.0-beta.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect_30.0.0-beta.7_1749008152376_0.8726014477571815", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.8": {"name": "@jest/expect", "version": "30.0.0-beta.8", "license": "MIT", "_id": "@jest/expect@30.0.0-beta.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "c1b16bd38870fb5c226ccd0003969001e8410ac8", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-30.0.0-beta.8.tgz", "fileCount": 6, "integrity": "sha512-JAoYx2uahZjxx7k5ok8usbQxNNuBiDj0k1p93fQbpzOAiyYDBV619vuz1ruPu5hrZrq033Qo/6kOPh02lj7+4w==", "signatures": [{"sig": "MEQCIHZdtZ40szkZhciAzjzMFu3t5YcIMYPQMIjlLhrj1Hh0AiBAzbX2a/EM7HtIfOgaWIhBGnuoqZKkKXMuZojVp4DKVA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5674}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ac334c0cdf04ead9999f0964567d81672d116d42", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"expect": "30.0.0-beta.8", "jest-snapshot": "30.0.0-beta.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect_30.0.0-beta.8_1749023600845_0.3948939465293799", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-rc.1": {"name": "@jest/expect", "version": "30.0.0-rc.1", "license": "MIT", "_id": "@jest/expect@30.0.0-rc.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "9d5925c7359663cb99f77a431d9e4a97a9a64d78", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-30.0.0-rc.1.tgz", "fileCount": 6, "integrity": "sha512-iOt6Y3B6cd4QDS+pa0zwTbG0iUHTY85PQyk4S0llhEFWTtUScExPj6inuaHR8ntH5xAw+XHa16HGnmVIi/aP8g==", "signatures": [{"sig": "MEUCIQDrndbhMRMB+TTDMD/q2KQI8TWf4i0abq7fBzGYxgU5+gIgUQ1JKuN0R9KW1STSz1p1Y/7TVaQehhQlFLDL0U+rNQw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5668}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ce14203d9156f830a8e24a6e3e8205f670a72a40", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"expect": "30.0.0-rc.1", "jest-snapshot": "30.0.0-rc.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect_30.0.0-rc.1_1749430976449_0.7481910118746418", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0": {"name": "@jest/expect", "version": "30.0.0", "license": "MIT", "_id": "@jest/expect@30.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "3f6c17a333444aa6d93b507871815c24c6681f21", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-30.0.0.tgz", "fileCount": 6, "integrity": "sha512-XZ3j6syhMeKiBknmmc8V3mNIb44kxLTbOQtaXA4IFdHy+vEN0cnXRzbRjdGBtrp4k1PWyMWNU3Fjz3iejrhpQg==", "signatures": [{"sig": "MEUCIQDH1352gcqZ73KZBXGl+YSfe9mbb/YJQPrlGJ3dhoG6ZQIgf9OFhaCmcaHtxuU8mZqBP3Sw21f+I6L9aLq0Fr9jyOM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5653}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a383155cd5af4539b3c447cfa7184462ee32f418", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"expect": "30.0.0", "jest-snapshot": "30.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect_30.0.0_1749521761972_0.6493468027398563", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.1": {"name": "@jest/expect", "version": "30.0.1", "license": "MIT", "_id": "@jest/expect@30.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "8b61ca41942e4d0e13b33d4cc1609c6179117625", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-30.0.1.tgz", "fileCount": 6, "integrity": "sha512-mxhK5Zt8z+gOrXkv6RxQoRb1741EkcliTaNAIzrj1w4ch3TruFW+1QbLOTarovxo02EIh+a+JGky3r25p0nhIA==", "signatures": [{"sig": "MEUCIQCxMo2KIsB1faero/T5j6pEnzPlO1lbfDLyFprqIjNhugIgIGgl6hbBw0a4zNBzD/2sLEI0/9Mt51/hYh3MLB9TOvk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5653}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "5ce865b4060189fe74cd486544816c079194a0f7", "_npmUser": {"name": "cpojer", "actor": {"name": "cpojer", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-expect"}, "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.2.0", "dependencies": {"expect": "30.0.1", "jest-snapshot": "30.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/expect_30.0.1_1750285899368_0.5577430957508782", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.2": {"name": "@jest/expect", "version": "30.0.2", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-expect"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"expect": "30.0.2", "jest-snapshot": "30.0.2"}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "393acbfac31f64bb38dff23c89224797caded83c", "_nodeVersion": "24.2.0", "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "_id": "@jest/expect@30.0.2", "dist": {"integrity": "sha512-blWRFPjv2cVfh42nLG6L3xIEbw+bnuiZYZDl/BZlsNG/i3wKV6FpPZ2EPHguk7t5QpLaouIu+7JmYO4uBR6AOg==", "shasum": "b3d5adec28f3884d6fd0746c4b5d0d2473e9e212", "tarball": "https://registry.npmjs.org/@jest/expect/-/expect-30.0.2.tgz", "fileCount": 6, "unpackedSize": 5653, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCBBO/qS2Foln+5E6dMSnkJzq7Ach1QIjRQyl7fsjHkzAIgZPwVygvGx2bAMfVtflnU/GxDXqNqGp/QFcvLUXtAQcA="}]}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>", "actor": {"name": "cpojer", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/expect_30.0.2_1750329988889_0.5718937936456354"}, "_hasShrinkwrap": false}}, "time": {"created": "2022-02-16T18:12:21.821Z", "modified": "2025-06-19T10:46:29.394Z", "28.0.0-alpha.2": "2022-02-16T18:12:22.055Z", "28.0.0-alpha.3": "2022-02-17T15:42:30.106Z", "28.0.0-alpha.4": "2022-02-22T12:14:01.687Z", "28.0.0-alpha.5": "2022-02-24T20:57:27.121Z", "28.0.0-alpha.6": "2022-03-01T08:32:31.900Z", "28.0.0-alpha.7": "2022-03-06T10:02:49.010Z", "28.0.0-alpha.8": "2022-04-05T15:00:16.346Z", "28.0.0-alpha.9": "2022-04-19T10:59:20.799Z", "28.0.0-alpha.10": "2022-04-20T07:37:33.621Z", "28.0.0-alpha.11": "2022-04-20T13:31:04.376Z", "28.0.0": "2022-04-25T12:08:16.166Z", "28.0.1": "2022-04-26T10:02:45.024Z", "28.0.2": "2022-04-27T07:44:10.568Z", "28.0.3": "2022-04-29T10:44:28.022Z", "28.1.0": "2022-05-06T10:49:00.223Z", "28.1.1": "2022-06-07T06:09:41.980Z", "28.1.2": "2022-06-29T10:34:00.845Z", "28.1.3": "2022-07-13T14:12:36.215Z", "29.0.0-alpha.0": "2022-07-17T22:07:13.800Z", "29.0.0-alpha.1": "2022-08-04T08:23:35.521Z", "29.0.0-alpha.3": "2022-08-07T13:41:44.864Z", "29.0.0-alpha.4": "2022-08-08T13:05:44.640Z", "29.0.0-alpha.5": "2022-08-11T13:41:02.731Z", "29.0.0-alpha.6": "2022-08-19T13:58:00.340Z", "29.0.0": "2022-08-25T12:33:36.853Z", "29.0.1": "2022-08-26T13:34:50.462Z", "29.0.2": "2022-09-03T10:48:28.167Z", "29.0.3": "2022-09-10T14:42:01.034Z", "29.1.0": "2022-09-28T07:37:49.411Z", "29.1.2": "2022-09-30T07:22:56.476Z", "29.2.0": "2022-10-14T09:14:04.973Z", "29.2.1": "2022-10-18T16:00:21.495Z", "29.2.2": "2022-10-24T20:24:10.867Z", "29.3.0": "2022-11-07T17:55:53.902Z", "29.3.1": "2022-11-08T22:56:29.726Z", "29.4.0": "2023-01-24T10:56:06.180Z", "29.4.1": "2023-01-26T15:08:50.901Z", "29.4.2": "2023-02-07T13:45:44.861Z", "29.4.3": "2023-02-15T11:57:33.160Z", "29.5.0": "2023-03-06T13:33:41.596Z", "29.6.0": "2023-07-04T15:25:54.802Z", "29.6.1": "2023-07-06T14:18:36.108Z", "29.6.2": "2023-07-27T09:21:39.709Z", "29.6.3": "2023-08-21T12:39:47.293Z", "29.6.4": "2023-08-24T11:11:15.257Z", "29.7.0": "2023-09-12T06:43:52.697Z", "30.0.0-alpha.1": "2023-10-30T13:33:22.139Z", "30.0.0-alpha.2": "2023-11-16T09:28:40.422Z", "30.0.0-alpha.3": "2024-02-20T11:09:25.607Z", "30.0.0-alpha.4": "2024-05-12T21:43:39.590Z", "30.0.0-alpha.5": "2024-05-30T12:44:18.933Z", "30.0.0-alpha.6": "2024-08-08T07:43:15.434Z", "30.0.0-alpha.7": "2025-01-30T08:28:43.564Z", "30.0.0-beta.2": "2025-05-27T01:23:28.330Z", "30.0.0-beta.3": "2025-05-27T01:27:56.973Z", "30.0.0-beta.4": "2025-05-27T07:04:34.903Z", "30.0.0-beta.5": "2025-05-29T00:30:22.084Z", "30.0.0-beta.6": "2025-06-03T23:50:59.516Z", "30.0.0-beta.7": "2025-06-04T03:35:52.653Z", "30.0.0-beta.8": "2025-06-04T07:53:21.043Z", "30.0.0-rc.1": "2025-06-09T01:02:56.653Z", "30.0.0": "2025-06-10T02:16:02.210Z", "30.0.1": "2025-06-18T22:31:39.549Z", "30.0.2": "2025-06-19T10:46:29.105Z"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-expect"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}