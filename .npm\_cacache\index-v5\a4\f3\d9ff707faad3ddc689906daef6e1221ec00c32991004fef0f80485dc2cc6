
4cccbc5fbab37bf5dd32d8d735ce5bd288fafe75	{"key":"make-fetch-happen:request-cache:https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz","integrity":"sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig==","time":1750659382762,"size":1528,"metadata":{"time":1750659382352,"url":"https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz","reqHeaders":{},"resHeaders":{"cache-control":"public, must-revalidate, max-age=31557600","content-type":"application/octet-stream","date":"Mon, 23 Jun 2025 06:16:22 GMT","etag":"\"f07933e3b37ddc954a8b88c9add93a0c\"","last-modified":"Tue, 30 Jun 2020 13:11:53 GMT","vary":"Accept-Encoding"},"options":{"compress":true}}}