{"_id": "@jest/environment", "_rev": "152-9aedf80625641f02cb3f814890c78552", "name": "@jest/environment", "dist-tags": {"next": "30.0.0-rc.1", "latest": "30.0.2"}, "versions": {"24.2.0-alpha.0": {"name": "@jest/environment", "version": "24.2.0-alpha.0", "license": "MIT", "_id": "@jest/environment@24.2.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "bcdaf14f23c47c01ee172950df0115badc7954a9", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-24.2.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-k5LWCxeL6zdXktNka/holrJmBBEAJUbjobAgDfP3EMPqx7X6mK108AgXsXgUpR4l3Kh34m5MTMtxWvQ6YCiGwA==", "signatures": [{"sig": "MEYCIQCPhNCtj03chuvCZJ0JVOYiBvSN0zao7VYJ26w4k24djAIhAKgd+N3lwBR0PHB8dp7XZaKPSoYvgma8kIOJ8sjX67u2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13034, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcfo7/CRA9TVsSAnZWagAAGxsP/24INbfMbOgk53HFOzro\n77VDYhrQ5VRnhgVK1BTR50YccWlyoGzStRtCpjzQoPaxQtyOnF3Txk/dpWdl\nmIsxmXc2+EUoOFcKjkhnjLWHLg1iAP86koPha/4MDrFmbA8zQJgyXZN7ac1h\nvLWXBiJ9JxTaF7pZofNnuOnej5L4oPRVqB+NV2nmJ6uikgz3Bstoehq9RNmw\nrOUivy1WDe0ZeQ1sTUZ6P7hPZqCGw+kPdWjvrBidGGshxhQBJveU1UfZwjpA\nTCDMCsjQLq3R0omeZVlsOuyEBv7lhV0ompG8hMPg+Hg76pkIZWrN2H42UmdB\nBVrP56dNAnjsw3Kk44E+otAI//wpkXrVa2P8Kk/MSzbNyzTy60s/JgmAwJeW\n5LJvyI65azRRpVlwlx0Mx/tfuUGTh8XuqIAMkfHl+kngGfMLEBDDcBbuPYEA\ntEEB2XpbMaMvAF9TqsQDriFLG6Ylyy9QhlHZKu4gC03NQlRuvFs9OEWfT85O\nJuqClBAfHLxGAORj5lDF5lqbhuKD8n1SEfvVASZ4XzqkBKbn8fnBaCWT+816\ngkAKvQMikXuoY0tG1nQ3DEFKaZr10nIFR7ZNQZ3abv+4Z74XL0sG9XuIzys+\npAIi03sWI5JKVGzj8CstvhDIbrrO3ta6wweMO3kQ+Gw9UVRIUtqEmgq+epW3\nFw0G\r\n=1yHB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "800f2f803d01c8ae194d71b251e4965dd70e5bf2", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "5.6.0", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"jest-mock": "^24.2.0-alpha.0", "@jest/types": "^24.2.0-alpha.0", "@types/node": "*", "@jest/transform": "^24.2.0-alpha.0", "@jest/fake-timers": "^24.2.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_24.2.0-alpha.0_1551798015189_0.39809747667179796", "host": "s3://npm-registry-packages"}}, "24.3.0": {"name": "@jest/environment", "version": "24.3.0", "license": "MIT", "_id": "@jest/environment@24.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "45e7c5cc996cb8f2287a30f8de08b152fa226fe2", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-24.3.0.tgz", "fileCount": 6, "integrity": "sha512-rPrnhX3cBvGqODfd6aUsCruUijVp2tmBC0YfeXIku0MciQSR9ek5tjdEk31iBvxE9WlGQus+E/slRLqJmCRZTw==", "signatures": [{"sig": "MEUCIAXHGNHbjWMa1YNMonQB1G1T+pdPmB9/l0FzYx+Vp0G1AiEA/sMtQYRJVjVKqWR24EO9w+XjLzmsRLA088RE1OqDRh4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14093, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgRXhCRA9TVsSAnZWagAAe8AP/1gZjzOt5zU+OIWHbHYP\ntYpI3J1TO1cJegEGvjUVDYmQHyoifESvooV+F4KPusKXprV3Gz4Clzmz+dg6\n8uda40DF0w04xtmEAtyUGKfB4zR9rr/u4EuJE8fB6Qu4TH/WCJgYIpyJtLQb\nInYuC8NxZmdSEc+laF1IOXeTXOix/rGiggPfJcLgGA3tZqhYcj8Q8mkCLIfp\nbp8nQhasa+1fz58Zyxop/INnDAn+jlSQs57Nw/aVhmmMDrwD8eJq+6wp54SP\nqi31RsNkLYu5IFbcpo9BdSxnWaGyfKtAgk58fiSp6HPYTfVkCBwySGMierg1\nv6MDbpASUlBZkMg59anirusiV3YDp4vj9z7tQ0e+j5pj7FrWcVn4KPP6Vgej\nLQn77aqFq9XuN+8cwu/KVL8P+I6QTx7KcrZMw1ZcxLvFvlqrktFfQ2nsbfBy\n2O/PQgibUt0psz7jA+OZI759tB8oKeQNS4axPAjrgQMGM2e+EF8CIiG7o7PM\n0bxBhVF/eTWfVDtThipoRISBA5GLNGVE8l71u45/u0FawvmNJndxsCFDCKfT\nuqUv2UFWnRLJXHWw2czkUGk4NhbkcCY5oSmJrQeE0tVBERT1H1ye3Fj+jg62\n9XpZJ5hCSIJ5GxxYSNc2TptK3lBpF/dD/GJgX1datY7eS9LYdTxVfnSRqLX9\ns4Ea\r\n=8Cpb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "3a7a4f3a3f5489ac8e07dcddf76bb949c482ec87", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"jest-mock": "^24.3.0", "@jest/types": "^24.3.0", "@types/node": "*", "@jest/transform": "^24.3.0", "@jest/fake-timers": "^24.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_24.3.0_1551963616951_0.8988165390727727", "host": "s3://npm-registry-packages"}}, "24.3.1": {"name": "@jest/environment", "version": "24.3.1", "license": "MIT", "_id": "@jest/environment@24.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1fbda3ec8fb8ffbaee665d314da91d662227e11e", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-24.3.1.tgz", "fileCount": 6, "integrity": "sha512-M8bqEkQqPwZVhMMFMqqCnzqIZtuM5vDMfFQ9ZvnEfRT+2T1zTA4UAOH/V4HagEi6S3BCd/mdxFdYmPgXf7GKCA==", "signatures": [{"sig": "MEUCIQCKThER2l4k+dcM+UZGxJFpXuHc1zWXuAhivgRK2h88tgIgKV26Bf6NkzlZKYcTe6W/3OuQPE4ZizyqoVPS28CDc68=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14093, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgaVnCRA9TVsSAnZWagAAxO8P/A6SS5OuO0CmB/MfTXjP\nAXtuclKMaAbwsIdbx3vVSkjkoP5YE1jG3nS/Rm2jbtaZx3wv3D0WxruaSTJv\nQxXYboStWI4pvoGPsCmiLd6AYuhJb5KzqMFsEtWWCRNysHFjNrOU7bVbHXq3\nC6boTZwXYEnozdmyYoT8KJD5lbby0OPaqnd5s6W0kb4W5Zr+XUFLDXj8oX/H\nsB3rwlLXh85ZUxIT69uEAY33eV2WBfNSTl5p9caeCiPY0BG+AoTWfEH1PwtC\np0mJy0G/U2EcbKFvdXL+nAQABD015x6nDB7tBDHWQzZe7WshZ5Iq0sLwungf\nZnwAidxnu0TO+dC84IbAT5kD+E7bLJEBIhk3veUSdvW/sZXCgMw0zOGoMY7q\nbcpAqZeIvCTbS0QzSA9bU8wny1UvR2Rzsoc+w1bWyqAXn9hbdSzxXix8UAg5\n4t5fQuLtkS4LWWFW7bFf7iF//4mN9zST5YujaH85rVwgmrnDNIaATUpQEhAm\n5T+WInHH/jlInmZz+RBhBtyY834v79140hzveM7+becZvqYbovqkJYrhs+dz\n4wCOBdac92S6ovG5ryDRGTro0zun1cwLG5F76NAEWgPSxN+1LCy6Yb3pWrk/\nzVwKx9x0tf5NF6hZ+yeXwZ89imlfGs2egF6TFuB2z5GOG2nATFVnbezwrKLE\nTkSw\r\n=9Wpl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "65c6e9d5e398711c011078bb72648c77fc8a8cb3", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"jest-mock": "^24.3.0", "@jest/types": "^24.3.0", "@types/node": "*", "@jest/transform": "^24.3.1", "@jest/fake-timers": "^24.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_24.3.1_1552000358497_0.0642450808153987", "host": "s3://npm-registry-packages"}}, "24.4.0": {"name": "@jest/environment", "version": "24.4.0", "license": "MIT", "_id": "@jest/environment@24.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "552f0629a9cc2bd015f370b3af77222f069f158f", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-24.4.0.tgz", "fileCount": 6, "integrity": "sha512-YuPsWWwTS4wkMsvCNXvBZPZQGOVtsVyle9OzHIAdWvV+B9qjs0vA85Il1+FSG0b765VqznPvpfIe1wKoIFOleQ==", "signatures": [{"sig": "MEUCIQCfR01c3jXEy9MoSQmxyc8aB9MvvQletR4NaecWoBqCuwIgWsTzmKcLiCO7IoAkmtCCfnl1yJyxdYgAEKA1yy58ElY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14093, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJchneCCRA9TVsSAnZWagAAS0cP+gOdUsoM6UZsyQzebBGq\nA4DaVzTtlog4y9EZfWz22xAJ2gNj1w/zb42Dpd/9pELpK3nJnHjULghnVVVQ\nH1SEkXLcGzqRsAN5KH8NfErhSp73oCSUmEgoDbSWpxr9C5AmtqViANr3hcEA\nk2TQPIecRzOHYcWS6YK2KEkX6/+Z/axHnHVW21PzVGckJWiz35HCB3327/AT\ndvCBbUJoGTW+P8N7AIhiUs5qV8xSLgLmaD+LFx0i3xhhdADp8OBtxPYF7vaW\nVjKWPxeujoC9AC4zADGQbWom/rhUdcYlv0YEXpKm0/wYvPx3zS2LFLsnfTx8\nwJKwSdzgK5oC8gG0tuB9ZQthhyV4y5gEj77qWg4zbfKIs55U+1staiLYIkw6\nT+vvWtp4K0efTyFX85uu2GZxGF/oPaXiQW7jHwPP7cV7kteN4/QyM7CjuICi\nP2WANRoDVskrpOroTbM7ywyNzps+Z94TLzXyIvOBk3id2pirMaH+A80SQEd0\n5z6ISL8pkkSEn9lt+d+CgwPckUObPCWdIeiLP411HX7hRbeqS1hLYtqL4aoS\nMnCQHzj0MOwJnsKOKd6eDl4ONMjQWikBru+xXTZXIU2gylEbrD+P7yWRJif7\nrZE0Jdan0yehxUywV668/aSwyL66DeeIKmMhviSi1WvqPekV1hXeJqvXYIoa\nfSMA\r\n=q5T/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "a018000fc162db3cfd0ebf9f23fdb734f05821a6", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"jest-mock": "^24.3.0", "@jest/types": "^24.3.0", "@types/node": "*", "@jest/transform": "^24.4.0", "@jest/fake-timers": "^24.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_24.4.0_1552316289348_0.24713097222137237", "host": "s3://npm-registry-packages"}}, "24.5.0": {"name": "@jest/environment", "version": "24.5.0", "license": "MIT", "_id": "@jest/environment@24.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a2557f7808767abea3f9e4cc43a172122a63aca8", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-24.5.0.tgz", "fileCount": 5, "integrity": "sha512-tzUHR9SHjMXwM8QmfHb/EJNbF0fjbH4ieefJBvtwO8YErLTrecc1ROj0uo2VnIT6SlpEGZnvdCK6VgKYBo8LsA==", "signatures": [{"sig": "MEQCICqgUhZqeLFF29yuTqupV3rM8r6TSxjx2xIyE1aI5J8cAiBT7E7dcOEDxBvCcrp3YImVDZBrDpuRwYCAo5ZbjrH1Hw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12994, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJch+LwCRA9TVsSAnZWagAACW4QAIDezKBT7mfpAudzERgA\nnJ0V/S4vLrHP3FKEsCCgFD5Uvo4xUO3RUiSIDcKzz4D/lEbUss8QMdGmYAIQ\nHsTKYydl6XdLSeh+NK2Digls0VVDXNODTMY67Grkb7d4O7n8OniO3uZofbZ0\nb5WE1MXJQcUmlt8fS9EhzZs817QIYYngtFMl35C0zdzA0wH/Bd3NlSeeCBLr\nlqaKBMofkr9zdJXbPdKmSbjOP/gfJganqleWZNuzlf/2SeAwjTY0mbqg/WyE\nc1udTrEpj16/s+6cMvGcaNDKdhDCkd+EnkKahRliTIFdR1nVSiCUTRNs0iR7\n7c9jeh5aI64nh0g0yRqkEYx7yEvhFbJqzkOJdAw7cNIfYIEiQixj+sZVuKjx\nfgJz6mAYUV4D33TzCPu8zytwzC2gGumEBYgTvU07kbqvWkTZ01qCs/V9DNC/\nyV6OWViBaRmijn6Tgp5YFwDJbdG1nbpNFUW1c2ZHpJzZGHkDZu/Yhd9VSYL4\n7L2qnL9Ig8iOsUVIsgK7wUXADoPLwbMv3o7OXq/r/XcTIOoZaKT9kIYSKAdD\nDZGolXkdzFrwfSVqsStAM9o8yrIJtJhbScyflC9l8huFnHNQdEtOrGv4Nk1M\nO9aHSXAmqbxy2wKyg9vTBaae8cguNZMziYRDXUm3QDTqP0LLInYjZuKR2+A6\nWEkP\r\n=B/xK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "800533020f5b2f153615c821ed7cb12fd868fa6f", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "5.6.0", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"jest-mock": "^24.5.0", "@jest/types": "^24.5.0", "@types/node": "*", "@jest/transform": "^24.5.0", "@jest/fake-timers": "^24.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_24.5.0_1552409327836_0.8645229961353178", "host": "s3://npm-registry-packages"}}, "24.6.0": {"name": "@jest/environment", "version": "24.6.0", "license": "MIT", "_id": "@jest/environment@24.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6dea095baee2ce23ed05328468f945291f30ed30", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-24.6.0.tgz", "fileCount": 7, "integrity": "sha512-LccuUfnREDNFbKmMWrtzUJu6fwU1E6ddYlYSDuClEQvboMKQQMUuCSYXvRUQFtDdeVjUfxkHqfSVvBzuph0b7w==", "signatures": [{"sig": "MEQCIHb12/hINbOsYEnsIIBnbecfyYXtcjPawXG2LeokT6/5AiAymK9j4XUe5LEax6yu7WksAVlQQ4SzI3X92qso1Lg3YA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 204226, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcopA0CRA9TVsSAnZWagAAGkAQAJlbRVrM0osD/3v9qkHS\n9hYQGvSIOL7cap+wkmhdCk8tRhxzJBfPPuFx9LTRhKNDibDpdtFNZVYl0EUw\ncx6Zik5U+wWs6WrrYOkVvXz4WsPRHL8f+qYEkwSGA4QwkRcCrvHJaPQRAz7K\nPojJa49k7PzcKMH+fPf3TQDmtBxVl5mhEHSAytm/yN9ubCH5uScBPY7SPL00\nfY3rEdiREd2bYcTir/WS/qWwecXE+IUA50ik2L0dmFY3JOoQZOewG9E+ECJ7\nKCmgR34P9ngWtNMIxI6L3lCpngQZmTF354xxrkloCT6UxWH46NEcvfHzz/mj\n95ZyNhkeePEQNZUC3nbXyC0Gz/wKI/tj31cyQE/4/yFhFDZJDEKAtbSq/+gh\nUbujlvHttMpkvIWss2lxhTA1rYgSxh8ZkNoPK9eDCTiazDBKfGkKxp9/StAv\nC+/QxEQJC/bHqzlG+PoPeDbGrVYtmoMFqcO/yo8nlXEM9z/956bqPEj9P+Zr\nzqBsp1UMi68yquMzthe3JsmzmasOOOGA0fKwMXk9mOKXt+E6/F8hyd277VBh\nOGMVetd0bDPqh2vqP55Gju+joqd9P8Wb5o1DaAbRobYl75fuekchWhHoSIL2\nZhVJ3Fq3RSbpUT7KTEujXCkSEFbXWitRAo/dsApzLGPQv1lH215pc8utzLFD\npzzp\r\n=yu/K\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "04e6a66d2ba8b18bee080bb28547db74a255d2c7", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"jest-mock": "^24.6.0", "@jest/types": "^24.6.0", "@jest/transform": "^24.6.0", "@jest/fake-timers": "^24.6.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_24.6.0_1554157619878_0.3172322845452875", "host": "s3://npm-registry-packages"}}, "24.7.0": {"name": "@jest/environment", "version": "24.7.0", "license": "MIT", "_id": "@jest/environment@24.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5ff0099e1c184e2bd3a0517f60135baf28836b2e", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-24.7.0.tgz", "fileCount": 7, "integrity": "sha512-Vfv5vTPcE5Rp5TYK/hpUI07LV+OH6HOIpDNZ5lWLQ88HkPsDi9ILcSDLJs4tBZLcYltotlGapb5XUTjAfaRWow==", "signatures": [{"sig": "MEUCIQCTFSMMMUb+aaDxxbn4sFNXn0539qRX7cKylbIol7ekPAIgXPGF79ycNXPLgNFkzQwn22l+smfXPFi+aQiqf/9vJf8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 185368, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpC7KCRA9TVsSAnZWagAAu+0P/17cQJ1lzak7msHGqGTn\nGnQ5p/lt0wnhz31/oTzbTmpmrD37T4ESqxszY0HKb+fNMX/S+Jup2Up3cHfH\nl/HOQxfa+k7ukas+AGdwA399bNCB0nL0HOnVsGFyhOxT5XwS5/5GjhKQylMW\nolRtfSQtWgg7SkUGhdFXyjWX0dvCLMuvRwdTEx0Xvx4fbAhUiUTpYH/j0SO1\n1NgSuHmpppL1RSwPp1ReG6Qd+chpU7ReMcZFIf3c9hLfW04dHYSxwkpeZemj\nXUfTchMyG2Y1ecVnyHAIgzWTEP5m0ft1rLkY5pw7paZ//pwRS+QLvUJWdygm\nVCMDhyE75k4rSge3MWO4CPaA2onA1HMumqZlr5ARJiYzBJnckK6n/quu5yJc\nF6AIxHYm0SDh6aWjVYCy8iJXVzpPDhXrSocuYWITndjM7lN7mYa/1BMQ1ssy\nE++gVa35CVWTYLCigF/KIlMFRFSFHmn8aYnixkNdVc+h/E2ZDFxV5yQjoGYk\n7g5DypS0MhVG076WivdWbGyyIzwqZUkHbvhSnuR0C/UJbTkhAkzHSmqyFsga\nCKKCDiwTTE0Ao7YBWOozFQVbHmGjgJWY5BHTf0rcdJEOwRC+MdKdDvXt09iF\nYJ4RS/oLkS1hOBvKX/jZHhAUB4Bp01pYPvrmhz1kzf55DhbSIxwFeXiF+sDz\nxCXl\r\n=t7yv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "eb0413622542bc0f70c32950d9daeeab9f6802ac", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.13.1/node@v11.12.0+x64 (darwin)", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"jest-mock": "^24.7.0", "@jest/types": "^24.7.0", "@jest/transform": "^24.7.0", "@jest/fake-timers": "^24.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_24.7.0_1554263753895_0.19695780623650916", "host": "s3://npm-registry-packages"}}, "24.7.1": {"name": "@jest/environment", "version": "24.7.1", "license": "MIT", "_id": "@jest/environment@24.7.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9b9196bc737561f67ac07817d4c5ece772e33135", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-24.7.1.tgz", "fileCount": 7, "integrity": "sha512-wmcTTYc4/KqA+U5h1zQd5FXXynfa7VGP2NfF+c6QeGJ7c+2nStgh65RQWNX62SC716dTtqheTRrZl0j+54oGHw==", "signatures": [{"sig": "MEUCIA8VanpbOSzCeyp4r7rrxMV1MiwrfSjIe9ZI69ua1NB7AiEAiLSQQ/lH+vG3tj9yjhak+ouSXDH7D73Gg1TA1nVVt1M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 185368, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpVuOCRA9TVsSAnZWagAA8GQP/1utE0Vn77taxhAOdxFS\njUL3XQnXxkwD/PaWGcczFYZTEqmstYZ666Bd+F6LmlXZ++Fl9b7wyfsAUtAi\n2ZOcaEXxKl910A/tk+IxFVN/AKG1IM9oII76oMEteZUtTOg0spWP9lseWQxq\nbqn9Mgp6ayHbizj0pnZ+pjua0H0zoaPfkEneomPMnE8fbYNNm5gbTgIdDoh7\n7xBi4t+zC4Z5mDcXgmC6NgF62B5Ey4YV5elftNgjOlfhwbUshmRjycbPcHWe\ntq4zXAmItNmDWgCl/xr/gPhY/NUkRA+XC+xEnRXKSC1JXkGK+Rbqo+9ZgKhT\nlTzlIxG0qRTpEliPxWAc8+sOlApsSgHpmjpfWqtCLtor6Im746EiEbaaFJgS\nhGJJ/kYat3hXluY/Q1AKsByAoWM66mad/sJfLUPL7e2T/sl2i+BlOoCAR+Dt\nMXRwpbXjvZCPpEUdORBnvWw3ft7Wv93uFnNZnsiWGBjMnKxYSpsVSeMZDvf8\nVF1wPdLhognDITdkHH0NpJsIiHuycnIXwyjmqDE212J9nhTbANnR1Nv6cim1\nA3ZMQycyZ8DSaza6d4KU/+8gTRdZPV1f0IzUmGE1mUFLnlPR34HvACJtzrRh\ntczy5WxjHCYoXl3u8j1c87VYgExVu88svSKZM3maG9C9mUzOte7ENHvofTae\np1Vk\r\n=R1BT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "0efb1d7809cb96ae87a7601e7802f1dab3774280", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.13.1/node@v11.12.0+x64 (darwin)", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"jest-mock": "^24.7.0", "@jest/types": "^24.7.0", "@jest/transform": "^24.7.1", "@jest/fake-timers": "^24.7.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_24.7.1_1554340749600_0.8242452433909744", "host": "s3://npm-registry-packages"}}, "24.8.0": {"name": "@jest/environment", "version": "24.8.0", "license": "MIT", "_id": "@jest/environment@24.8.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0342261383c776bdd652168f68065ef144af0eac", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-24.8.0.tgz", "fileCount": 7, "integrity": "sha512-vlGt2HLg7qM+vtBrSkjDxk9K0YtRBi7HfRFaDxoRtyi+DyVChzhF20duvpdAnKVBV6W5tym8jm0U9EfXbDk1tw==", "signatures": [{"sig": "MEQCIF99k1MOaZcYGOIca77FbYf0x7N5x+8Kq+pacOp7cYVaAiBd3EMLLy5bxFFcmJp2eWPiviWX504phyAVJuZwkduX4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 189341, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJczkRMCRA9TVsSAnZWagAASeEP/RWRqVSY3gSiCwScmpjr\nyxQVOYMsTzfBK2uQcQ3DVJ12ijZJ3jEUUqpN3sFvbYTi3bVEKlCrc3HfrVIl\nt5voMZ8PmPptrzPeOyq5M9BJu41IT9LjA9NV/axIPN1DCpSLwJBGDz1HUKBR\nmBme3P78RcuXnhcy8Qm0+hPzVyvHADxHsxVkrQkUaS61PDmJ7zDSEUnqpwfg\neDpm/Mjl218RZW+gVwImrXCNDYnUovB2LtL5pWlxQDIyrDFUsic9pxiIn3Y2\nrs0vb2Z/t6sMLjaYqbtNiJmIYbdrPcar25d2hEWMtPwcElEBBTZOD+bUzWmA\ndjImnSZJovKnDZwv5f8uEvFsPPWd1Bcdj32bzgqAywTdVymonMbvXkwAXg3k\ns1jBeWeZTYg3hfBYOiI1YM2zPg7PXSHgl1dq//pEWSCveYRPKV/EWwI1B6bU\ndmrRFhclKuesvxqJe4EylvzjD4UebQ2nkbQ1pCQBRWZo+YBsz/ljX2eeeFp5\n8j3Y2IBzSLvS9VLJI1c5tKO0BK4fmyGTcDrUkNDJB1nzmU+KQZUSXN6y1WZL\nmFv0GZyFXsd5V4DNCUi5+Q4qipvUgNTmVahuAwYjB/Gug0UmHr64HgpHvxqz\nzrlhwY9DfpxLYP4ilqeGFCWymKRUaFH66QNQ/10R1uNrMskrvL1O2JiaSfTR\nCZQi\r\n=MIH/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "845728f24b3ef41e450595c384e9b5c9fdf248a4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.13.1/node@v11.12.0+x64 (darwin)", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"jest-mock": "^24.8.0", "@jest/types": "^24.8.0", "@jest/transform": "^24.8.0", "@jest/fake-timers": "^24.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_24.8.0_1557021772241_0.5031591493791638", "host": "s3://npm-registry-packages"}}, "24.9.0": {"name": "@jest/environment", "version": "24.9.0", "license": "MIT", "_id": "@jest/environment@24.9.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "21e3afa2d65c0586cbd6cbefe208bafade44ab18", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-24.9.0.tgz", "fileCount": 6, "integrity": "sha512-5A1QluTPhvdIPFYnO3sZC3smkNeXPVELz7ikPbhUj0bQjB07EoE9qtLrem14ZUYWdVayYbsjVwIiL4WBIMV4aQ==", "signatures": [{"sig": "MEUCIQCN38o7s2nOh8g5NDXx+/SYphhYhDCvqZ6I0SI4xURQSwIgbQLTkLnOzM7CoxBWh5XLF4QYtTzNUHSStrb8DG21QyM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14489, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVkuCCRA9TVsSAnZWagAAY6cP/Aj7p518tRcsaQbYqfwk\nHfbhRvr0AWBQ1pW90Rn1rFch/YDxs9iVSMy0sxWVQxaddBighDhJzNmfw2Dp\neKX3PZ+35NbK1ysjXPD5o9JBK/WV766WJZMw9xbL5HsVa2XvF3i2zmuHzrSo\nsV/y99IsODd1Up1Uwn3g8fwvAOfObrWAkK+iEGP5gUXvXxlQUyHL3FTbcDdb\nDRjSedtmFodj9k7TZOrTVheM03f2pXSd9GpCFPdPcAiU8ULq9ilCPzKi9lCE\nDA/a1CNlYIRj4QCvFMsRzV8OmnJlkoCm8VBtcuP4bo1/xIHF09AB4m1v0c1V\nQfLpAuPMMi5zVxOeNhZ5AJbxoS9KCPDyXXmC5KQ98K1WwsLysjGCDRLgeSMm\naBheBd8eexjWfRo8tulkKv7xkHodedTQMkS5v+uwXzjsU1dE0dz8PKrOtCGN\nuA3+ZJi6pSONW/1Mnl7RpDpe6GcbJkSSAbDme6fTzykfrzIJe7hyHaNnFQ3f\nsWHJ2U7Z3kEur/3pO7liuI09Usuab6qa96Yo7GJJwgv5/kzc5a6mikxE+8BG\n3f1GLHU03yQBOTtNZajuIhrWCbCSPGUDaLNW5HGo2QcoZrxVZEJoHRp0eIaW\noCyGmHd0vwsoPZTxBNie4PkzN+T00S3atvFzeR1rodBD6OUoDkXoKiIgMyhR\nh1Rk\r\n=w6AD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "9ad0f4bc6b8bdd94989804226c28c9960d9da7d1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "directories": {}, "dependencies": {"jest-mock": "^24.9.0", "@jest/types": "^24.9.0", "@jest/transform": "^24.9.0", "@jest/fake-timers": "^24.9.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_24.9.0_1565936513926_0.28707630545051854", "host": "s3://npm-registry-packages"}}, "25.0.0": {"name": "@jest/environment", "version": "25.0.0", "license": "MIT", "_id": "@jest/environment@25.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2d177755f7c34544c9a3e32612606be9f7ce4d31", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-25.0.0.tgz", "fileCount": 5, "integrity": "sha512-gRpTtYUHmpXnlfcpwyRz5PHAxPqJaQlUWvKsnkGk/mhXtmlNnsRJTo9RFPxpDYC4DW8frfeffZttxGmWPt7p2A==", "signatures": [{"sig": "MEUCIBZ/ImfXjmmXR5nt4+e99Shb5X53/Vqyv8uylZt8z81GAiEAx/WKBJQzv7B6yM8CvuJAffmQrlzNZ3V5YQWnGMbZS00=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXgrtCRA9TVsSAnZWagAAcNkP/10+E8uNipp1C8j+oOHR\nvWgQ1AcZopFPoLputDpFXUCeNL1qBSeca3pwKCbsqCZc4uPmtGyz3EVMNaGP\ngILJA24/FY0MVeAExzTUSkDT4eMseaj8BHJchxkPLfRsXxGCK+rnd8kcIkIP\nYu0dBHMhhMcr16WUiRP0EIugTKj2Ho9xMjt+ieciI5m0Il1qPyDjDxG2ba1D\n3l/h4RdV8OxCnzD9SsReKwPeovFa54Lm5flCoGeqoNjuMw2B71JTfIz/NCj+\neCRMQTKq+/XNsNgifInXSazMTf4syBAijG4wxzr5GLrMS7oZS4ciuW7Kqsca\nmt1qa7h+xX6VGrmTprjPnGujH/087bqiMDC0RxY7UioiiYhUE0EDvQa8aMCX\nKyq7RBvQV4AMGOCU/EtZpnsLqhQxLIKJ5VHfB6s4etMAkVGsqgVaSDhwKfHN\nBNDDfAkc/959fBILx0+kXYMEIlGN2tw8bdQC2SFgMUb69HFRBPsatg3uNS2T\nvxKLtLeQX+o3GX4kqkm7R3RcaEY1EivI5nD4quHwtzfoZUDsNgvW+47X5etU\nLbAz4+QuohYyYQo00OU+QYSYQr42SvVm+KxpBlgNdK3dfFDx/m5obemX/MPQ\nhOfh+EXba6zHG1RiaJYluVHxBicksI+sayowvZdYDiEm7SZWNSEClp0x7YmP\ntiU5\r\n=Yi6p\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8"}, "gitHead": "ff9269be05fd8316e95232198fce3463bf2f270e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.16.4/node@v11.12.0+x64 (darwin)", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"jest-mock": "^25.0.0", "@jest/types": "^25.0.0", "@jest/transform": "^25.0.0", "@jest/fake-timers": "^25.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_25.0.0_1566444269138_0.41377911348142415", "host": "s3://npm-registry-packages"}}, "25.1.0": {"name": "@jest/environment", "version": "25.1.0", "license": "MIT", "_id": "@jest/environment@25.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4a97f64770c9d075f5d2b662b5169207f0a3f787", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-25.1.0.tgz", "fileCount": 5, "integrity": "sha512-cTpUtsjU4cum53VqBDlcW0E4KbQF03Cn0jckGPW/5rrE9tb+porD3+hhLtHAwhthsqfyF+bizyodTlsRA++sHg==", "signatures": [{"sig": "MEUCIG/BaHr/mZ8JNt8418HceY3NHr47cCVLLVl8OBcO1/hMAiEAg6x2vnunuuf3Qn8mstoHxwEd7W9p6XYufZ7+GKDx6j8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15864, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJ56lCRA9TVsSAnZWagAAtlcP/2Dl+rhDRBP6DUJN3QLu\nsu0h8sfm2nm7KanHjbjVCeNvoXY9PtoAiru7j3URcM1WlLnFt8bdSHseH5Ai\nzOX7kSOoPcAhUbV7icyNmNM6DNMYuyFb5pbkVYAJl6w6MtT1B1vGcWDX6k6/\ntduE2fme4WZ7y0hUjUW7jTfBKdaYxoEdw89eyqizWdgsXEq8NJLaHTeHbXcy\nFw3+2X+7lQGtbEmErwMB0CGlZ73zhkNhdihwqFA+t8Y5GIvTkjIOQSy4NTY8\nXxSk4TP0g070F7xmf1N2G5ZEi/8z4MZtQkclNnLZYB/G4eBdzADd2HLmmPaZ\nCEp6aK+aCjb7s37UX8H+QAC5QSEEcw8Nz+a+XSFfPOHBDpCrxxDqokqPAszn\n49VMM34FO4GaDLOWy+MVLWaZX40wbK62ykdKzitBbvMphPy0308kxWV0q/5Q\nrxDxBkJHiwIXX8UEzVfnpzYLTP0IO4T53tlYlEYR/zpXLZEvgFyY7XbD+8u+\nyNpSD5Hf65dhSl93y0HXhK3eYFqAEVvryRdUOLcVbAHF+6Bvra4cUdqh+vR8\ncA3QCXSbcT8H1ADw/nkcwPkjL3z7yKXT+1CF3nXo0CJiwZFA1vItQxcPm4xO\nSSJLMRsuBlDjBhiz/kFY52RREi8XUnQeI36R9kauch2+zBJpYSm7y74Psr7B\nWGMA\r\n=Ycme\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "170eee11d03b0ed5c60077982fdbc3bafd403638", "_npmUser": {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.20.2/node@v10.16.0+x64 (darwin)", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {"jest-mock": "^25.1.0", "@jest/types": "^25.1.0", "@jest/fake-timers": "^25.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/environment_25.1.0_1579654821001_0.4045987221452665", "host": "s3://npm-registry-packages"}}, "25.2.0-alpha.86": {"name": "@jest/environment", "version": "25.2.0-alpha.86", "license": "MIT", "_id": "@jest/environment@25.2.0-alpha.86", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6917fcad0ad7392468543f8e532fc3992f5fdaa6", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-25.2.0-alpha.86.tgz", "fileCount": 5, "integrity": "sha512-jnlecT5e4q0cWMuDZTu3SMOTL9fagfW4SqQWIKn+fGAXZqGeat7s7pgQjztmxhFS++PuHY8r5qe3i6DRGaWC4w==", "signatures": [{"sig": "MEUCIQCRyS+I18P6fNibpzoZDJ6M9XF0/g1Sg8ED+ROeyfzTagIgRit76RpRzphe2/HzQnz2DtWEot/HBdI7F6+WAtBsCkY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15716, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee5IHCRA9TVsSAnZWagAA73UP/3TLeJNKISnsi3AaasAG\nE0cVbvZqHbPoEiGecDT5TRGLRfZgoKQLSkjNB9cz3OzbRpi32Xgl8kQcidOq\nzgHK/Z+Hynq9kFggUp/b4oFRvfBohEwT3kTyGav6rVLjUIV5CPp8xJYy06SN\n2xlphMIH+ZrFiVJSvEDUaEwlDiqG4gXkRzTv4KiUHvFNVblFn9Iu8q4J1eQ6\nBlWgLIJBB2j/Y6Y13J5zCKMe0QJprvklnBfWVIL5dzAZz6/wFH0ivwTXt2Ij\nTnTwMO5jAYuNw432CSUrNb1RyenT6dumtQmZ+fClcRM+zS/ebRnjaGUfR0RF\nIsM9Vj8XKnN1UqEkPnyjqdQpDm9Zn878JmZw7PQf0UVUCAhvYpSS37w65Brf\nH6fuHX2Jb/cfFU14YLM6BJaVv1cwqTyFa2H9lYufq1OGTSOEiljrMPkOZpGn\nug2veScPWfWymPhs5pnvv0bY6Cwq+6E4NRk9zWR8UQwXQ1Dx2YoHJS525hun\ny7SbdW4pjvwdo2VvS058H8ThapGSeGF3cl/BQ6zOSZeM/ZxKZC5HaHCwodBZ\nqboDiPbv+JMqDou7nvZ6JBFSvYqJtqiVLrilNuiyeQ/lZ/bkHnNr9Lj/xXCo\n0V70ahXMD5mmx0sWEMFfaY9f60LTb0SidMxiWrigfSGQEL6XbFoBBq+LcNrV\nUaUP\r\n=hj+6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "cd98198c9397d8b69c55155d7b224d62ef117a90", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"jest-mock": "^25.2.0-alpha.86+cd98198c9", "@jest/types": "^25.2.0-alpha.86+cd98198c9", "@jest/fake-timers": "^25.2.0-alpha.86+cd98198c9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/environment_25.2.0-alpha.86_1585156615435_0.05258241696262411", "host": "s3://npm-registry-packages"}}, "25.2.0": {"name": "@jest/environment", "version": "25.2.0", "license": "MIT", "_id": "@jest/environment@25.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "20984739c9d85d03a15c0fb2a75558702202353c", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-25.2.0.tgz", "fileCount": 5, "integrity": "sha512-cLkCRmVYg9QnyTLxZMUK72BVnwe/+ukxhwbt0DyFK+wayrlUtseusLfl9yvnarPzHtCWVx2LL68C6iOg2V1TdA==", "signatures": [{"sig": "MEQCIH0/CDQJ11hInOlqCbzdPAEQlJnYsdKYrGUAZlop8DGSAiASFQ/Mo7PjoIrDiYgG7jRWD5X0ivNt+PK0+Oy6ZOH2oQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15640, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee5vVCRA9TVsSAnZWagAAOTQP/i9cC7arWliRcqr1l6d7\nROQypATkWcJLm/6J3SUKfY+cllkgcYa1DQ1+pxG0jl4t0+QNuXft3i9/G/aJ\nQHBs5Lw6UJjRP+KzOnQSnub4H7vg++VldNZmYar0LAnoSx7vZsldk2AzOg/W\nXNt8WmfA76eweuKo64Jx/vq3VGYfpQdZiwWoxVdBFkWXhKPXkbZ6vQDEyF1j\n/hfg50OFBaTLOxeMj/B+6lYpqOIDQZ3KQquvrBD3qjiE1k6HFfCvFMXo0Ddo\n7FYVrCXdfCBmG+p+dUJVWS0WtefbFiwEu6NzLJ9n4rJhCY+WrYYNcy4+E+48\nwuBfadByoKA+9L/cUYFzGXSgoXQkaVk2A9avxJTspRCbSdUwW310i9LhSP8J\n9UfW2JfKQ5iyLWdjLU+rHDWuPnvyUI241NJAWM+6IdqjqqLEyft/ZPbUuo6x\nklhN8xKYM6nOz/+xEHrqSRCtEL+2ikIQyMRVp/1sIFg7BCO1dccB5yZtMChK\nIGomsbRjxnwFuPmWk3vtD7EcmI8C/FHgNtVn+mEV0OXAbit9JqJomPABqLD8\nyaSupDM0J2qWjy6r6gs5Q/kBjZ4+la3c3wKhYEkDgH6DE88W5FV86LiOlCFu\nzpqc8iwpQmYhCAiKtPE2qfqDioAb4mNbkIMD9ThbUEYZABv4DBflJRodlRIy\nKMs3\r\n=hQsD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "9f0339c1c762e39f869f7df63e88470287728b93", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"jest-mock": "^25.2.0", "@jest/types": "^25.2.0", "@jest/fake-timers": "^25.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/environment_25.2.0_1585159125259_0.8403061897867619", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.1": {"name": "@jest/environment", "version": "25.2.1-alpha.1", "license": "MIT", "_id": "@jest/environment@25.2.1-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5d6abc2a9e7ad697979ef058f18ae6d2b2bd71d4", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-25.2.1-alpha.1.tgz", "fileCount": 6, "integrity": "sha512-Ei04EXUbhDVk6l4taDzIZmFEdRF8CQwW0GeVD1UNFv1SZmprFN7iv818hZ90Cx6FgYcz7BY3lRgQ0J34O4pkdA==", "signatures": [{"sig": "MEQCIBtFvxJ//JFDuICkZs3W1cvvjocAm6HURYySfBoI4KOEAiBuDgOU7cLcXRXPbDizX8GTuALRWWlP4RK2H7vOFvGpog==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32666, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefF/FCRA9TVsSAnZWagAATkQP/Aionj5UmkBFXylZ3BMM\nwx51ODIiqdBfrHpDr22zKKHajXvZqkcVY9ACDxMwlQZMdSiC08DyIW4jOYXY\nzLG1c+Z38dBSXZ2wvFypsBfnWhR2XXNovmxPZnLb5TbsQzkRxVyHjsOZuoJ2\nbknHyb7zfPaw7UfZNzRTwoIs0zlszAY81BG0QZofDlQifygRKQtGccEScIKc\nKbzpetGbeYXcCatqNdbBJlhcaeDxHMgJbF1hdj3Lf6MiP7ILzV1U+dkLaL+u\nn1cV5cNmM4Iy6CrPxrUtHsRuVS15TEPdNSh+5mgjfnSTTSfZbVpPBCTR1iuQ\ns5/4KN22QbqdMAGjpzjARDKv4s2wMIyGnipRCZa0t3kc3aOD8P4v/bIghsbk\n0tKNte3XhaKZCFR9dVi17Xpo/bV+GMMlYnm9Wv3ZTg+d7qJSANF+FikRbfQc\nY0AS/Xu/Xoui9HGhtiENcQZ8nNEd8wcOadqjwwHX9HhRAf8mSx6jLGil1L87\n8bbgg7Eo5gynwAwgZTjGo6z8eNlsy9KiO1M1tz733POpC12iQb3V1O5y8VJ+\nSlGO7/MveTjhFGIMugqRooaEzhiobgcWR34dWkniugOSGNQKTmZ6foAnVAUe\nuLzMuryqyCjm+72T72z7F99HCJccmjV1GEUh6FReDvBg6j+jDVR+sqYhFqE1\nL5lm\r\n=BzDI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "5cc2ccdacb1b2433581222252e43cb5a1f6861a9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"jest-mock": "^25.2.1-alpha.1+5cc2ccdac", "@jest/types": "^25.2.1-alpha.1+5cc2ccdac", "@jest/fake-timers": "^25.2.1-alpha.1+5cc2ccdac"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"*": ["ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/environment_25.2.1-alpha.1_1585209285382_0.12561381994678822", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.2": {"name": "@jest/environment", "version": "25.2.1-alpha.2", "license": "MIT", "_id": "@jest/environment@25.2.1-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a05d9f2bacd0aa7bf72d86a9e05f1f2186763fe1", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-25.2.1-alpha.2.tgz", "fileCount": 8, "integrity": "sha512-awbljvLgR2Pvx0edLlKzPXlaIy2TmmTUdz2V28RDAemc8423IFBGBW3wQeZK0y7nq57tYu/XZQ7ZEZFY1SKaZA==", "signatures": [{"sig": "MEUCIAhktTXd2S76vZudFbPMqmv48KIXQT7OJQ2IkZupHCR/AiEAyzYHOtJHATh2kwAX2qTkiETug1nrkahEiO+qhVK2wUg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46728, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefGOOCRA9TVsSAnZWagAAwboP/A8aQDV7rVvisgrXRuOk\nVAiBnzQgi1+/SpTqhNWjOqHcf1eWaNFgtiATG/ek1CGUHRB9kdhO9JzOeffk\nkVuAvp9rccumZrDLdh2nRcwJr+pyvu1oE+L4Wu9YG2sN+N8Qnu7gOaQrfaxe\nEIGVdrCp0DtlOQvoXP+xjHIf30P8CUwjiEpSxqaaKE5YCZ2BFvI1Mc5P3I7O\nFkoISf8EW55uoFQnpNCfuCPE+YokL9n5lrW+bl4fqY9r2QmAXfIM5wqYXfHS\nz5Bsnoy1qzQSyzSM27T5wHvQT70yG1nXk+mwqdHLCM/5/h7ljpx/LfGYr+MH\nnt2MywGq0xIzS7U8FQVHA8i86OqckK43ZxF6oeAhAqD2U6x7N1wKffyqBdsJ\nLJDZPNLVipAhLrdLYEmLU8C3URIiksM5iT4RNuodaLnhV380gqZS8l/YLfc6\nMbsyS1mnvsxAZw9wcSSqaA2+jvFoRqe1o8qtqB6Rm+O2cktPzOIafymA/rNf\nVR0L0zM294h9h1QfRTypt0p9MPCUMPt8qi24pXziAEQHaXWbRDAF2queTuQu\ny7mFRh+MNEOcvIW/lAHPuS/rEUHCo8fmdzSqWetlepRwG0FVvQ/7ULflV2le\n1mOINFKy3pLFtk7pmUCp346yvTrFihIawTXwke97tOkYfvXLF0/4txLpnEuv\nAL2P\r\n=xr+C\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "79b7ab67c63d3708f9689e25fbc0e8b0094bd019", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"jest-mock": "^25.2.1-alpha.2+79b7ab67c", "@jest/types": "^25.2.1-alpha.2+79b7ab67c", "@jest/fake-timers": "^25.2.1-alpha.2+79b7ab67c"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/environment_25.2.1-alpha.2_1585210253609_0.8383830966938013", "host": "s3://npm-registry-packages"}}, "25.2.1": {"name": "@jest/environment", "version": "25.2.1", "license": "MIT", "_id": "@jest/environment@25.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d967f38c05accfb2dba325e93238684e8b1706bd", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-25.2.1.tgz", "fileCount": 6, "integrity": "sha512-aeA3UlUmpblmv2CHBcNA7LvcXlcCtRpXaKKFVooRy9/Jk8B4IZAZMfrML/d+1cG5FpF17s4JVdu1kx0mbnaqTQ==", "signatures": [{"sig": "MEYCIQDpxnIe8UrBxyprMEb7+su8c0h7wUPIBeDR26+lbGve4QIhALhBMGbAXy0+7PkJornOyVQk7meWDdDPPjdWfFvradXt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26068, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefG9zCRA9TVsSAnZWagAAuKgP/1n0BFTMMhwCJ7owkM8X\njoFJ5lZstq8okJvZrHrSXBxfuGhz0LB7jBO3KYMociopWZY7Dop45eTB/ykx\nkpV4RsJJtNHI997my11RYlfwzE8vPbFYrOtIXg6IRIvVAb/+Iyb+gnFuNGAq\n2Edoi3/zJx7ocUW+UXb5FxCSJTeXnVyCPWzzj/drD8flWjHs+UUr5Jg+9Epl\nOZnBfxXPYC56iH2EVzLHqS5O2g4MrHa5qlYHv+eWNhoY0aMIG0FTYQMMK7GG\numdjLe1F2GyLSyHxlZ3KG+MWL+Uucs0FILpyDW7ivj8wFSOXR+AW9yOk7R0u\nGvNBA3vCHXcDKHcE5l07u2VLjYHiPXT4v4VPLl/CVeB28milSThaMJXiO+9P\nZN5kVGw8PlW/P42D9IU3uIwR0sQk32OzcD6Ybq+HuvwGh2dLLBgDMWW2B6yH\nnmngHCI1Ch9HIZDaQjlLa937SuYsVfiL/OIWhUj4zSLOsjQAqcmPQlzNGceO\nbzjs9IJ5B3ZRb7ZgUqPBezIDa5cNP1IlteXosQPn0rLNhMsxVWHvX2ylCKPS\nwm/BYMXnKri75/LF63HTnkpQ1bUCneIPe9ucJ2xxxWdqSUtF4LkBvN+1Y6iY\nio3qw/89+VydNaZXo7LI9Rm1nhj1ZEo/JySkVMF4kbJlrKhmRJRf4HKRXkqe\nxYlK\r\n=2wRS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "a679390828b6c30aeaa547d8c4dc9aed6531e357", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"jest-mock": "^25.2.1", "@jest/types": "^25.2.1", "@jest/fake-timers": "^25.2.1"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/environment_25.2.1_1585213298997_0.9006048164450402", "host": "s3://npm-registry-packages"}}, "25.2.3": {"name": "@jest/environment", "version": "25.2.3", "license": "MIT", "_id": "@jest/environment@25.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "32b3f216355b03e9449b93b62584c18934a2cc4a", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-25.2.3.tgz", "fileCount": 6, "integrity": "sha512-zRypAMQnNo8rD0rCbI9+5xf+Lu+uvunKZNBcIWjb3lTATSomKbgYO+GYewGDYn7pf+30XCNBc6SH1rnBUN1ioA==", "signatures": [{"sig": "MEUCIFm3wGbgyg3j3cA5g8bLccqGCSdJvd2a1w2xP9wLoscdAiEA/1SXG77RxGmb9sjoxN3/Wt4ne1zwZC8L9sR4x8KhP9k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26068, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefQ+tCRA9TVsSAnZWagAAAKkP/R7ZoLturWi+PKmPHcq6\njsZcJoj5HFOzxGyW2oUKIsFWb80+dqbFys39/aEfQPpu+iUL5YtPJ2uNI5M9\nRoU/kab6exkJqzMyPB1Zs61giM/DyGH+xWsMLgFJPtch2ltzRYsS5dGR9tvs\nrWd8FwyRPdpruQOIz5JCbWuCiNnEsVOJHOre4RZ0btuKPC1EbikJs82PUl/8\nOvI+uY5eWwz31+gmZ2urZ+MgKTmpxqFwkIKJ7bewh82Bmax1AkwOEoxt3BNZ\nADT02vuONH9UEHS1vyhIzbbNcJeAtHNdWcrb6JMV4BqjsMYTSXyljWz4InzA\nRcgvm9ZPZWW9J8Mc65Dtlz0W3ZD+RtVLbea97NQDlQskkA56Kcl6j0V0xaiH\nH3ZB+hL5PbLcXXYX4PsUG67KNy0MQlOeVueCraMwxFrlgNWS+fE7/gSOSsyj\nT9cPHE7oNBd9RSenI8eztrNhc3qPkafm7avWqTCM6EhTBRO84PtfwJbSqZTW\nv4RjgI2fkjfIiauavLfGBTTph4hmd+bCAs65A52DfzxiXpUhepij4uT/vfEt\n27fgrJ3iVJ9/9T0GxP300WbyeuHEoqHAfrXrU3BvbfpR945UlpD+lypnPILT\njkkNPyZEOb5iSYbTHWgSkGDGIWLKEY0V0ybo7ez75HTnEVAI6YaztehGQjz8\nF2wc\r\n=Pd1H\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "6f8bf80c38567ba076ae979af2dedb42b285b2d5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"jest-mock": "^25.2.3", "@jest/types": "^25.2.3", "@jest/fake-timers": "^25.2.3"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/environment_25.2.3_1585254316785_0.600029677171444", "host": "s3://npm-registry-packages"}}, "25.2.4": {"name": "@jest/environment", "version": "25.2.4", "license": "MIT", "_id": "@jest/environment@25.2.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "74f4d8dd87b427434d0b822cde37bc0e78f3e28b", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-25.2.4.tgz", "fileCount": 6, "integrity": "sha512-wA4xlhD19/gukkDpJ5HQsTle0pgnzI5qMFEjw267lpTDC8d9N7Ihqr5pI+l0p8Qn1SQhai+glSqxrGdzKy4jxw==", "signatures": [{"sig": "MEYCIQClKgkib2g+pomB3RH8migj3rb/hAWeZqGVWaajhZqhnAIhAKy64qmr5fsui76f5hyuEOCCjGaZIA3Y503hEyigGaJ6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26068, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJegPlLCRA9TVsSAnZWagAADD4P/0Zy5jrQr2iQqL1piDVo\nprw9zuAf0wtHAuMRdAdkiKrJEfZ1EayneKl91AN4x42RblmwYK4DNdVDaTR0\nXjtWUZM6AJhyAspk7kpbtMCE/RaOj7Z/hTbN+yDaVyxPv5J/+QFXd6updATa\n4FMhHFvidUcaD9e45enaC5b5jJZn2nRp8QTgn8dTImk2MdZ6DNPE10VgKxtf\n5BrRwEc1R4Qgb+Ipx4kt5bO9nyHLuosPY6PbOdzo/v0GtHc176GuFsOjyaZi\nQI1d4yVl+A9ZDTvF9nEcPMPRK6N0m4v33tjY/aUt2X4X5UvDxQySoXvw+LXB\nSlyXCO9Fp9VDPjE3aQEfEd+qJugZd+EUBYzfk87Lk5G89YhdXvC3/TOSEbUt\nunEXnsceBUUmfOSS8WHI+c9KDVnU10fjDdFCkV97SuK7ImXuFu2dNV5zK2IV\nplhTqOSq9hVpckRVIIl6WV8m7L/4konBL364FR4WJ5Jpz7OncVAdqpFNdasU\nxnaQ5yMwNcCpGO5wJVIhij/rzcuy/1qNkjbOj8ipcmVkZzzujg3QpE1V8WDY\n0AZUFzF8xmODOlaw/yRqkLepd3t9unzwDR0Atvo9qCOC17otOSnOuEEVkV6m\ngX+PJ/0PHrWFPEtROwVCj5N2qsKfDpFdsRH9gNOD/BOg+OsqwQXMYAgEgkcn\nEp85\r\n=y15I\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "324938561c608e0e9dddc008e5dde1589d7abc68", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"jest-mock": "^25.2.3", "@jest/types": "^25.2.3", "@jest/fake-timers": "^25.2.4"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/environment_25.2.4_1585510730618_0.8134572343944313", "host": "s3://npm-registry-packages"}}, "25.2.6": {"name": "@jest/environment", "version": "25.2.6", "license": "MIT", "_id": "@jest/environment@25.2.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8f7931e79abd81893ce88b7306f0cc4744835000", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-25.2.6.tgz", "fileCount": 6, "integrity": "sha512-17WIw+wCb9drRNFw1hi8CHah38dXVdOk7ga9exThhGtXlZ9mK8xH4DjSB9uGDGXIWYSHmrxoyS6KJ7ywGr7bzg==", "signatures": [{"sig": "MEUCICZWWzY8gPLqFAEl0Ia98PWj5Hthqdsv+Mo/mIFFZuLWAiEAzImB9axYAFzpwFvHcJQ0mXxVkVjLFv3+91EzuoYeUvc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26068, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJehb6KCRA9TVsSAnZWagAAE5IP/1LWl+oit+FkdEL9Ea9D\nWkR9XURi6dcRth8mcaIbhmsky4GIziBCiz2ECVY4U78tlK7KJuZkj9MW7xxG\nlzQvgZuDQfyLWvgiAnvSc1ky3mnKIGxGbnWjmxGBYortcyecoU+BJ6XfofGT\njU0IOf9pz/I7cEHk5Wl/sqwgy3ynPyrK2CwTwtH3aGPXpSfe4fWjQGV6zIgn\nBuzDYqD0UPJO9xC0oEfJ2XwS7xVkEbtTcnuX3CZQFf6EmrH7+9v2Bi38dA+J\nJOFnxGJgxNVt7aAZKwg8+Ir9TarRHFfUGuO5soaeWdDNO0qNN1nytK+7OXIz\n9OerUZMz4XzdgBHA5bYsNv4k6CqGgQiCrqnVUWSHkiDxN7Mn1dZww+QC9JOo\n4uYnuORg5DlgNfKdNEquDLP2cvK2sggMq4AnQg1JVdotqa6sdi51AU7qti3E\n4b2Z02vUmVCoBg7hdvMwgi3TgMWk4Y/XBz2TMD0JXxsev93piEGMuMkFU/Xj\nU/JGF4ffQRVi/X+Z8Zm5NDQzo6+koH1EXHG8BmGJymU933YpBtQUrc3yzYJM\nn4lKLyjHdbKjC67Xg9xqIEYMFlBSjKOSJZ2E+9tkkTyPBWSzS/T9tIewuQFX\ndTm3yyAhY3M/nFY+QjH1OjGdlav4RhuY259U33fHceJzs7VjbmfqZI3xW0iV\nEbaZ\r\n=tanb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "43207b743df164e9e58bd483dd9167b9084da18b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"jest-mock": "^25.2.6", "@jest/types": "^25.2.6", "@jest/fake-timers": "^25.2.6"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/environment_25.2.6_1585823369454_0.6254278770690767", "host": "s3://npm-registry-packages"}}, "25.3.0": {"name": "@jest/environment", "version": "25.3.0", "license": "MIT", "_id": "@jest/environment@25.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "587f28ddb4b0dfe97404d3d4a4c9dbfa0245fb2e", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-25.3.0.tgz", "fileCount": 6, "integrity": "sha512-vgooqwJTHLLak4fE+TaCGeYP7Tz1Y3CKOsNxR1sE0V3nx3KRUHn3NUnt+wbcfd5yQWKZQKAfW6wqbuwQLrXo3g==", "signatures": [{"sig": "MEUCIEiOggCxkYwf6l6o9CshBpdsM/LLQcjrOASzA2yJdTCAAiEAkj1zHNgNGDMW4mRkeBj77aIklf7wHLpIcHaHmAbjMZ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26127, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJejc/hCRA9TVsSAnZWagAAa5wP/3GDXFGuhtUFJUpEm9+u\n2TZ4OZ371IOZ0zU+O8ob1s81aJs7wQHIVywWk42GQy/aAQWKfijIjxhPfIxn\nhmekLsRiyIAqcT2fkezoS9BBnDfiv/irPxBd3WYvbZW6LzpwlrfyVujhswwF\nBhigRqAjRSSEBiAgykMCJ/+ucIIjKSjPNOhtzeTZXuL+D5R1MT6cwOQAvXVl\nDLu0fQiEiAr2BDLLBIoKLkQ0obPZN1//DeLptum9xFAi8GmTdqjT4+KlSp0g\nkvE/C9PRMHSTHZHH7TC17+Nvde32lMaNsTO/U7TG9rMI0vS9HuUtRhGnwhhj\nlBRhtt6icPX03S1ZiJoxQBpOxus4ncqWYkOYnOIzoTIR8sK92BdeBq6uLyM1\nYPzEuFVzbrmpnCd5b/jpBqSraF3+13LhnVZVKAKth8YdorDfYEP3ICuFH0M0\niDrMZ7dvrWSbUPxBQxnvB4k92MTSvZh1VShK3pvZVtlNfuDcJbnESb7jfehv\n5EU7cDmjrix4+Orza/2kfx0jqqYZ9Ny/uhNW2XW/tCvMAsPcuMcLNmpa/tIH\nkPbCvPfuEgToqDeX3OV3pMZkUuQmzH9eUCiPIFvBj8//gqWpSen6jNTFfOyU\n8g7LB2kBl8eK4uqorGndlOGYKzZMqGl8QGUPY7IsHW04lerO30OKNla0iBl1\n6tmP\r\n=vLwI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "45a4936d96d74cdee6b91122a51a556e3ebe6dc8", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"jest-mock": "^25.3.0", "@jest/types": "^25.3.0", "@jest/fake-timers": "^25.3.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/environment_25.3.0_1586352097360_0.1306008076381433", "host": "s3://npm-registry-packages"}}, "25.4.0": {"name": "@jest/environment", "version": "25.4.0", "license": "MIT", "_id": "@jest/environment@25.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "45071f525f0d8c5a51ed2b04fd42b55a8f0c7cb3", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-25.4.0.tgz", "fileCount": 5, "integrity": "sha512-KDctiak4mu7b4J6BIoN/+LUL3pscBzoUCP+EtSPd2tK9fqyDY5OF+CmkBywkFWezS9tyH5ACOQNtpjtueEDH6Q==", "signatures": [{"sig": "MEQCIGWmHqod+ZR3ZzXv0ocjbQGr7Zx/ccqMHD3J5Ar3ZPq1AiA+2vdo6Ua466Ele+6sNHEgnkerGMM4zqe9okpS26vz4A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22344, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJenMetCRA9TVsSAnZWagAAH2gQAIPZeaTszemRhNPQi5Hn\nNiB2fkAksnQbSFZBE8cP0+5IFw1XKZgZsAjSzFVN/vXlumPCxCdtTzISjrxn\n8io/mroTotIrPj6MTdJAkYbLWdGXruOWfRqKfO/mh7Dt8FwGH1kP4mstslKD\nkG9OH8xwZ/AdQ0BmTUnkOdtCcRLbahjFB/+qXQpRwPoMUVEwmdgT1jWUmFXA\nHZz72IkpYgxIACjPYR0ZXTy4yL/xoq2LVG8OaSVALhZwrV4oaXyOBYXDQPCF\nUSQygaqs+bV4573fVlJW/iyvSnuTjehteQRluE2DI/9hGrj69vlE78y5XZUX\nmfcx9bcb9eJrBDn2dKk98/3+v4vIh+F4QNi17mUv0FGRbcNA39f8vaK48nsN\nYhAEVmShmVl3IynO7EMHPHsiFKMpLXF63Rp02tJw3MyYupeff1AhNNmYidoP\nioZrCW1HK58np9dcpwC7sL7AM2/Pw6YAsukZoP7dqFZM3k1TCksTwEECaSOM\nTng/RUnZijt0JHNA7xa6p7HjLGl2YJMdM1vWAcAY270t2gldB+5eO51z+G+0\n7t/B32Z+o8rPAqXtB1B1WvCUpBQWDqbYJQo0jBLRov5m4wP1Wu3xiO1qERIk\nclHHI1sW6XPy6G9BDGHdK1KJW6AkuhbTbJPtMJqAkym05QMsZsIGViuCKSw6\nDR6L\r\n=BNeW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "5b129d714cadb818be28afbe313cbeae8fbb1dde", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"jest-mock": "^25.4.0", "@jest/types": "^25.4.0", "@jest/fake-timers": "^25.4.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/environment_25.4.0_1587333037296_0.22674420178904575", "host": "s3://npm-registry-packages"}}, "25.5.0": {"name": "@jest/environment", "version": "25.5.0", "license": "MIT", "_id": "@jest/environment@25.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "aa33b0c21a716c65686638e7ef816c0e3a0c7b37", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-25.5.0.tgz", "fileCount": 5, "integrity": "sha512-U2VXPEqL07E/V7pSZMSQCvV5Ea4lqOlT+0ZFijl/i316cRMHvZ4qC+jBdryd+lmRetjQo0YIQr6cVPNxxK87mA==", "signatures": [{"sig": "MEUCIQCj1mSJE87wUnEMUJZcwYNnNm9aE28IsfcZ48FVhVu6BAIgQ+r6MP/+oJFoCOFnnVH1CfniuxBOF7O61VkwyrrBYT0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqIfeCRA9TVsSAnZWagAADC4QAKO5mY0e9fqTdzs7S/pR\nCVhLiqocHB+5VXqDZ1NsikhpkTtkVF3AANZ7p18QmAUATaIceBQiF+40mkvN\n1Zm8KYK98rHUCAnzss8bCMsNAxzbNwdRv/upF7T9cjdTbULFeNCMh3+MlFSj\nVCwfwNqsY8tW+Gedoc7X2YRnku37d6VUr4brpkyafl3T0EYWzD5gzTd+PtaY\nT5nbYm+cID1dc2ybXI9+INLYxa3oTsZzqfSzPgjyht6xxKvKf4M149MvB+5s\naE/qVIQDCsZ2qjyqtQSROtp+OJPBQkqQ17xZKWFlfEAXIs3KVhYyJSMqblMg\nT+GsiQcf/B/3Oj8k28n+AzOKTNDqgJTNzfqY0wZGKi/vbOXXUpqgsmrHMOcH\nNBiN/GRkj0mnCG3ex7aDDsCMaRop2vUQ+C4bdplSQdJ6nzgbUixbBR/T7rfG\nWRPTy3R7KTe54yCwOQVLQS1dPS5WtEfSnb/r/BGUQI+FrfFr27hQnAu6OmJy\n4MEKeb0OEoPhs7cwSKVkr9UvATq1w9FVMQyfyqN4OrZKIXklE+dsoA+Hs1hB\nAoypBQFoBWvs7/rd4it8uiEISTkMiVVDz0G6oHozQVy6X3rNpimBv0h+DEvG\n4yL3t5qVlV8yAwkrp+rNqBghMdbvba6P2ClK6uGirYF/q2gaFPZjvAC9sSb+\nNIlm\r\n=PvZp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "ddd73d18adfb982b9b0d94bad7d41c9f78567ca7", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"jest-mock": "^25.5.0", "@jest/types": "^25.5.0", "@jest/fake-timers": "^25.5.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/environment_25.5.0_1588103134156_0.49716510517004386", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.0": {"name": "@jest/environment", "version": "26.0.0-alpha.0", "license": "MIT", "_id": "@jest/environment@26.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "575f01b2c82475f8ff7de6c83b9676f4a7382094", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-26.0.0-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-ku6+aj6CE31Zccx6RWLnISI8Ks/wAEkrySars4JpFdFbfc29XtJW8N6aNXSXEtkIrDEBi3BzantplTILj6/oGQ==", "signatures": [{"sig": "MEUCIGAnZ1s3H7i4GR7lu/IBT1SJDHeDKE9uIGu001oV5N9IAiEAwxqoYOXrsxhH5INbCGAmuyqkkHPTx/H/p3JVlfZv6Bc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11788, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerWPdCRA9TVsSAnZWagAAKxYQAIpR4FqefVdvJtM8dEvC\n0bdhe81pOLexAUsl94FyBDo37kKZKvj028aJSy1KZXwUdFN51dfQRI0weJNa\n1qQF2vXGEs9ShWxgjIgEZLK0AD+hXuh5XvenkcRPx0Zqp8+SNhm1ARUTer1F\nakoHsZcf5U18Pb2IyTD976J+lVtKq7EzqE3rpGv29uI4FYKNaeJ/QG9smLV+\neKw0n3mw6UxhlcE0dzB+gRPadiwGUOfZLyZnWN9N95ZfBml+MwjZxtjhEeV2\nf2slLdhpuaiEcWHj3UFB3HsNj8chXjAQw8LNMbPZ1Qv0Tz3GkC08q7OfBRxD\nI1XpcWAmJbayf1hm3T8/cWM0lYZxyWEMVw04NkwxLdau4h2GUR67GOzDehr3\nS/K5Pv1ve5c307j/CZxbaL3TAgk3WFJX2PjXv9+JJIgjMbdlKBDh2XkkD1GF\nTHnvFBIIKSYMzGluqt+YE5FqY90G5gImYDTqlg+sg7sVuzdSAPxUMrf3sSl8\n2zQp+nAxjnoY12Gz0l2HZTmJyfHciWNkusyM4dDRIbgEzN/c0SmqkHoKi3OJ\nh6lRkrsulup507zh4jcp0u/BZBbIhFiw+l8O3CtOS9iqJAzhyRoTCgPHB2+z\ng+bcXsjeU5iTypkQrNZftzMUKlzOv3xEB5Y3rVYdCjEOWz6VLdSlESDU7TXR\nhgRX\r\n=MLIJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "ba962e7e9669a4a2f723c2536c97462c8ddfff2d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"jest-mock": "^26.0.0-alpha.0", "@jest/types": "^26.0.0-alpha.0", "@jest/fake-timers": "^26.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/environment_26.0.0-alpha.0_1588421597020_0.07021119918850971", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.1": {"name": "@jest/environment", "version": "26.0.0-alpha.1", "license": "MIT", "_id": "@jest/environment@26.0.0-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e6d806bfdeabb40c23c40ac87f735fccbb672714", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-26.0.0-alpha.1.tgz", "fileCount": 4, "integrity": "sha512-2TU55HwYehpjGf2gF10XnP+zay8g0DVnb8mxOGyY0baTCKMnNyUhGIephSrh0FbHLhZBjENuM02ghxhFv6YLqQ==", "signatures": [{"sig": "MEQCIFJRYOMoSwHiKw+AmjOTnrZMaePm+rrYpgXHylsZ1PznAiAUv6WUEyJN8PB3MmE5xRJOC2vmRD+F1bLHpPEnwsqQPw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13054, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerxHtCRA9TVsSAnZWagAAgJsQAIbNDOIALnFT1cv8UIwg\nz854DeXdQTDiv19GooAZR4+WpyrKibzOFJsaEd//KEf1lvMPzlLb4na9OG2g\ntzzGHkaxSj2CFbc9R6zKpkO7bVVAZX9s6lwdZEKJxWJieO6W5glhxoEpJaFi\nen1V2yjoYty82udWXo54s05yblgB9VH6biKhbwr24SZtCG7m9hSWjPM6akT4\nfa/DxPiUUAr36AqcCY4XgHaoiySDSvRIT/P2F2qOpILfHUBgk3u5N2iIb6d8\nirVGPFb4AMU+WuEM05dQXHl4JNespxHiAyt7GhOBqsZ1Ah1UsCr5CKNX20B3\nt+ERRVQkOmQG6JZEyK55sNiyIMM/dUBPvRY6T0H5+YPvjAcdFfu9UGN5RFNj\n0R8kcRf8vBlXYP3705oQALFjUTElJ3Dn+Ta4HzioGKU1HLkd6BJSCl+XvdUR\n2zmOGYuulbTjaXItEv86OmhRZdEXtBvoWJX4prYjOOGF1RYvwrMzI0E8QC2Y\nWbRkfI6kb1yzBwqK0y2TAsFUYPzqp6LLzuUrsdzdDP1ALjwxp46/ECeiqgiD\nAd6lrKPisPnaQ3GMYn20rDLLrydQJjMpKNaTda8uAPcK0Ui9s/RDjei9+5a3\nDUMyVJLrbVymhSbyiqTF8GohoIbRMZs87WtKQUTPYFO5Tfz0gGBFarauDE/b\nouKU\r\n=KWHF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "2bac04ffb8e533d12a072998da5c3751a41b796f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"jest-mock": "^26.0.0-alpha.1", "@jest/types": "^26.0.0-alpha.1", "@jest/fake-timers": "^26.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/environment_26.0.0-alpha.1_1588531693177_0.5315795690826892", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.2": {"name": "@jest/environment", "version": "26.0.0-alpha.2", "license": "MIT", "_id": "@jest/environment@26.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0ae8e77b4cc2d6e47c94be8e811b864fa42a19d3", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-26.0.0-alpha.2.tgz", "fileCount": 4, "integrity": "sha512-ZV6yZHO3gtXJbEV4wtrV5BjcuVAQnRUpn8We7Sz4bhijyyF9F6gC3XcuPnA5/k5y4J0R8Z0cRb9X1lK0MlP7PQ==", "signatures": [{"sig": "MEQCIHf06uY0bX+kpTU8MR/j78yrFA2Sq3op7riIAqNJ1YSUAiAUOtaltkt/ejgbpsqz1hHeE9KOfLnrQftQ9ImmeTqnRw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13054, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesD1WCRA9TVsSAnZWagAAbRwQAJFLroMsi7rInsEzEXKu\nNluJ7jiWv3XaCg6MFpZL1UYIbLoQ05g97nlztLzT0Lmx6P/CJqxKCB1q8+gE\nkxyHniG9sRpuSggcPbFplOIgsdmDge5VYQk/70gIzF5v3U4eJls1RPOcwcem\nf1qN2NN+3A19KLtthKnyHdx9aF2h5nv3hzV+9PPAF+/jIi1FCeN4U0PFpTJd\n8FWZVy8/11Xca24BXKs61Ax1HfSRw6uBdrde7TUAVW9SmfM01rku9H7meUeL\nRQbngHw1sHJBBoTBSXW9rrWk2FEtr9ltlnpPn8Meq8YD5hKPwi+Fsc6a9ZM2\nUTG9q5hyLNwgrYQDRWxxbniUvjKoHBHSDp032amXGUIxFk74lp8lBZWOpgue\ngLI1VH1DejlDdKLPbdjPXW1Kma9Br0nTHBwI0nrCrOz86wvjENZSZp50pwmx\ne9vHgLjTgOjjc4nMZpVY0YQzETwp4SeOJIHg8Ud3ltl7yBtZVLRsJNzG2E+M\n+QgUzTYffl9pUm0+fIJU619L9h31AAmNkQlH8ENfAP6T27Ngg8yc3QXCo9Jt\nwLo2ol5nhRm5iq2HwzsMd2y7x7iBthwRc8FrAtqppQakF58G1IVsxWmUJcid\n2JAWa0w4ae7sl0anDs8yOvshb2j/XCa1MmZsSEaRWCj654b50U+vlhoh0/CG\nCvdA\r\n=iKBm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "68b65afc97688bd5b0b433f8f585da57dcd1d418", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"jest-mock": "^26.0.0-alpha.2", "@jest/types": "^26.0.0-alpha.2", "@jest/fake-timers": "^26.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/environment_26.0.0-alpha.2_1588608341709_0.3746696827571061", "host": "s3://npm-registry-packages"}}, "26.0.0": {"name": "@jest/environment", "version": "26.0.0", "license": "MIT", "_id": "@jest/environment@26.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1960b88aecc350955546c72e3914ae35479bf69e", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-26.0.0.tgz", "fileCount": 4, "integrity": "sha512-CARZC7Kz2dK0TndW9Vmg8awCfV89BKZI1UfiUcHNfYPH5kZWZrHV5J7NridBiAlnAV5NRL1NE1+MDsJ4Tq6hyw==", "signatures": [{"sig": "MEYCIQCo+bqfHNT3ypVBiszNqgSWo2VInsrjNhi+BlQL6BsergIhAOtWnFmuF6uGp0BxAIIcONkEjjGR76ITnpcx+Fk69dsb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13022, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesFaQCRA9TVsSAnZWagAAkWcP/3UnLpKfr58JwPwQgHNI\nanZj35UCJ3epG+/goyPfhnfZ6pnACQlCBqWZLPBWZHTfwHa9fUxKH+F0SOuK\ny5atMVAHUWluspU1apxLQXPwIRos1d32qWtfrDKnyoATD6kKCRNyhy9OsYGn\ngMq9xaXx8IXU3T9SJT+HLGzEgFEHfmAUZN7xus0atq1l2ronQB+8wKuuoxhm\nPTWP6MnKtBV+EEMdEr5VXsZ5E11y/bJN8UrRT5PtOgThrvz+S58rKsx/j5+g\nzFB+WVe4+iwaVSEkAB5YATbPDma181cNyFKYbkQZjEt26i2PbZ3XBSIC1Y/z\nqKc8ocNOTuyzR1mZAClMLpVqkcIY4lUNTQSu54z7Pv+pPrhtksNN9jHtaLEG\nsdoIoDIo3+9ubzXPWKsctQUE8oBlUUemhOMo6PTlA+XpmnDD7h3W++bCNNA8\n6i4QZ8BD/3ha7fTv65GL5KCLeiHSy/JMtgX/bCz7WjmDlH1+lgVhYokBSou1\nbdIl73UUDW0c7zhke6yN0Wg+bNF7tcBpCUF6NmeBStcnMEEIKyUYwiHvu9HD\nnoArfzyY0IN14fcLqscKVLtl6+xeDF4whXk939FNtN1j9ecDOSeRj8//TZGf\naIIy4qt2bvZ0dQ+cJrKZd7kaXar1LY2LrCJb5d4uT2GhNQ9sTJ48Ddc7+Aew\nHjx8\r\n=bmXC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "343532a21f640ac2709c4076eef57e52279542e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"jest-mock": "^26.0.0", "@jest/types": "^26.0.0", "@jest/fake-timers": "^26.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/environment_26.0.0_1588614800122_0.991520223999752", "host": "s3://npm-registry-packages"}}, "26.0.1-alpha.0": {"name": "@jest/environment", "version": "26.0.1-alpha.0", "license": "MIT", "_id": "@jest/environment@26.0.1-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "76c8558e47c312ca0284e783e307f23d56d029d6", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-26.0.1-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-0X7tvqq3OrPHDtqRWEDQAUm7CHo+9g9w3mSfm45lgPEyJa2tLnWBOnya+PAkveXzEMX1EeLYyZr/Ziw0kEvraA==", "signatures": [{"sig": "MEYCIQD6ZkYVaofU3DavsKfyj1NBPq/sfqaWZ2Xxj3SPxX+fAAIhAIYdRUO5cp1VIIMAhzSfMwWoj8KKRjt18QP9k2R9wAQN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13054, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesJQoCRA9TVsSAnZWagAABQ8P+wYOtJ0OIMZpo8/bpsI4\nQ8Pu0EsHICIB9oZ7Tc2FZSsLAt4Z0y6VCXnuOFGUxErnNaajRAV+ZjN1IuOm\n0BN2fp1QcTkgpobW5/wARSa6roWFZ8UCyRahF8Z9pCmbhcVwdx8d0WIgIAPg\naiHJXnY0sjrzXg2z483Vtxq5wJvmNiVkM4zkGYrmiXZ91GCD0z8/V6eMr5Jo\nrS8FehOXFrpJE0y3VAGLwxbD8nNJH6gr9QwEmg7LbSazjooaMjdRpMB9t9z/\nM3qUvnTAPtn7JGPZIUHsl6dmN6d8TtiD32y6V0I1kgIyVdX+yQ9O+FeqWu5M\ni5e2niYJF5xiSwpgnMDK78dtVE9uFMp3jyUK6+WlgtywrlBIJqtOf18o9/dd\nHwA3ogjN4eJxzHg2KdTY8FyMr4b013Me7t7pU5v7lPxnQtyGpLJi82e9YFKb\nwoG+shvQeL9MKJkXlqORtrJLcBI/8L6R0cS2esSUB2P4jkvHz/pj8KZER0fE\n7vAUmX3GxvC+Wul1KdRrJAP72X5t4NxyweRcwxgN0NKlc6q1yppZyUktJBMB\nxQyAQUnp0Gkp5+kJhQppz4gNNZNcKu1wlWslDOSB2oTgwDZT/m/NGP93NRNO\na9McQw5tPEkVZKGD8UsHTOzYaWLIkqc0//64nIX9cNpt52/nHljg2/AgB62N\ncOBW\r\n=C4cs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "fb04716adb223ce2da1e6bb2b4ce7c011bad1807", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"jest-mock": "^26.0.1-alpha.0", "@jest/types": "^26.0.1-alpha.0", "@jest/fake-timers": "^26.0.1-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/environment_26.0.1-alpha.0_1588630567235_0.14445515446541668", "host": "s3://npm-registry-packages"}}, "26.0.1": {"name": "@jest/environment", "version": "26.0.1", "license": "MIT", "_id": "@jest/environment@26.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "82f519bba71959be9b483675ee89de8c8f72a5c8", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-26.0.1.tgz", "fileCount": 4, "integrity": "sha512-xBDxPe8/nx251u0VJ2dFAFz2H23Y98qdIaNwnMK6dFQr05jc+Ne/2np73lOAx+5mSBO/yuQldRrQOf6hP1h92g==", "signatures": [{"sig": "MEUCIQDkLTdIRC5z6L0tlFWGhBPI605nwsHiyhVT7vKsJF6KBwIgEtrBnLbV4ZUcDxzw6bgclWGP3/uzob18fn1xO4XWKLQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13022, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesUK9CRA9TVsSAnZWagAAw64P/364fn0yy1+RiEJbYYXx\nA5LLI16/t+XFOJheewqBIAkjUQwb2vliluZ5zK6bl4xNFSa88jK4FcAxA18h\nizxyFuLL/Xoqfv+a5y44MdxthoZk1b/FNQkbuVJOjWGFxeYmjkZ3OjWze9rJ\noSjm3NAivqbDAjiJQLrc+XdGUt6mScMZ6jolW4uvbFEGRiOw1uJt5rWeWOoj\nxWO4ETi643aJPsbf8KDT9s9dLyBLycacALJEiE0WVnpHbTmMHPaOyka91K2H\n7j8gr8bbMdCcicuEscjWh0Ckhr3eeejG5gxnTs8phiIWr9KXVe21VdcXCaGV\n886mjtxFEMSbxlzTiqrweMYNAbVMCzvSZK47mKMqbYWrhs+DkL8tHNw+XbgI\nL5j6S/D6xiQrHIsCys6bUKt7BQaJsZdkEC7zJi+UzyGhU9vTCt6q8w+GhTFU\nnDIgdvNgHCDhVsjRYa2vr3NxbOjf/qi67+cujRvs3F+p5OX7+k+pdNZi6hsl\nX7j+/dGrVk7dkE9slCK3/jWTaeusyAebj5PoltUDAl9/gDCQPBtVbyIOynas\ndFUXLgEWzh8mWMGr8sVFPXD4VK4nZ53lLaBfJXue09JfmYqPXI8NPYykGosp\nkR/xgpL68yyrMM9o7jRTUZjtEurZVJc8a/gUPElQfCbAUuW8TKp658b6zcDf\n4P+D\r\n=LOuc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "40b8e1e157c9981dda5a68d73fff647e80fc9f5c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"jest-mock": "^26.0.1", "@jest/types": "^26.0.1", "@jest/fake-timers": "^26.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/environment_26.0.1_1588675260994_0.26568514572553226", "host": "s3://npm-registry-packages"}}, "26.1.0": {"name": "@jest/environment", "version": "26.1.0", "license": "MIT", "_id": "@jest/environment@26.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "378853bcdd1c2443b4555ab908cfbabb851e96da", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-26.1.0.tgz", "fileCount": 4, "integrity": "sha512-86+DNcGongbX7ai/KE/S3/NcUVZfrwvFzOOWX/W+OOTvTds7j07LtC+MgGydH5c8Ri3uIrvdmVgd1xFD5zt/xA==", "signatures": [{"sig": "MEYCIQCk2bPqdSI1UH2uDOnDNKNBvLlzimeHw0kadjEAEuMOwwIhAMdQ8VhCZD1nbS9cCMxeb2kwxWCMZ7lxCy332cIj/cCC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13029, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8hyNCRA9TVsSAnZWagAAcR0P/2o/+33Y6nEnkJizwO1T\n8Q5UInaxjrbFjn7ObjE9x/LD1unmomIAUpjMQgcyeULFH9xBw+XiJMenZXch\nc4+r0ja6oyN7r4nzaB9LdB9nK1qX46dm0HSalOPf7BuGgjtvM1QUbchLEHTP\nkIXvRAF/HE/pKU70rzBVVSk8nlDkC82R7JOch/Qv5yg9XpvQ8vUVUfHEGuhn\n+edNo+Gi4Tsaar1nqxnNM9lR47VmtdUxy1IoPjWNEahchCtSwVbZCVKN7b/W\niZTIwuiq5Bf+LbNBpxgGyOAaynXoNFHsrt1RAEeeJ7droB9dFBkfGTrGXs0E\n2+z+F7wQ1dPclBilx0xzT3r7wkxUyxz3paQ7nL+rARSVheFGlvYBni8LygTP\n7HuF1ZFlPJhNm7Jrk4Vt6oPnuOT56lvdDiAUCP0sGc6Ie4F766s2Arus60u9\nttopcFThLLOg/bICIVJW01WxQIixFZWBgq/tKE7DeWUiapDprFISsGE8cLS7\npbJIKtVEHSEBKJNbby+u5/yo4WUh9rJGezPhpbmQ+QOIYCsIynBNNY+VqGnO\n6as+RhQQ966NDItbIANIO2qnT247QJUvlHfpos0HYfoGd1Nf03pE8kiGqf7q\nanTlrFfmiMCD8dN5nZZLj0IbEGDUxldSyCVGE9B3vnMwOOv9NAsr0PpV+/FI\nh82u\r\n=KLQX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "817d8b6aca845dd4fcfd7f8316293e69f3a116c5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.20.2/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"jest-mock": "^26.1.0", "@jest/types": "^26.1.0", "@jest/fake-timers": "^26.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/environment_26.1.0_1592925325310_0.24298745922634857", "host": "s3://npm-registry-packages"}}, "26.2.0": {"name": "@jest/environment", "version": "26.2.0", "license": "MIT", "_id": "@jest/environment@26.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f6faee1630fcc2fad208953164bccb31dbe0e45f", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-26.2.0.tgz", "fileCount": 4, "integrity": "sha512-oCgp9NmEiJ5rbq9VI/v/yYLDpladAAVvFxZgNsnJxOETuzPZ0ZcKKHYjKYwCtPOP1WCrM5nmyuOhMStXFGHn+g==", "signatures": [{"sig": "MEUCIQD64vuox50UZAG6WILFT1v+3wUeGqhmlXBYcBwTPI2IKgIgSpe1BeFELi5c9msT6DwGj1UpXQPwl9FE7cWjaeb+qBs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13002, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIpzsCRA9TVsSAnZWagAAfFIP/1oIf0XxWV1PmEa8HPEJ\npUe70bxY4FBFeRqxSuL6lXAsshVHMlABHGgPSEMWvT6xL5YNyV2ZFN9JcdQB\no8WPHeXSSyu0tt0PrVWYXwlbCZ44jGTXc9dBjWvkvJWZDVck2nyH7aYAv/n4\nYqrh57b+8ohyJ1fNTd+62LoqZPL6WpDVT5/xWw4jhhIGO/Hvz/J1uae/fpBS\n8pn7JPX0LPLX8fkL5OAwn3qbT6ep4ChKuWuh0huQlA3Fv7cvtp2n0u6cjkkn\nwY0igyNBDQK/VJmyhiDDnq5iSeLo07ErBMlUcEeiBbssTvbgLFxCO+p3DDym\nBN5Fkkxi/UD2+VmpjgTpS8BZRHtHF4VvXnR6Xa7z8iY7UmxjVgS/V76wjk8K\nXpCZq8XLSumKUEdS0h3Gma1KTNdFY7Zc3INZAVgj3lig6JLQlcZ/j8YMzdjw\nf/M2xe0Az+EzauCHmthisbAYR4CXQWUiSDTyWwmRLLbvurHLcAQVYHgwNQP3\nVjv4REfMitH6tcpj9fwgLurbI9xVXo92Ylv2uPjdHG/ih3+Xx07EDHsPpEsV\ncrc/GRpXOa0vD/tc2N3EL+gEOkNs/iE3SDfSsXQLQnnBpR2acaMHx3fen1l9\ny+FI2nIaRazpr4psoh5A37Vbe76atQ/F4qrGR1wcaAEBB5xSQ21ttghM1+Q6\nEY5U\r\n=IKwy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "4a716811a309dae135b780a87dc1647b285800eb", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.20.2/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"jest-mock": "^26.2.0", "@jest/types": "^26.2.0", "@types/node": "*", "@jest/fake-timers": "^26.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_26.2.0_1596103916086_0.7440188043546831", "host": "s3://npm-registry-packages"}}, "26.3.0": {"name": "@jest/environment", "version": "26.3.0", "license": "MIT", "_id": "@jest/environment@26.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e6953ab711ae3e44754a025f838bde1a7fd236a0", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-26.3.0.tgz", "fileCount": 4, "integrity": "sha512-EW+MFEo0DGHahf83RAaiqQx688qpXgl99wdb8Fy67ybyzHwR1a58LHcO376xQJHfmoXTu89M09dH3J509cx2AA==", "signatures": [{"sig": "MEUCIGEmqWeWNMQZcIA/aM5jCC398BD3Hx8llSn7IbJYNIl9AiEA2hUJP5f8yBNvwrxhj5RvPgsgVCDDYYHhx8o1rJ9F8S4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13002, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfMTA0CRA9TVsSAnZWagAAFgsP/3Ch5Fn0YXcLhKYfvmha\nZ6i/yszIxyleRcFGm2JEu7wIB3Ug4AU8nLOWdkUA9d+t/3qVBYxF0/re6uyp\nIhd/oJ0xgIsXR/ztXCFOojMi65rZ6c5w2b6bHJBEi6AjOrAEwnvJ360qJNVL\noUmS7pXNyMKzKCDgZECYcP4nL7a4PBVCVm81x2qOhm7RzQtc/fhQGvGRWAK8\ncnQQNKM/3etqbFPbk9MINUuJaUb85P37ZXZDSN8Nhxr3pdQKsec/SWpEz7fm\nXoLNbrIoBvRILhyFaAmIKEcYL2C1PuiAsRrl7f7h052I4VYPJ1pzXa6o3Vcb\nYedFxJAnCwW1MH7Rf5SLEqj8XyfIqV/e1ko4koKeoz55Dmg0u3cxB36CHZ0l\nTsTDUPppghmnaHpu1vaN+R1oB1A3BeUaLIFvuL/Br4Cu3kbM6oTDWYyDThwR\nmsz4YTbkTcVVMs76ejSn1tO3cYAW6DgWt5dRgUeVbNh1p3p0krIQ7tVev+HL\nByV/XQV81DzI2TMg52Xb3aFFwxzGppHtRlozPGqXe021L8vcyqP3QDGEXf5I\nQRDHX53ZSVnEevFp14IHobmAbvMXgChgoEqfU/tV6ai//Myt8cspES/dUzev\nYTqziy7ZIAQ1ngYFOz/geEz/s4JwCGzKVSijCqzpQLKgFm9TuL8VVEEDtZtx\n18EH\r\n=J4Ol\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "3a7e06fe855515a848241bb06a6f6e117847443d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"jest-mock": "^26.3.0", "@jest/types": "^26.3.0", "@types/node": "*", "@jest/fake-timers": "^26.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_26.3.0_1597059123737_0.7428216994363388", "host": "s3://npm-registry-packages"}}, "26.5.0": {"name": "@jest/environment", "version": "26.5.0", "license": "MIT", "_id": "@jest/environment@26.5.0", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4381b6b2fc291dcff51e248780196bc035da7190", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-26.5.0.tgz", "fileCount": 4, "integrity": "sha512-0F3G9EyZU2NAP0/c/5EqVx4DmldQtRxj0gMl3p3ciSCdyMiCyDmpdE7O0mKTSiFDyl1kU4TfgEVf0r0vMkmYcw==", "signatures": [{"sig": "MEYCIQCOToy0cNRy+cIWMa54fZrZ74fvj15U5FOy7jVyaNkOAAIhANvcAsR2pov8QVMXakvbqlXTv/M1w1hzUFXHph+J04Pq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13031, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfeuc8CRA9TVsSAnZWagAAkYUP/2WZDv1cnE5GnBBGSNWy\n6MjEwZcY2nNRBchLmJZ+AljLetygx3OMTpIhJh+e40uWJuEdxSUu1KeophiB\nQBCBXvwkPh8mHsg9ZmekZUO/BWUPukSBkpV6F3wAGhygPSRd/7TEJpswWW65\n0WZbiLD04pSdpD89+LvQUbV5ZpxfhahmmNNQtbrh5D0rRyHsPnkvxeEPb+P5\nTd9VGLaY2+PnxQXy5yuve3WRCk/EblLLEixKn2Beoqq0nG9m8uBQt3LEzpLL\nHr8b2tuux0T+yRud+KUJheyay+lvlyWOS5AmSX56F0l6EkxP03C5Nqkf/8xd\nPg8DqwMKtvsZEyqrfdw+yO9kUzJPTzXymMXgl2vUut3KZwEn0nB8wOdhErhX\n3GNd5zNCZVe65JDzziAOdksujwQasfskQZc0plOY4EW3g4/hIkdBzGQj+zDe\nHBcYa7nduV9+K6EigGGQGKvUoiOL0vbHnZBxZylvlWdm3WR31CJPeISQaZ4Y\n5XsfsT784X4NK3P640gnkcQji7yTgq4iPSg9l6EevpHzYDQ4hXL6450fR/o9\nx3IuevyPfeFyTYUkwB6vnY6CdGqvQqe2AURNfKxUPEOWbq3bfW7H3X30sq6u\nnxwiV7+zX+palrh6x9fo25m3fegOZSfhjW49GDIVEbe5WABtpEa2rZhPMwDN\nRo1K\r\n=kCgN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "68d1b1b638bc7464c2794a957c1b894de7da2ee3", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"jest-mock": "^26.5.0", "@jest/types": "^26.5.0", "@types/node": "*", "@jest/fake-timers": "^26.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_26.5.0_1601890108205_0.9797606133521635", "host": "s3://npm-registry-packages"}}, "26.5.2": {"name": "@jest/environment", "version": "26.5.2", "license": "MIT", "_id": "@jest/environment@26.5.2", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "eba3cfc698f6e03739628f699c28e8a07f5e65fe", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-26.5.2.tgz", "fileCount": 4, "integrity": "sha512-YjhCD/Zhkz0/1vdlS/QN6QmuUdDkpgBdK4SdiVg4Y19e29g4VQYN5Xg8+YuHjdoWGY7wJHMxc79uDTeTOy9Ngw==", "signatures": [{"sig": "MEUCIQDZJR0E2ZetcSVu4EKkVxCQkiZ/UYfLSKMt2lcWAe+cagIgfRIC2IOZQwoC/VCLGU1iXOxY6sijh9ajlsAQdYfJs/Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13031, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJffEyJCRA9TVsSAnZWagAA+4wP+gP5/lqjEjxzT878NKTC\nO1NK3F49NCPURJ5uKN6apxPhvx5Hti1STu459ZEP7BOkyfplvhJ58D8QTXWN\noGQqDFgqfHUcyO5xaCovY+5bsnegBBOJqdI6n/i8RUMNHnljgdkuw3geuLYo\n9ZoN2W2J7qG7Eo1Ht0sfNW7nB7KxhnBNEJfJewZ86tq10pkh2xwFGkp6Neei\nz2AVhb4MW5T/4PkQbHfJBqk+l/mcQhZ/6vxuyZrvCTvt4WkN1VkwK+z5o/dQ\ntB6e/6zQLeFp7vwjH2U1ng1RHynIa6yErPMc3cy3traJG9tq7+kpsY5J5+l/\nWh2fHMpK5Kd41rMV1+PmyDuQLIQ3a0RQcuPQtzz0p+k2Y1NtrIIWonh8ai06\neAB3uPXNente6vNzmy+ek1kA8BDMz+MxLAn+sF+bR1D4TQzDY31WgxhTac6C\n9N/WyEHEOcnWaGBEhmEXs7CZPQPS9fpuwjnvH+VOKO5dDvg8OC2Puud029KJ\nokJ3sNFYz15sM7Bl61J289wQHT2b+rF0E3U3+HkOzPXYJdR01XGx8Ax0vbnR\neCsr1Xf+RBr9kXfFPlmxc77xR/Gkysg48XkN+f3Buk94uvef/AaU395IZxR5\n0UYALXUHrEU0NbxD0hFRFkWeb/wpkhuNDdV9EX047T6TP1co+A/t+I+m+aln\nxZlv\r\n=bIVR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "d2bacceb51e7f05c9cb6d764d5cd886a2fd71267", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"jest-mock": "^26.5.2", "@jest/types": "^26.5.2", "@types/node": "*", "@jest/fake-timers": "^26.5.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_26.5.2_1601981577563_0.5463362084973868", "host": "s3://npm-registry-packages"}}, "26.6.0": {"name": "@jest/environment", "version": "26.6.0", "license": "MIT", "_id": "@jest/environment@26.6.0", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "695ee24cbf110456272caa9debbbf7e01afb2f78", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-26.6.0.tgz", "fileCount": 4, "integrity": "sha512-l+5MSdiC4rUUrz8xPdj0TwHBwuoqMcAbFnsYDTn5FkenJl8b+lvC5NdJl1tVICGHWnx0fnjdd1luRZ7u3U4xyg==", "signatures": [{"sig": "MEUCIHOxRepYngCf1LuOxagc0mfOOJYiCR+ISxI9dyH4GABgAiEA6KBT3suX2wFJBe7IpWXY+bKXrqBVL1ldwbbz6+N2geM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13031, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfjX97CRA9TVsSAnZWagAAfQIP/RZRBz5hriuJuM8fdUcC\nKrf8+GWt+4zYCt/KcpmJO9NIJhCffUsQ/r7EdDexhUpdtkYBRXIyPdxMh0zf\nNfjEHTDdTMUHTVz4Fai03cDq95c8EnUgYWU5ap/pYitXP9VI4gEUkl09ePeS\np6RCYu8Y+OnVg3VJNr5n+3BFfPIbPWhgPROnYmMmN7HTFDszDVs6Bl/cQMyS\nVpQ5H3YGY3Tf4p9ulexwR1Xg+jWfZvp8D38RhQlo4yLCuZkBsCVPDfPwHFOD\nGC7kMoPupIQwMBDhu+NVjLu8SKYp8SlOEtXjea1muBt2f3VxFJ9f9Rc6z20Y\nULH3DHUTiDsD2F6RuU0GUVJGMobjpVVEqDz7xAIDfDzYfWsJRANLqU0Y6oO7\njj3kts1FlE6/9hgLUlhYBsSMUb5mZRutRvZjX8BjxXlLTfmsXKYvupzaBt7g\nAfpOVN8WrLgUdAUwPZ64et6mHlvYICEjTVFR0mfwUtwjw7WM3JP3ncWgxW7P\nRuKuXOgvJdn/YAdvXGgSRXmsN7XCWjlBVXfGA5JEG+kLSFoe9zCd960BZlhm\nv1ckIhfyeKYGDbapwrq6RGOrpoM8vO0cwxYH4QVnQpW9xS1JILo+tbd6P8uj\nGUz4BtDmVi2nn1/h8qksMSU9Ktiw+IEWdNPSECiJfW8hYP8i3YQnLqR+mcvq\n+Hnm\r\n=wgnj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "b254fd82fdedcba200e1c7eddeaab83a09bdaaef", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.22.1/node@v12.19.0+x64 (darwin)", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {"jest-mock": "^26.6.0", "@jest/types": "^26.6.0", "@types/node": "*", "@jest/fake-timers": "^26.6.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_26.6.0_1603108730959_0.3750355428448289", "host": "s3://npm-registry-packages"}}, "26.6.1": {"name": "@jest/environment", "version": "26.6.1", "license": "MIT", "_id": "@jest/environment@26.6.1", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "38a56f1cc66f96bf53befcc5ebeaf1c2dce90e9a", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-26.6.1.tgz", "fileCount": 4, "integrity": "sha512-GNvHwkOFJtNgSwdzH9flUPzF9AYAZhUg124CBoQcwcZCM9s5TLz8Y3fMtiaWt4ffbigoetjGk5PU2Dd8nLrSEw==", "signatures": [{"sig": "MEQCIGUvuyzL1iiSX+1KNLRmKDuQc4Zd/MY3RSjqYe1cn3DwAiA6lKJMJgwmvMIaOBmzaUt33Rm+G9vpwhObMekRMHuFpA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13031, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfkp0pCRA9TVsSAnZWagAAfmcP/1NEkax5SpIfBBrUSiYN\nIqvuD4eBOskzBzP1j+kRPmzsc4hVvdIX/zfFzsrr4+FsQ9kQvauor28LtDz+\nN9jnl6o3iJ1Ee7/4mLeavsuB0YIFPGnI5bCP5eAM0tNcBXx/ctvV/rwGC0qd\naBXC2d7Vf5xFEtgih6J8F7bnyTXaLBXsWAqYrFKONqvFu3hTnVltkwP9MO7E\n8z5by0FU8uZnDPggS6X1k6dLV0h0Iwd47SWyMrlHSsQ8RRrE1E+LTvIpCeKD\nhiesFw8ugNKtUpsDqKGp1fbrC0hnYVAVPpH8J6fW1nFT9izXYKsZrEFpGfMY\n5SrYH2xn4EAbZwK4WQhAZ73Ej6aQmk/dMNvyrQW/2MUSzyv7e2HEcVvjWqmg\nJRfoBvj5gKk3FjtqUzOW6o+6HuWtMzqQtWMFzEw12SFWRI2mm5QJ/3CvTKQm\n4iuXqq1bWiX2IgJM2Tyc39HUORm4zSss4di8eAzpaA4VuJ6NkUBxVZZs1Eog\nXZc9n38mhr0rZ6ymYF4Uw4EYyVVVzWCQlEmm3pX1E0HEnd061Hu3/8+Y62fz\nUAMlbuid3RoZLZTRIoYknSFEvdH6JVQVKA3m/9Ie2HDMck89+DXTx/hz1jsF\nE/ubyEdcCXQWXTPCZ3ATO1/ayu2Nte7DdlY99Vh/8MCZm0pFFLFJ99GMTaZz\nRVbL\r\n=L3S9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "f6366db60e32f1763e612288bf3984bcfa7a0a15", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.22.1/node@v12.19.0+x64 (darwin)", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {"jest-mock": "^26.6.1", "@jest/types": "^26.6.1", "@types/node": "*", "@jest/fake-timers": "^26.6.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_26.6.1_1603444009208_0.8976401175006139", "host": "s3://npm-registry-packages"}}, "26.6.2": {"name": "@jest/environment", "version": "26.6.2", "license": "MIT", "_id": "@jest/environment@26.6.2", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ba364cc72e221e79cc8f0a99555bf5d7577cf92c", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-26.6.2.tgz", "fileCount": 4, "integrity": "sha512-nFy+fHl28zUrRsCeMB61VDThV1pVTtlEokBRgqPrcT1JNq4yRNIyTHfyht6PqtUvY9IsuLGTrbG8kPXjSZIZwA==", "signatures": [{"sig": "MEQCIBDcTnCjGP4wbm/OcyBaLan+r4K8JiaRGbIoX02YpmBOAiAuxTOkeuXAAHh2lEo/qE0mwrfSOjcy0SEX737bRL6v7Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13031, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfoADkCRA9TVsSAnZWagAA/d4QAIDDMl0RVbUCfekB2WTh\n7F+k0W2klVFYPe0rYEIl7XXY7htGgwOrd2ERvP2OouR44F/6cNOCV0pe+QrV\nztANVwsWpRm7x/HOHOKxEwTD4dkzXQ5kHAol4TqNULsKw68fCDSOWn+Yq/Mx\npPRFnFueSx4+pwAMJQ6mRfOv6NrOTqtfELQjzqhAZof6NMsUA3reyLicjyb5\nabwTU130u8rfYlN+Z+E61EqeMBSV5lMc974uvNlTcK19E5hDv+QcEvZvm9mA\ney97tCj8K6aCPzzXD6XddIPOS+9SeggJIiMzWK8yOmJO5yGLXVzA9PSiDsyH\nUf+szEZbxPE8LTKhr7n+gZLE7XeOgTCD3zW/Eo4rJl+ezy2sPNaW3bdizcdF\nKB+KJ53KER4ZdkRLBVMI8y72BbL9YNpWi8OER527MoSATKrUq1sOrTwUxZEF\nHJt+aKykz3mw1sYah2xTofTFxBuvNi7CcEglYDuD8IxlaZ3RWUs66oKWrF7V\n5gyAsvgZSSaoYISA15xVT7FvefDQVdDE+r7POkVZcYpC1FOPGnxBIh92Hb1G\n5E1xfIFNOOboWMnIvRQE6mMZcZEUOf7lVtIbo8c6/uTUSVPeLMy+853Qp4gz\ntwKfUEVYx6JmlDtX9h81JeKtbIsoP25uc7XTMvR4XnOSaLaijsCNnM5vOGAQ\nAluz\r\n=eDj1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "4c46930615602cbf983fb7e8e82884c282a624d5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"jest-mock": "^26.6.2", "@jest/types": "^26.6.2", "@types/node": "*", "@jest/fake-timers": "^26.6.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_26.6.2_1604321508466_0.8239547794888866", "host": "s3://npm-registry-packages"}}, "27.0.0-next.0": {"name": "@jest/environment", "version": "27.0.0-next.0", "license": "MIT", "_id": "@jest/environment@27.0.0-next.0", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5dd5f5b936bcf631dd5ddc9ad6ce0c6b9d9db897", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-27.0.0-next.0.tgz", "fileCount": 4, "integrity": "sha512-rZPP8nLcxrBxD4xYI+LgzNBAqFUCE0BJUSQ3QTaW6jpRPWXZVNVE52nX8aAhh+2xCQBmR/UckyErQvgEWhYY5Q==", "signatures": [{"sig": "MEYCIQDPKzgFS17qa0hkJoBBwGLUpvE/t1IHHx39rse5V1tETQIhAPqNXyKkcWxblTfOumoy+NdZEeTnxrNInrglSQ3jJkNU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12148, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfy8KQCRA9TVsSAnZWagAAvHQP/jfIbk5HL3Y9BojB3wbu\ndN3VUUjx9oOEzW1WNTRvndF7hc7li0VJqisP+G4JEvahIQ+mycFZt+LQL4ZL\nNvXERtNIsyA0n7tfp7Nubq85EBbt3K0OnTFOzWTYd6KIShr4sNouQM0djAIN\n3+/X80UsLSnv+zyXgsV36FflJGyZKNztFeRPyJu9srT5UnRqz86O2zeLIAVL\nKZcq7f0YwYxb53Jf3dsEuva7u9y/nXhDfMis2jE8CodPSing4AF1CnEf20jO\nvwzGTi4FpiHjqu0tG82ABSMlrpQik1iGwFRYapC3YnoLHWXD/mfFB6UqfQjH\nBCSG/1xSX2wA1V4QLYvrzHcTUEYVL0zRYlVv2mum6fWQpa56Qy+0mf3lT8Xv\nx+QMuUGqiaIVPuQ8WnyV+PM/H40fSb8oej8Sw2NKz+8l3hW92xODGfPmBqlL\ngdImUsdhE+4kofxET+bS0/5wKg/d/4AT3YYI5SVWjIYUlLM0XqRYcl9GqNor\n9G8i7BOQVPh7EQdCk/C8R6/WxO+iNdvlvohpUTXXcV0oTikjiqAaSPyWopMO\nvuppqU48LjlTBtmmKAljyzCYegsg3hwzxjODowfSZuWuNQTlYDAkQ65mnRn+\n7CUweRiFow3fD8Yt+MAH587gD9/JIrwrPOE/J/G7JGA1jKozJPbEsnRfT1/L\nzkdt\r\n=sUxD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "4f77c70602cab8419794f10fa39510f13baafef8", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"jest-mock": "^27.0.0-next.0", "@jest/types": "^27.0.0-next.0", "@types/node": "*", "@jest/fake-timers": "^27.0.0-next.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_27.0.0-next.0_1607189136243_0.43192433975772104", "host": "s3://npm-registry-packages"}}, "27.0.0-next.1": {"name": "@jest/environment", "version": "27.0.0-next.1", "license": "MIT", "_id": "@jest/environment@27.0.0-next.1", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5347c2a825b47ed9c877454528ed5c3f5888e60e", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-27.0.0-next.1.tgz", "fileCount": 4, "integrity": "sha512-H3rg05KS9TsLd9KhYU9azyQfLNqTc83To/1C51SWKSdvmAjY7s5rftjb71dktM+D9vaXEHy+l3/gpXamg35QEA==", "signatures": [{"sig": "MEUCIFwAjlSPIGlOjl/NTWP3IRIiBqE7IzBrEwb430+EOtNbAiEA+T7HJbreq3O/eFnxaYDluBdAy7Q5mgZJ8cyxVlKiCmw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12148, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfziN6CRA9TVsSAnZWagAAvRgP/R3U/K0EwMuPq2PpVHdt\nnnZs6W8Cf2gnJeA8e3kY1uwdBBlE0wQdtSeORfCqcjTJVqLjcyzK2YRoJNIr\nG0QRuFPNUnZa3NxHCg5GUN34UziAME6xBqEsJiRIRGgSZ7nyE1nphqxMaL6h\nJEoM5uN5NhMJ6XbeJxahMh+1U1pQEE6ig9U3Telcmbg+T9ycS2KXea5DBSxl\nRAW8i/vSgcOvvVOv/iYUooEX5uIBttKS/Y/JaRw75iz1HiGtfJFryibbilVQ\nDiPpwUzox9DdIflkxpMx02CcHei1gVfKYCDztesbCthmR5wn2i+kZmbMIJCh\nAdgFZf5bQCXFV81vagMTaIE0Z5vzL9DjHu2as7wvy5WqJxhIkRqEbbZL8Smr\nLA2l6rPGNTNutLyEY0diIKejvzJKWbVSGN6wU/4odZ36n8uveph74XF283up\ngnyhrlqQvP0YGyzzlbUHHrqHXu445+gLvUeHgNx2JPBhHX4t/5pywQdfVV7/\nhcCglp+bxwFn8Gl8b58DKNVWeANrh6smbw69j76Ic61E9+ldDIyBLdVhrvZd\np5G4bkoyV7v1Aw0x4/n0o6uOgZXscRr8KoXZA0Vpv+vh83rJV5OcEqeV/ZMm\nZPjUAWS+fopXDiE2qFmQmrRzbHSe5gGnEyrKO4igbamZ8h0fLOGxglPdeO85\n3pkp\r\n=CcPO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "774c1898bbb078c20fa53906d535335babc6585d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"jest-mock": "^27.0.0-next.1", "@jest/types": "^27.0.0-next.1", "@types/node": "*", "@jest/fake-timers": "^27.0.0-next.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_27.0.0-next.1_1607345017996_0.3475381455599702", "host": "s3://npm-registry-packages"}}, "27.0.0-next.3": {"name": "@jest/environment", "version": "27.0.0-next.3", "license": "MIT", "_id": "@jest/environment@27.0.0-next.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b6a236bdf5920b009fcef3e8171395a528603f11", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-27.0.0-next.3.tgz", "fileCount": 4, "integrity": "sha512-8wgGEj4rJFE8J0ythFsQoLCrroiwnISey2T44yMuXpEYgtTbdM2tnKakbT+7vWOkqWYsp7hn/dVrZkFvYkB88g==", "signatures": [{"sig": "MEUCIQCLDCIgzpEqEOhfyv/pxOPoeLuYmCBW5wAufpobi9NcXAIgHUBXV65GX4eGw0hl03ELetTPTCY0NtspW243HpzDYNg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12148, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLuXECRA9TVsSAnZWagAAK7IQAJhjQg11Frl7tK8y0t9b\n/SlZUrqTLWkBQm3tLjcsmEkte7X5sE7NFSf3PXyAZ1t/7JmEi3vHEVUI/3Tp\nrOGeLOpbbp2U65lxbjMbryK4d1CQJBZCIfO8+ioZzFV0tr89TlmR1mR53Fcx\n6nF59nOXYp7wcL5HyiUuKttzw0KW7ajaNqixrdSSjgePHt/Zh0FLamfR3ZqS\n1nX9yl/o2hn5XBhRb/nbQqXcm7lCIPMU8Jp41VN7U3x1XM+x1lJ6RqMA06fd\nD2v+a51xFM5mhhwdCufyGraa08ry6mhHSz39DQbdFpbuX9LVWQmzZ/4hyxjm\niwX2PuIiR/v1S4ufi5B1cMtqNELKe4ODiFiaasR9oil1OOzSub67ZskopSOu\nrGmn1M+kOc22ioywpxPjb0beFMWtr4+pPBiwLwiXa4rilF80bCeXRdmF4S4Z\nglvqmR1cU3C+EOYbAWLhsO95ryOugXrzU1AMMzcK8cwf4EKRAeOoyvFF4pSl\nlPt4L6D4/f1mnG8ladL4hx44GILzjox2wJGlYlzNgGq2FXkiRGlZFksoX0ee\nLsE9c/0D/GGJ1sXbOO2SHSdRfbMAt9p6FwOlo4NThqkdpsXKh7Ykl//JYMq9\nnqlVDHdLZmXAmmzcW/FhPEh27+1iXV33bpPGVmmAPcOV5pDekw7HNtBkTkKB\n1cZR\r\n=Sd1V\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "2e34f2cfaf9b6864c3ad4bdca05d3097d3108a41", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.22.1/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"jest-mock": "^27.0.0-next.3", "@jest/types": "^27.0.0-next.3", "@types/node": "*", "@jest/fake-timers": "^27.0.0-next.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_27.0.0-next.3_1613686212500_0.7832543079347938", "host": "s3://npm-registry-packages"}}, "27.0.0-next.4": {"name": "@jest/environment", "version": "27.0.0-next.4", "license": "MIT", "_id": "@jest/environment@27.0.0-next.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2a7a6002b2d418fd0c9acb5f4ef0f341cfbbfd21", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-27.0.0-next.4.tgz", "fileCount": 4, "integrity": "sha512-xpO2/+OsZF+P8iGSyJu0lk7GrI5F0v/pkiFF/a5rSdyAzF3jCRTFzsxITq+3b1fBR4BamfTe/92k9UEZN1M9tw==", "signatures": [{"sig": "MEUCICQR4fI5IkG/ya1WOsmIDG5z03BK65i0z5BPqGZTl5s6AiEAvdwg1psVETYGlGlhb2MC/wT8rhXYD99MDsHqvjiYuT0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11967, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgRipjCRA9TVsSAnZWagAAJW8P/AixrWHXUIr0ZbBYPGtV\nD4ZyB8Ug6oM/HvdD6I9JZ8BNMmajFefTSNErUKERhqo5sdV4d91OWyMoxzQh\n6YnpBHAPVK+0Lm9lGm8lv4of8B/PCo38/NsRUHAkl/imTxZE5EyAGSiMSQwo\n9z2dQKflgfy8BRlyOQ7SIAsCFx2orDrkcnHeKoTVizTVdwwudayV5V9RFvwc\nJUCKIMm6Y4yhuVnQG5YoTlXsLlwBUZZPFXwK/upGe60dPlfSiU8YAditQPMD\nmfdx/7vcFwESOp9dJzMZwMQEnWKiahiFxwkR5maFPqs8ZqL2XwkB8cNp6BkR\nHQSqmPTEi9EwVoWUy9hYowswAoJy5Va1+3XoL1x363H8ddRdixl01gC6fbAs\nBLxLtoX2llqMrfvx9oLaqNCGHr4D34yswy2gzzSLu2xP1f3njUSK0YUIWO18\nyytqMncNl0KDVmrywrImONfy0WLk+fVIwyQMx+yr1r7GBIRzM7Hyp8UrPzsd\n2M0d5kfo1ZRRO4xvq8sto3Xfgzan5kFDU3Tf9UfPBPLD6446IFNM9x2jTdkF\njuyiiSLTAzEwwxbzwlXA435z/NwyVFrZJHxIxmKsMDRfmBenY712V/f7/sch\nl686UVANidNSpb/z9QHz0OrUl97elK0dbImLfP1JGyJGauGX6npoSAY5yiZf\nZW9g\r\n=3ZSi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "941c414f0b757fdc619778c46c21fda26b3e5504", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"jest-mock": "^27.0.0-next.3", "@jest/types": "^27.0.0-next.3", "@types/node": "*", "@jest/fake-timers": "^27.0.0-next.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_27.0.0-next.4_1615211107262_0.27285757582192627", "host": "s3://npm-registry-packages"}}, "27.0.0-next.5": {"name": "@jest/environment", "version": "27.0.0-next.5", "license": "MIT", "_id": "@jest/environment@27.0.0-next.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3e8e2edec93ace0e9c98e95da1d7e9f3e8158a37", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-27.0.0-next.5.tgz", "fileCount": 4, "integrity": "sha512-hTig1NXT4yOJOpw7sTVGDo2x/DbyPDsnyJfOMy4zHIqNtPk6fB714HXWW59Ku/f5d8RmNDaVQW0EKMDRfZ62+Q==", "signatures": [{"sig": "MEQCIQDbrvXYHaTVYeJNMxmslAKQM2TsH4Iof3jxsh+aiy2QrwIfQfh7R+9qrN7Eb05CuluXnIUXul/jGqZQPgotqEMJhg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11967, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgT1snCRA9TVsSAnZWagAAqGkQAIhx9l5Wn1OfLkNXp7jJ\nPLRUrgZA+wQ5+cgsxfJMJIaWbblHqZfmSpGBgaO0TYBGj+R7kl8yQrq0yVyR\n/P4w4JjvZRNWkkbzS/Rh5yXxuiHSR+evQ5Qq9+xmHDmUorp8L4umrrr3mW5O\nuThAp1sR/59Ktbvy9OCWeAFbICvsImQmSNLdcNN2lflCBZwjzd1bH6hogVKC\nn1oa3Wu6OxtRFnVMaSbGZxTeHFaTVr8Kcm4nba0aLNMcKFQBLAjzVPu3eJDY\n0Td80jcsV31CVUdWFDEt4GxxTu5H3H7NlXcKFcjqivwpihA9I2FIftCIEiEm\n89nAl7CxMqvBnxE+PoKo6k2V0S6nCFegmUxb1qu279i04JqfhQw8GApb5T2R\n5W9XKwrjrXREaXYidBRJIZ9jO6d5B4jL37t9xD8K0XHOsXG2vgK9IZNLh7Rk\nDHqOecJG8pA7BBaVJhejwax8ok9Wxsc6exkr0xmqF8rZwbKzJwVnqwijCFFM\nwvC2YWaP/cYslVve8k+Vy4obi1ZhBsEllpbODFfsNjavhKEBoTNZ4YacZGat\nFo+oKyfF9mWLGtuf1XCFNQnuxCjdjqFtKTEQEAe857J/RFclPrvhOsJKnu4D\nFAST942o9vcQXKIa/rmE9NKculgwKuPX/9mFpghrAkKGHq7yC6scCG8csjWW\nKxbj\r\n=Rh6K\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "0a2b94282170b6d4cc26c2d2003cc04ffebe5e3f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"jest-mock": "^27.0.0-next.3", "@jest/types": "^27.0.0-next.3", "@types/node": "*", "@jest/fake-timers": "^27.0.0-next.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_27.0.0-next.5_1615813415436_0.15038361410494683", "host": "s3://npm-registry-packages"}}, "27.0.0-next.6": {"name": "@jest/environment", "version": "27.0.0-next.6", "license": "MIT", "_id": "@jest/environment@27.0.0-next.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0d9886b19eed443f756828821ab7de7759d1deca", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-27.0.0-next.6.tgz", "fileCount": 4, "integrity": "sha512-Y6rP5VIX+hbpvGVb4+dcsOmdEppgc09q1aMXaCdQE+ArOwNi47V5A+hk5m5323FmnVqL6EKTBGf2sI/jhnUOPA==", "signatures": [{"sig": "MEUCIQCmz8opMSIDdTuGMoK+A6BPeL697wUbmkMIdIW8t5GQzwIgGKqGiU+GjZoFrZFIJS7m7wvXtlDs6TcMal2FHIMfLa0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11967, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXOcfCRA9TVsSAnZWagAABEEP/AnhpXDAwnACOLGE+4QO\nbqHF5wPhRLeDQApeYPPu0qKemw28FOMYMsCR5fZeRgmgvgQHVk8Uf5X1haP+\ndFasZyf3ombxp6zb3URmfRkqlGQSshuL2yrjp0yrvC3eex5Bg1l9WXu/C/tn\nYKGJrCcnPQN0EAIE8Qy6s/Ie3pyTdj11Lx5w9pErIe2jdBjasSNiLumxWXoO\nlUKkF7EdVJk9k6hO/+DgoXwoco6z9w2ElEAs3c6xtmNxok1EvKmT1OrH3d8a\nhotpr+rS1JezjGyABLCVyNnwTDXrdcECKgG8huMCAIeAL02cWpqHWYnQdfVj\nyx6C/KF665ALTjGRRpv7aO+PYZG1moVh8yUOOFBS4QsHLJLsxNrzO24Ack8Q\nxwzoAKd6L3ikMvklYjAF4o/99iNjtIarQBUt1o8aiMuS3RJY7Pf4HrEy8z2P\n4kPXtlBPZPGYjlGl+FvtGh41b8LSCq++mfm06sgHs7qslsI23GvoSNe82AKO\nR8IqSIoCLQtSWvSGgqEbAOA+65O7om3xJ7XYtAzp1l/X7H0To/4oKtupGdCa\nTbTEfIrau//WzPWEMsFyjS8ak2XFKlrpgr9kuQt07bViVHxPl8Hu0fywr/tO\nMVjoW7EUyKXwEAjg24xiQ9ntNEyJOfCrSGvsBnDC4oDWvWuPwqNmxYbbxXVc\n5z/T\r\n=GDcK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "974d2f22b7deeb4f683fb38dd1ee3a0e984916df", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"jest-mock": "^27.0.0-next.3", "@jest/types": "^27.0.0-next.3", "@types/node": "*", "@jest/fake-timers": "^27.0.0-next.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_27.0.0-next.6_1616701214561_0.16442095871116913", "host": "s3://npm-registry-packages"}}, "27.0.0-next.7": {"name": "@jest/environment", "version": "27.0.0-next.7", "license": "MIT", "_id": "@jest/environment@27.0.0-next.7", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c420d12b14769929a9283fda08f33504749ff745", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-27.0.0-next.7.tgz", "fileCount": 4, "integrity": "sha512-5U2JardL5yLQKJ0HUbJmffKdOzD6jLpKryAr2eSnhL2mOESNdClZP3tqVJEBTrZlROsx7QE9eV5cgMCo23Wfqg==", "signatures": [{"sig": "MEQCIELtaiDvuwHBdIwxGd4F9Vub6tqomKJFubcnpXwIZq+DAiAlDuTEH/jbsDe0dnFXZ6pfyhzbc93vKjtYKFMaBAf7nA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11967, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZyCZCRA9TVsSAnZWagAALh0QAJeeC3OW54w2e3q36tKl\nBoll9Yalf9r/doQQWqd1I61qxBxZO9RWv+dl5vrgzgJfOby6QlR8XA9lMDvk\nyuDoDVZrWDQW8WwWzYzwEQ4MWni383B8A7N1s5UB55d2diYpLW2Pvc74lLYO\nrSd2zltMeH3n3l6/1oxtPCaBOU7wnzSEerkLgNS97Ua2C8VH/Iul6/8XKpEu\ntmspkkz3RbHWORgFgkHf7sDBAlrXcjC6A4e7kQ245mygHgBW6YtyvDDD/Mxo\nJJJgbLBecDB5qytF2acDW11QHx9RtrHfPuys4lQ41EQtaffB/3pFVInvWKPU\naGCkvmP6Qhc0mPM17Nmskbd62q8kK4TkYojgcvzDyY2cMLb/G82po+aYnbj7\ns5TCDJ3Ta8vqQtT+dXk6BseZWGVSVEw3XfSsNom6xgIU3CdWzRefn3WzgkDP\nlMWRBYvqUt8lnsrOkhHaXwcZmHnQelG1zKQIY2B1ED52lx6Zu2RUfsPcUHs6\nBPnYp7JRScO/dcju0WvW3CYY/WvqzNHbsYEfSLKF8ToTvbfiQIvgeNzsVKw1\nARGjPn+tsG52g9tLnC8O0il4zHh15NT/UFbvotYS7TAN3jIi8I4sDZlovJt6\nX2XkJqEun/lvKKwV1z4usfcJi+BouuRUWd+JRLcKV8hMmpq10FhWJ61MOTRa\nBzQS\r\n=J5zS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "28c763e6be8f57bda89238b95dc801460c2d6601", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"jest-mock": "^27.0.0-next.7", "@jest/types": "^27.0.0-next.7", "@types/node": "*", "@jest/fake-timers": "^27.0.0-next.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_27.0.0-next.7_1617371288559_0.1367895759516169", "host": "s3://npm-registry-packages"}}, "27.0.0-next.8": {"name": "@jest/environment", "version": "27.0.0-next.8", "license": "MIT", "_id": "@jest/environment@27.0.0-next.8", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "88b4bbc4b9717ef57a464c14ec7db5cfe5fb909c", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-27.0.0-next.8.tgz", "fileCount": 4, "integrity": "sha512-ECb+ZnpiOGVg6ZvwMzutPGKS+wpwu9yLoX/hr9VxxG8wOUld49iWtrAZooKlvBNWTriXqPGrCb7mg26HIYaLKw==", "signatures": [{"sig": "MEQCICO4lt4YX5DYYE3aZcCc93P9e/J2pupUlVIMKtp1KXS/AiBxpl+/5MtF0HOSGEgDeMZEiTJrnBq8IrX1Dx4mKhxpxQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11967, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgdMzgCRA9TVsSAnZWagAAd2sP/0n3cIRr8NbmBLAImNg5\nZo+gxHMO/TM6CZXPaNOuZeNuLxrzL6n7kzzNCnrYoaLj/7YJ0MWpF4aMGMTv\nv+0jddS69xzllbAAueqd1fWldDCIlg7TfEqp28ohyaikVZX6D/4dOQzKAUkU\nwSrgC0r8Yv0lFz8F4eKBmU6fmqDfXaWW3Wb8+nqEV+wNOvJ9movRyXcpMKgD\nwFoWyn/7aF1hVi+CKIxatefLxEuOr5jkJ9lj8LieknamizjuecXI2flZw4yE\nR/bYG7v3D8vhZaotN0e7OPR49ZGMUu/cHYc/nChyUJuK6ER7Z3oGkclljblH\nbi3UnnD2ICgy96FaO5Mh0FvJbyTMwgsEOeOEpBryYx+U5/oeOBgyhEH/XOxO\n4MOTvFtWWUavK9ML+yTmiUQDFVN98odAD+E5D6AAfW+4LRzU0Rfcw8StjPdN\nFKm39XRqq668fKyGmf9tP0zUdABsWFxPPl90XF1NVj8r9rZ5xnWywvwtoC0+\n5M2ZY1BBkPByjdMwFWraKdgibP1TsJFcboWpcyis0kvsR1PI9LsUpcI0jIX9\nG1/s5EUvrCqCHOnoB60Tfz2bmhXoaFXAhd+Hf4nRgPU9140++CbOSWdJLysE\n9h97tAIihqSGBrX/3gPox1W2xwRvC2Qtbv52D/YSliBAzN3cWv+lU3fvcXgv\nu0N0\r\n=RX+q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d7ba5030e274b52f029179dfdb860349a36eea37", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"jest-mock": "^27.0.0-next.8", "@jest/types": "^27.0.0-next.8", "@types/node": "*", "@jest/fake-timers": "^27.0.0-next.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_27.0.0-next.8_1618267360308_0.45356263026846766", "host": "s3://npm-registry-packages"}}, "27.0.0-next.9": {"name": "@jest/environment", "version": "27.0.0-next.9", "license": "MIT", "_id": "@jest/environment@27.0.0-next.9", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "585a2ac297819a4cc7f50d723fa4ded364c99c60", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-27.0.0-next.9.tgz", "fileCount": 4, "integrity": "sha512-TLevPfItvPBFFRFUOtL2dwqi0HLjgveMtc2RW+o8P3uFgHhJkEVAqyqgaFYP8mJ6jzExGHZuZIfJLiTF/mKaNw==", "signatures": [{"sig": "MEUCIQDlLVXNXw3o3kS08fe65xZlx/Nw8PBiB6AHIsmLo5ES8QIgGmBrKNpC20ulNFc7VvbA4ly+0/hFC3NBjJareudM75Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11967, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkOjQCRA9TVsSAnZWagAA3jwQAKUpPlWYxB1bYL3SiGgb\nyo3xSQrKzn9vMAvp80PReWlPoMk+O/yWMulSEfGhYZsAMC8BMGBtsj8M/VI8\nIIjWXqR/DP4oL6xevJZLefHpSvuCK5AUWG3XjiYPvx3bM4RwrRdePvA5lLdS\n/2aqk4dB2LIaeXBxZUyotnCL3UZkfs/N6Pl47xVoObu5V0LHsTrLQKsWQgP9\ngmLG9sUPGQ+CQPQdSGh3w9cWolscadSJyxGcdENsPGQqRtIyVd8yYn8+LV5b\nIRZMkDtUWioIFFY85vfKzj0RiZ2gXqK6ESJQLmYiMppbIk4Placb/YUaRoIQ\n8g5XaSWziUKqwM2rAKG9lz44HCDNKPUwsTLrSSJOXXGSZwd1BpOVRygHiTsU\nii9qQL8aT8pHgfZGE+QtE56LqNc0OymG4xO/xO7mM12ujWqRxhbc0+OBGW5p\n5SU1IianaySDA6P7A3MZ61VS68VWwN5iZBJEUZv4HbdvAnwGz2kFK9ZcOtqn\n1G6RGs4+9riWK3vlWwEZrEoYL23kFdm1XPmmgU9crLIksrYbIW1kvl10FfMP\nxsQLFJqD6VntKRcIUaL7GuwCMqwG6jfJOQnxqZiuw09RYYIZ69V1fwrws8rh\nsRmhfEnBu01L5hTwL00rBFhFpJ2nvlMVOtneDOBO4ywFNAT3fbFyFm/ZNfcN\nhoGM\r\n=maQF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d836f33f98845794b4eae8149548a81ddcfc6521", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v14.16.1+x64 (darwin)", "directories": {}, "_nodeVersion": "14.16.1", "dependencies": {"jest-mock": "^27.0.0-next.8", "@jest/types": "^27.0.0-next.8", "@types/node": "*", "@jest/fake-timers": "^27.0.0-next.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_27.0.0-next.9_1620109520508_0.6615685317713935", "host": "s3://npm-registry-packages"}}, "27.0.0-next.10": {"name": "@jest/environment", "version": "27.0.0-next.10", "license": "MIT", "_id": "@jest/environment@27.0.0-next.10", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7937e6df3834721a45f962c6542472db945713dc", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-27.0.0-next.10.tgz", "fileCount": 4, "integrity": "sha512-iAFhLyvJg1iuwldPKUcKuIBJLqewSva60eVPsH9zCBR/s32+QKw0fK9fJrd39nSaikSqeUv9eEq4U2s7jmLTaA==", "signatures": [{"sig": "MEUCIDAe0ImHZI7eru04eaUBCcS5WFdEsznyLEuuER7vm83ZAiEArOwowIonjVAnPxJI8HvGZK6KRnTRNwSzQTmXdiMVfHg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11971, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpm4UCRA9TVsSAnZWagAA4vUP/AyGGx1OYWja8ux0ZDju\nCfshJsVIAknF1DYqeneoF1I5gKDR/lbXPcU1Y8lncOhOYjQ7f1YjBpApWP7p\nK3wprkvcDnMdfCi3Tvi/ajPbSoIPkL1y+sLxrPiO2HzqOblUygsLyzuWB0ZP\ncFdx8bZ4o6vYyEfVgqt/gwgrGjDBG2zz8iBqbk8pK3TlUUsvAGG2Eg5ve4Aq\nLDqyZD7DyUZKGQKF8+SXu70rCzYguP+RBd6psrI/xg04XLSA658TKuxUkar0\nC7VTF50P6E/D+6zQWMosPlaPEyXYB6mveZmYLAKotjqr/k3u+zy1xXfHiF4P\nPcSx6iC6m/w1jmogmdnjlHj5fSk35zWNX/f+btYGOsT1He42Mo2gfYY5BJpV\nLYjP/TGbIhV1ZNr5hnjpee5gJsrLN//2CR3ZOxoX1kgGIVFpyuydne6n6Z1S\n7J4ogYkNdpjcU/3MWrRFKHtOZaY12VVtBFjZOgCtidmFL2JdkqQ+P/Z5GB/D\neq5ZrjWQwwzcpdhV7N5UBrA6k11/O5CH5VgF8AmFXRRdFIFyCGWxj3+ExWdh\njE7v8Qs+A86Sw4Nb6WtYVUSzpFBY/ilR38eJm29C8xEyD2+1hVOACdjX9zZx\ne69PFLJikXGellZK7IgpbuzCOUluxxowkl04RdrcdMHfJD+6FmfiOG0vVnfp\nJyZl\r\n=0sjj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "6f44529270310b7dbdf9a0b72b21b5cd50fda4b1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"jest-mock": "^27.0.0-next.10", "@jest/types": "^27.0.0-next.10", "@types/node": "*", "@jest/fake-timers": "^27.0.0-next.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_27.0.0-next.10_1621519891884_0.9621350623272169", "host": "s3://npm-registry-packages"}}, "27.0.0-next.11": {"name": "@jest/environment", "version": "27.0.0-next.11", "license": "MIT", "_id": "@jest/environment@27.0.0-next.11", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b1132753861d3bfae694c4338e1d16e2f20eb317", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-27.0.0-next.11.tgz", "fileCount": 4, "integrity": "sha512-BZvcMYGzR68hJFbyrw1QaNJUp96vuMdtWxKIe+XbdR2uAUNC8yPGEGW4lRn0w5gR1+vHCbBbLxGumvX/fsXlfg==", "signatures": [{"sig": "MEUCIQCS9ayvd6CGec7ru0Ck4g6QRNmp61OlUHrqH7O1i8/w8QIgHzPTmXj1Hd6RKIj/dJzs6rHCMKuEoiEonDzMYap8wGA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11971, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpuKlCRA9TVsSAnZWagAAN4QP/3ui6XhGqgqNn5MGfCmn\nEcJvJ37JGCfHY+Y032jzzHEcN2ep9mNlhKobtapN8nScZs0igbDaTbbhbboN\niyNG3+qYx8FO1OXbKU03cDK02C7XmOuBFEYLp0rKEDuS03ue3KLwAaI4mB7t\n3gEcHgUDHBCLlZEGsb0OHhhuq+tH8TiHe1dbXAuRHis3xxFyYJyIyFTiBveM\nZ5+r1iqMvq0xK0B4vndUOfJKnDOdYx0wjf61p9DbmWjKyq/MXjP9bb7ZaCeU\nglSztgk2wszaAA7d5cpgRE6x8dEqVo1P1W87FQqvs6qR89E8xfTTyDFr6rYP\nOxQ+FX8nj5oRSs7kIDrhJPoe96tQka7644+kTsj+h4pphfpsk/N1xN0Uf64l\nWJGQrMxnIqLkDrrxNLaD6gl9/YROyDwXTJEkGvFRfiBDOBr/Ryg6GhncP0sU\nP5GVTGOXOzJODnSudqCYRSPD07I1JZvUdtCjN98SPoxZDk/ARXBvXMKvvVRo\nWMmxh9TKAKid/hWng/mNUZ1OnMWmL0K+Uysss0TF4Ut1SmuspcvDJ+WQOewQ\nh3WR4siH8Ae0SZV7r34QwZDkvfTcdurpx7eyL0kWvVgAv6J414dD8hB/rtDl\ncaXJeRT5cSII0NN9fDY+Pd2/XxW6JLSF8KPDBFlP+LDo8Jw+R7jGZgcd3lI+\nBGxG\r\n=y/Ts\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "e2eb9aeee8aacd441f1c8ac992c698ac4d303f60", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"jest-mock": "^27.0.0-next.10", "@jest/types": "^27.0.0-next.10", "@types/node": "*", "@jest/fake-timers": "^27.0.0-next.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_27.0.0-next.11_1621549733315_0.11385649311237533", "host": "s3://npm-registry-packages"}}, "27.0.0": {"name": "@jest/environment", "version": "27.0.0", "license": "MIT", "_id": "@jest/environment@27.0.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b30b6e4573e4d2c8cb0d6cbebf7010a515f5d15e", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-27.0.0.tgz", "fileCount": 4, "integrity": "sha512-WuRP1wMoi4p0vKSF40oOY3W3gDH1/rc34XygM3L6EAP5KmfeRQhLUAA3YzHvlBnnToA7MdOwLnKKnKUcX9jpqg==", "signatures": [{"sig": "MEUCICuheXpsl9PLnPm5ybuygf9m1spG1XQamHiEwIDxFtheAiEAkgbhO62+X5/hgKQ0POyBzr8Z72VUlsEfMsl574DZMgo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11955, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrLIaCRA9TVsSAnZWagAABlEP/245UFYt+tmpdXAoKI0S\n4yCJnoz/3Ew7Iy1gXjHPdj+fYlfXgLwIO4nv9WDjGXHK00xxrXRUwS/v2lRS\nhBPm3IbRs3dn1TeguW6LyPjycyVVIF4j8J4iEyonNFW9XlWNWgz5mXluCWjj\nUwqlmqhFKVPD13Zkb36Vkrgr1qdvrLxQqUODSfhHEKgnYa8on+sPS/YHZBBP\n2ZmWKAuFZ8JhMkigmaWHUC5o5Lq7tX1Nk9bLM5+vS4PgJ7texDVTu9eEJZr0\nzVoPfrUlO5gQR3Y0QmPr3cOLreitmmdDlOGZJxmIoeSvOjjjfK2WhMFs7A/M\nc0ari4lwPFx7j4otYyd8E3HW7yHYMlR72WoAHz8ClpIMQiKxFJhB90FR9t9U\nddxpHRZlOc3cbixBFnKsg0rmew+WL0wgFgg4whfDmImtwLfSCPLPSHv2q63m\nGgujPVjJ8wq0gj5XvFm1zkEyHi4oay30MNal7KRb/cA7I7Ut8QLgHOPY4rqG\nDUaQZ3162tir0kqOlpMmGePZMAm83NhT+Rl+lKt13z33iiZ7j5LUEUy3fnUI\nq2Gz5CbMR74SlTatL9rTXm8vuaF9ZW/ik7oS3Vag7TqwoqIl7piaVHE0NZWA\nhMxf9fk1SGS7thCeVtZTWabagOnsAtVVuSpVsIyaRRma45WZ6Rv2BQa+mG4G\nytVr\r\n=y9ea\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "be16e47afcc9f64653b9a47782cb48a5ca243e65", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"jest-mock": "^27.0.0-next.10", "@jest/types": "^27.0.0-next.10", "@types/node": "*", "@jest/fake-timers": "^27.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_27.0.0_1621930522111_0.1952538991678734", "host": "s3://npm-registry-packages"}}, "27.0.1": {"name": "@jest/environment", "version": "27.0.1", "license": "MIT", "_id": "@jest/environment@27.0.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "27ed89bf8179c0a030690f063d922d6da7a519ac", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-27.0.1.tgz", "fileCount": 4, "integrity": "sha512-nG+r3uSs2pOTsdhgt6lUm4ZGJLRcTc6HZIkrFsVpPcdSqEpJehEny9r9y2Bmhkn8fKXWdGCYJKF3i4nKO0HSmA==", "signatures": [{"sig": "MEUCIQCJVmuOITn+dx99PDl3MZFsH3t0fHa1xSb39Wo1aLbxdgIgaBigC0P2jy4YtNpZeFee1B3b4x3hDgrBgg95CCgivSE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11939, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrMw1CRA9TVsSAnZWagAACAQP/jl9ydySepWAMd4eOJDa\nfnFTzwh3MGZoIEXwcbKU0QmmCUp/3pXyrEc7/W+KfLAQvJeB4gl9dc3FYluB\nMHJqlt43daMrEuXFpX86ixNpOJvcwG7WJYYB4NBGRXFK0von6p2pKs9bgRqc\nYggAIOW6ZnNmgbxiFkYDazQ4E2mRYCz9sj1shlzroOlmvIN4Bkt6WvYGxPus\nztqYSU+Y7JfXIPU5AUPmHjTySk3LdxLNS1yOZAz1YZjLwX18l2AJ7J7FTccZ\nyZVSBBsR1M4vPBzrJQVXMHdNN0OPzfRHU5vW3E16LbAHRrvYF2EYc2g622t4\nFf3DGMW7Hk8xMsJk89Bn7Z5uecZB+6jkY3sVATZhO8wd33oUu6lzb5PKmJkQ\nogA0yZ4eKUezlhu0KMsX2ZX1zh7tZrclPpMALtLXXWVd1J/K3yObGDLYGPyE\nPxbtrP+Ur9qsrj7DwMcZspFlVwc/V7os0xZQ3FR2YSSM8bRnE6TMXmpzdUw4\nZnT2iTlAsRRNB26bit4UIwODtwg96v5RO3k9VZWRs2eBITistje669GbxB0G\n/K37RRNd0Yw93nvIZgEo2Y+VaiwGPpXb2bYcetxG0FLPz3yM04PSIoyNiXTU\nwl/uC2eCdA55S513gn5C9kt/1Z3FcJ6Os3uY6Knsac0opfUfHuB3W1NCd03x\nI6kL\r\n=LtPG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "2cb20e945a26b2c9867b30b787e81f6317e59aa1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"jest-mock": "^27.0.1", "@jest/types": "^27.0.1", "@types/node": "*", "@jest/fake-timers": "^27.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_27.0.1_1621937205494_0.6037618403291336", "host": "s3://npm-registry-packages"}}, "27.0.2": {"name": "@jest/environment", "version": "27.0.2", "license": "MIT", "_id": "@jest/environment@27.0.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e674395c371da068534b2a2a4b59ec752caeaec2", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-27.0.2.tgz", "fileCount": 4, "integrity": "sha512-oxqbQKlpZkHC5UxYwMHQlaetGTx+IlL7fFnsEM8WkqDmquxEfsM0QH+g6J7lMbNFwMHM9s/6C2ddTaVAP/75rA==", "signatures": [{"sig": "MEUCIQDBRk69WbFFY77WDuhpVpV4mttewAdMhu3jMUI2tyETKAIgfEzk1r06XnjEEN+cKqh9+35F/cwI4vDh4PNLqWRKfgc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11939, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsi6FCRA9TVsSAnZWagAA3YYP/1/QKbxKHGTjD3LA3sJ4\nrY4wv+/3R7oHO3deo53eqRc7m4btAhv60QIWFTbsvQqtDzrJ50VHXcGLgpB+\nIvFZAZt76OH4H9oGciD7cY70V4vYA7ZYHFl6kqQQPIHU/JJ7Y2g1paIXb5/n\nUAjcYXgUmumOPvHn29WTyN3uTd60Kv5mqeOYlHYoB4G+MrrdI3Lsqz2itpqk\ne+Idvp2tyl/osBRsf78qoZZz/f9FLlEVLURajLTzyZd18AkTgLFuTTswdmhH\nRczZY7R5aa6SeXSMt5j<PERSON><PERSON>ev/wTDw9jJ7dbzL4+ido4++zfDES1L2/ifrBUab\n46TU6Prvm1h+/uHHKMKGMTtxiQMqkWWcypmcJXTjP3OAQXXDNCP1hie+9nP2\nb9/xkpXNsuYCnvYHL6Kc3KvRPPsDR6SXZChIT1ACn1/nDNqkbeVlBsRMDHSl\n1G1I+scTvO2hYT/QunqI4O9SGE0goVk+iIDy6UEKoj1XrhWVsdefyOpWlyL3\ncqu2FhPSwGtqYTTjmkc5kf5CVi040hlThdhKeSXKTN3XECvTvXoGHccurWPb\nm3ieNQiqMqrzPk5zD6SzB4EWEw1+weF6uyzAzaRCwd770uvPc22ke0gDyXAW\n3YFe3LT5inLhCXEGWrzbWLsk5KBexDDC9TrIyHZaIbrg+X1umkz0znF+OCwr\nLgDY\r\n=vxYb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "7ca8a22b8453e95c63842ee6aa4d8d8d8b4f9612", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"jest-mock": "^27.0.2", "@jest/types": "^27.0.2", "@types/node": "*", "@jest/fake-timers": "^27.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_27.0.2_1622290052629_0.2577074093387943", "host": "s3://npm-registry-packages"}}, "27.0.3": {"name": "@jest/environment", "version": "27.0.3", "license": "MIT", "_id": "@jest/environment@27.0.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "68769b1dfdd213e3456169d64fbe9bd63a5fda92", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-27.0.3.tgz", "fileCount": 4, "integrity": "sha512-pN9m7fbKsop5vc3FOfH8NF7CKKdRbEZzcxfIo1n2TT6ucKWLFq0P6gCJH0GpnQp036++yY9utHOxpeT1WnkWTA==", "signatures": [{"sig": "MEUCIBApSPA3SOVhl9lx5FN/78sCFIljjZvu1tDHxFtkZAjrAiEAjysT6ByEXmlfNxzsiNAeQ0klG+U6lJy4fDf2m8wnbSU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11939, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsn49CRA9TVsSAnZWagAAHi0P/05iHPpQOGRZducWjQtU\ns8ix01J4XIADOoPlJFyAblh37X/HfYhrr+d2W5vDygWcScGoACgIu65gvO3B\nUjjkNclora6JhVOKtiw42EuIeWnkffJEJyGQC51oqrCKIPS7gpPWSeTSj9qA\nsZFTwXb8/icZGMoW3Tv2ri4EjmAPNvIC/XbiRqS5qr7UyKieRaVRuJMhqS5A\nsazlE5kJKkaZrXEotSaZBnOsdBju3Zfn4YaoIaWXhWvt6WlMXwl7ANEV6cWw\nsI6OobCYZGPwxQuA/u1l39m4U4vq3YMIX+A0Kazful+a6t7ir1VqsGR8A/jD\nQ/EjMyHDlTPOOPDpLa1HbPctv2ARECPID/QCoaG8Q0io5AuQ2p6jCIWbW+rh\nYT2e+I8S1fJeb2z3TMJmiOxGIlzXyE1zyWXJz2jP0MUplI3RtKFZuzDH/1IW\nUO2eMzQnIUmeEOkM6JW9Wddo8Fw1XP2zl1nSm7PpzAjJn35/sMHPsqdVrRE8\nlhOccaA+XDrYt9Tf3aQnwEo5vDQep0h3pwXTxf/ktanD+dIDnPS25Pq7Z33a\n7Msca67eJT+UvrnPZ4nH9kG5FeYnkQ1fezKPJ2gSFgvYDR/q1Z7uTUB2LMQa\nP60Z/RDY9Rle4V9EH2DE9pO+z1cReIaACL19YaaAy3Uy/SikY/s1PNNrQ3ZC\ntQ4g\r\n=3ws1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d10f645b04ba9febb7308392e635c0351d0f027c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"jest-mock": "^27.0.3", "@jest/types": "^27.0.2", "@types/node": "*", "@jest/fake-timers": "^27.0.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_27.0.3_1622310461280_0.08404904373537292", "host": "s3://npm-registry-packages"}}, "27.0.5": {"name": "@jest/environment", "version": "27.0.5", "license": "MIT", "_id": "@jest/environment@27.0.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a294ad4acda2e250f789fb98dc667aad33d3adc9", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-27.0.5.tgz", "fileCount": 4, "integrity": "sha512-IAkJPOT7bqn0GiX5LPio6/e1YpcmLbrd8O5EFYpAOZ6V+9xJDsXjdgN2vgv9WOKIs/uA1kf5WeD96HhlBYO+FA==", "signatures": [{"sig": "MEUCIAPh7Di9B7kiMB5vHp7rt8eNKjj5CoEXVV+XlJkItETuAiEArcMPXxGBuRJ137n4EhuV5UmNWG+21U/gXj4enW8hp6Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11939, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg0cU2CRA9TVsSAnZWagAAtIwP/jT6GuuzQBv6eDDJzyeD\nShaxAWfkHZHYmMlZHlRT5x+F9Hj+Nd7pKzw8EztCGwNZKicLiPhk1QehrPqw\nDsLftMlcMj5rQLxhjM1DPP3+0EpfXqt8k+Tjnye+VaBIKmq8pymci8Eg8co9\nKMs/zgqwjUYHjq0cInnJE0CLHOJEWUsyyRQ/pv+mi7d9Fo9fbABw6Ptj+IG4\nuLW5CbYYtzGtgSHKWDCIxFdTdfrsmS0a5PQUFiMdqhjEItyelzaeI7Mfr07d\ncgRgiXL32KtoUpDnaqJFShQg6EQ7G8jtBwJYwkwzt+tE4D/MjgCaWO6FweV2\nGYwGO7AHbdrrqr2zcrOFSABtVRExehS5WxurhiOoYavm2FW+/gZIUcF2dlAm\nPk3uDkxLPVjbkeZkQCtUDcZYklFiIZ4ajCroGmxGU9SAZDRxFYqsd/Jaz5IV\nhY7oY5Kp2O8div7fSKK2ahdXjFNf23Kf/pCM0WdXHeZ9SLseaBVf9CzmwFwZ\nKUubR1NlxuTChqbdzoMUL8NhfjwdynNqekKnz+8xmYmx9nTf812lSa4fTQU9\nR3hHHGwnlA4xaph5eOuhdGVrtyIAU43BR/x3mlugFS1uaTkrh98YZPiEXSJb\ndHDh9bNUEpJ52qLitVeS+avV4J7BnyIniFqW7dpH2bfscqPd5FC+WZLcZIYd\n2D2y\r\n=TTHm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "dafa4737dc02887314fd99e4be92781c9f8f9415", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v14.17.1+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.1", "dependencies": {"jest-mock": "^27.0.3", "@jest/types": "^27.0.2", "@types/node": "*", "@jest/fake-timers": "^27.0.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_27.0.5_1624360245803_0.2793079336472355", "host": "s3://npm-registry-packages"}}, "27.0.6": {"name": "@jest/environment", "version": "27.0.6", "license": "MIT", "_id": "@jest/environment@27.0.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ee293fe996db01d7d663b8108fa0e1ff436219d2", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-27.0.6.tgz", "fileCount": 4, "integrity": "sha512-4XywtdhwZwCpPJ/qfAkqExRsERW+UaoSRStSHCCiQTUpoYdLukj+YJbQSFrZjhlUDRZeNiU9SFH0u7iNimdiIg==", "signatures": [{"sig": "MEQCIEs7Ywo6Jbkf1+7gOIwiO3KHpBjFQ7Gx+QLiTcl7gfwyAiAPvNTyjglRKP/oXsbRyZZFijYUF/44bwvfTIABJjF9aw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11939, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg2gFwCRA9TVsSAnZWagAAO+8P/jYi2tuf+RMNvvyd6V6S\n8YCOioSJa2exxKWGzpsYlzjXZbScFEUi0sgEghvXqwHQTcEhj7Yj4dzm/pQ6\n6mo+nDbNMop3jKCYE/u8fDBAZXL6Lhcb5ErNS71LRuoUhFLJ/QLfpbrye/gr\nwYXbqNDiZlqD78H5f/VKTsvBqLadbi5R3hl+UFrw3ni4kSnoe0db1A9iHJ/0\niSBY8rAVYB2hnmnlbJ5CgBa3dcBdCn3+ZXIuxRfHvnEaujw4DedwEkBQoNQb\nl8vTjme8u6tVFs/bKkRb6ysjkj9h1kp5f+xwm5w4tG/mi00fLco8exTGxwwI\n3XRxde0oyyw8Q85QegkKmMSuMJdMXr2bw9njU+cbpONWYnhL/TXJxhZ08PKO\ntVMNp469GrDd0OJ/uVN9NFogUYkoTzeIlUys1Ug+24UVIvtsePxHoV+9CTMN\nwRdCw7s7zD3tNbcytx9aCwfn8YLSwoigdJW8K1kb72H0uJdyotQq98dTergl\nUboviOwurquRtp+clopqLlVNlcQ32HRlKhB49Mk56QsP0GoB9Gj+mLk+3E7K\n7nS0AzHEgSK9qSt5+jAbYHmI31GBd8TIq97+/+czpUrxMINypilIwwFCmont\n8blG9yQuEHrJBiihRqk70RRx1yvzOdcZWY3TUA87DUkiavmuzLK+smYgoDoI\n/jqD\r\n=HPth\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d257d1c44ba62079bd4307ae78ba226d47c56ac9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v14.17.1+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.1", "dependencies": {"jest-mock": "^27.0.6", "@jest/types": "^27.0.6", "@types/node": "*", "@jest/fake-timers": "^27.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_27.0.6_1624899952330_0.6183631231110986", "host": "s3://npm-registry-packages"}}, "27.1.0": {"name": "@jest/environment", "version": "27.1.0", "license": "MIT", "_id": "@jest/environment@27.1.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c7224a67004759ec203d8fa44e8bc0db93f66c44", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-27.1.0.tgz", "fileCount": 4, "integrity": "sha512-wRp50aAMY2w1U2jP1G32d6FUVBNYqmk8WaGkiIEisU48qyDV0WPtw3IBLnl7orBeggveommAkuijY+RzVnNDOQ==", "signatures": [{"sig": "MEUCIQC8iF5MvKh3//lk0lUpvDsrHLGGlE7yuauFhNprDoCYpAIgW3Q+qHqLr4ulCQijcx6RwktpYn01/eqMvKLw5f1jmI0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11954, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhKLeSCRA9TVsSAnZWagAAm4AQAKC0WSDgZ8A4pbEKT3WA\n0AKzDVbLmy8TR4ALNCGpqujL1zFuELn2K3MzFwj1GPd+T06mwU7uINK6mUIK\nGU+ubUqw6injJuG2IKemN2JA01a5Jrkl8cma+BfzM0vZ1M6nBkbKibnwVTnB\n4sJEDbMnqVHkOlUWqQjia3ynGkxqxFyOAuVg2NlQQgH/8k7jXGnmGpA8hci3\nYthM7gdjlkvhBhB3MbSCDfEglFF+ETBtXdJtqxu1vDO+1VkIP2rEVeeoiU1x\nWCmc0Fp7VDeWuJAdlmx8gY/y/GMwcdOtCsrsDPw097UXZQEXYgswCpnSNm+L\nZDIAFl0+rvkWtMsVsD0aOljeJN5WpMU2syjjtW5v/ANy6Bwf0AxkWr7CGtQo\nh0gUggYJDfACK2F8kjzzCiSnDKR348OG/MEc29qMXFUdgOHBSIM1rUu449iu\nAk38f74V+4r9EDQyT4GZ9BERSFNDOK/KZOGE71MvdHWLrRqPpSUpjX2JtaMo\niYsMcKCzhhe6G4DFrDPXClsoAVfzO/QR3yXGO3ZjLqjZz7S0Ftqn7u177vbB\ndfk7MWTdOpreoOYJVfMGB7M7pbjtKqA1kR3CRbH5DfgWWYdGaLx0evTM3CCp\nYOm0ViBOSiiEm5r5MF8WYsAi30n4f1gjNJZVZ4cHzW0k7Mt3Uu41ZQNdi5/e\nN/yP\r\n=YSan\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "5ef792e957e83428d868a18618b8629e32719993", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"jest-mock": "^27.1.0", "@jest/types": "^27.1.0", "@types/node": "*", "@jest/fake-timers": "^27.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_27.1.0_1630058386689_0.17522484386830017", "host": "s3://npm-registry-packages"}}, "27.1.1": {"name": "@jest/environment", "version": "27.1.1", "license": "MIT", "_id": "@jest/environment@27.1.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a1f7a552f7008f773988b9c0e445ede35f77bbb7", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-27.1.1.tgz", "fileCount": 4, "integrity": "sha512-+y882/ZdxhyqF5RzxIrNIANjHj991WH7jifdcplzMDosDUOyCACFYUyVTBGbSTocbU+s1cesroRzkwi8hZ9SHg==", "signatures": [{"sig": "MEYCIQCtWTrOuywKaZYSUaUo7eJ9iMDNhBtZtRx3KoBnwjBsywIhAL2PNdeh7Ig4KNzk5OsV4HZQsSWteMMx+D9ea3URR2nv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12199, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhOIyECRA9TVsSAnZWagAAGhEP/1SJpN1iN9WCm5QTX1k/\ntCgWaJaPebDBWiMiLL0fKPLMBv/D8t0JPHCh5m+UKTHzbh52lsIi+FgKMMSL\n2qhA9c44Mu7rk7jbmjnneozPIIn8LPkj2P5dTjAyyk5F5YLZbC+3my+MmlT4\nzRqwUysZ93E3FPlzqhlbQazsPgAb/La8VHQMRuvwcKJzE6nFLfMjDgUu0bdz\nI3lzeyw+SuXZLOYKgrpFqbtQExfUCayasdHG3Oc3jFjpMJ6w14pj7GmCCHBo\nt0DDSaj+aa13M9kh02l+sJzwKdlE/beLhEDZGvf7XFIF8U4I4NTm5x7dVlyk\nxM/HA0mAK/G81tTxAsgnDrTwn7An2Wn6OAvOMl9r4LrgRuOQOPrcwfixrKRg\nLawPTz6FsOIQcRqYVSh42MvwqqWGiOtjGQx8xbkkHviJzUvDDOd/LswHb2vI\n8Nw7pXhpaH2HPHaHbU4XjfMYuAD45jZyRy2p+1bhKDcyiE3TxsB6VB0kgVya\nim0IU80pqPQgXoN/sKeIjkrmnbiuSmPde2ngLxSGA/h2LhC30h5FjyM9chRV\nMQ2eophuNVYlH1WDeSdS+k713dHN7br+TxCYdOZV9d0mXFBANRuMyuOBbIvF\n9XJqd682FFp9eqWh3hmdELsmJPB+YD2nV46AooPX1/pIxVoC4iCuz5yt32an\nce7/\r\n=yZqT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "111198b62dbfc3a730f7b1693e311608e834fe1d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"jest-mock": "^27.1.1", "@jest/types": "^27.1.1", "@types/node": "*", "@jest/fake-timers": "^27.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_27.1.1_1631095940854_0.17546084005347784", "host": "s3://npm-registry-packages"}}, "27.2.0": {"name": "@jest/environment", "version": "27.2.0", "license": "MIT", "_id": "@jest/environment@27.2.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "48d1dbfa65f8e4a5a5c6cbeb9c59d1a5c2776f6b", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-27.2.0.tgz", "fileCount": 4, "integrity": "sha512-iPWmQI0wRIYSZX3wKu4FXHK4eIqkfq6n1DCDJS+v3uby7SOXrHvX4eiTBuEdSvtDRMTIH2kjrSkjHf/F9JIYyQ==", "signatures": [{"sig": "MEQCIDu6R0plXX35YhBIoDtaP7ltoi4uKmSemGkNiyEwYDLnAiBaneGi3Qpl2LHkGIRXPClILj8B1iI6LHeZsq1MrnTYDg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12243, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhPwacCRA9TVsSAnZWagAAVe8QAICW9BbYDX62QWe6J0H0\n1VFcDGDvDsT6tXHSzOsjiLuFEBruL/6RBcBVyZDF0qWISWE0zUjM/7X232DU\nmAl/lW5AGH5DsypcCvcz8CDJn+TW3hplTK3pgraCugJOPxTf8S1V0C/CXuQD\ne1i4sHhi+r1xCJWKl7ZPfxhsyiUlPkE70Cp2hAr4MQRZJVCkPzJeAr6B0Ndd\nsHxSOctqjaGyr6aZbqMomF45UcYc4T9FRMi+LH+yLTKugypTD85/zLDoA/8w\nGKvq/G2P4DCCMSZ8FbWNT+RRB7PeKYELnGkpxuh5BfL3hoNn74ucbJ2B/dwa\ny23zoZOg9QAIuo10CwoEvIB/C4TZwjp4WzWmjel5aCNHXLtReZKxO+U31tOf\nXkjfX87Wk/0vltkg4yrr/Ol5MhF4eBYfOAJILU9ZEq8cxHYhZ5SOvAz/7OgX\nJz7r7RWsBPFKQDWxvyWBSzphPizjVV8b7w+qRGjaozTxHr8ZJD63sDRJTd/i\nV00qjxRPPnFgejbXPxdQ5iXYeOfjpbY/JAE6O239sa/Mgnf9oHj68lYvg6gf\nWFaFGDV2VL2ccaP5jnE/CI5EqcM5DO/8MlMSFVMvv+oYk/5sB3v9JXfoT/BW\nykWY2vaOetTssTFaMhVaCbMqVE9a9gU1pIr+LE25Q1eSolmJqL1TefX3+MiW\nn7gc\r\n=t4xA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "b05635c539f8f673dfed5bf05ea727a8d5d7bbe2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"jest-mock": "^27.1.1", "@jest/types": "^27.1.1", "@types/node": "*", "@jest/fake-timers": "^27.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_27.2.0_1631520412800_0.3375764411343922", "host": "s3://npm-registry-packages"}}, "27.2.2": {"name": "@jest/environment", "version": "27.2.2", "license": "MIT", "_id": "@jest/environment@27.2.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2e57b9d2cc01028b0e35fae5833c1c63df4c5e41", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-27.2.2.tgz", "fileCount": 4, "integrity": "sha512-gO9gVnZfn5ldeOJ5q+35Kru9QWGHEqZCB7W/M+8mD6uCwOGC9HR6mzpLSNRuDsxY/KhaGBYHpgFqtpe4Rl1gDQ==", "signatures": [{"sig": "MEUCIQDQCb1FmWs6W43xzQ58Mg+NSe/uvIpmycew6Q+EiY5zJQIgLuC0ThrmeLKrMQn9A+Nj9fSaNBMmxYe+mZgx1eoIMfQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12243}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "f54d96fec55518640b900d6994b2c4153316d1ed", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"jest-mock": "^27.1.1", "@jest/types": "^27.1.1", "@types/node": "*", "@jest/fake-timers": "^27.2.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_27.2.2_1632576910930_0.6089639777778473", "host": "s3://npm-registry-packages"}}, "27.2.3": {"name": "@jest/environment", "version": "27.2.3", "license": "MIT", "_id": "@jest/environment@27.2.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3ae328d778a67e027bad27541d1c09ed94312609", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-27.2.3.tgz", "fileCount": 4, "integrity": "sha512-xXZk/Uhq6TTRydg4RyNawNZ82lX88r3997t5ykzQBfB3Wd+mqzSyC4XWzw4lTZJISldwn9/FunexTSGBFcvVAg==", "signatures": [{"sig": "MEUCIEq9DWuvM1VcU7mKpkdPvrjjR0G++pLqewlTuAk9rO5GAiEAg/qKaBdY4RIGhX9jtoWWRpAr39UBkQ8PfjROdW/3OLQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12243}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "ae53efe274dee5464d11f1b574d2d825685cd031", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"jest-mock": "^27.2.3", "@jest/types": "^27.2.3", "@types/node": "*", "@jest/fake-timers": "^27.2.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_27.2.3_1632823887138_0.1652615839827336", "host": "s3://npm-registry-packages"}}, "27.2.4": {"name": "@jest/environment", "version": "27.2.4", "license": "MIT", "_id": "@jest/environment@27.2.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "db3e60f7dd30ab950f6ce2d6d7293ed9a6b7cbcd", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-27.2.4.tgz", "fileCount": 4, "integrity": "sha512-wkuui5yr3SSQW0XD0Qm3TATUbL/WE3LDEM3ulC+RCQhMf2yxhci8x7svGkZ4ivJ6Pc94oOzpZ6cdHBAMSYd1ew==", "signatures": [{"sig": "MEUCIGOO4+923RQ89FrqCJDkjQ8hWd0vvjwAQH7feGGsz5RgAiEA1bR9AH7TyItj11NdwQx5pwwrHu4FUiQao7x5kfTv+x0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12243}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "5886f6c4d681aa9fc9bfc2517efd2b7f6035a4cd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"jest-mock": "^27.2.4", "@jest/types": "^27.2.4", "@types/node": "*", "@jest/fake-timers": "^27.2.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_27.2.4_1632924292617_0.03395804641870126", "host": "s3://npm-registry-packages"}}, "27.2.5": {"name": "@jest/environment", "version": "27.2.5", "license": "MIT", "_id": "@jest/environment@27.2.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b85517ccfcec55690c82c56f5a01a3b30c5e3c84", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-27.2.5.tgz", "fileCount": 4, "integrity": "sha512-XvUW3q6OUF+54SYFCgbbfCd/BKTwm5b2MGLoc2jINXQLKQDTCS2P2IrpPOtQ08WWZDGzbhAzVhOYta3J2arubg==", "signatures": [{"sig": "MEQCIFreYfMS1TLee4EvTfvMAo5rNZvu8fFJYfTNaHOa+r5LAiBuDyVNhNuGhAKxeyEAtP9vPb821saEd0SoAntKridffg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12243}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "251b8014e8e3ac8da2fca88b5a1bc401f3b92326", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"jest-mock": "^27.2.5", "@jest/types": "^27.2.5", "@types/node": "*", "@jest/fake-timers": "^27.2.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_27.2.5_1633700368643_0.09599603506768495", "host": "s3://npm-registry-packages"}}, "27.3.0": {"name": "@jest/environment", "version": "27.3.0", "license": "MIT", "_id": "@jest/environment@27.3.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "21b85e6f0baa18e92c5bb173a65c0df24565536d", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-27.3.0.tgz", "fileCount": 4, "integrity": "sha512-OWx5RBd8QaPLlw7fL6l2IVyhYDpamaW3dDXlBnXb4IPGCIwoXAHZkmHV+VPIzb6xAkcPyXOmVm/rSaEneTqweg==", "signatures": [{"sig": "MEYCIQDPBHkgzdnb+f/UcggEoVc04h9vntGywH2HFqCb5uDhnwIhAKuIQOkrbpiijhCmGF4uiWGBdn1ahh3DDhLq0mAUrIz3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12243}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "14b0c2c1d6f81b64adf8b827649ece80a4448cfc", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"jest-mock": "^27.3.0", "@jest/types": "^27.2.5", "@types/node": "*", "@jest/fake-timers": "^27.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_27.3.0_1634495688835_0.5804588936102579", "host": "s3://npm-registry-packages"}}, "27.3.1": {"name": "@jest/environment", "version": "27.3.1", "license": "MIT", "_id": "@jest/environment@27.3.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2182defbce8d385fd51c5e7c7050f510bd4c86b1", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-27.3.1.tgz", "fileCount": 4, "integrity": "sha512-BCKCj4mOVLme6Tanoyc9k0ultp3pnmuyHw73UHRPeeZxirsU/7E3HC4le/VDb/SMzE1JcPnto+XBKFOcoiJzVw==", "signatures": [{"sig": "MEUCIQCLW2dkyJs3eeguKxes6SLzR0lyq7h7rS0tgLO8hVgJVgIgKIkFp0wDmJWSuTQ5x60FD0BKWuXdDgssxjnbPQ89YkQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12243}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "4f3328f3227aa0668486f819b3353af5b6cc797b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"jest-mock": "^27.3.0", "@jest/types": "^27.2.5", "@types/node": "*", "@jest/fake-timers": "^27.3.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_27.3.1_1634626655762_0.5178836930822464", "host": "s3://npm-registry-packages"}}, "27.4.0": {"name": "@jest/environment", "version": "27.4.0", "license": "MIT", "_id": "@jest/environment@27.4.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3d7c162904d8ec5e5020c17d1276943d36402562", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-27.4.0.tgz", "fileCount": 4, "integrity": "sha512-7HJ1c6lVNuxrj9PT5AD4yVDDqFt9B0lLsshxZJXShL/LOkLnBO4MoZMH3w1lXQJY3zxk3/l1yg2j7uRKpxF4yw==", "signatures": [{"sig": "MEYCIQDnAkzX0al/XmicFLQlJ9zucKQtEah2n5N64ALbRd5VhAIhAIGGTFnyFB77MXm8+RPVaVI7kCKVW5/R6RO8ZOsgVa6X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12305, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpNedCRA9TVsSAnZWagAA2bMP/0i07oN7T4R52xBPegdR\n+AIhVUl3kjThazo1yC2ImX0mw8nRUuZXKBCo/PJiWmFpzwK3UScdrX0sFkVJ\nltFSyPObbgoxGbf0Wtgk/yUx27qCOhfKPBeSWfujUi0B5lb4Q4H2DPLzA60b\n3wXxGOOl4zuNJtGZlWZRb/wmPIzZcQ4E053PCfSz0Ltk1UQGcruy+9DMAaNL\nNC2mIi87xB0NZW3sjf3ylAkt0BTHSePXVQ76jA8H1NPZlIfE/uoLUM29UzHA\nP14gVa5OSshgUZVMFdpSq6jISDltusLQRND+/gLZLbimYbzrHon5mYdmhDoJ\nu9qFNUvWXtIRGR8mjyTKrreizSer5Wkx3Wrsa+s1a9SdERFq4M4d5F5i1TUB\n3IrNTq71rhp8nRDI7gr1vesiEjJPGf6fZHYGQp9amMQMLpFeh47POv/D56js\nDXLqWfAagaRDP5WRyhxoEvczsHRmEhIguG00RFxculpAs07n0ERVxUhE6L5g\naD9Tv9qgJlcYw1bqPvRfkvqq6Y0h7jYI8Za6LYHH0109dOxY2UA5WOOnzX4p\nPbJt+Sp82QqBHQY6RpjAojPm7Zi/kpq/CbCpW3HvuhcO6+aZAIlBWfdDLWhf\ncB0zt0nKNhhBoj0vqtaUjsmUAi2loHPjb1x2sCu0C+nDhgncxuQcBlbATAPR\nKcqd\r\n=Em0Q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0dc6dde296550370ade2574d6665748fed37f9c9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"jest-mock": "^27.4.0", "@jest/types": "^27.4.0", "@types/node": "*", "@jest/fake-timers": "^27.4.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_27.4.0_1638193053596_0.1897107936308926", "host": "s3://npm-registry-packages"}}, "27.4.1": {"name": "@jest/environment", "version": "27.4.1", "license": "MIT", "_id": "@jest/environment@27.4.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c7ebdd4f6ad2e586658fa3e288d53147d5b32fc5", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-27.4.1.tgz", "fileCount": 4, "integrity": "sha512-SBlb9hKroh2DA/2sMMfrI9emBgPzua8OzXBga7RxVRq0iUc1TiGfrWl/+aCSSSdqOOobwB/b4Dj2uGitE74uUA==", "signatures": [{"sig": "MEUCIEBKJpUbFidxB+m3UW4PKAa6p9CAWR1bk4LurSCx7TkIAiEAkyeG/FXkoYV+UzogHdinkSsZ778v9ZQS8itorLny3gU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12305, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpeK9CRA9TVsSAnZWagAArFYP/1QbhdTqHWk5RgA8HhC9\naGGyvuKBFESdnH06IZl1D1Qa/CzU2iaWelWc5zu8HXXI2uNbTJMZsOwkWXam\nkF5M/QWvDF+9ycL6T17v3irqvTu9G5kFY9CoALyW2mq1ToCm76BcK23mZy/S\nyAR+BUdROWHxqhkQByvaJKjQ+Xm8L96qJOSuv4k4APYh0Sdw10/oJ33Jahp2\nORU70h++OTpISuBnZ4hZPMep4pGF0EIJ3bsSHbwavfYQGcE8eXPJkRd29vEM\nA2pZwSid+0nLvhleXK6SE/Aoc8JCrvxOmnz6Sdd3HdhRlzGsepjKvYNtWUIr\n6UWo5cX+pBn/FPFYNPwrvxrN94GybzwjH4ktcgfkDBvyS8iZ/Yxg/CBY3uNL\nU/V64QTfFJBBnJjdnlb9kEKl1oPVIwJ2/tbBwLAg9IPzRDiUCTjubnuyuAcI\nSo3dylnH6PQXzwj8WsfER6VXSPk2OMafbdLixLx6fwoilMMKRcqlJISwfqVL\nUk0MCOWip+6TXf8R1CJm9rm+i3M1H81uoSdk6NirhwglAtdfZ6FPdoBD87At\nv0LfMRl81AvDIAH1mO3X3OZuYfpiZM/6KMAOHkYCFduu8gKL/78xYWkCTWvH\nB1Gk/ea7pV1rNIOwCDuYSOEeWxlE8Q8ePi71a8D8IhTmcB2UHJmQIMhM447/\nLJll\r\n=4wfn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fa4a3982766b107ff604ba54081d9e4378f318a9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"jest-mock": "^27.4.1", "@jest/types": "^27.4.1", "@types/node": "*", "@jest/fake-timers": "^27.4.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_27.4.1_1638261437318_0.4944540216764759", "host": "s3://npm-registry-packages"}}, "27.4.2": {"name": "@jest/environment", "version": "27.4.2", "license": "MIT", "_id": "@jest/environment@27.4.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "03efabce528dbb09bffd3ec7e39bb0f3f7475cc2", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-27.4.2.tgz", "fileCount": 4, "integrity": "sha512-uSljKxh/rGlHlmhyeG4ZoVK9hOec+EPBkwTHkHKQ2EqDu5K+MaG9uJZ8o1CbRsSdZqSuhXvJCYhBWsORPPg6qw==", "signatures": [{"sig": "MEUCIF0AgcaLZeh6wig5xVxiBg4RgaYtCSiZyUSctnOA6rBbAiEA2LS8hUfTWp6jKdURdgGIKx1DbGsqKH1HPBRDKs76tn0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12305, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhphDUCRA9TVsSAnZWagAA/t8P/1w1LQ12Bratfdn2M0+p\nCTpVJWPlF/znd3MMMdLlZuYG75QP29iDBW/hZwjLWqLMTojLJZNOI74tpTLJ\nddPDmNWEmIbrGTlwve4JZu1rIPLs4Bagk1gnzkReIlx8Hi0TTUKu+dvXBAJL\n63qTTnIs+KbVRWWw8w3hjFqR7nD+W0ltPgkD4kQ3DyKpQYjH1kXCgxJ6q+x5\nuz4VkNEQ30yr6O1wPIZHUbPC8wRbM2oSp+A2RrrQK8jnRWUwJPkxGin2L580\nhHWK1NowNtqqPc/Pq49IxTelx/8o8/H4eHcTZEQBKltN6Y3RQEHfGY16RQpt\nRjImZAUNgGmlx01zb+9OAnKUqqQjlQo6yFseW96aEwxDTQd2bcxoMvv03lsu\nmkoIwevSWG6e9g9nNPI27tJgKDNg/MoIFQyaGrAfNGAG2YXUdKLxHbHZGx7I\nnSb/WlQPtpUf2EhBMQErPRzLfm7yiZ1e6JK64zK09frwMEMDUs3ABKsRTkaI\nUD9bkuLPfLWYYCjzuosTNEpjMBWzzx489kGODkB+kTEp4pNPHXogFPLFhjgn\nHxEXIW+80rdUF42JertrYkSvsiDw4O8TG0lE5rx4dSuiJ6BKXCfVfdKvsfVa\nZkO4kgLcsCONZp9V9nIXdJ71QZfvY9jeGQrHXyYvWEK74+B4FfUPmnkpZOsW\nSdpt\r\n=xl6+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "7965591f785e936ada194f9d58f852735b50ab1c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"jest-mock": "^27.4.2", "@jest/types": "^27.4.2", "@types/node": "*", "@jest/fake-timers": "^27.4.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_27.4.2_1638273236001_0.3414428930121558", "host": "s3://npm-registry-packages"}}, "27.4.4": {"name": "@jest/environment", "version": "27.4.4", "license": "MIT", "_id": "@jest/environment@27.4.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "66ebebc79673d84aad29d2bb70a8c51e6c29bb4d", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-27.4.4.tgz", "fileCount": 4, "integrity": "sha512-q+niMx7cJgt/t/b6dzLOh4W8Ef/8VyKG7hxASK39jakijJzbFBGpptx3RXz13FFV7OishQ9lTbv+dQ5K3EhfDQ==", "signatures": [{"sig": "MEQCIGdLOVS3TRdfKn/Lq3QjRDT7Bfk43EiUG7woqeABeXI1AiAlkJSM0HCozrmstxW1poRgsX+zxuVLcK2B9dVlHkh1hA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12366, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhstrbCRA9TVsSAnZWagAAymEP/1AOL/G/qNtDtqZRRKhM\n0yEOMNKUzrq6DguQ6Fj4KcWdrjkl1IPhRT7mXw07UINCmUMojGhJuMAtQbhF\nShRDONRHINc6zTgQGuZlookptOoFq2KO3MKvI/qPI0a/px7IqQCX6ovybg1K\nw1lJSkO+j++lKkvfLcTidB/uHxLrEbDCsFIxjis7dpC1ICGLLraf7JPNewV5\n1D9O/WT5ve4Y0fQ4EmGyoGNUY3F7fCZs5pCCOJ4rbqplpTeTbqp9gJTTu2r6\nvmPdWfZBsWfuPySNRsXf2t3wegnObQ2PfvMeJOWQ360qC1P21JoPg4xU3qeW\n6xIqcb585mg7a+dX7kXprSzOrQ3bDTWDSgwUgxXEiOlbeoXt0nFBdtlBuDDX\nqEbv7HiSwt3YK20naL44/HHxLcJ4vHQR4brSmlJ9F/X6qezOx/ehIhIimS8E\nUzImUh0kUOf5cDuHfvZr8an35Xd8SN7+aBIaW2OhJqP/hrQW2cFlrbjdZf8+\naIs8f49aVDi+WlX/LiLe48HNzpsCilVh6Ai8mXLIqLNRf5SKoAKA0vtAwzcr\n6Hvb5NnVbzgl2Fcc8clIAMp36yK50C0wvIUWDRy5bLALLY2mnxzTIY+wgD6x\nAhvINqme3++B5hjyIJ2JMeCDn4Dhx5fTMKynUQF6XBGLjRRW27wbsGucL0qB\nylRx\r\n=rLfo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "e2316126b2e4b9b4272e5a0b651c3cb5b0306369", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"jest-mock": "^27.4.2", "@jest/types": "^27.4.2", "@types/node": "*", "@jest/fake-timers": "^27.4.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_27.4.4_1639111387188_0.8887317891961508", "host": "s3://npm-registry-packages"}}, "27.4.6": {"name": "@jest/environment", "version": "27.4.6", "license": "MIT", "_id": "@jest/environment@27.4.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1e92885d64f48c8454df35ed9779fbcf31c56d8b", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-27.4.6.tgz", "fileCount": 4, "integrity": "sha512-E6t+RXPfATEEGVidr84WngLNWZ8ffCPky8RqqRK6u1Bn0LK92INe0MDttyPl/JOzaq92BmDzOeuqk09TvM22Sg==", "signatures": [{"sig": "MEUCIDNE24mSU7K1VysBXNdg6ht30tP8SYkOgX1VpGfnviUEAiEAzujc4NzoZRhY5BEuTCql3QrgRDFqzR+Qjr3QLz7S5gw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12366, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh1NJRCRA9TVsSAnZWagAApDoP/0JBAd4Vd8v9B25Vnmh8\n0S57W3AHNzxEVsRC8/+rwzTtOpNAz4ttyyF8oNLzcvAF2kBw1OuQEjW/tvZh\nYxsYXeFh/47Pz2lsufZBrSiRE6e5VtcaE/FMgYCA7Q9gQ+N7tgIzeC/yh1tZ\nV71GCPhLyT0nRrM9WjFlpu3Gs69ws3/DAkLp0B7OHcsH7nTM4Jj6GX7HqosR\nCMyyjw5fdB9R7GPQ2pJ2e2moaTlqqVDcKiK+IcNv0Mq/ikw09MSPCxeFi3gn\nfU9IsCAUH/K0h1gHJejQLlMHlgILEx2Y4V6ZMeFtb3hyqFsehugOkuoya83N\nUcfnSdSEJ6yBr0/cWmZEN/DpNBec3BNIxWhGfGL9i6zOOJLPI7weFZX1YqH+\ncj80OwREW157M0ed2Z2p2BDKyjFYzqMyQ30OdIvr52OFxtJ39YyxAhLzkykX\nI8N68cmIIMBTYc/cmbEhJzS4IKgXuyuBpKLkEML/aht7g/Wb5o5N1cHx5yAz\ng1mOM+nVU6NNvzN5mbhyBW34WgAOAFVax/G8PHyrgDIeZgG7VDJtGb/Ppf9a\ncQky/YN+379C1DZP7GXCBC/Db3W6KF+1737MZQGXUj22oPndI9vF53hRn8vj\n4h8zzP11uaUjA+qWuFiWcD9dHBd+yzH6BDqwHiQz0hMuYwwelg7PDo3RUBGr\nu2/Q\r\n=nCZS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "644d2d3e53536b0d67e395c0f35f8555a67beb1e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"jest-mock": "^27.4.6", "@jest/types": "^27.4.2", "@types/node": "*", "@jest/fake-timers": "^27.4.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_27.4.6_1641337425380_0.7578970610162306", "host": "s3://npm-registry-packages"}}, "27.5.0": {"name": "@jest/environment", "version": "27.5.0", "license": "MIT", "_id": "@jest/environment@27.5.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a473bc76261aad7dfa3a1d8e35155953a5ba3436", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-27.5.0.tgz", "fileCount": 4, "integrity": "sha512-lg0JFsMaLKgpwzs0knOg21Z4OQwaJoBLutnmYzip4tyLTXP21VYWtYGpLXgx42fw/Mw05m1WDXWKgwR6WnsiTw==", "signatures": [{"sig": "MEUCIQCZcRzNMXjimSyzDrE1CnvI1JN5XiyFTJfn1+NxwXqgZgIgCZiWpVnBzgzQ4OAM73HfqUu2AaGAhZhHY9EXEcBMXAo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12412, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/kqCCRA9TVsSAnZWagAAknMP/AymI8IZ210/DOevRvas\nTAg4c96Qgjd4S09KT5zJmcT8VQRRrhLT2MqHDIhDsB8XNCrT8hLqCYiD3Sif\nWSdNLIQOX2+JiP+oUO4OCSKOIHwUVBRoM2Go6HNXkto/ZPQOO8/jxtI+YKc+\nX4UZrgDJGGpkXyKSqyzLtGbSaxbyrEE252RXIsuLrWgb9kFKxzUHgFxkCDQ9\nBaoXulPZgE8l5Ai9feae1YiXYIffUi0eprTs/fn+TP2f/D0VyEV/EKHf9JRy\nxRa8i+DvwPx0Yn8jkx5elqZOPEbFDb7jwz6WxmKUgVEHlQfwIzWMqvq9mTLB\nqMOhCWuc8TCVim7EjTaqjx+jVFna61+A7hu7UcoXysF5SAkx43ChYSjWNJKF\nf3/USfO33Ucp8zocDsqnPCUD9O9Ri03cZmlFhePH/WDlpWF+SOGtQwxIxSMR\nWm23U7fK5rmz/iSm8fwU1zELautJnnOouYDqKahjR5TgJeX5YGnKuoRMyi5F\nqBqIxhrNwyQ7DN90n5xd7OSmbkaPz9YzXfhlfnbECqKH5FFoFJWSx0lPriUX\nlJVoBxrVZKsroPiFUgwYBsYL6nA2aYDBunyhDOY4W3FLvhikwdkSNUYDah+k\n4O/oYJ2I/aYkKl4/AFgkHTtYkQV1Lq2M5cNjlK0k+MCeysDboAmulj3Dt0sB\nsivo\r\n=7jGq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "247cbe6026a590deaf0d23edecc7b2779a4aace9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"jest-mock": "^27.5.0", "@jest/types": "^27.5.0", "@types/node": "*", "@jest/fake-timers": "^27.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_27.5.0_1644055170289_0.49926859830370196", "host": "s3://npm-registry-packages"}}, "27.5.1": {"name": "@jest/environment", "version": "27.5.1", "license": "MIT", "_id": "@jest/environment@27.5.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d7425820511fe7158abbecc010140c3fd3be9c74", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-27.5.1.tgz", "fileCount": 4, "integrity": "sha512-/WQjhPJe3/ghaol/4Bq480JKXV/Rfw8nQdN7f41fM8VDHLcxKXou6QyXAh3EFr9/bVG3x74z1NWDkP87EiY8gA==", "signatures": [{"sig": "MEUCIB7MF08g7rN/KtMDRpmNgKdxn6lgOQmy5mllS9/W+WAyAiEAyXNEnmcValDWxWGAzPhJ1O9LHWvC376A5S3k3VPYhwg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12412, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAktrCRA9TVsSAnZWagAAV4UP/3J3mJlnMFmB3rSGXaoF\nuBU1gEavQotBpkAWe/1daMmA0bE2r6Xc1uvvpxF4vXEI89P0Lis3Gwuly2bT\nc7TuAd4vK+IhbKXOJvMR4YaSFtK2bpUejJq02v3cWCF3jxJONuvBLq8Y65+1\nLsxXJoq7aDm79bKmvRnHKJAfeMi5c2H9u7SjwoGdzSqAfA59izksv/npx8l3\nNfEJXK7G15czIhW9Z5ZFrJoFN8kUw3mfCmXB3sQQCS4pwRpmFRBSRN1GHcOO\nt9EpaaW2lvGKsMZ/Sdb6wGoerNHA/hlOC0h2zImgWRQ1l6Yv3l4FK+ZeQxOf\ngS78kzx8JaV6W0sWAS1RH/a0fsPZK03sFqxDC5edttAfWUVyfrItET8tc9r9\n3m0WYFRJ9UGupVRkOs2JkiuzRTnwyHobeVVOMakdOJmcMiXxW1vi3czVKJCp\nvLHW6YU3EV95Be9Nlv/WhlTmceZt1BLPxUOVQH5tOdztmNewhJlCMSOT12al\nZuYfNJ/bnwBhjkuuvsWciu9jhXr3fglQl0PxsGdIOlfAIhPXJDpaQ43QgjWs\nTYYeAbrOAykLhBhjIl9pLYXqgewns5qvgqq0iRb12Mk+31qa48J+whq82e9j\n2V8++P9hxbdu1ldTnA2tBAZq4o36ZjXooc5JasT3ZaeTE9z6HYYy6J+5GR9y\nkH4o\r\n=/Mfm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "67c1aa20c5fec31366d733e901fee2b981cb1850", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"jest-mock": "^27.5.1", "@jest/types": "^27.5.1", "@types/node": "*", "@jest/fake-timers": "^27.5.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_27.5.1_1644317547395_0.14291691512996896", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.0": {"name": "@jest/environment", "version": "28.0.0-alpha.0", "license": "MIT", "_id": "@jest/environment@28.0.0-alpha.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "46af87d031747b65f2cfd6a271d247be4e3c1d94", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-28.0.0-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-KlWIwl+z1ZaS6EpwusDuWalnZR55p0Kw9/2QAcN/h4pIDuBrqGGRg7hLAKANgmIxDYGML06aNr1lzRiyDVhwEg==", "signatures": [{"sig": "MEUCIQCFoRmMbCxAOXYNzirwOUV0SQMN+s7dMwhi4qqiQoXArAIgcIzPwy5zcJzw5iCfF84gFOawI3u0jMZz0srVLlRByCQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12188, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBVbECRA9TVsSAnZWagAAIUIP/ApflriGofXMR3gtzQtj\nAVK3PU54fvcnMUZE0vOG04RkITEFoul990+PzJi75mevQ+nSqYli9zn8WWrp\n0yO/b+TnsrWbYod3kOOnxHai+Clz+9ClOnzMZvNW6nH9FcqdbpG1XF4WYs0G\n4x1R9ppGZgLYerch99hL7sHrrBuY2+iXeAJ1SMWVqhUyqQr+ZIANIgooUIZZ\nW4Jkt3DG4GSf/XSteje77T3b6F/RTOABskCPwK4uaD1ax/77D1P2Ito2OJrS\n7EejFo5Pd48/s5Ss5fmRBsbnac/TMtiqRV5U72wILD3cLOWTmZ8K/0kgWpkd\nZ0LYluP4aejkMk8AKOVCWlSnpsE+NM46NCFgWQjoHqEUIINNhzXxwCoMxilM\nJS8kmACZsFWtVJF2mNVWD+1MMsJVXSnMhm7arCGbchAMtynxw0x3HVkC0mqS\ni5+OI+oqzYJBHwwk0cMRcj4vC7erc73nd9oich4rW0QO3fJCvEbLHj4smIb3\n7fW0U3TqIQbpupWGLMmvs0XIkfcZmXg8lNNz+G8hWY+bO9YMQ5IxEp8N37Iz\nao9nDthV8JuebXZ9pBRHI3QbVptCoRHJ99Ukw2RgUgUkZgWeujFwCOC/OP/t\nALtoIUwqWnkCN3LyrafLG8NUcisHfC/Pnocf5meSvt4zTubPh6e0ycQmi382\n1F88\r\n=SuGv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "89275b08977065d98e42ad71fcf223f4ad169f09", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"jest-mock": "^28.0.0-alpha.0", "@jest/types": "^28.0.0-alpha.0", "@types/node": "*", "@jest/fake-timers": "^28.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_28.0.0-alpha.0_1644517060674_0.005862889849210795", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.1": {"name": "@jest/environment", "version": "28.0.0-alpha.1", "license": "MIT", "_id": "@jest/environment@28.0.0-alpha.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "21bed64a63b79530080dd2d516872fde6aae7bcc", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-28.0.0-alpha.1.tgz", "fileCount": 4, "integrity": "sha512-lmtmhOGlMJGZ1Lk7k78R4ZlI2pIbOBAA1o0bF4Akxf5KCGxlJfmGi7yF9mqkg3iyfpo1nv7g4fa6k+NYy/ip2A==", "signatures": [{"sig": "MEYCIQDxHj5IexGFEfyVIoSc/qwmA1mVB+W1L9MJuFrw1T3B2AIhAJrnUcCHDNQZyPWJTCKm9gpECuvBBIlWewZ8qGxMEoaO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12188, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBqqCRA9TVsSAnZWagAANw4P/198yI+f9dNdiyLJpVvm\nRHxrusOHGLsnRolLQs1wOc/7mNHQrjBb5SaM2TgWMMP+TEt7i8Jhtn92+IFP\nb2kWts0v3B9zlbWLsEGQdDjwyEZCh7oxYqiTWd07s+vyo76GcJWtXutV9b83\n46wfqCWtQSnmP7ULDf6SdjQyraoGt2pzauUW0Q/6Ug5DRS6/dOA2uExJxowD\nNalCoepG21Ysuep1xWwMbv4KUguJEHWJ6dYWrWHEsplo4mAreVLtBZ/dwPaE\n4XihYVYMjsmztAWrdxsACyFI3fQHg0Ct4RpAUlsmxPQBq7VwmOjLqErrkh5p\n9WLiBYPlvmGApmfAaYpuIqx0waFhrV7Gw88dIY9QExB0VWdseQ24u+nZFyw4\neFbxrMW9VzY7Z9UwxFcCTYTUTJWejMo6sN5W8v1RbmC9nH38NDlwp70+1wQn\nq1tQ9iU54mNjQUwBnYLqfPnLLEUAPkMPs28Z0EgQBv9esUZzNBPzmS6lxi+B\n4k6MOpTEXxy5xrVTfgptNd3YSNEUHMqFozR1Jjlx785zXerhknWPpyN+Ko2G\nyjPHzFycp3SqkdORHJIlIZe1zleeMlTunktTfDgGH2DZhmyRXSsHalVnTIR2\n6868cvP2Qfq8K+Zyxw7Uo+CZpDqBOo/yB3B+MPnrhaGeljbsy62Vxd3gdKB8\nXQGz\r\n=gKwz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d30164dde1847166fa0faec98d20abffd85e6ffd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"jest-mock": "^28.0.0-alpha.1", "@jest/types": "^28.0.0-alpha.1", "@types/node": "*", "@jest/fake-timers": "^28.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_28.0.0-alpha.1_1644960426405_0.8114729474810127", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.2": {"name": "@jest/environment", "version": "28.0.0-alpha.2", "license": "MIT", "_id": "@jest/environment@28.0.0-alpha.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a275519d227cad47accfa9440d57d79d7dc5c5f4", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-28.0.0-alpha.2.tgz", "fileCount": 4, "integrity": "sha512-j99Yr9tcEUIITf3RRRFpsbRx+ln8oxJ90oW7BPeAY4OICuY3zP7nbansOdq/gvsOc7tWvzvRuHtRTdzkL4MDsg==", "signatures": [{"sig": "MEUCIQDJHIKiLXZlfJAzmVaRjlfqcvTWeqpQPKJnwqGFXJAiYQIgTrNLSmCdWVtJ6YvCjTTqT8twyBcTJVpJ0xLM+iGuXKc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12183, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDT5+CRA9TVsSAnZWagAAVpcP/RCzuPMKDFliNEU8pOR1\nJkaQquaP22Jt2NIRd1N0yqFDEwG5siW5j8TuHAhpCujnlLabCG4gyPC9c5dR\nQQRkRp8Pqxwi5+Kn/Kfyt2OH0q46bteNSpotDn6NLtr5atWVhsDai/7pDTcn\nVDq4KdDGSPHmVjs750tBx4oKmocNa1li+Nsv0rz+1eGNRKtAxY9YDm96oYnn\nR+i9zEpKY9JihuVm09cf2127sp0eZm5AjX3sZzODVOqVjmTbJA75/axweZyi\nWkW6l5T0K6eaCFMDAXL3N2ZhvhSPqZhgFr53j2RtGAqMUVlQnzwV5iT9dEXo\nOAhfuSk1RXJpc3cPQzxuXNrF9zmOtQAjBPutlNgF97lAIr69DnheZIvmAT93\nIEM5v7UNMIpf61y3LZIoWuf9VQQB8FEK5Piv4CsTf68BiaT72CZ3CMAcOwai\nHwUeMETC24qcOMDd2OuP/cGolan1hKpMgKvy//ACgFuuLmoh5RKNKrJhyq0r\no+N0kbFVMbBphJ51Tm2VND1d73VSMAElvd8PydkghQFQDHG8VH9s6sVUeg64\nEzVwRVTzMT/hrgKCY3OxiPesAgZPQBep/z1iOVYph9QFYE/61f/hHxE4RWR5\nGvjNh0gwsq4Xj2qC6g/sIUmHJ/n+Uo7dfwR6AzAmRcPTihPSHRKYYLuPpqYW\nXiA8\r\n=GuDS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "694d6bfea56f9cb49d0c7309cdbfff032da198c2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"jest-mock": "^28.0.0-alpha.2", "@jest/types": "^28.0.0-alpha.2", "@types/node": "*", "@jest/fake-timers": "^28.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_28.0.0-alpha.2_1645035134469_0.9391732678333644", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.3": {"name": "@jest/environment", "version": "28.0.0-alpha.3", "license": "MIT", "_id": "@jest/environment@28.0.0-alpha.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6b5c6307b75ba1a92bd2fde31bb43c049c98783b", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-28.0.0-alpha.3.tgz", "fileCount": 4, "integrity": "sha512-R4DqFkqH3q1NWMLRuIvkBc1lzwDP2HDFlRUSTGbKoUVgDixRH6JRfOMQsaP5anBELnZObPGqKJV76x6ToKDpjA==", "signatures": [{"sig": "MEQCIClM0ETrEfRvkAdyqNgmn3KAG1ZlrYfWVhtUgrBrJFVxAiAgPXBavXmpwZ6MpjvdmFwVpKeKalEMyRGC+7dqY6st/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12183, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDmziACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrqZQ//a1y8dWGaOItfun5K5Ddw6yoqtqphvBmWlLI71sDUxaT3BnH0\r\nz8YUC5/MUJrP1qTY5pKw6Z86C0fmmCqK0OunZLIHm9hpViy845sFPhUMN83H\r\n7YI+pk7RG1G8bT7M+ZCX3vZmdpsFlHrSYMW02JnL0tTLr5Alf6bnJTffUyl1\r\nRPW4Jm/Zc0EfFFM6S2q8bvpEPrT3oNFQ6+MgW2g8MYaaI4DQUryQLYRoZcB+\r\nXL88qiruga8XOm89X5aOoIoGk+szhhMV86YtWT5CQuFGkCvctzrgrsFn8rNZ\r\n62uWJaYQgXS/xnVN43JSytcB/t+u9ojlpO2Iv1gq0lNFiUl4ukcB3+SjYyYe\r\ncggT+Tp2WcgYVjYhkJJ7PLVR8QNe/60MCCViWyHSnQAVQ+KMq/Fe/KzkOjDU\r\nvX4AY7nR0pVf0BdhiEcGeqgNA7yEarfjwlsUwA72ELKLT+N9FTGWmDqpuoOn\r\n2Va5f9i8f6tTrw/+13VyR2ckHcZLX2PpRkvOnYSQDDcjh52ZDIi1hl/gGIpm\r\n+qhf2PCnNXDR9YP8u88dTXuj3T4aCgMv4DTDaVQ/xmexYYO/HUqbTUR7mrHI\r\nN8G71l+FkMW14xOW4aHdTyMtqUs+R03QLCA532M+0uldhIn9Yh7QuN7Iy6TL\r\n41jcx8OHFbScC3UABKT8i79rmLpY1+At98k=\r\n=rRqY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fc30b27bd94bb7ebeaadc72626ebbdba535150d2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"jest-mock": "^28.0.0-alpha.3", "@jest/types": "^28.0.0-alpha.3", "@types/node": "*", "@jest/fake-timers": "^28.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_28.0.0-alpha.3_1645112545853_0.6459488271008009", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.4": {"name": "@jest/environment", "version": "28.0.0-alpha.4", "license": "MIT", "_id": "@jest/environment@28.0.0-alpha.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3e035dedfcf4de803bf1afc659411c81be9d96d2", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-28.0.0-alpha.4.tgz", "fileCount": 4, "integrity": "sha512-jc4YrQyMgDVcJlfzH7e+3Gw75lC35pHTxVOgjico7Azb4Snl7BXkbQHMZ5VywaGEVtd03h84ScExav1ObeNcBQ==", "signatures": [{"sig": "MEQCIBReM8A298zhPcBeAtC4l5WeEeBPEjG/robv1GPFDF3kAiBZOANhdJshIDjvL0JuZQr2LZPWu9T8A/bVMCBMSJ3HKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12041, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFNOGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqDEg/9GJZpBCGVXWDJ7GTZ0c40U8tT4Q12C6YmLVS6F6gULn+xpc4J\r\ntQv8UHgEtupjWLkFDp5UxqwIdBiohEUvlzn/IH+UrBGHrlBd7la0KI1PzXUt\r\nPMu/ItlxkGYn83fQ3n8KHZpejlpY3S12yDfy2w0ar+3SmjKmKR1yN1KDLjWZ\r\nCmOfetUgVcK8oMD5lCnBsJJFI+eVr4IqlKcDGghzWUvALGqQaBQOmNQClm2D\r\n6wlPtb43RzGuUjlH67hA/6SHhednomClNsjfAUNVQbcB2GpIxhVscBzbd/Vi\r\nWkYxCUDOikVWX9FL5a2rDJ34zaC8fgDFMFWn51/VnrX81SFK/blRSViR56ci\r\nkTvR9o+OTlWq9kiWrkaKCT9oEQxL3sl3N3vHf6sfftu8XMFY43iKFXNZwzgi\r\nLDlI42bAdZr420dkZ7nzJMWi4ctqEe4P8k62/2378+OSIO7JuKihvz0KFM8W\r\n59EOzHUuvavWHRCHX3+fY3ITC4RI6GOs+Gvi9DQ03CIngmuIPh5Ob5/lmF2Q\r\npFvzzfOSkiWia1VsIn7J9pkO/aFQABst51p+/xAutzTxglOCav2YlfFi+XYz\r\nIOXeQRpwydye5uRjgSXwZc9E1wzjQnmQDEATVhtDUwDm25ZIkP1OlRx0sA39\r\nmE1IbIGuLKPdbYBzjcBAH6A7i4LiQsigfJw=\r\n=pq9Z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c13dab19491ba6b57c2d703e7d7c4b20189e1e17", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"jest-mock": "^28.0.0-alpha.4", "@jest/types": "^28.0.0-alpha.4", "@types/node": "*", "@jest/fake-timers": "^28.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_28.0.0-alpha.4_1645532037873_0.4823956198098571", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.5": {"name": "@jest/environment", "version": "28.0.0-alpha.5", "license": "MIT", "_id": "@jest/environment@28.0.0-alpha.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b0dc9a20a8d9cb8d5d66659b2ec36defc3bc126a", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-28.0.0-alpha.5.tgz", "fileCount": 4, "integrity": "sha512-JqshiAEiyRGfxOZAwUEqVY7QQd+QKy+3OZhFjzQOWAt0qaRo9HZX22TbbAVknkhBW83fSNqW0X/b/KnqiXCIgA==", "signatures": [{"sig": "MEUCIFaAN5ezr1JfDukaJKfFk3TtrxGSCb8Nfmj0lZzeu5XuAiEA5Pycfune7eYIB2oZZN5C/MwwS0WnUZQVbzBdPUCy9bw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12506, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF/EzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpU9Q/+LnrZsnV8HZZA712Aw1Nb9uxQ+fqt6KeWOnzWf2NTaZbyK1yL\r\n7HqK+6XU9b1u/BxUH4x1jf6Z6gegqi+s7uAjewJOw9EKnDaOZdKEk1FbWuBt\r\nYgQsGbARzX1tQhbujpf6nn1tZkLxMRD2VVwV874hXmC5FeaKqLebhdwlDP0F\r\nepkT64VnG+JRJ+KwrugynzqRvBINx+V3vrQlbfEigH3l/6Mo0LakQyy3Dyjj\r\njRQSVNUBqAMK5SImQEv7NMvWEaeaUGyXdoq+jBhWNOYYL736/eyuU9m4UHiM\r\nD4yBVP05FIvP7q7oRCztuPUOJWOQ5EGrFGR0SmD2nmBN89okGZNMCDfxdNMR\r\n1jPvI3lKLOmF+OC7b+TlQ4Gise2dsV75jce8WSo62FM47mFWHny/qxaQy5rO\r\n5cagePApZ2nstaDFdTPwAtIXbsAQszQjh++cE2/gtGN5nrwTF5f3zyPllOWL\r\n1FZ6Md86gGrscnSn0g3AFvqKKD/XhXSsvRUTSrl+HldSfAn8IJx8DlPm8LFH\r\nBkjB0RYk2QrE9ZRWBFyHvBaz6rZR+MhX9MutlCNC56VznH2tFuSqAe4GXGyb\r\nJ2uGT2bL/TpPnSPSveRNz/0+atbKNHk6F92UNx7YqfAgpkpHwZK0qA2K0d+v\r\nZgkUa8oIamClFslgXmALO4eQnzNtSIQmcTc=\r\n=IXdz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "46fb19b2628bd87676c10730ba19592c30b05478", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"jest-mock": "^28.0.0-alpha.5", "@jest/types": "^28.0.0-alpha.5", "@types/node": "*", "@jest/fake-timers": "^28.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_28.0.0-alpha.5_1645736243297_0.11056552842360023", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.6": {"name": "@jest/environment", "version": "28.0.0-alpha.6", "license": "MIT", "_id": "@jest/environment@28.0.0-alpha.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4f8e189751bf411a1cea5de101113ca8ae26e188", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-28.0.0-alpha.6.tgz", "fileCount": 4, "integrity": "sha512-IEJUFJVZ9QY5j6kuf10tHjToat78QcPt4OI1ub8n3id5jbZj9zzuZu3zdqvlSC8Zap8Jaci0kCjPPmmwRjmKqQ==", "signatures": [{"sig": "MEYCIQDThCFihRrnhIVHGaysV/0fbPyv1bBdPOpFL+W6nmQ9JQIhAJbNeNqRcw5mpTTwz7T4+CVY02MgxtfWcdto05SJhgP9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12506, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHdobACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrx/Q//b3gUDQeR2DPEalwGVhd6Vloir5gZEpTYKqkqSw1/qPwsx3uv\r\nIo26/E5voE4Q9iPo/a4LuZ7ueZcz/9t4D/p/jqPhIg+ZLsJ5PQr1rrIy16+K\r\nj0d6xD5xt8iEllUqyCdPOww0x9ZS9eQYHEnFovUKx21bGhDMccFK0RJgeAmt\r\npuAJeE0/3LWFSZH67OcWtDNHaUW5MUBfxGucLZ2YkUVNji1EuC2b+4XneRA5\r\nyndoQ2WVm2Dw/HKoB8rnEUIRbU3+oz8tM8rBOPcoL4w5aE5oUEhPZ9nRMjO2\r\nuCRzLYz4UKvPrb68+e7qZkXj1GiULqKyZYXO60QbYNVMIfX8Q0Uw460FrsGp\r\neL5Cs0jydwAB+7w3csKABQGzdFCsuY+jyR+81KoGDw6k7cUANH6taTlOss1L\r\nzBi/H5GbwZbGIZdpRuyVp05BUxO918aGogRF6O1clKHTlUBTE9XWUXM+Ov52\r\nHABq1MtyeBoVq9aSO0SA2awVZajiFjuYBVCoCiay+aYAp0YkM8S5qjRw/XEP\r\nGoEtv7lHtP1ZncASW3qyqD3xTP74497vZW6l9F9EBHt2ATiLiSFPBK0Z80aL\r\nJq3v/VR9zi5mbAy0t0hH0+lJuiDz4MWAtkE1wAKVd7YHZMAiHTBfwrlDauFO\r\nVJ9PNNwZ1X4eIUH5wSvwj1IDE9kmqZkngmQ=\r\n=bsCZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6284ada4adb7008f5f8673b1a7b1c789d2e508fb", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"jest-mock": "^28.0.0-alpha.6", "@jest/types": "^28.0.0-alpha.6", "@types/node": "*", "@jest/fake-timers": "^28.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_28.0.0-alpha.6_1646123547537_0.40296698631690053", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.7": {"name": "@jest/environment", "version": "28.0.0-alpha.7", "license": "MIT", "_id": "@jest/environment@28.0.0-alpha.7", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1806876898a016870dba2125593590bc2fde51eb", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-28.0.0-alpha.7.tgz", "fileCount": 4, "integrity": "sha512-w6UhqaSdGzCzHt9F14X6cGsdf40okukCgTANi3998fNuTGaGyD0RDAlPWumSj+OjOFgIWL5CgSaSBao5+tXJXw==", "signatures": [{"sig": "MEUCIQDWFJ4uAe/jVykQCsKNZCc7kOMxVxjm2N2QlFYi0lfaswIgI0tH/rcNpCzYbYwq6uhga/h5kj5QMF6RAre3iaSClKk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12506, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiJIbEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpNXA/9ExYnPQss8SURO0C9DNYdd/3upFPCc/nVxVB9HE0RfDNKyRLW\r\ny6BWi/STowh/CxlY3bLQpPXTW1wkZWS3kJLLIiLnjsPEcJyseOGfWQuSXk9D\r\nwY98828FOdZXWGnuvzVjUxAFWrnGkquY9zk+OZdYfPmy+8Uu0P9maN8enTcl\r\niJvoBYiz+ZG64CLNToEKwCbibytjcO1vtztNlA4HODLFyuyvKauXpyR81Mn0\r\nGUxds/voI4Zi2NrqMWhI2mqwhprDsFP+e5PnPw1rU2E6R5mm94J1gXW9ETYL\r\nIfMAWs0ZX5mRsABnWAW3kxsKk2KVmK6qfb+wFWVbSZH7TSG+DyM57vzYrY2q\r\n6YAX5CXQkKfYvylOk8qApoMM0ywwn2vtdqlHK/Ar1JZLKiMX2KYMtcz6N1zz\r\nN1UGRXcfQfAgJ5Z9ykoIjMTtS/SBxGUYs3Mh514FLDz+BqeDjeTdfjBCR5Cj\r\njXCxsFtMOf98LnJC2+kiNKSXDLVG0NEe58XnkNggzxs2g6h6u1Ixtk78o8bT\r\nen32QcpAq+5lczkm5iRuRJGXVW1l1x7OCuxL6p1Vfg9uB1MGNkdYUDatUnpB\r\noVk842FRFj+uFu9RYjUpEFbKGMrbG3nscve+8wFRiPXOnFS01/KlTtRZ53lM\r\n1WlWbCmB3ncm46GnSZ9A4V+x7b+BHvPeEZo=\r\n=p32x\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "06f58f8ca70abc9c09d554967935b58ce85c48d6", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"jest-mock": "^28.0.0-alpha.7", "@jest/types": "^28.0.0-alpha.7", "@types/node": "*", "@jest/fake-timers": "^28.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_28.0.0-alpha.7_1646560964004_0.7044099109620343", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.8": {"name": "@jest/environment", "version": "28.0.0-alpha.8", "license": "MIT", "_id": "@jest/environment@28.0.0-alpha.8", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "87d1979d54d7f48f029a3fea28c03e0a419adda7", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-28.0.0-alpha.8.tgz", "fileCount": 4, "integrity": "sha512-GWpyWc63uBoWa1q7K4PuC2hciQt5jkGKwrvVkFvGL/QbVcYGZdFbhwFlb7LFiVFRCtXE24cr5fFhX3BaxpMung==", "signatures": [{"sig": "MEUCIDs6K+IjyIZK6MGsk5uZRPBc97dSbk2UWdy8v+0TNRbRAiEAycUXFBmomq1fWztcPnVFHzhl5v3yHquoeg1lpeX2C6E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12895, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTFluACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpurg//UtzOMo9SZErM1FXHTxuGMdbL/1fJJVBz7/3xIhprKiP8iFVn\r\nZDK+/BM12Ud1qJPFK2SlIcUBdP+/6BtwxQnoOeqkl1chnXP5OfqZfumFaT5L\r\n+tornrdkfoVZpZbxKNzjI7WL7yJhUdPHLC7KC+ozaEMKmWUSUv6pouWCjAcd\r\nIEuBcIyEjAyzMSOL5j4ODrEzEBfmtV3/KaTZL/amebAPDvwDBogSVr+AWfRi\r\nJk4A21OHn6J3tRH1TQJYPKe1gHns8SJiUmsXJuX8I+rlg5oCG8VejfLSn4yr\r\nwNAREpwoFE01gH6WyjPp3qdJjWRiretCwWR+H2Rbr0MPNtA7DdM14hem54fK\r\nN+A1dMeogQN5qrOJdtX13NuVoL6AGP5RzH6JJzPfjJgk+hkt+bAbylAXTk+v\r\nLKsd5x6SkBa+br4ry2+VdXDXpsmPG+XynAx3jRRgIiUxy+eVsc7fJHte2du8\r\nxk+CmpNqk/Lg6KcGt2sc//KshImXBAoIrOjvmavV1TVKQPtNZbXb2t4eS5n2\r\nxD3yxWhFk0D6owBdxXJ30QQfghJPjozKtn6pf6iqQR2KHZYpVlw2EdQaL133\r\nTWDJ+cwHmOJfenC9Qedj3WWUduEmJxdG5MInAE84aPJBO6knr3sNEbvTUfF9\r\nc5C3lKKMLnkKpToAW3axvDqkN4kphV1rkoY=\r\n=Kwcs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d915e7df92b220dbe6e124585ba6459838a6c41c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"jest-mock": "^28.0.0-alpha.8", "@jest/types": "^28.0.0-alpha.8", "@types/node": "*", "@jest/fake-timers": "^28.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_28.0.0-alpha.8_1649170798350_0.6241326240022909", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.9": {"name": "@jest/environment", "version": "28.0.0-alpha.9", "license": "MIT", "_id": "@jest/environment@28.0.0-alpha.9", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2685c10fe62f1305609bb0c5570a16d32f170991", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-28.0.0-alpha.9.tgz", "fileCount": 4, "integrity": "sha512-VXjTLUDZ/vdQzn39993Tmu83gd//ZXgkZwoGLRhDoMpWurBiavOxTiNSpFENMc4FASnTYRuC0uMoyLgAhypnmg==", "signatures": [{"sig": "MEUCIEPYPrSsv3Y292AxvPWVL5gIIycnzf1kIvjMtdd1YpH4AiEAwlfPsSmgTh0gKzaplKp4CxtpgMOSiKgTGH5BxHG4Hpg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12895, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiXpYGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpp7w/+KEcmpPNyaEpT1o7U+G6ht+UcAA51y+bH8D8++cGXHMuRCPJO\r\nkE7RflTNZKWYXZ8dpTS/+PZkMrv4QhxDCfoMo7lYBU0UOHktO9cTbJLFhsBp\r\nhnZTXbrklmie1RCWEpTrdGQwb80H8EVqtuk7g+jXqMMZRmHI2JLoT/HL+aZl\r\ndrO7cNnxoHv/5cCc12LkSk9y/LsdF2m2VK75egrtkYvzj8zDFIPbV3Q/HZAr\r\nnsJJYSYvotqZ9uVIvUsNkcuWaORiwSginvqm39FCjCMPzD6A7NL89q+SEiWI\r\nbxwy9heJbQVjli/D9Uhnb3z0x5d0lQsbav76JFoWYVCfirXm2eGEqUpp7eqA\r\nDe7a5Q1oUAZWTyHhp5vveoUZZmmvVRwl09jV5r5Z2oWwCiv4soNN6At6a0JC\r\naeyHOWSfY1jbQUO5EGdBG9xlXjnCI15rWcCwyx+xMhD+w27VPCzRJXWfixvY\r\ndDHPbhrih27Su5FVLYwtes+HWCV2gJr4fjcuX4xG4cICdiVso5dz4hgnF8r9\r\ne60D+fEd/wVImxcqE/3PAZ3tpbYoD28vvgE6OQbmOBYD1BMh/Qz/CTapzmBe\r\ncYDu3zSpowNLSnUbU/kTsNDbhYnNAdb37blejKR09viwNTni1vs6T59y1Bii\r\nJ7pjfcC2u8W3S2YaT9WzdQNyv5mUD/hevlw=\r\n=Iqiq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "7c63f5981eb20d4b89a4c04f3675e0050d8d7887", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"jest-mock": "^28.0.0-alpha.9", "@jest/types": "^28.0.0-alpha.9", "@types/node": "*", "@jest/fake-timers": "^28.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_28.0.0-alpha.9_1650365957925_0.4675691490796363", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.11": {"name": "@jest/environment", "version": "28.0.0-alpha.11", "license": "MIT", "_id": "@jest/environment@28.0.0-alpha.11", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3dc1012ef417f37705b442ec88e05e29c6d9d249", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-28.0.0-alpha.11.tgz", "fileCount": 4, "integrity": "sha512-GbBx4cZJAlw+dyomnyNUD0PXsBPR5L9SGhFWCQWTowsAJFrf5GiDBS+Z19a+FR/ifq0msp4jw+Nqr9s6acr7nw==", "signatures": [{"sig": "MEQCIDnIItg38S/frmgmksqXoPZtSCP9AqKooNDhp+EtyPDYAiAnJUMhJpfH2jU+BAUoGAbMGxEKkA/DV7dOjotTbVjUJg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12974, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiYAsRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqCmg//b8IvHjKbuQlzqgw6WSbI7dwtWjuBTGyOG996+3hI27p7Ywff\r\n8jPcXmg9vifvct5ea7qyYyoUizvm/AbXp4yOUNKG4oDYpoT1qZg84qRr8JBN\r\ndEoMYRiS7srF+TuJK1ed9g+TfkXj2A6Vj9Vimj4OI3fCfKHOcc/k5RxVnkS8\r\n8BT+V4Zu0fIrQKjMmuRvJW36pRj8XA1Sxc9zuFA8RxtHgu28u8X9jtQVgRQu\r\nttKlGBJz1YJCKUVt7gkqUFTTiKKmWPP0gh8DlbECgcXHIduU34Sjz30wWv+U\r\nDp4gbLJ08q4U9jyrAYFxYMnf3WDpaUHwVAnGl0gENDO07jOtPwQxJk3n3/AY\r\ncCz5QaJSQSg1fiC3SX7KE+q1xvBy6a7gmcXlxBeIza2Q1t7EUI6Yu9WYpLeQ\r\nYuGP7yMErxBHsJVdREq9lzJVESWdrnVWch6gNR9YXU22ve16fp+YgS3lC/Ya\r\nyJyMBWeOoJpazpVEAXAS1YFEOlK7y8QB4IfqJoqUAQoPIT0QrQ9bcmo+JwEE\r\nXe6BN5ANXgk1F5w06+MfMn4koFHyW2sFkovmQSEYbp7ybjUn1iWCzfdsz3Nb\r\n7AfXWWgLSaMInwfmAixaqdn2r+T4ZeI+MKwt+YU16oetq2HwivARMSnz3/Rg\r\nlEpYxpKJfq9WIR+S0EgRfbOQ6LyrL/CLM7M=\r\n=h68Y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "8b4b78759d255746f11e396efe7d06ac93dbd05b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"jest-mock": "^28.0.0-alpha.9", "@jest/types": "^28.0.0-alpha.9", "@types/node": "*", "@jest/fake-timers": "^28.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_28.0.0-alpha.11_1650461457774_0.6751648980724043", "host": "s3://npm-registry-packages"}}, "28.0.0": {"name": "@jest/environment", "version": "28.0.0", "license": "MIT", "_id": "@jest/environment@28.0.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "591a856a3ddaf5c89b1be5efbf8e470e83aace59", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-28.0.0.tgz", "fileCount": 4, "integrity": "sha512-4JW8g0UokMK8fHCxtg5N1xowzQPQknHYrcQhh/xtC2FuRN5nC1P1Utxt7FERxT94gxTbAkE9PGILVgFXQxEU2g==", "signatures": [{"sig": "MEYCIQCUwhxhX2A9QP26vQsiOoVq8hTd291pI8OnH+cl3ghe1wIhAJOyvSSv1MHN6sEmCBxWexPBX3dQyeq9BPQ5Va8VIBXX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13209, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZo8tACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpNpw/+LualvL0UfYhpVQlwBS2Sjea5d79y790apb73MAviInPZ6I3Z\r\nfbgal6CKjpCAjQb71XYJ8BC3QSsovUc6dYn+6a2EFA3k5BGEwflMR8r9kDh5\r\nARyAYfzParMgW1FkTCgHnqVAwNs4KeUYBEwHnj3Kdp+kXRcT4p4cYyH8C3yJ\r\nfk1sN5iqNfw+y5PoM6ZZud3/Nfd6forHe/P5hRo0veKZIiIiePdx1d1QmXGr\r\n5r2rnpnabGhpwvXi9R4aa+B55ax7qMOuUrojxjud9e70LgqeoQOIU4ih5ELU\r\nI3X5G/j72EHuhI6/uwvpeF3iwWa4cbzOMFwb8ULxhzADpOdw/YZCHwnEUt8Z\r\nc3Ktt6puVyCtQs8wSsecZZKLqOO9YPKqtbjCQD11fuOLf32LRYmBYuLJaCmp\r\nyOjH6NMwul68HVo1UFJNr7wB1Ak+6ka7Jx8rAMJimqfWRdNVeCB7j/5QRVxb\r\n96QWP4ldaGRaRScYw3THw1wk5aZ6mDYUBHE3P7DXRChJC7fyjR9re4pu7iew\r\naBnqmcdOiLCYtbz2rEvxEJIfQiFVpfbBmjFzFl2sY4Ws3+NG4DtQpviTEWIq\r\nEmVZy/VwEoiiZBPaAWav36H1B31V2CrUTVACuP6E4V0wj0Bxlvz4k9YstH90\r\nZpMIWldL7fx7K19OKF4xM/2NmjI4pGaIBGE=\r\n=TrF+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "8f9b812faf8e4d241d560a8574f0c6ed20a89365", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"jest-mock": "^28.0.0", "@jest/types": "^28.0.0", "@types/node": "*", "@jest/fake-timers": "^28.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_28.0.0_1650888492924_0.9983637395878566", "host": "s3://npm-registry-packages"}}, "28.0.1": {"name": "@jest/environment", "version": "28.0.1", "license": "MIT", "_id": "@jest/environment@28.0.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a50cefc75329b7f499b65694fc405509bd3a8ec9", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-28.0.1.tgz", "fileCount": 4, "integrity": "sha512-PuN3TBNFSUKNgEgFgJxb15/GOyhXc46wbyCobUcf8ijUgteEmVXD4FfUZpe5QXg/bpmydufzx/02BRlUfkM44Q==", "signatures": [{"sig": "MEYCIQCtu2SJQHIZ8jkRcKAIyz7Sg6zXdSyj76sCfLaovLlWjwIhAK5AFCVicldnvc4jDqscTyI8ZbjBEY8UioIMS4qa978W", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13209, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZ8NDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpmSg//WhAVQqRRyVkdhpwKN/6O7EpeANQMExAFNj2y79igFaF1rbn2\r\nKzm4pmS0dFhKo/ooKE2tqSvcRzhJQBBg0qkGDgJAhjs6pz07Qu1w2RpPRbBj\r\nDVRVSSKLpsRPu4rcLVSh6GtYyCh8RPzhTdEAZEu9IroZHpO0Dt3e32eEzLAt\r\nQ/Sa2wUOAXjccby8PuHfeAZkpg3Ny4QVEbdtuPqaqcRgaB/q3fTEGsueP2Qi\r\nmkMSc0NCBMkv0WvGSW2BHPEy5MCezE6JrH4Nxv910NFrj0hA5SsA2yB2sRpt\r\n713rbg0nsKKL4RpL2KPGXA+KOcljMM7XF26wNRwKF4juFq1P97MQq8gnJn2S\r\nhQgr7n87/0LgAtfWADZxCz3SrUSzjxfiDttv99axUdRgnKb5vz7h7rncs50Z\r\n0udBO5Bm7Juxu9M7TPTzepp1pBi4hSnD3hkRwjb5VuDvAinedQ9z9tMI3IPg\r\nTJcQQfx1jYd3BG8A+dwSEqFFn7IwqhULnm/T4AkSVLvq2fM6ch/oZEBIsJmJ\r\nzmgxyVqXWVNw6aX4aJBVenozMs0NX/kU0/GFK/CC+HHL5Lv2COcjoxM1u+Sn\r\nbMVxm+KXxXD+0U8Bgrg4X32PdBaWys5+3mTDV39JhtR3/OEqBwtdVVmwkMpu\r\nUE+jTRW5KYUfu59+TYc7LGPwJ3mSKujl9D4=\r\n=93wG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0a08639e4299f07becf1020a761adfec83536018", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"jest-mock": "^28.0.1", "@jest/types": "^28.0.1", "@types/node": "*", "@jest/fake-timers": "^28.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_28.0.1_1650967363192_0.6995706140214666", "host": "s3://npm-registry-packages"}}, "28.0.2": {"name": "@jest/environment", "version": "28.0.2", "license": "MIT", "_id": "@jest/environment@28.0.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a865949d876b2d364b979bbc0a46338ffd23de26", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-28.0.2.tgz", "fileCount": 4, "integrity": "sha512-IvI7dEfqVEffDYlw9FQfVBt6kXt/OI38V7QUIur0ulOQgzpKYJDVvLzj4B1TVmHWTGW5tcnJdlZ3hqzV6/I9Qg==", "signatures": [{"sig": "MEYCIQDJ/NodKM+LMq3zyltg66LUJUklUhwMuUDS4qPto71ZsQIhALgGY8GGu2v33C4G5x9muYfSQ3//7bZtbErabwU4ZbU4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13209, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaPRFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpMhg//eYvoWBALaZBHr6lkPniK3L9HK9ta4X6KtFteGcRqu/A66tPv\r\nwxEs1Cz7o38A5jvDBR1qb05kPfZDuKCb7IvJvE0udfWAOqer0KVmudipUUGh\r\nA+Kq1xlGflXel5qWTMLog1Tj49T5AbhvFqBFVP7YR+rwJgl6NPp4HYxeZiGA\r\nAaUmHvXeoOUnqfdgRBJznN3rvXTvmBiCKnfoCENQZ9pLLdA6uySj1Sceym1n\r\nWuXTITpzo9f0u5HfL/JUBr+JjEZAZQzUsIir55ZdlWGMhVCIfAXs+eaNS6Ap\r\nOiuDfhhlDGZjJxafX6XIoQSPts0dHxRBzm6qNt2nN3JfdugEhZa0/Zm5ujLD\r\n0MjnxdPBxQ+DepPUIOjN0HZm5Iui34/l6pgkLzgpS39KLd4rdTOypFujJ6K3\r\nbpYKRgU2HCLqo50TYWaJ8TgD3mZZ5pbx83xKBpvaa+ReKmW05K9BTHcpdVG6\r\nuJE0ONsUXd2UQRT+3Tafk57mmTY6qXU2Pu3Q6Nt9zjnHhhjZs7FWK5L9exLk\r\nsXZY6zVSI4mtdbuTI1z6NLIZHA7LzMh6ezudHerScELucaturQY1CI7S9iho\r\nsz5irNQCjt7nwLdsjqkWmPRzmih78rzdTKWiu+egoUV4KDsTygjc0twAT9we\r\nMhOCEP9JGPCKQn+39/zsp0Mp3JtWZotv66A=\r\n=4kGs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "279ee6658d763f024d51f340fab6a37c17d94502", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"jest-mock": "^28.0.2", "@jest/types": "^28.0.2", "@types/node": "*", "@jest/fake-timers": "^28.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_28.0.2_1651045445710_0.26797628040783783", "host": "s3://npm-registry-packages"}}, "28.1.0": {"name": "@jest/environment", "version": "28.1.0", "license": "MIT", "_id": "@jest/environment@28.1.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "dedf7d59ec341b9292fcf459fd0ed819eb2e228a", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-28.1.0.tgz", "fileCount": 4, "integrity": "sha512-S44WGSxkRngzHslhV6RoAExekfF7Qhwa6R5+IYFa81mpcj0YgdBnRSmvHe3SNwOt64yXaE5GG8Y2xM28ii5ssA==", "signatures": [{"sig": "MEYCIQCn0JZ710KK8DF4xtRNSTXOjx+mggRpifrdVn1LXaU/8QIhAOOCdiGQ62XAzME3lLCpwuBakG76+pT8u62vydZjO0V2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13209, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidP0YACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpp5xAAnaRGhZiBJT3IdsU/qb8+kT1i7E9oZHd98SxZYM+PdKTSpwxZ\r\npT9o+pgIPyaxNv9Ugnn+EVJQATYEeQXeBLOvn15CCMpSJsUhPqBeEz2t9fC1\r\nwW6S5nBNhwCkax09yjwCKDAmfPcEMgXhXmTtlP+0M5xph0aQc6s8ooe86uzE\r\nwmHoHtfYQODAXWrWLtEy/3Zh5+zYfvMpf/md3TLxWxLpoIKCDPAtHqm0MOe4\r\nzefvudjGFsxb/w11MCYhm2OFv8bDPVO7CRqxWveW0iJVHQV01K7IM+R9BV2n\r\ngl/sGO2aFZO7clfcw4vnIwr514RI3nMo92tiVHZZ/sBIc1PF7CWqYy+p59tS\r\nngg5tQwJVbRGn1VWEW4453HaaafXCXFLkElOlThYU8aQeQ8NUUSQEOiS6zLc\r\n99S3F40FlzKpCpv2U6ijnpx56p+Fc1iKu1mkj5V9Ig15Pe/MQifRzcFubkF5\r\nWylDGjO2hbvEP+bRgpvZVFaf8e6suati2dqUVlOYFK5uCZgH/p+UcHEDQUn+\r\nbG0QTyeTbwH5QFx7fyMZJq4eM55D6yfqwmGFCcKd9QljsUvMKheTbJzXc8Vl\r\nFCgFS7Wu9v+561kE6UMQJlEKOWpgOIzFdh9Y0WZpfJ1zjQ8eB8QofC6ExQRa\r\nicAuwBB1KYUdkpHCu8ETSp7PhUwj8RiHUIo=\r\n=BGuV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f5db241312f46528389e55c38221e6b6968622cf", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"jest-mock": "^28.1.0", "@jest/types": "^28.1.0", "@types/node": "*", "@jest/fake-timers": "^28.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_28.1.0_1651834136830_0.6212055504541696", "host": "s3://npm-registry-packages"}}, "28.1.1": {"name": "@jest/environment", "version": "28.1.1", "license": "MIT", "_id": "@jest/environment@28.1.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c4cbf85283278d768f816ebd1a258ea6f9e39d4f", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-28.1.1.tgz", "fileCount": 4, "integrity": "sha512-9auVQ2GzQ7nrU+lAr8KyY838YahElTX9HVjbQPPS2XjlxQ+na18G113OoBhyBGBtD6ZnO/SrUy5WR8EzOj1/Uw==", "signatures": [{"sig": "MEUCIFWDnPVDXjvCi3/bY31/48kB2EIBT3z3zkIwX8FENJRUAiEA8srVyo3h55H64aDTbNpqQNeapqmrELvUQtuS60C0bU8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13209, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJinuujACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrkGQ/+MxNjCm8cpCqorrlnFF/DKidmsH81x4DM4fTpLLf54Aw9uciW\r\nw8RnFGpxSM5zbaatukADgW/ub7gdHow3nXYWrcaIifQSXIWmDH9ax7kJvj1X\r\ncuK15WsJpRZwe613282R99A6JlIxz8uwhlI1JjViCzdoDctYSD6uCtHRHcv6\r\nuuVx9CbgOWG33ERD6m52sJumFazKXZPxbBP9UYcobijczEN5gXFQEtLJjPVi\r\n9136Rg9KnRXa+Jr1lXY58TGVQ/5+mU5Ayrg4vWGgK4AyIMAxflxTO1HA2ifD\r\n9b+5xmy1btvd+pUkuJEpARu1J2dQqIeyNrkvlFxyK5BjmyrZjibOgpTw7llX\r\nEzghZ+R52DpRGr51R0dI8Z6P6/7zzVofOSkmr3zxaWKLAzdS1uHk4ub4xhzo\r\nGCkF74mXl1cdIA2L+NrTQCzJe5oChOz6BowuPwv548RxkSqmGODJDRi5V//h\r\npdHPeC5hw7U89IWXnbxhA8iUacZTu+4235AopYiqYzsV+4YqZGyUkHjxlDFQ\r\nKQFE6pPdKlhwGYx6ZLlSx0YK1sS/pamAq8Qjoood5NGsE7T62sJfJ7si1N6W\r\nZA0wHaatdghAKOzpz4Tpx8LN9ahu+YIMb4iQQDsldpQDOdXSK37g5zqx+PEn\r\ny8eDnw8uLqr+oFl2RImhpCMNTQv2aBLTWkM=\r\n=4/kO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "eb954f8874960920ac50a8f976bb333fbb06ada9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"jest-mock": "^28.1.1", "@jest/types": "^28.1.1", "@types/node": "*", "@jest/fake-timers": "^28.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_28.1.1_1654582179118_0.9602371233579223", "host": "s3://npm-registry-packages"}}, "28.1.2": {"name": "@jest/environment", "version": "28.1.2", "license": "MIT", "_id": "@jest/environment@28.1.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "94a052c0c5f9f8c8e6d13ea6da78dbc5d7d9b85b", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-28.1.2.tgz", "fileCount": 4, "integrity": "sha512-I0CR1RUMmOzd0tRpz10oUfaChBWs+/Hrvn5xYhMEF/ZqrDaaeHwS8yDBqEWCrEnkH2g+WE/6g90oBv3nKpcm8Q==", "signatures": [{"sig": "MEQCIF6CuBQKdy6f2ZE4gSKiDb/JgkxjxqsNKDFo2GU6dv62AiAeMMflWBFzBJtgjB1soekPexMiqICRK6Wo7eDjgijWGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13209, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivCqWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqzcg/+LI96ggF3Vtd3un3pK9r0mHZn4Y6PdL7uQVY/kg3fpbv5peJM\r\nMK2BoK5Im1q3K/dEBKK27POMkUdX31gEluQf8ub4R5LA/oQBAn/XzaJ21/q8\r\nEP5YFyJDRX3EGTkNc8H4v49aONNB2u231/MP1Cwb9bXeRGj58nxeWHNGBLb+\r\n7+LJY3LUBrgGpGq2XWkTf2yLxtsWNncEHIHu7AHzn8qa/S8XcuCVJI+icsHK\r\nRmoz9EDR4ronhZRStUL5tLSy/+9c2IUuCS0SJO+xXYMerCYR5XilDqnaBadb\r\na7BX131PuHzHiRBZvhhu4bHo9bWQBCyH5YMy/gysKsSqqxoBwjaaqmEsj2OZ\r\nscAGMN3oqbu5tMRgWQVjwrkO86G6Xm9wYZPkXoEdtqSCO0YCfo+1SKhvrwxE\r\nxABPdbg8Dh8LZwCtXRzpMY/h7sG4XpqhLzxLacJivpPChF/2V15ER4SA7s93\r\nAaB7+VSCkYzhNuJnCHCQRayWbnOwZg8zVsCCirL3MuTbU8pGbCpZtjWXdlfT\r\n4N+uE2OfeM1qd6D6Ql4FcMO11pYOsl84KcyWocNLZ3A6IwWitwNt27jvp/Mk\r\nGa8ppMfKdueLfStAeowk8+cYgbc8x/f/lVrJgVWYri+/9XDwiyvnyIEW4Sun\r\nuUpnTPNxgPu0iUrHDzzQYAlUIjo0SdGqhTE=\r\n=YYpT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "789965efec4253fc54ceb3539711b3a3a6604d94", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"jest-mock": "^28.1.1", "@jest/types": "^28.1.1", "@types/node": "*", "@jest/fake-timers": "^28.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_28.1.2_1656498838040_0.6033551292343331", "host": "s3://npm-registry-packages"}}, "28.1.3": {"name": "@jest/environment", "version": "28.1.3", "license": "MIT", "_id": "@jest/environment@28.1.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "abed43a6b040a4c24fdcb69eab1f97589b2d663e", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-28.1.3.tgz", "fileCount": 4, "integrity": "sha512-1bf40cMFTEkKyEf585R9Iz1WayDjHoHqvts0XFYEqyKM3cFWDpeMoqKKTAF9LSYQModPUlh8FKptoM2YcMWAXA==", "signatures": [{"sig": "MEQCICjzzJzQKv1IK0cYq78ZNgePeSflzXPbSuljOZplznJBAiBi0RwamQbg8qqQ9bY8ZeFv9yjer0iFNt2RQmWYDmKxAg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13209, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiztLPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoBDRAAi6F4JArboMISDbUz7vc87bNaOr/EzFlmu7aF0/Kl2T3coV84\r\nNcffyu1AaEGxxUdE8TqStMooEE02ErpwnH4RgzZGstkUEhD7sH2B2KaoLwaV\r\nVcugp78p0EfogWYgGi0V68Am285Ij4Xhd4MQQtcwFNtsO/ov25xNQit/wv7G\r\nKmH6H1e+dXejfMtTmD5WgehQocP81twqEX4ztBNe9H23+A40SHVqpkdJQ+bf\r\npROsnEn0Tcujw3VUSpRrBJhVONcAXWgO6PHxfM2TmKdProJhg+bXA4IM9DNb\r\nC9Tdf67PR2x/8ftjHuJWAaj7ahLCdH0HXgfuxSUdfOQzFPqzuN9sGOQGe47C\r\nR0HbELQNAHrmb32LxnteFeobcdgk5yb8XCaSIgL5JP14uvxICzKXTkJd/lqY\r\nmdNHe360EeDknuehg3vKIXubwdyv1ogL4VuiitVIo7WwiPM0LBLth3sOGabs\r\n9PxOe0ZxR5cX43MBbvkEfpb34ifvh9QcFfatxDqTXVBV4HoF1TpmCee26JIv\r\nX0lc+rBY4Zfg1rMz52e976PUvQe5SiMSsvfmWnTXEpZopPxxGj2Xca9Ssqnr\r\nbNtJ7V7lZjWogK+d5dONY4Z40Ig3OeyrOLJEBPQ+rMhYnw1vPeBP2L+pwEpY\r\nXwOXpbJS/zrrFyQ7fYEogK/UWgjloBE9evM=\r\n=wjjQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "2cce069800dab3fc8ca7c469b32d2e2b2f7e2bb1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"jest-mock": "^28.1.3", "@jest/types": "^28.1.3", "@types/node": "*", "@jest/fake-timers": "^28.1.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_28.1.3_1657721551771_0.36620569036277106", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.0": {"name": "@jest/environment", "version": "29.0.0-alpha.0", "license": "MIT", "_id": "@jest/environment@29.0.0-alpha.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c5b2ba97e3020f3a85225b4cb62c377f1fd7ef97", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-29.0.0-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-CbSMkJpSc9rheIzNAMT4Zq7ZBGDmJwMrBSHN45RQ4QrYD7pq6FFuw8KMKxXnQT1r6ow5qRcWOZOxEHsGDmckXw==", "signatures": [{"sig": "MEUCIHH97cMwm5SNVU6ipawwHg++e9fb9MLjkhlvq9tr+h5qAiEAp6W57C4otZYd7RXZJzjgyhVb3PgV7iA+7VQm9MeMU/4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13229, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1IgOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqpdw//Q6TrbveuIw6VFihcz0+py7unO384zkWqP46ZiwLhN+0Etxrc\r\nHIzVp28IYw8THsZ/ZNp1vqERhCrABSp26K5ugizKDVRVx2ol9zNFT4Ysm+Ln\r\nRDzExvd+cixZpgNOgMWlFZA5mp7E6mgazVHOEa3MsphZ8CX4k+OUWiCR78BG\r\nXnpMc6AzK1tpZ/DN8R/7Jr+lQtgNmC86uVnN73sp2O6DlxmaT3fer7u8QzRJ\r\nV9pKyZtb2FSeOg+s8WAdL+VB0bRaA8fCZTFi2Hb7n+pg5RJb9N5kt5DeXgSP\r\n61NkmzaDzn6jBCxZJNEAkNkilTO8y5RVxmdSu9xuxy0GdVZs80kCTO1W1tKV\r\npO19bpKefrOdrQTjcKkcGsCJRSt0ZH0jQ2QRExUmI5HFF12PzHNw56cCSG4Q\r\npr6mav34l4TSvZ0ge70tlG/mDUiUDglSR0nXMDlYNn2sirXgDElGlXWvWlHz\r\nzj95EWqu5OZvB5DN06X0Qj8v2RB79RPIdTXzC+uW5Aoi0xUDOAuIpRWkCdYE\r\nKFRXZY5GbTfIlw3mB/PZ2BvUqRr6EcqE1n2fgzbMpEJAJWsTRLswY8MngChH\r\nb8ps4XRTaeDR273MKmg0V3NcVCpZj5o1zKUaT8GLwCqeFkzTPOhMHAyTnXTR\r\nNGFGA2BABRwKfkH2NFRB/X572vHFxmAgxJ4=\r\n=d3D9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6862afb00307b52f32eedee977a9b3041355f184", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"jest-mock": "^29.0.0-alpha.0", "@jest/types": "^29.0.0-alpha.0", "@types/node": "*", "@jest/fake-timers": "^29.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_29.0.0-alpha.0_1658095630258_0.15136342651731516", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.1": {"name": "@jest/environment", "version": "29.0.0-alpha.1", "license": "MIT", "_id": "@jest/environment@29.0.0-alpha.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "39967c25cd71c0dd105d58ddf67784eefe7b8960", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-29.0.0-alpha.1.tgz", "fileCount": 4, "integrity": "sha512-Jf6Z3jFqGFLoI/9DJxhK1wStGKlfro4PBEv44qNlTy8mRX062Nr4TmdgLldeR/mGCvxeeMH7mzSZ0kvO+xlopQ==", "signatures": [{"sig": "MEUCICroXBa8ad4vbazC6r44hvX32QuXAVOsJSz4qyXI/TxBAiEA0q0kZjhbcEsUJI+u5l+RZU5Y/ohXF796Pi5Un7SW8A0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13229, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi64IEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJRg//aoi+aBiQF0WGy4Bhb2I0HZYujBO9vBZqgbYfr+AIOhMVIVlX\r\n0crI2WF5cuzafGCOlMELYZWzRQhdXBtrp3f/AgBu1L9c1k2JPw/H7vucGWd4\r\n8/9rvuhtoJ8NOWnwChknrpOe6EKkRAIxoGwssXRUiVeXhIoW0oiYRacP4qz+\r\nLQ09zNF/lNvibqSvCINJHY2xJXXJOIbBW+rym15M2TnCV7LHVG3lCRJpo+T3\r\nNnej7jI0aaRz8uY+hfGT7n3MXfFXVRH181+DvFT6dWxhspupuT+KfJ1OgPx6\r\nQB6tOLLF8GKsnC32zaFLWxjyTq5TxSezQe/46uncD3mSKpTf25Bgb+4y2v07\r\n/szalfIpIiiqmdF30GK7hCceDUii639MRJZCGrN01Y+7FLFWWkEmO06T9ENI\r\nOS/ROwHwRwHgVLQTTs129UBzJZFMX3nVbOi+K3ofC18zOTyADqVP0j5h4b4/\r\nUL5iPgOufTj5WFRt1qFB6RC+YM3G5nZV49lkIZQuJU3DsMi0PliAiNvnFUR1\r\nkC56+1OV519MiY4l+tL1OF/w1ksofsyUulZjz+Y3yX/s/528UIEvjzxEqK4P\r\no2TFZ/Q4dSyYUkrw0B8HiWe5+6WVs7HzLN/XcbssI9ETvfcTPcnUPVltKVzF\r\nsz4HYgnrW2w8H7N+/65UIPf3UNPUquKybBM=\r\n=XrYC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "10f1e7f52d9f876e6fb7f20c1903fdcddd8db8b1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"jest-mock": "^29.0.0-alpha.0", "@jest/types": "^29.0.0-alpha.0", "@types/node": "*", "@jest/fake-timers": "^29.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_29.0.0-alpha.1_1659601412419_0.8485228987230278", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.3": {"name": "@jest/environment", "version": "29.0.0-alpha.3", "license": "MIT", "_id": "@jest/environment@29.0.0-alpha.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a436b75d104a6ddb87ed80bc423cf851d44a0774", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-29.0.0-alpha.3.tgz", "fileCount": 4, "integrity": "sha512-Mts6zTR6cSled3HvoHGxCDjevlzFC5d96NOvqa9nANRI8203uw2Td9+ojVhG6aW66DSkxQTkMezYDVxf67E3ZQ==", "signatures": [{"sig": "MEQCIFA4ANPKAvoRjBlThr4pXoz8oMBcfSdHY6qVZIRG0WowAiArdAqOheGWoxEAt82xvAeX6dy8VbljYTv4NeqY3EPyIg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13229, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi78ESACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpyow//Vfki7Uk9YodFwCDZaYDU+HLgLDqLcju/L4BdG59Z10nbMQjF\r\nSV7q8k7aAsXeWPqTK7PZyqnBYTRJ45I1v5r0HMPx2tbbSccydVZCqQL9pPyt\r\nJlYaP2k2cHSVM9LSI4mOtmLep/Mqj4wEdY3d784ddtwl+HldSIFNCLY8EWRX\r\n2Ze5IpUJ79ZzUOtSZlwyGfHDFNPGYd/9iYcxNRjoh/rAZ1lL/8/p77KpV+yk\r\nLHSKqwTt5Eg1pJkQMcf/IAy8OwwKqAGps0pSSPWmX7gNO5GZrR2NSrLomOBr\r\n/vxVGALBFdpG4+cp0813JisenlcJP7USCrUV2n4qoTEvVCGURBi6cDy/2iZ+\r\n5HjsZyFJvx82DGqznmhP+WzwGz/K7TGEXoqRNDGpHp1jKDBgDKLABrGG900A\r\n3V6dxb9IThoiMqP6JKmRIHTr5rgXVhh6fl6UPMNYRm71lZE6vjlzq4Uyh72B\r\nExDomkv/yM/PoXsS79FtM+Aw0SoY3xk/7wXYQpfzps48EMVUuKpQ2bmclAty\r\nCuwy0wmSulMS1iwusbayLBBSx6/7/qv7gZBV9bU8LAr7NZkHrMzI6BwskEgr\r\nGpKv35Uj18dHL0IvvU4CLjJIyqNCgCmGY0lyd6JCw/WQhw0Y4GjGB/CKb1oZ\r\nFbsW8+2KruRD9qXIMTMpjzacJyXlMdVf+YA=\r\n=7ZKo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "09981873c55442e5e494d42012f518b7d3d41fbd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"jest-mock": "^29.0.0-alpha.3", "@jest/types": "^29.0.0-alpha.3", "@types/node": "*", "@jest/fake-timers": "^29.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_29.0.0-alpha.3_1659879698507_0.11675056973882492", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.4": {"name": "@jest/environment", "version": "29.0.0-alpha.4", "license": "MIT", "_id": "@jest/environment@29.0.0-alpha.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "41bd9c9e5ef4036fcb1be857b481018070767129", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-29.0.0-alpha.4.tgz", "fileCount": 4, "integrity": "sha512-RhyjSuJgnJnRuVGHn/c1/U+l5zFDaWWFlnm+L25d24/+MEu0aJoHUEhTBBeJ9aarA0GyFVqNyKgDs9YzDUMyoA==", "signatures": [{"sig": "MEUCIBgO/4J76S0FsS0ONdrUxwT82ZIDmUBCiL27JmbgFPMXAiEAnu02Wqv595E8rECPZiBJ8479VL/ZXBuVxToKDXxOK8A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13229, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi8QohACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrTXQ//QHlcJVgh5Yu95nXFK55mMXTElT9isGJJ90fNCgQM9Gn57+c+\r\nZFnprOcdJ0TiQc+0+9EPMVzji/7rWIjBhcWrD422Eghpuqr13EmjB8rQO8TN\r\nADWDbVpwX5NiWJfypuX35S/u90oplVu6Gk3T8j3eLP5Brecrh21xz+Sk9O5R\r\nXnNjpnflVVqXhhPmCGH25rBaUEGmnSEwFpRLUQjWt9us6bDUpn2L4a51YFPc\r\nk5LwXjs6Uu1Hl37DP8l7wY+TfVRhogU5ZID6r0H5uQy4MiMOOAdpdbgVXlpm\r\n0YQkfnP0Q9elCHvhzRTTeMHps0i7UZ0YKykiKkfRgUNMzGim3NJnfsUQITrd\r\n5j1qv7ZENhASrSNlY1Xc9HPfWb3/Ge2VrBnmPqW++csXCyyrPlw0cmZN0tiz\r\nYsBl6yxbnokP221vc2kNkTIJjQ+nRt1LsINteLKqv7Wzs7KJXM++FUSlEAjM\r\naHXxmYn2sv7lZa2iAMYKlfA2vByPdNjAhfaB7ouFAb6alKhZ1UKUjzQkOCVB\r\n4DLgwsmsKCtqMFoCFVSjnYs49xV9CnJomDcoDzzEsRojyS67mA5NI8PBwrOU\r\n2JSmrE4k6uIcAfNdOppHK8yiQVW8Adb092fKx96t79mE60P7Kk5o9ZBD6+az\r\nnK6LeE5HRqM2W5A8G2sq+zYK80d/lSxPLCQ=\r\n=h5MY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "98a833bd4bc0bdcfcee5d4f04c2833400c4e2933", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"jest-mock": "^29.0.0-alpha.4", "@jest/types": "^29.0.0-alpha.4", "@types/node": "*", "@jest/fake-timers": "^29.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_29.0.0-alpha.4_1659963937286_0.9643779264879924", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.6": {"name": "@jest/environment", "version": "29.0.0-alpha.6", "license": "MIT", "_id": "@jest/environment@29.0.0-alpha.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3ad7ed6f8db666ce6cd081ade10df5b899b21a22", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-29.0.0-alpha.6.tgz", "fileCount": 4, "integrity": "sha512-aiQyEtplfXsDW/oDqPRnzj1Bm0jn9FNMZfw2zWd94E4gFHXECEnO9vRf8I4Pan+mCpUz8MqW1K05757vExkRZQ==", "signatures": [{"sig": "MEUCIQDC1hReg4F4n4ZCtacB9g8ZP+HF9TkeVNjvw1BGdI89rwIgatXs1tYu453+jDaKwnoN8CPcJ0ZtRbmh5LEpjg5JlFc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13353, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/5bfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoMUA/6ArPsZskrQG+dgT4AyV/WfkQitCeJo2P7hPu9qehdycZ6fvDr\r\nHVzAP1kdp1bnbKa4ZKaaueCn1XxLsvbphIE1/ItZJE0yX4DewSmPw7cdkntJ\r\nc+pGp/EzTIU1CbQ3tEJ7w4S8ZgliIcko9Kfo+Cc3/EYEwjg5N/yRvk97GFOd\r\nl0GRKII9zwUjv9knNx14ewjrksm2OMBPH9+eB9loa8JKT4AZV2A6RpBqqS63\r\nBuT7COQvzxc0t043JP3DXAkbJlu5lcCgnV28SP1g9iZ/P77Hk3PmF5ksisWb\r\n15ikRbG9GJrjK66ndcR2zpHjOEKnkfL+3PdjKtzgsbEpK7/h+KqigwIr8FPJ\r\nVxs+Rg7rmocPBD88/8+SneFJ3Kx53IWCkTv9Xqq3gZR8bmzwk0nu3Abxr8Jt\r\n4L4xrWAg+Zt2GjruLG/8woNubvvA8lWbf2uZ+DNaDVSpm9nDmF7b7y8l8lvz\r\nBsjG3FbyzhFpg/8t8BHDq2vgeOziG6RG2rBQZQN2A+DoneXBa5Tc+iIy8e1R\r\n1m3l/I/j6si8gxz8uhGEngDJNTrjuG5bOpusME2mWHdtEKq8VO2CVeqMO16W\r\n++LPtaA/pciUWZn1V1M8cMBRqaII3cqSyh2APL3yaeH/uDY7dYxcPVGCxaPL\r\nJjSJCAJ5Mm4oxovkypEQV69LpZkHnlewlQ4=\r\n=2c2Y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4def94b073cad300e99de378ba900e6ba9b7032f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"jest-mock": "^29.0.0-alpha.6", "@jest/types": "^29.0.0-alpha.6", "@types/node": "*", "@jest/fake-timers": "^29.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_29.0.0-alpha.6_1660917471752_0.24246307470688322", "host": "s3://npm-registry-packages"}}, "29.0.0": {"name": "@jest/environment", "version": "29.0.0", "license": "MIT", "_id": "@jest/environment@29.0.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "069758196cae8458d8b7a7778d0877f2ec04da83", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-29.0.0.tgz", "fileCount": 4, "integrity": "sha512-ZHLvUENMAnwXowtyhmPRS0QLCXM4TS0ZfuiSR4QfRsJVN5lG4KdBDvI9kHJe/21vrgzPVOkvI7IBnkyPFCbV7g==", "signatures": [{"sig": "MEUCIQCSddV6e3CYzwvJbxYCeIaeh6i5up6ITtSDV9wZGgI1iAIgcCv6zvZCy0nvWsQWi64CEbNoMdgAUSxKbJudg3AzWMc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13321, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjB2waACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrHPw/7BRSYNxbGSTTt/SycCM/DXzw0+Dpt6dp1CvM4xjjv+J1dub8j\r\nqHe5Pw/w4K1OZUpyQj7Lf4j+RupIylN7LCF3cJvIKDrypxagiNTmlyjKIa+Z\r\n0PBSajw038nRwZpo9LxQHtBsiIbXveK4tY46v90nrVtA9sZKZoaPp3x9/mDF\r\nJFhAMSGs5t7HDr43nuZ6vuy0yjQ5cruXSMVlNJbwsMs1O6y4fEYrBs04bTIa\r\nXrlXuKF8dtG7/pVcvX2y0jCnMPg0VSwFHN+AyUdsE/VeY4SPSEZV2SqZaVEI\r\nQK8KSo3hXxn4QrafIHkyBD/rPDfyeh3BjqBA0SthkNVl5i+QtPVLG5GxH7+a\r\njnVBY/gWYvgxOc5euZjn5XVI1Nn8R56JAfApqCrAzJrOqwK4OTZw1gJzdmvN\r\n46TsaJY4QDr01ja5/GjTjhIo2mcdTVBffoWfEG3G16ww/mL77PV6/bdPJ0po\r\nGMyHyh3txvBq/bJwJ4m0glvSB9hPzZk5HAKGqJ7pAaITG7QVhL0Ku5FK2GJ3\r\n1CpE96XOsomZu2a7mBYURVZCOKjzi7/hdNZfLS0mH1DTAeE9H4IRbJn/YK+P\r\nc9c203rCi9SNX05cforZHuRz2UZY8PHmwy3q81Xn/EZLGVlW0SWJDiDS6PvQ\r\naLZibT0ih/HeehoSDLmF7EVW7IiQ+7XqQUc=\r\n=4G90\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "75006e46c76f6fda14bbc0548f86edb2ba087cd2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.0.0", "@jest/types": "^29.0.0", "@types/node": "*", "@jest/fake-timers": "^29.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_29.0.0_1661430810573_0.18775578445147012", "host": "s3://npm-registry-packages"}}, "29.0.1": {"name": "@jest/environment", "version": "29.0.1", "license": "MIT", "_id": "@jest/environment@29.0.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d236ce9e906744ac58bfc59ae6f7c9882ace7927", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-29.0.1.tgz", "fileCount": 4, "integrity": "sha512-iLcFfoq2K6DAB+Mc+2VNLzZVmHdwQFeSqvoM/X8SMON6s/+yEi1iuRX3snx/JfwSnvmiMXjSr0lktxNxOcqXYA==", "signatures": [{"sig": "MEUCIQCs6nRRihbxKHWSEX1sAQOjbI+Bk7QqfEZbfEJPXgg5ewIgTyTqjybX09f/NoBgdU2W5bWdUhPdx7nXAlLiEDcspEk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13321, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjCMv0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrIChAAh9bPGOO2pnA2rwjHjBDidcUiqeCAZgPqmjfXOO7RsaokBWuQ\r\nHki57XafeNC0iYLvccpoIi0IhtlDPUfbg/7+c77zCRARRZ/K1AHCQrrzuwlP\r\nT8F5b858iGHvyDlHIISpPPLh/yC+HbavGpvgraWb0x7cD3k5v2IsyOk/+12k\r\nSmZ7NaAQCHqScrY1qSQXNKb0rB5YYZMpU9wzZDeh6h9a7GwViwdTk9q5lXHc\r\nFz3nEs5UrB5Il0SjMp1hRJp2hHeNM9hveMNav0ll/D/5qPYe44Knc1c9bn6P\r\nbMtJCwvyTa4gRffA6/MSG0ay0mlmDWXLifejNLyBhefuU2kMSSbQUwA93F8O\r\njHNOIf5wO+QcEdouB3aJOAFFZTtwYIc446S9OG417jpQJ70TdJduccCH9DKC\r\n/CVGU3sJmJPL+fnLRKmE7/jEdTUYhld8BWAfEP9KGBTGSA4sM7IltQTvHKUe\r\nL3aNllenxEKI3wiXJPBjcol9EpwaegQzcoxwQMmMfSx73fYowQ0F7z0tbQh3\r\n+oOt1RpMdUzDUUepbQe0xiyutv6JR4QBPgBbq9Npyg63iTzdka1JV+IGffAN\r\nABaXOn7tnjGnIsHDVilzSFjiLoT+axd7l+it/L1VGmcFxqBZOdMW+lu/N2Ad\r\nZAZXvVE8ZfbsFrCxP/nP6PSdw1FpBl0C6xc=\r\n=oxbB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "b959a3d3bdf324ed1c7358f76ab238a8b0b0cf93", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.0.1", "@jest/types": "^29.0.1", "@types/node": "*", "@jest/fake-timers": "^29.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_29.0.1_1661520884239_0.660970490394011", "host": "s3://npm-registry-packages"}}, "29.0.2": {"name": "@jest/environment", "version": "29.0.2", "license": "MIT", "_id": "@jest/environment@29.0.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9e4b6d4c9bce5bfced6f63945d8c8e571394f572", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-29.0.2.tgz", "fileCount": 4, "integrity": "sha512-Yf+EYaLOrVCgts/aTS5nGznU4prZUPa5k9S63Yct8YSOKj2jkdS17hHSUKhk5jxDFMyCy1PXknypDw7vfgc/mA==", "signatures": [{"sig": "MEUCIQDp5wxPqn4VmDoFyyXk301FD6CUGYC0r5WWlbmx2hq2YAIgGeEtF67AiI3yyemV/x/LWV41ESnRP+i35j7fdoICwD8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13321, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjEzD2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpXrw//S9B7WwBi2+BN6391shUT/zVZMovBazdC15RhFl5E+6e0iVid\r\nOdJx+c26bz0lgdx8wakjQJUAyYdU4jXPjgncN+dxC0M1StlLGAme7AJRh+Ud\r\nfxBE+pKSYiJTUjRP6q0LVOzXY1Bx6ubchDtxSjEgK4urxLosKRB+8pwCv5A3\r\nZ16MLp2H9j1YjGbIGy6Hsx+ff86X4+qDPSVLIQGtIVgX7Lt5f1vIV525gDDE\r\nRtiMzKp2VoYzG2sx1mmGDDAP6GBlwkg6WJyle9E7zy+FYStOxuH8wUF8Kv8c\r\n6Bt288qe6rZhXp49Cmt7TNg3hut6BXJEa0uB2UyXhxLMFvyqfI1qgCF+Z4GJ\r\np8hdFNC1UM66M1GZYHHfj76dkcDlpm463cbCLkmf1VcF0CqFZDXZ/FhXOcLv\r\nqJoRG6XI+eQRky0uRxEl4OfXxGbbxp7yF9abZmOu5r0d9OLHV9XEAw5l9cYn\r\nV1Np+6R92kVMyI/sjjlZjkbIZemMLO995oEThsVddGK25z2bl9WRHgpigdGN\r\nLTLMjteGkd0Y4xdn5s/Si3zwpja29UTooTgEZHjrlq4QwPtvn6qXhBfFZMjy\r\nsfU3plQ0UcfOgtueYByjnr9UA0lfu4Bit4Jp+6KHrdyFlkY+8z225ftrKzY0\r\nvlajn+QK40fOW5QAQvpxT26kkPrnEfxMYz4=\r\n=/uOV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "616fcf56bb8481d29ba29cc34be32a92b1cf85e5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.0.2", "@jest/types": "^29.0.2", "@types/node": "*", "@jest/fake-timers": "^29.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_29.0.2_1662202102047_0.5470552817555159", "host": "s3://npm-registry-packages"}}, "29.0.3": {"name": "@jest/environment", "version": "29.0.3", "license": "MIT", "_id": "@jest/environment@29.0.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7745ec30a954e828e8cc6df6a13280d3b51d8f35", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-29.0.3.tgz", "fileCount": 4, "integrity": "sha512-iKl272NKxYNQNqXMQandAIwjhQaGw5uJfGXduu8dS9llHi8jV2ChWrtOAVPnMbaaoDhnI3wgUGNDvZgHeEJQCA==", "signatures": [{"sig": "MEUCIQDBXgQtf87yErahVMd6+RvZZxw1mOk2/uR12epWH9RIxAIgMaSPPRQdvotnDHKWorFvjesDmP6VSWPXPR5EmXQZsPk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13390, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjHKIkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqB8A/9HKFcAoqzfRdOgQBPdvFX6BADqC9L/cfsvte2j0KKVsA4atDu\r\n6kTNEdbzXHGXaaQaAjE0VDnuVSrg/ci5nN/n3GDNh5xrqcl6nZPKmKKQ0DQ+\r\npCLB2ZDHiNZfFbV93q9mix67LXCxuPWQ0TMmvdVlf6HT7+otcZJhbgOHJwso\r\nhX2zV1t34rEXKKRkGZgD6oW48zT8wiALhnL1ME5KA4qYkVWhhMWx6UFL/G7s\r\nRDRhTWkdCzcwH4wVU34USRF4UbHaaIhoFGZwvpcYP8fxYxVSFKUHeMxfdejj\r\nWJnzexgTHzVKSpOWLL8ZYh98VbV70RUZAgB6htSab62ejBlFqFH772syACZE\r\nHID2dddz6nAkqL9kk757NXp8wFEmYD0aLOYV2xKa9+H6XTr2CnUe6oiyojLi\r\nQO7FI/tJ2govtq0VE2szhOJ6ryEGlj91ynYQLb5+1k0AX2n4e8g/kBvbuUcH\r\nC1UKiim0UHe/gBz/1/vIE+sS0UTkBmkfwMYppx9DpgZIue5svfWEJ7LBVuYz\r\nQSZeTzj4VixVlPLvrakpCWrNNzKEIeBC5t/dz7MvV1sau+lDohQ+iQG/dvO3\r\ndzLqJUbikY/GgfwGXYnhp2AAmDs2sFvRMyYX2hJCax3tBEKLi4fCVvd1tbQP\r\n0gGmgy4vtbNJS8LpZNd2fS9TPnYdxDmWRwc=\r\n=C+Ve\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "77f865da39af5b3e1c114dc347e49257eb3dcfd1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.0.3", "@jest/types": "^29.0.3", "@types/node": "*", "@jest/fake-timers": "^29.0.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_29.0.3_1662820900463_0.9382373402632131", "host": "s3://npm-registry-packages"}}, "29.1.0": {"name": "@jest/environment", "version": "29.1.0", "license": "MIT", "_id": "@jest/environment@29.1.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "688671c8a2e4eeb37fd275d7397d85dcc44b6d2b", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-29.1.0.tgz", "fileCount": 4, "integrity": "sha512-RmmFwXbZiAzJw1qa3dBvAI1LYz52MpdBRho8xSY1J1Cxcbzd9x42MpXx+NFHl9cymuQnvzp2jlFHV+JHy3k8Cg==", "signatures": [{"sig": "MEUCIQC4VS0VylQym8nzV0cYVdd7KmNCLU5sxXkqGkVPOb1oXgIgd6jxoVltqzzL7NzEK/LTFH8yZ27zGigkTl4UfVd/QCg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13500, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjM/nIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq99g//X28Yq/7QQ+Q5t72fi75Jqq6TmFIk4wUp/Cdvju3ubNXccBRP\r\nAf7JoJRPIKozZ5FtpMlyRr/0+JVmPcdF9HfNODXxsXaa6fJnYSAE4sRdDD7C\r\nhRofm7yZg2Ha20kAnyzxg1GuLYuNNMkRdTk1izcJt1Bb/Gp8XmU/EJex0Pa0\r\nJ2MRuzQfkSaEicO63lUp4tywciBUHMoP73DL2mlSwzfjpeFDzxxffGOGbEpg\r\nSv24mb2smm5+aps+ZOZj9+AQLMALOoSF7akQGFGMcrSu8ZvaqlCXrEB/kPFR\r\ndWD6PDmjWihCIM9SP//D8RqhFj/AKpZw9UaS9xjsRes1LjJWTei0GWE2V+t+\r\nfNViTOv2Cb8mp0HkQfDkJiwV3jSKyOeV2lRtW4RaohybUVkfqpWUVfUjraqO\r\nNlpEiSiKqR2Jaj/acjF6tc8zgqIdEDmNOsiWSxAe8F6RprVVGKkdDkZBLZJl\r\nDRqD2acL+WZAs8M9q4Zsu4Y20d9RBK5f0JZ+hWXiRT80KLpKhOvBAQWzq8+4\r\narOaHCBQwGhk1TJOVqcsVGosRhj0R1844xO7E4J5pGs1HemToQV8mbTErGlo\r\nLlg0PA2MH5C/7ZIpJCrCttTqzYh5Vm6rNRi+qRXiwh5CIODB2YlgQJAOs0nP\r\n1Bm6UQ3WymfSRb232cC/lSj+EgXb/Bz9BZg=\r\n=ufmP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "51f10300daf90db003a1749ceaed1084c4f74811", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.1.0", "@jest/types": "^29.1.0", "@types/node": "*", "@jest/fake-timers": "^29.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_29.1.0_1664350664331_0.7011208164202427", "host": "s3://npm-registry-packages"}}, "29.1.1": {"name": "@jest/environment", "version": "29.1.1", "license": "MIT", "_id": "@jest/environment@29.1.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "afeb66563d8d366a329908087b6697c3bfaed3cb", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-29.1.1.tgz", "fileCount": 4, "integrity": "sha512-69WULhTD38UcjvLGRAnnwC5hDt35ZC91ZwnvWipNOAOSaQNT32uKYL/TVCT3tncB9L1D++LOmBbYhTYP4TLuuQ==", "signatures": [{"sig": "MEYCIQDaVZYLlPnTwiggLuTd+rdVQkYQX5WWKVUlax0bW8G+MwIhAM4Zelb0NZL4Csc4b6WgUKEcm9jks4ONB1tWopwxTiFL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13500, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNABPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo+vw/+KaKE3IBgKQXCpi6bhT0yn3Y1VEcukiq59OLETvAD6IifRg5A\r\nvhwA/p852MOuFX9asAVIOnQahSQKu+dtyy//3UBeMJIYgipSDX8U18TK6OeH\r\neeN25hdMFyFExdnFMzUut3z0fntOU6ycwWN8PpVl3gOeQLSMsjWZCAZSzpsl\r\nHqpIU/ltUWFrJTpAcNJA6Ks17nta/xLpRUKzmVYvVN/Ocp30zFKa+AaKJxk2\r\nDjVp7AqZj1ObOJ1D6MQAzuIG03mQa0dBU7RvxiB0X0wmtKiH7NmICbA3DGss\r\n3IlAebSi45QXEORbUYjwgKMoVgT7rtleCEkWGxcq4HeAV5LPmpJEsjwur3WQ\r\nPtguRyj0TDbSv+gRggoJg+zZkkC9XTz83vGfWPMtIhJazuy9H5SVPD/yIzTS\r\noyAG05hCNhi0iygOoEF2R/JaD6quCZIrYgobc/Fv+hFX6689tETusPW1P+bX\r\nXLhZhCOMlipln/e64PSWE5UNcclmCPETBe5tm9kwbuC+PPz7+Esvp6bFOqvl\r\n2eOnXtTEDb0bj4ewDlC0qeDr5U7QP6nvBfRojdW+/dfcc4Wlk1PvZifpTBQb\r\nigETBnJJnM7buFcEXslaA7xcMbcQfEYz8kWIsp8VYZfeyCM7QrNjLc8yMztP\r\ncy5nROub5r5qkl2Sgy9RU8GwR9IOY2Y6KBI=\r\n=xEOK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fd9cd900ad0904421a3d97661fdc3337194da1f9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.1.1", "@jest/types": "^29.1.0", "@types/node": "*", "@jest/fake-timers": "^29.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_29.1.1_1664352335633_0.7869130503777602", "host": "s3://npm-registry-packages"}}, "29.1.2": {"name": "@jest/environment", "version": "29.1.2", "license": "MIT", "_id": "@jest/environment@29.1.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "bb51a43fce9f960ba9a48f0b5b556f30618ebc0a", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-29.1.2.tgz", "fileCount": 4, "integrity": "sha512-rG7xZ2UeOfvOVzoLIJ0ZmvPl4tBEQ2n73CZJSlzUjPw4or1oSWC0s0Rk0ZX+pIBJ04aVr6hLWFn1DFtrnf8MhQ==", "signatures": [{"sig": "MEYCIQCfV7aK3Hqe8EtE8k/DS/oWFyByIPpjp/WT38iZdRXCDAIhALXYPVuX8v7Z8ys+MuUlGCInrMe8s2VMUR/nieRDA/VF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13522, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNplMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo8OhAAhRVZJIGqG5RwaI+j5FfWsfaebWxshz6hSH9YEOEgpmJvdGQ6\r\nWABKJYqJvHbLKnTelo0w+o+XomEfAz0mlobQswcUhqCL5IG8FcfvF8DipxUQ\r\nB4Le/HdoLpW2yqWRzxOs1cjAH8upO6vBI/XSf2FljWuVLxGwAg3nBNgmY9Zz\r\nxWvpLSDlBR/4r5mMTCqhKc5WrEo4J9WIbCqUC0Rqu5zIxCP2EDjax6CSCDwN\r\n4v9ryoyc7XW8KpJzmb4b/10Xa2GEIdtgRk93k7fHFAySZvwerCYJYbAxkAuS\r\nGKSQs5V32ztkIb35PQNqMiUHWBuOJTJaDV6f4Uu+zHatXDwIwyZ1nftz4k5C\r\ntMqldBwR0obAhpME8F52mVH3oQuIPzbH2n7noxGIc6cr1aoC9CDeocFqWY0e\r\n9VNZ/tbY74sKMJK3rNF2jFu13FXf+SPPIemeP7R9DzZ5pIYyAUy3L2ztDjaG\r\n4b6fkj/J4BRgf8D/Yk05XFKGeXp5fRZQ2ZOUAhKmlcwEkQmQwxScxi+eJYvf\r\nXOdgXTqVr6u4LQFAudJptFVc82YuPWvCAbWnLC/QJRB/x/k4r6Xm7EL6uCBI\r\nVO0aZpvs1ZV1KCfHjZjHM9wwUhDgmdulKcCSpGQA19qbIKtuf6d7jFrBLedt\r\nFqR2Du50LEu6+AUm74DkqhmWkr7iLLWzhcw=\r\n=BSfR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "3c31dd619e8c022cde53f40fa12ea2a67f4752ce", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.1.2", "@jest/types": "^29.1.2", "@types/node": "*", "@jest/fake-timers": "^29.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_29.1.2_1664522572165_0.9049077773884713", "host": "s3://npm-registry-packages"}}, "29.2.0": {"name": "@jest/environment", "version": "29.2.0", "license": "MIT", "_id": "@jest/environment@29.2.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7e5604e4ead098572056a962a970f3d79379fbd8", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-29.2.0.tgz", "fileCount": 4, "integrity": "sha512-foaVv1QVPB31Mno3LlL58PxEQQOLZd9zQfCpyQQCQIpUAtdFP1INBjkphxrCfKT13VxpA0z5jFGIkmZk0DAg2Q==", "signatures": [{"sig": "MEYCIQDHF7nw0QPD73pibUfUv2to4eufQwpr/YzMImALHJCjrwIhALd60U7fLLD2ii1+JLIJQbl5JgJwvUdKEW6OhBa9Gl/Q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13693, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSShQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqcng//ULXYE1B6TKMJFKmwBcsrF39vfqBIiqGuI3cqClMn/WGY3gvI\r\ncSJClzjpAigFReEP5A0vNgk9xhbuXb27hdRxXmKW1qAApNcsX1K9VfJk6EDZ\r\n+sLMoLl1aCXgSVvXYXX2+4yH2Jgpy6dAhuaSugUx9nxyyA5D6vjo/MRmG/cu\r\nh3NOAVT0u8SECYWRgZarhrUFzJd9Wn8AY7+Z0diVY9Dqs4Uzpt0qqcYx0alG\r\nu1/7k20PmbPMxH5Wqs16kZ0PlugiSfXmPEGIL8waq9wiY3akiq5HmdUVpBLw\r\nhWDbzCxuDNl5tTFCBEBFETc8BRK4gkz2jpt08z16XldEh0EnRrlU2051UXDz\r\nQNbGJNlapmNgw3qCQk5+i1HcO0IES0kQ18RNkN0FH+zh60us8q9NlL5/gAqZ\r\nknHZqx88Y2+bYg/zA0a+LoGqxvShnLPpC3LJ9oKi5tLsgnJapPaFOAUEXUsf\r\nfy5xYSukrN6vdxwoHS160u52OULZV8jRt5PDjVWoMYM+JbtBC2IYlIzKXU71\r\nSQI+FQmwo+JLHU78sxWtxGZaKugOF6kvaPuNfL132Y932ze66kTaR8LxPCHX\r\n9C+0ZOMrJUgcaeBvrkxkkaOqt7DBLYfztbHjkDdaEFnbchwPXzjWdcFv8op+\r\n8q4AQuZYrYS2uKCstOHder5Tf360lZIaONY=\r\n=9PeU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ee5b37a4f4433afcfffb0356cea47739d8092287", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.2.0", "@jest/types": "^29.2.0", "@types/node": "*", "@jest/fake-timers": "^29.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_29.2.0_1665738832629_0.48060361804460405", "host": "s3://npm-registry-packages"}}, "29.2.1": {"name": "@jest/environment", "version": "29.2.1", "license": "MIT", "_id": "@jest/environment@29.2.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "acb1994fbd5ad02819a1a34a923c531e6923b665", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-29.2.1.tgz", "fileCount": 4, "integrity": "sha512-EutqA7T/X6zFjw6mAWRHND+ZkTPklmIEWCNbmwX6uCmOrFrWaLbDZjA+gePHJx6fFMMRvNfjXcvzXEtz54KPlg==", "signatures": [{"sig": "MEYCIQCRzVwBf3IMiQh6RcTed0NwBE8rNKnyfe3CUcP1mKOx8gIhAP9GjpNDBP6kBsfzYUmSH2kHeIXJVhbK2Aq6g8DkCHIV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13693, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTs2RACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoPGg/+KVoSCSDhY2rl5KlwHJd7AzoIhggekPxzZwHyEB66kxNWfh5m\r\naDb3iycxXUxFFr+0LoS77QtYr/W9ylIkHX6fILDV0C14e7sRKoHAjh/T5yxQ\r\n5fq4MDiKAacd6DRsoD9G0OJAZ5lYoxvOMK1LWyCLNPfSLV1V4b/bdzR8SjBz\r\nfVKqfCTk0zWcEnKorIa8RTWZo4UdC1pokVYx/s9z/rePxfJRHaOpGxprIvKA\r\nYy2OHdq/zkyszIo4jeREYuSe16+jOo7SP+E+KzcwaFPEiLL7+c90yhOAbWe3\r\nsStZ8O2eQwtWZWRsBrYlZ2yTQJZSo7QHD0kYjIyVhv1id8h/uIJ+/bmlHsFN\r\nobeklhyt4IQJ8/Th1zjQET3oZkPe3Knj4TDvc6/+6mZQpkBxXLoLmDPzNVD5\r\nWJu7W/JjGr1en8uPxCADSNNFc4M78tycCq5lbHNs9H6khDohTeq9LVt9/gM0\r\nE7RPwjKenuSCbGDjJvUcCayq/N1udVGz/Q8itFrvtMmkiE22zu8ok0DoqEPd\r\nsmcbaM63W1wgY0M6fej1O8pDifF9LIU3n4NzT96NCEEyY/AiFKPFjIJ2jMTT\r\nzaEPLp6gvDZtaX0GXHxmB+86oOmSl/akZ8j/pel4khVvZNStF2XxUMyMqiIa\r\naSfjh8//Szu+zecIpXNpTLvOnT33Xz+0634=\r\n=KRsw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4551c0fdd4d25b7206824957c7bcc6baf61e63bf", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.2.1", "@jest/types": "^29.2.1", "@types/node": "*", "@jest/fake-timers": "^29.2.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_29.2.1_1666108816852_0.4770452051554057", "host": "s3://npm-registry-packages"}}, "29.2.2": {"name": "@jest/environment", "version": "29.2.2", "license": "MIT", "_id": "@jest/environment@29.2.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "481e729048d42e87d04842c38aa4d09c507f53b0", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-29.2.2.tgz", "fileCount": 4, "integrity": "sha512-OWn+Vhu0I1yxuGBJEFFekMYc8aGBGrY4rt47SOh/IFaI+D7ZHCk7pKRiSoZ2/Ml7b0Ony3ydmEHRx/tEOC7H1A==", "signatures": [{"sig": "MEYCIQC3pyo8K2xsobqdXBGgvg1G/pRORrKsNGGMSsPY/qVJNgIhAKrSk9H5hjdLpGJ5sLpMC7+PH34HERtI8kAe8cL+RSn1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13693, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjVvRoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpXhA//R/Qi/HsVtqovVzai0NK4j0sBU20GOmlXBkCQxPPpTG9GgckM\r\nlvc/DE/HB+qujok+rjFxuwAMnN5amfDD/4RGfpooD6OBIGzesIk9wDA6gj87\r\njutPuQQ3GIe4455Depv0L0LbD2ZLizc4aqTuFFOtSi5V8aC09zvJjYwPvm5g\r\nJKgpCePqhEn8VfYM/cqFiN/ShWpx3LfvN6h+5QnLvVCwKK+PIpLG7gTwSvkG\r\njTIy2YTcYNQ1I5J8YzzJxEsFsrKwsN2hHdXCSgBzGE8Nxsen/BtT/8BEOICR\r\nwy9rm2fpx5lCrwGiCOg+O3+V7ZJk/V7MV9kfseM8KHBr2KaPTyjz48Mm/OU0\r\ngqdU5hfVOQeMup0w/GteU3FGZgroZhdpGu7v4NctBHk29ARLj7SXacJUK4Ii\r\nyU//g2lJkjiGbSjkj4li9SdL545oPDHwWTTBu7D4oJ/mEewPUrKgwa/Cg25g\r\n8bNYN63/ZUCnMQz/2ykHfRDZWjdUQFIzDtSrWgHDrVtFaMEqqtl2p1fI84yF\r\n5RfjNswgWgMDCbQkdlqyuUBeBjiPZQ/3CaXtBukPrVGkYvJtwDMcMggfF/Ap\r\nXfnycrijtvDaqX2Nsqgbhn70knVN9PGe5VSSgOEHqWgCB7M2EOTGb3rD34tN\r\nVnYV2d0XitwetxGBivONFQJTRKHXZhxBSHw=\r\n=PNx+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0a8edbe0ac434394a16cc173a03ff54a9cc50e41", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.2.2", "@jest/types": "^29.2.1", "@types/node": "*", "@jest/fake-timers": "^29.2.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_29.2.2_1666643048744_0.6959157933722904", "host": "s3://npm-registry-packages"}}, "29.3.0": {"name": "@jest/environment", "version": "29.3.0", "license": "MIT", "_id": "@jest/environment@29.3.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e7bfe22531813f86040feb75c046faab32fd534d", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-29.3.0.tgz", "fileCount": 4, "integrity": "sha512-8wgn3br51bx+7rgC8FOKmAD62Q39iswdiy5/p6acoekp/9Bb/IQbh3zydOrnGp74LwStSrKgpQSKBlOKlAQq0g==", "signatures": [{"sig": "MEYCIQDi/uGuHwRAWzLeawEAdvm+blfG/hAuJgo7eFDEc34Y6AIhANMkPjjHaA7atqwowXiCnZcv7Gl4WpzrGeSxjLvyPBkq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13693, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjaUalACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqTARAAnc851qMRL6MRWxjNGzAtCAvZZYf5xWJIBBDw/gzEyJJxd00q\r\nvcn2ByO52dvZ1HyYaVCYnajoph27Q4Cs2R8olgoUme8EzesGp0x8l4nlFlPI\r\n92eg/Ib5GBdeLdcsomNvGhjgaj7L7dVQtI5lzf+/XZwzqNIyx9ItYtBY7U3J\r\nbBjP7SL3QahExWnV15Y8EiwjtizuxaTGkNi/VPTtD901czR3/XjzMHOlV+q9\r\nAK8XchxsUE14KPIKNiHrLj/dCV/y2berxeOzmd7vvMLrSIa1ROVf2SPBPI2o\r\ny2sf4oScapcZlqxIYXzGgLdWAOnkioeOjqMU82QTprhmUL9+J6w0a6WovDQZ\r\nythIR4TmC4jsE7huy0sX3aWYw1ZM9K3SHTQ0EfZNej1wfxW18WkfQ2l1YbNg\r\nYSwEiinpMjIY0VW65WjyHGaoyPjP90ueubxnMNHS0qB2l2Rxr1NilOkfXJlJ\r\nUgiwW3KFe+GutgGMTOesolum21IhIhbh67YXmyLS38thN7VMANuiQFr8POmG\r\nRU9Jh+/7oc5TC3q+yY9ZeviKcpwDupI1LQ6bXeiZA1G1bGUBhtMi5P6ic5Ww\r\nifb4EqI1HHxnAJFn1Ccrx7aGOlzKEYNi6MlFgv2eIPNCg3yoK+DRxs7oFm6b\r\nTS0KstH81Da64uDrOCkkmQCFercbRuYz6nU=\r\n=K3tY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "84b8de987b33e2da20dc833aeb65f23d72a673cd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.3.0", "@jest/types": "^29.2.1", "@types/node": "*", "@jest/fake-timers": "^29.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_29.3.0_1667843749529_0.9993367416612136", "host": "s3://npm-registry-packages"}}, "29.3.1": {"name": "@jest/environment", "version": "29.3.1", "license": "MIT", "_id": "@jest/environment@29.3.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "eb039f726d5fcd14698acd072ac6576d41cfcaa6", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-29.3.1.tgz", "fileCount": 4, "integrity": "sha512-pMmvfOPmoa1c1QpfFW0nXYtNLpofqo4BrCIk6f2kW4JFeNlHV2t3vd+3iDLf31e2ot2Mec0uqZfmI+U0K2CFag==", "signatures": [{"sig": "MEQCIEGuBEzFl5Q8KikV2IJ3PUUYRTxvnOFXWWe5ZFnwA1B+AiA3y04GN9A/qhPkscZEWQKiUJ/qIqAb9ZBzkgn9+njELA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13693, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjat6ZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpH9w/+NVDZGWomYISr2aaSDR2H412R6FBDnOCpurs4MrIzLeLwbvZA\r\nwghsmACeFQn+eBl4t7Ur2AjsXxqsGWdBanrB0yvVmMT2NVZ49HaxcCnXeefo\r\n0BmAejL6lEJi8OskxUMDSapSgl2fRT+8oyWcVBVJG6z72zU4aK2wB7+jp4ND\r\nqHLT9Za6K0FfLXeq14fRiaj0t4JIP8JsghbaYA3sYgimkNhrljvwEbIswY+9\r\nrMz+nF6PFHU+H0wxdMnauNu3CAvTqwi2FjBFtV1zp+12snO36Lk+Y11hBoDf\r\n/Dv0YhLc4ajl1SGOAlrd7tafvQUwa9qbrP8NFDEE8ppYLhzZsySnRM8L/Vlq\r\nzFiiLoSRYDDc0G0aRbZziznpgdw1OzxTfV5Ok/l80Hmq3zRUKP54At7o4Esw\r\n8DIV6tDK7qOc9NwdOpZxv7bVcEnWtEv/KXdey8gGbsqjsA1zxcXXGxK5S0+s\r\nBA8PTYYsjXqKo53QACQzAV91zEkST/bygiK+0HtRA2/UjmVm3rClr2p6YLIj\r\nCa/SbN4YQmiCYQ3uLxdu8ajahqc1GtVjJMHuM3tWpbnb85nWc1P4JOVP/tMT\r\nqE2qiSpCREdeWqN2yIneRkSURdrlUUtbP0RKUx1E6jG1JNBpiczZVCk+sUEd\r\nxxGyhd56GlhZMqnDIEFMB+a8kmlc+j6KXMA=\r\n=597E\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "05deb8393c4ad71e19be2567b704dfd3a2ab5fc9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.3.1", "@jest/types": "^29.3.1", "@types/node": "*", "@jest/fake-timers": "^29.3.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_29.3.1_1667948185666_0.1549436278296734", "host": "s3://npm-registry-packages"}}, "29.4.0": {"name": "@jest/environment", "version": "29.4.0", "license": "MIT", "_id": "@jest/environment@29.4.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b15d7bfc873d6348dfd7323e50083d2c1e067750", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-29.4.0.tgz", "fileCount": 5, "integrity": "sha512-ocl1VGDcZHfHnYLTqkBY7yXme1bF4x0BevJ9wb6y0sLOSyBCpp8L5fEASChB+wU53WMrIK6kBfGt+ZYoM2kcdw==", "signatures": [{"sig": "MEQCIA44hcvh6/J3KAvxyXKwmCO/oQcybLlcIbY3G8JHKofBAiBkOs9wVdBqw+ydVeCFcbWB5D9O4T1WS2VgY6VPCSPkmw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14925, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjz7k7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoomw//cnkHZLDUZR4ZPgjFcnT77tKlMW+fqKgFvuLvcgaF51FUlfaU\r\noqNJ9EI9gH55/y/hWIhnFM+MM51eCbF1f/5VbfAq0oCZwlr8xgO1hqYWfxKe\r\nRVpH5CJ81QsSOYefeEyTM8Y/3ChlMNQ7zK1uoJ5+7Vm7JQsF9AIsNevKscSW\r\nCkeiibTj+MFr+NmQHrmvBZWfYvHUxz1kOcHR0/ENrGUTb6fZN3AiTGQT4xdd\r\nQfx96Rart1vUXJGmTnbYI8+cucRsVeGxcN0FksJDc1DN/ZkqYLpSkDy3ooIm\r\nEa2sRPezMQhkcOx/5NXu3Th+gRkEV3yiVnV3Y/NB067b3jhi7rajtGYCoXgq\r\nVofpYAsscrB0V5p18weP9crFRteoJBhWBqp0Inkf/hqKJPHG00z9M6RK0n2O\r\nGLgScuFcuLajUSwohFYefBwT288cE8uA82MW0hGvapLAaRbMR2YlQhC1VrUH\r\nVRDWyTFD+yZ/4uPO74BhWSdJAfw0aw8lHbXa1sd4D9TmBdzxE3uuEPN9XOLc\r\nS+vap3Ogw5+wJCbJz4/82K85zmof+72AHkD47AT62zvZVrO5pJ9NE1/CjX/E\r\nr0J9U8iYZiqXxVrp0WrPao4uxKPqdBSkjo2MDEPkhoDkzVLDHpYeT0fpGosx\r\nIr2hxyiVSDIZTaFI7CkUUF5V/2HhvBLwIDo=\r\n=MT2b\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4bc0e8acaf990e6618a7bed1dca67760c20bb12a", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"jest-mock": "^29.4.0", "@jest/types": "^29.4.0", "@types/node": "*", "@jest/fake-timers": "^29.4.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_29.4.0_1674557755418_0.40788534718662817", "host": "s3://npm-registry-packages"}}, "29.4.1": {"name": "@jest/environment", "version": "29.4.1", "license": "MIT", "_id": "@jest/environment@29.4.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "52d232a85cdc995b407a940c89c86568f5a88ffe", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-29.4.1.tgz", "fileCount": 5, "integrity": "sha512-pJ14dHGSQke7Q3mkL/UZR9ZtTOxqskZaC91NzamEH4dlKRt42W+maRBXiw/LWkdJe+P0f/zDR37+SPMplMRlPg==", "signatures": [{"sig": "MEQCIE2ZG19TlRxoweR/4IPaZTjclrQ38uXaHN5D9PHERh90AiA2jpev+OHHE2iypvT+SedGg+gnb0odQUeGil+7GWCmtw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14925, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0pd4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrFcQ/+I48jXgsCYQJfBs2SMymEJgiFvb4pDoLbTxyeZ0pdxo1urahh\r\n4knhBKq5qKrJek15HdLL1OU3f4nLrYzlUxm7ZzTH9hJlLOFB3/S73uvfgTqv\r\nXBXUVe+RCGhQf1SY2oL/tm5pXhujcs9XK7PGIN7KC9Kn8SAbHLVKH+iBeS0y\r\nu5YcMvBf8anDffWQ36wnnOdEsSJbNMTF72D3R18Fg0uti9AROpICa4kxAGm2\r\nFiKCUjO1Z7PHr9JxNDlTi7iLA8WK+AQI8dVLEj7v4WUCJgQwYtmwA+uemQFC\r\ne5V1NIjvptZPH564jkJ8iaf8k27WA3C5H+n87mmgpmc9v/KZboItYy6jv6we\r\n6S4s9u/x7a8HQdfpPIll/Yctbz0zEk7dFC1KFn5lbpmREGWdPrq/HLcuOjWc\r\nCa3xS+qcNFt2J2S8J4rj4bQPbhVLHzpyp08zJnxdtUKnPxacrW86hYNwbtIS\r\nQJl33FPEX/KszCkydDrbncETPovwpEXwalHtxTEjpOZyFtoqGQu8e6Knep0e\r\nigqsu4ZPVvlw1c5aHufka7tjAHZ11j0uUeoUGQQ6h+FNtbT7pQYK6zR9EBxZ\r\nCaAxErF25P+I/Ih09O/AVOnGLUUJdEop/gzpBS1rayqS73YvD/HvyqeXuYcy\r\n9lIcVcxKOciEXhirdqiTU5l/ynD8XbdCAZg=\r\n=kXFj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "bc84c8a15649aaaefdd624dc83824518c17467ed", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"jest-mock": "^29.4.1", "@jest/types": "^29.4.1", "@types/node": "*", "@jest/fake-timers": "^29.4.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_29.4.1_1674745720590_0.6263167074931684", "host": "s3://npm-registry-packages"}}, "29.4.2": {"name": "@jest/environment", "version": "29.4.2", "license": "MIT", "_id": "@jest/environment@29.4.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ee92c316ee2fbdf0bcd9d2db0ef42d64fea26b56", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-29.4.2.tgz", "fileCount": 4, "integrity": "sha512-JKs3VUtse0vQfCaFGJRX1bir9yBdtasxziSyu+pIiEllAQOe4oQhdCYIf3+Lx+nGglFktSKToBnRJfD5QKp+NQ==", "signatures": [{"sig": "MEUCIA3Jzy8SDkXi5fKEdGIeWNlc7sutvzB+TNc4eDE+zPP/AiEAuWlML9aEJEyNBQQjVLRdt0qGTvCEohCmx79anY2t3/4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14548, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4lX9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpwwg//XJNhkrhOVo0++YZU14WTa+ZE1yo/B+1zJUYE3LJy+Ywkf8Ei\r\neM9dCDLeymki1y+ccBsfnqHLqh6AuQFJwRfs0ligLSo0Znyh/B4geJkfSfnO\r\nM7YDWtcfgZvQArLTRQOx4K+q3PtAFsBFNC70iilRZBIP39uDHLjFxigoMOxH\r\ns5liDhVNYCCCeEksL+M4XzvFr5f/88SxWWJZkkljefpRubRoYS9mFco50V/C\r\nLz/mCCNY6Vydgei0pMDv2lkf2nT81WeMq3KT87Dz0TMjHAAueCJGzDBaSKHA\r\n3VryxxDizVTtA/g35mu30SsSxy4koPACMf2ersfzOEkthPJsS/3hCZ6OkkFV\r\nGvJz6QtXVR00JmIIBVvWbokazeKlf9gszgIe5PbbWC9AF83SNQUIa50Evlv0\r\nfYPQNCz9x7UccFKLC533IdbGyP7TMmApUENp+U8tS4nyZCMPtGF6SQi9kIwK\r\npgxVyxpurqelRsweBE7k4cdcI0ozqzXjCjlGIQvET8f6iWCRzWTtsB3SMeTB\r\nc6BVgsyVwUvHFxkUDe3pkmLIL01gMcZmFqBrGm7n5IhkSbdfq5ZqaEC+0mUU\r\nRzGiicCq+1oDOG6ZNWYlcy8R3ZeYt++OFl0+YT64X9dhOEUQVn6laW/3OVbV\r\nW2+pgB+Tej8sMHbcMZx+vha4ZgJDexFASMg=\r\n=w5Yu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f0fc92e8443f09546c7ec0472bf9bce44fe5898f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"jest-mock": "^29.4.2", "@jest/types": "^29.4.2", "@types/node": "*", "@jest/fake-timers": "^29.4.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_29.4.2_1675777533199_0.9616518299572343", "host": "s3://npm-registry-packages"}}, "29.4.3": {"name": "@jest/environment", "version": "29.4.3", "license": "MIT", "_id": "@jest/environment@29.4.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9fe2f3169c3b33815dc4bd3960a064a83eba6548", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-29.4.3.tgz", "fileCount": 4, "integrity": "sha512-dq5S6408IxIa+lr54zeqce+QgI+CJT4nmmA+1yzFgtcsGK8c/EyiUb9XQOgz3BMKrRDfKseeOaxj2eO8LlD3lA==", "signatures": [{"sig": "MEUCIGTyVL446SMvy0Hr7POfctje3edyg+r7ZMN9O0xdfGkdAiEA4H/hjAgnAt3B+ZYp3vFjUOI9jOqxZHYqj8OYCH95QOk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14531, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7MipACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp1vQ/+LAjkWF7Iv7+h/oYAMD3aqs0UkQNBou4v6QvxKH/XH7xUys79\r\nE1+yViw4V++Mg/+EyiW7u4YZw0zMQ7kMX6sBxCxALfDiaEA/tgwObkSGXWDZ\r\nbN8+jU3HBkU75idswUV+fwPatsgrAc0FE2YWtkZSI2h2LbfXVlQMAeVFVP9F\r\nXFkBE7TVDt8y6XykcomhWTPqnlOP4RDnuGQ+Rp0wrSsEHsOcmATOL4oR4C6e\r\nL6NZV1U931SWAgA6cUwLimsbdh+ApOOpjxdx5MfVC689oWynH5ATlEqRL5m6\r\nbhu1KYu1uDSuLINvWle3iNb2cNp2nOhFQDsFtrnpZIO/eXWBjhUlK58f5Mje\r\ntd7ocokCMF50bmT0tGOnA+mYhhjueViLbZOtA8PYzP4lTKM918C6GJgdOKVD\r\naLjn65RQqejpSeIBO2HtJgkomx3XOdJBC8UCwVct9ohvqev3XbewXl78Tm/I\r\n/R4cKHuOVoPKp6Zh46AlfzvRapY0c2r/DXYXM3SNeBAnlI5OqQYuERcHFA/o\r\nr0BVRNrv9wTaPKWT6Rxt58Lg95ad6X1cbYtgywpu1x42eiaFE/h2RGKtuSkW\r\ndAnzIitp4EWPtXgrl2QNSD9dr9MXYT1Q2P4noe5TVI0hMkjGm5AIWC0N1RCP\r\nKrxPu3yIwIu1nA76JR3Q4Sn4HMQL9OesYsA=\r\n=SN3P\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a49c88610e49a3242576160740a32a2fe11161e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/1.13.0/node@v18.14.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"jest-mock": "^29.4.3", "@jest/types": "^29.4.3", "@types/node": "*", "@jest/fake-timers": "^29.4.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_29.4.3_1676462249462_0.18001931031336182", "host": "s3://npm-registry-packages"}}, "29.5.0": {"name": "@jest/environment", "version": "29.5.0", "license": "MIT", "_id": "@jest/environment@29.5.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9152d56317c1fdb1af389c46640ba74ef0bb4c65", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-29.5.0.tgz", "fileCount": 4, "integrity": "sha512-5FXw2+wD29YU1d4I2htpRX7jYnAyTRjP2CsXQdo9SAM8g3ifxWPSV0HnClSn71xwctr0U3oZIIH+dtbfmnbXVQ==", "signatures": [{"sig": "MEQCIGJG4etk6ZiDRMqhLs+SAUBGaDRgwYj2SsnkSWsoUDb2AiBkI8x7Y/JZeshMq5FOS9sUhex53MYaO0vp+TKqmVDGcQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15883, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBeuxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrIxA//WKwBm8rmGP5XHVb4Tosk+ZG6vroJ+t0lRFeuSMOhpTttfVWH\r\n1sf4CQwjBMp0ksZ24KbMfD9RlJMEJHaBiny/KecLodKTUuJ0DHVHZsXjIG8a\r\nVgl8Jvga4l8hY/5/xLDh2I5CdM8z202m1/8iGvIxu6bEVCvuNkXpeRhSCFnP\r\nrXzDT7rTssFF4qbOaqvAp/PHn/cnDTQpUH5trAZSoGFyl8QlqA4yIUvI80yt\r\n/6SO0/SWO1SW0USuTpBRfviFdUOECCaFb98A1j/Tz3VUWExeFXvReTDVRqp1\r\nFkVmFlJ1rsp469k+PyeK+34AVRix4xKHL547LeedPYYEwrdw3iYlkLGXc4Mu\r\nS0tdslkQo5YCqj/p7cz9Jgsz4SSIW4dvzjz4L2ik6NEbCdbuQrqXxXVcQfFP\r\n1I7e76OTkfCh+xnut86n732Si8ffs/YSzSfgWo9LXCjTklXqXWmAGZqq26WS\r\neu7FBvmYd1iLdCOyta9rRnRig3anmWTDT6r7KqxfPocpL9CX2v6NYt6Uv3DJ\r\npjsfLQTH8ijqXIB29RaZjL2ISSZToRQDf6ExqZFCInZnWooUyBOGdUIQwgGH\r\nN6edMCL0Y5yAWml/5wOTV5Vpd1+/ArIh/4W8l0zkzN9RTdJpGbV3fktdIX+9\r\nWtwSJwbToijEuwSDk0w8PDSMeX2Yq9UphzY=\r\n=p1F3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "39f3beda6b396665bebffab94e8d7c45be30454c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/1.13.0/node@v18.14.2+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.14.2", "dependencies": {"jest-mock": "^29.5.0", "@jest/types": "^29.5.0", "@types/node": "*", "@jest/fake-timers": "^29.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_29.5.0_1678109617061_0.16686238805229325", "host": "s3://npm-registry-packages"}}, "29.6.0": {"name": "@jest/environment", "version": "29.6.0", "license": "MIT", "_id": "@jest/environment@29.6.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a873d228159cbba812505f7d13e2d1a2d04a577a", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-29.6.0.tgz", "fileCount": 4, "integrity": "sha512-bUZLYUxYlUIsslBbxII0fq0kr1+friI3Gty+cRLmocGB1jdcAHs7FS8QdCDqedE8q4DZE1g/AJHH6OJZBLGGsg==", "signatures": [{"sig": "MEUCIHydFoVNFJk0r5un8Jx7RUsTVKc3f+vSfSH//1pjtZrpAiEAvfLolKsQtG4Hylr5gjKObkVumcIC1914mz1aOZz6Lz0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15883}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c1e5b8a38ef54bb138409f89831942ebf6a7a67e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"jest-mock": "^29.6.0", "@jest/types": "^29.6.0", "@types/node": "*", "@jest/fake-timers": "^29.6.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_29.6.0_1688484349744_0.6754929537484875", "host": "s3://npm-registry-packages"}}, "29.6.1": {"name": "@jest/environment", "version": "29.6.1", "license": "MIT", "_id": "@jest/environment@29.6.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ee358fff2f68168394b4a50f18c68278a21fe82f", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-29.6.1.tgz", "fileCount": 4, "integrity": "sha512-RMMXx4ws+Gbvw3DfLSuo2cfQlK7IwGbpuEWXCqyYDcqYTI+9Ju3a5hDnXaxjNsa6uKh9PQF2v+qg+RLe63tz5A==", "signatures": [{"sig": "MEUCIGftIfd6JA8d5+AC9q7AMUZxWxIn7899huNwpmu3Dxv3AiEAo6PXxqy1dKBrYnC/A/l2xm5BAGtagmgxlaG8PiW5CIQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15883}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "1f019afdcdfc54a6664908bb45f343db4e3d0848", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"jest-mock": "^29.6.1", "@jest/types": "^29.6.1", "@types/node": "*", "@jest/fake-timers": "^29.6.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_29.6.1_1688653108968_0.5797865081527389", "host": "s3://npm-registry-packages"}}, "29.6.2": {"name": "@jest/environment", "version": "29.6.2", "license": "MIT", "_id": "@jest/environment@29.6.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "794c0f769d85e7553439d107d3f43186dc6874a9", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-29.6.2.tgz", "fileCount": 4, "integrity": "sha512-AEcW43C7huGd/vogTddNNTDRpO6vQ2zaQNrttvWV18ArBx9Z56h7BIsXkNFJVOO4/kblWEQz30ckw0+L3izc+Q==", "signatures": [{"sig": "MEQCIHzVgXCgrSGAhDka5hvVaL3JPX1UKAu7NO6mcZd/U0vZAiAlP17/bCile0AsfaRMEjjT6X3GNx9K0QuQF4mHfBApSg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15883}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0fd5b1c37555f485c56a6ad2d6b010a72204f9f6", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"jest-mock": "^29.6.2", "@jest/types": "^29.6.1", "@types/node": "*", "@jest/fake-timers": "^29.6.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_29.6.2_1690449694095_0.5928419969314533", "host": "s3://npm-registry-packages"}}, "29.6.3": {"name": "@jest/environment", "version": "29.6.3", "license": "MIT", "_id": "@jest/environment@29.6.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "bb02535c729393a0345b8d2c5eef94d34f7b35a3", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-29.6.3.tgz", "fileCount": 4, "integrity": "sha512-u/u3cCztYCfgBiGHsamqP5x+XvucftOGPbf5RJQxfpeC1y4AL8pCjKvPDA3oCmdhZYPgk5AE0VOD/flweR69WA==", "signatures": [{"sig": "MEYCIQD5hikzhO8Yi0vHVwGgxJe6jcpEahdqI1+eXUsWcu1TRQIhAJ7xnI0ojRrqvszkB4K5OE4hqex2MfIxjzX3WaO2ku+F", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15881}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fb7d95c8af6e0d65a8b65348433d8a0ea0725b5b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"jest-mock": "^29.6.3", "@jest/types": "^29.6.3", "@types/node": "*", "@jest/fake-timers": "^29.6.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_29.6.3_1692621561840_0.40268324762748997", "host": "s3://npm-registry-packages"}}, "29.6.4": {"name": "@jest/environment", "version": "29.6.4", "license": "MIT", "_id": "@jest/environment@29.6.4", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "78ec2c9f8c8829a37616934ff4fea0c028c79f4f", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-29.6.4.tgz", "fileCount": 4, "integrity": "sha512-sQ0SULEjA1XUTHmkBRl7A1dyITM9yb1yb3ZNKPX3KlTd6IG7mWUe3e2yfExtC2Zz1Q+mMckOLHmL/qLiuQJrBQ==", "signatures": [{"sig": "MEUCIQCcY0+n3X9585E4kAmY+q+N+qkFp2MmFOpyGezsjsZ/9QIgW1Id0vtcpE+jFyT2JQL8zNIfgSDSmQDUHAtsVVQ8t9U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15881}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "55cd6a0aaf6f9178199dfa7af7a00fcaa7c421fd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/1.13.0/node@v20.5.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.5.1", "dependencies": {"jest-mock": "^29.6.3", "@jest/types": "^29.6.3", "@types/node": "*", "@jest/fake-timers": "^29.6.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_29.6.4_1692875439686_0.8530662645227827", "host": "s3://npm-registry-packages"}}, "29.7.0": {"name": "@jest/environment", "version": "29.7.0", "license": "MIT", "_id": "@jest/environment@29.7.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "24d61f54ff1f786f3cd4073b4b94416383baf2a7", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-29.7.0.tgz", "fileCount": 4, "integrity": "sha512-aQIfHDq33ExsN4jP1NWGXhxgQ/wixs60gDiKO+XVMd8Mn0NWPWgc34ZQDTb2jKaUWQ7MuwoitXAsN2XVXNMpAw==", "signatures": [{"sig": "MEYCIQDTl48wnbrEnG5K2YXsTL8v+VEUN7FtCJWSmzS0GxTshAIhAONTCWe9e52NCL8dF9cwnW8T+z7YozmLl4dFgp6Z3ekq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15881}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4e56991693da7cd4c3730dc3579a1dd1403ee630", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"jest-mock": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "@jest/fake-timers": "^29.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_29.7.0_1694501026658_0.7992561305077737", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.1": {"name": "@jest/environment", "version": "30.0.0-alpha.1", "license": "MIT", "_id": "@jest/environment@30.0.0-alpha.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "69ba8ed354d8d1caa8a81f7c15d47d8719cb4c1c", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-30.0.0-alpha.1.tgz", "fileCount": 4, "integrity": "sha512-Enuv6+wJ4Y1fV91XaHutrMqki9ZKZbQNGB7h21pXxQjU+v5dwL8g9lHcq3dJen2SC4cWlpS+EFrxQnnXT6ul9Q==", "signatures": [{"sig": "MEUCIQCDr0ltJBzLNMvudWIlVDCzGl74nqtU4xngYOvbx4O75AIgIq2rpSuc+uvL2PHq6Fy1dQRTNMt96jy/qxBqX7reyeI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16649}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d005cb2505c041583e0c5636d006e08666a54b63", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/1.13.0/node@v20.9.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"jest-mock": "30.0.0-alpha.1", "@jest/types": "30.0.0-alpha.1", "@types/node": "*", "@jest/fake-timers": "30.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_30.0.0-alpha.1_1698672795116_0.43473453387444394", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.2": {"name": "@jest/environment", "version": "30.0.0-alpha.2", "license": "MIT", "_id": "@jest/environment@30.0.0-alpha.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "3ac144fac515f09cd163c09ff40a32dfb2f2440d", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-30.0.0-alpha.2.tgz", "fileCount": 4, "integrity": "sha512-rlSvTu+VmsTi9rhAVX691FdAGbYJKCS7nB9eImkxvIIfF5ebvQbh8Wzot8lRWB3mEzu9W0vLX3RoUzJXqI5W1w==", "signatures": [{"sig": "MEUCIQDfkemLr2LMq1rrMIGpyJuwWrfvM4cIxTA2kiqGrNpaQAIgJ+7vA71KQgFceCW80KYX4hmkT+5O/hpbrDyztw/kI5M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16494}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c04d13d7abd22e47b0997f6027886aed225c9ce4", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/2.7.0/node@v20.9.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"jest-mock": "30.0.0-alpha.2", "@jest/types": "30.0.0-alpha.2", "@types/node": "*", "@jest/fake-timers": "30.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_30.0.0-alpha.2_1700126913248_0.420981120710646", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.3": {"name": "@jest/environment", "version": "30.0.0-alpha.3", "license": "MIT", "_id": "@jest/environment@30.0.0-alpha.3", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "227b05951e3fb8a3ea5b39b3c28bc422b6dc799c", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-30.0.0-alpha.3.tgz", "fileCount": 4, "integrity": "sha512-AcJ9kUhN0lVlm/cJCr59AUrsqT2ZlszG1W+ENssqkueQLObCkpv5Jpv5ISM1F2FOt136OG0vLMTAp+V+r+gPYA==", "signatures": [{"sig": "MEUCIQDpdlyw2r1za3qEUZVdwgNx/ZO5wjCNDTQ4kf5HGLN9dwIgBQC0vemUK4RJtKaHxIBffL5qHDjHO92UtaeCENFDsr4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16608}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "e267aff33d105399f2134bad7c8f82285104f3da", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.2.1/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"jest-mock": "30.0.0-alpha.3", "@jest/types": "30.0.0-alpha.3", "@types/node": "*", "@jest/fake-timers": "30.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_30.0.0-alpha.3_1708427352922_0.2327160118614835", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.4": {"name": "@jest/environment", "version": "30.0.0-alpha.4", "license": "MIT", "_id": "@jest/environment@30.0.0-alpha.4", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "fbbf03a048d26d9f2cb97a1087ab5c3103e4930c", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-30.0.0-alpha.4.tgz", "fileCount": 4, "integrity": "sha512-2bjXGDOkvr2o6zJmTDIKNC1mKruFhtgV/LEfXbw7BwfXSxRBcA1stRRbkbBxX8Ibk2hWLsL4Y5Q98OmHt22ZLg==", "signatures": [{"sig": "MEUCIBVm4EQchlKWlLPb9BGGyDb+j5b08zDyDQPkvO/6sZJTAiEArBawyr2Bo9iH9CBw8x/C7ptEip0O6CRYFcaQtr3wWlo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16408}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "32b966f988d47a7673d2ef4b92e834dab7d66f07", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"jest-mock": "30.0.0-alpha.4", "@jest/types": "30.0.0-alpha.4", "@types/node": "*", "@jest/fake-timers": "30.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_30.0.0-alpha.4_1715550211665_0.6958378627271193", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.5": {"name": "@jest/environment", "version": "30.0.0-alpha.5", "license": "MIT", "_id": "@jest/environment@30.0.0-alpha.5", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "7178b30e84e78d91a23d25459225a16810bb4942", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-30.0.0-alpha.5.tgz", "fileCount": 4, "integrity": "sha512-uWnbbtArBcrR6T2XKPlNp5eCxe/48U0imDJMr2t63FIqoIZJOvrVbRtKQEhjzt+c9YkzNoVBOvgu7aLdwVrZ8g==", "signatures": [{"sig": "MEUCIH+2RhfNXj+DVmLidOAAjKbL1LI/GUj2W464K9o1GjAbAiEA/7ipJMItlFWrbgFceSyHabvzAdtdlX3dm0iy5gi3/O4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16408}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fa24a3bdd6682978d76799265016fb9d5bff135e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"jest-mock": "30.0.0-alpha.5", "@jest/types": "30.0.0-alpha.5", "@types/node": "*", "@jest/fake-timers": "30.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_30.0.0-alpha.5_1717073051159_0.914281743731624", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.6": {"name": "@jest/environment", "version": "30.0.0-alpha.6", "license": "MIT", "_id": "@jest/environment@30.0.0-alpha.6", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "dist": {"shasum": "f6fd15d6d8b985d6cc8f58dcb699e240bd3a7590", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-30.0.0-alpha.6.tgz", "fileCount": 4, "integrity": "sha512-pjNYNkzq761hh8D2grrG77L6nhe2VBCFFM+G1hyqhaJ2MAzhp2Gh+G94uF3px7luSzLh8GYvGJQGYy197EUOGQ==", "signatures": [{"sig": "MEYCIQCl1+VGnod03eWuo4g5UuIfNk5f3+Q1bl7wNTn/YwVrsgIhAJG6zSh1SLQ0HPMumqPHbDyg0AKGhygIOmvxZk4su5W/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16627}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ba74b7de1b9cca88daf33f9d1b46bfe2b7f485a5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.7.1/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"jest-mock": "30.0.0-alpha.6", "@jest/types": "30.0.0-alpha.6", "@types/node": "*", "@jest/fake-timers": "30.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_30.0.0-alpha.6_1723102988998_0.9042265951815278", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.7": {"name": "@jest/environment", "version": "30.0.0-alpha.7", "license": "MIT", "_id": "@jest/environment@30.0.0-alpha.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "6de9259b2dbc1013fb55d73b7ebbdbc2345a5b8b", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-30.0.0-alpha.7.tgz", "fileCount": 4, "integrity": "sha512-sEszhsMrT7Jh0ngVjR8q36payUT2NU0kYwd5rdxUzlVha8gZg2FTq1VMjgETEnYyGXrEmnk7MmBUxTbT7dYrUw==", "signatures": [{"sig": "MEYCIQDCqzmOi0RnKdp1o6CyRjTSInFpDvHGBK4WIbO8nrSQFgIhAM95GHl0q/SshJdeNAhmpph4neqNlbGDBv4ij3ZmC+AI", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17201}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "bacb7de30d053cd87181294b0c8a8576632a8b02", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.11.0/node@v20.18.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"jest-mock": "30.0.0-alpha.7", "@jest/types": "30.0.0-alpha.7", "@types/node": "*", "@jest/fake-timers": "30.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_30.0.0-alpha.7_1738225716287_0.39798766141376296", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.2": {"name": "@jest/environment", "version": "30.0.0-beta.2", "license": "MIT", "_id": "@jest/environment@30.0.0-beta.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "05cd2a13017829f09f84cbda7aa2207288a2e3ad", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-30.0.0-beta.2.tgz", "fileCount": 4, "integrity": "sha512-Bl56xC6lYl+XLjXOEMEecX8II5iNdOLDVNYchEWRSl+uLm847xf2R7tWkilcvsZ57L5iG3ZegLW1fdgwaPhJXw==", "signatures": [{"sig": "MEQCIDSogLqKlR9AyFUg3zTz6/sKVASoySYroOJevyWE+2LlAiBYG/q0pBnoWz3zegPL7XuCWhyoszUqYQNqxuqz3pB3kg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17192}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "53a5635ac9a43099033f6103e179b13a5465e017", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"jest-mock": "30.0.0-beta.1", "@jest/types": "30.0.0-beta.1", "@types/node": "*", "@jest/fake-timers": "30.0.0-beta.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_30.0.0-beta.2_1748308996161_0.14565391451789433", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.3": {"name": "@jest/environment", "version": "30.0.0-beta.3", "license": "MIT", "_id": "@jest/environment@30.0.0-beta.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "ea02c3629eb4d8e2bef35e5d690a8404c7754b93", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-30.0.0-beta.3.tgz", "fileCount": 4, "integrity": "sha512-1qcDVc37nlItrkYXrWC9FFXU0CxNUe/PGYpHzOz6zIX7FKFv7i8Z0LKBs27iN6XIhczrmQtFzs9JUnHGARWRoA==", "signatures": [{"sig": "MEQCIBgYqPadzElvcv1djAvlK0isNrQyw1C75HCnEyuMuoxMAiADIS4g6ItCyNlSKCpNg8iafzV/A6MQcHjdDCLc1a2z5g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17192}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a123a3b667a178fb988662aaa1bc6308af759017", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"jest-mock": "30.0.0-beta.3", "@jest/types": "30.0.0-beta.3", "@types/node": "*", "@jest/fake-timers": "30.0.0-beta.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_30.0.0-beta.3_1748309270761_0.4027810996259471", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.4": {"name": "@jest/environment", "version": "30.0.0-beta.4", "license": "MIT", "_id": "@jest/environment@30.0.0-beta.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "2c30bb89029818f77846e01ad091a705dd08adaf", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-30.0.0-beta.4.tgz", "fileCount": 4, "integrity": "sha512-u4G5TV7PG2jFeLezqgnsneWOuAe3Xg9ENdIKGTIvxJf7TFL5fWgWnUa9dmoLR1zC+Yid6bR1mXbyoP5aWWqM+A==", "signatures": [{"sig": "MEUCIQCXGvdWtnPUg0EU31Siw20giEY9ztfkEbQUwJxTjktXigIgOsloOCZxB2gYvtMizN9kDVTrW+AZBcmmtId+mTaGU8E=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17192}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "69f0c890c804e6e6b0822adb592cd00372a7c297", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"jest-mock": "30.0.0-beta.3", "@jest/types": "30.0.0-beta.3", "@types/node": "*", "@jest/fake-timers": "30.0.0-beta.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_30.0.0-beta.4_1748329469930_0.880696880006149", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.5": {"name": "@jest/environment", "version": "30.0.0-beta.5", "license": "MIT", "_id": "@jest/environment@30.0.0-beta.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "47a9ed03ba4e291c017fc8379c811d3ebe69aa28", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-30.0.0-beta.5.tgz", "fileCount": 4, "integrity": "sha512-/ttyIJWKpPpZGhNPblqCGvBrPbLDTxZafxB+ONYPW/dXsFZV7APQzTa+rgniqUc6+siVH53GHFHwtY1aNHxRzA==", "signatures": [{"sig": "MEUCIBtfsV48dsWl8zspXt1ewKpBbKW5JJwKLirOceEhdtGbAiEAojn/bdUOXU55iwfanz3X0Gi8uvt4AMheTdSUbl0z8mo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17192}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f2171bb4c6836d74ad2b32a48151d9e0fdfa20a2", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"jest-mock": "30.0.0-beta.3", "@jest/types": "30.0.0-beta.3", "@types/node": "*", "@jest/fake-timers": "30.0.0-beta.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_30.0.0-beta.5_1748478612777_0.13487532441609118", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.6": {"name": "@jest/environment", "version": "30.0.0-beta.6", "license": "MIT", "_id": "@jest/environment@30.0.0-beta.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "4945b804bff5073b9e8a478699df60fe545b68da", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-30.0.0-beta.6.tgz", "fileCount": 4, "integrity": "sha512-5Arjyr2/7ptp+9J54jX/fcvys6Es54XkOAm8M6H5xjGPiv3OXW70+pfVe1m+t01Kdgo5Jm2yJSDH4MIqjq2SYg==", "signatures": [{"sig": "MEQCIBffOKUm7N3Has+Eepx3uaSwz1Yboiv5XuDtQn7c402hAiAKDsPsntwK4tu/tE6hhJIYeQA9jrp+D6TLYiYyks/xRQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17203}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4f964497dc21c06ce4d54f1349e299a9f6773d52", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/3.12.3/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"jest-mock": "30.0.0-beta.6", "@jest/types": "30.0.0-beta.6", "@types/node": "*", "@jest/fake-timers": "30.0.0-beta.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_30.0.0-beta.6_1748994651302_0.2905285288914268", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.7": {"name": "@jest/environment", "version": "30.0.0-beta.7", "license": "MIT", "_id": "@jest/environment@30.0.0-beta.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "fa826a0790780c4ca1fbc4375886e461abb72faa", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-30.0.0-beta.7.tgz", "fileCount": 4, "integrity": "sha512-oZQb4wIUbg2RQMr4FupbnjVYRdp5Ju77L7uksoXzGdVv7apC1rCSPliVM1PBJ4XD/sLQk8s2P2mA0p+jJ3URxg==", "signatures": [{"sig": "MEUCIF6iuDtaHoPk8kMT6akNA9ZsMIPv5GBp4dGaWT9pp/MiAiEA6RCUOY5bz8dDYc0pDxi8KAloCFTb9/mAaF78p+dLHqw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17203}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "48de6a91368727d853d491df16e7d00c1f323676", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"jest-mock": "30.0.0-beta.7", "@jest/types": "30.0.0-beta.7", "@types/node": "*", "@jest/fake-timers": "30.0.0-beta.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_30.0.0-beta.7_1749008144995_0.5913654229807517", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.8": {"name": "@jest/environment", "version": "30.0.0-beta.8", "license": "MIT", "_id": "@jest/environment@30.0.0-beta.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "2a0565a1efde0378dd1e638412c1a0a06ad42486", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-30.0.0-beta.8.tgz", "fileCount": 4, "integrity": "sha512-yMIi/n6wewZH2L0EiaUpjeeCYltVvcZ4RvsOm86CqiaDiO8sC6BX5Rg4VHzNoGjikzyf0K6Sp4mnny+JSTutjQ==", "signatures": [{"sig": "MEQCIEH6rUJKLyGF7WY5XoRKD30N9RXFgGn7s8UMBWXML/57AiA9LhIxzNaumD4EnHSKAObKKDWH1W1bFjKHSeSMyqaOcw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17203}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ac334c0cdf04ead9999f0964567d81672d116d42", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"jest-mock": "30.0.0-beta.8", "@jest/types": "30.0.0-beta.8", "@types/node": "*", "@jest/fake-timers": "30.0.0-beta.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_30.0.0-beta.8_1749023594352_0.38861599396265434", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-rc.1": {"name": "@jest/environment", "version": "30.0.0-rc.1", "license": "MIT", "_id": "@jest/environment@30.0.0-rc.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "180abf1c2e8d1d7f2499daf8bdda613adc147f68", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-30.0.0-rc.1.tgz", "fileCount": 4, "integrity": "sha512-SiLhwI/ZbwBD1SsHlJzC3/ykQIoXo7JbXFxtd/wuwRyT7xKcJSMySxDS2+GALfyysNX3ApgGVY8FAcYxW0ZT/g==", "signatures": [{"sig": "MEUCIQCjnExwOQzdFZAEiOA4HbYPN/4qeLCwLXcY8ejsvqTpIAIgKwdH32C5c0yHGVpLVvreRlMeJH9Lb1HOpSDMFP/gJBM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17197}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ce14203d9156f830a8e24a6e3e8205f670a72a40", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"jest-mock": "30.0.0-rc.1", "@jest/types": "30.0.0-beta.8", "@types/node": "*", "@jest/fake-timers": "30.0.0-rc.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_30.0.0-rc.1_1749430967826_0.8386795405030303", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0": {"name": "@jest/environment", "version": "30.0.0", "license": "MIT", "_id": "@jest/environment@30.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "d66484e35d6ee9a551d2ef3adb9e18728f0e4736", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-30.0.0.tgz", "fileCount": 4, "integrity": "sha512-09sFbMMgS5JxYnvgmmtwIHhvoyzvR5fUPrVl8nOCrC5KdzmmErTcAxfWyAhJ2bv3rvHNQaKiS+COSG+O7oNbXw==", "signatures": [{"sig": "MEQCIHnzsS9xosVtPES9uvp81kORSe3w4QwxosoXPkC2Av22AiBX+jlfVRBhvf9esdLUUzUeD+mfmhTO+gr9ADq4ld1Vmg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17175}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a383155cd5af4539b3c447cfa7184462ee32f418", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"jest-mock": "30.0.0", "@jest/types": "30.0.0", "@types/node": "*", "@jest/fake-timers": "30.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_30.0.0_1749521754503_0.5125139883552303", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.1": {"name": "@jest/environment", "version": "30.0.1", "license": "MIT", "_id": "@jest/environment@30.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "21ce222c00c7f5706852cf346a657c72b05eb66c", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-30.0.1.tgz", "fileCount": 4, "integrity": "sha512-JFI3qCT4ps9UjQNievPdsmpX+mOcAjOR2aemGUJbNiwpsuSCbiAaXwa2yBCND7OqCxUoiWMh6Lf/cwGxt/m2NA==", "signatures": [{"sig": "MEYCIQCpI8164m18Id05FMZLjfpA6cRceSJMMNWMJMplcceOjQIhAMUXObHiodxVTR9hx/zrAl6ZgWkO88OjOOVRZj7e+imL", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 17175}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "5ce865b4060189fe74cd486544816c079194a0f7", "_npmUser": {"name": "cpojer", "actor": {"name": "cpojer", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-environment"}, "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.2.0", "dependencies": {"jest-mock": "30.0.1", "@jest/types": "30.0.1", "@types/node": "*", "@jest/fake-timers": "30.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/environment_30.0.1_1750285892141_0.9511265865000817", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.2": {"name": "@jest/environment", "version": "30.0.2", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-environment"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@jest/fake-timers": "30.0.2", "@jest/types": "30.0.1", "@types/node": "*", "jest-mock": "30.0.2"}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "393acbfac31f64bb38dff23c89224797caded83c", "_nodeVersion": "24.2.0", "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "_id": "@jest/environment@30.0.2", "dist": {"integrity": "sha512-hRLhZRJNxBiOhxIKSq2UkrlhMt3/zVFQOAi5lvS8T9I03+kxsbflwHJEF+eXEYXCrRGRhHwECT7CDk6DyngsRA==", "shasum": "1b0d055070e97f697e9edb25059e9435221cbe65", "tarball": "https://registry.npmjs.org/@jest/environment/-/environment-30.0.2.tgz", "fileCount": 4, "unpackedSize": 17175, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQDJFhVrpHm7aAdL4ene71LQYQo1AGUzKEkn1PIEsohxcAIhAOws+Jg6dF5V3i863dE9UPGpsyfiJTvbqg5SFBUoq2iU"}]}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>", "actor": {"name": "cpojer", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/environment_30.0.2_1750329981487_0.49620494904633206"}, "_hasShrinkwrap": false}}, "time": {"created": "2019-03-05T15:00:14.998Z", "modified": "2025-06-19T10:46:21.953Z", "24.2.0-alpha.0": "2019-03-05T15:00:15.299Z", "24.3.0": "2019-03-07T13:00:17.047Z", "24.3.1": "2019-03-07T23:12:38.648Z", "24.4.0": "2019-03-11T14:58:09.477Z", "24.5.0": "2019-03-12T16:48:47.988Z", "24.6.0": "2019-04-01T22:27:00.084Z", "24.7.0": "2019-04-03T03:55:54.046Z", "24.7.1": "2019-04-04T01:19:09.813Z", "24.8.0": "2019-05-05T02:02:52.452Z", "24.9.0": "2019-08-16T06:21:54.088Z", "25.0.0": "2019-08-22T03:24:29.255Z", "25.1.0": "2020-01-22T01:00:21.098Z", "25.2.0-alpha.86": "2020-03-25T17:16:55.546Z", "25.2.0": "2020-03-25T17:58:45.406Z", "25.2.1-alpha.1": "2020-03-26T07:54:45.474Z", "25.2.1-alpha.2": "2020-03-26T08:10:53.849Z", "25.2.1": "2020-03-26T09:01:39.106Z", "25.2.3": "2020-03-26T20:25:16.923Z", "25.2.4": "2020-03-29T19:38:50.784Z", "25.2.6": "2020-04-02T10:29:29.612Z", "25.3.0": "2020-04-08T13:21:37.501Z", "25.4.0": "2020-04-19T21:50:37.411Z", "25.5.0": "2020-04-28T19:45:34.301Z", "26.0.0-alpha.0": "2020-05-02T12:13:17.119Z", "26.0.0-alpha.1": "2020-05-03T18:48:13.337Z", "26.0.0-alpha.2": "2020-05-04T16:05:41.846Z", "26.0.0": "2020-05-04T17:53:20.231Z", "26.0.1-alpha.0": "2020-05-04T22:16:07.371Z", "26.0.1": "2020-05-05T10:41:01.092Z", "26.1.0": "2020-06-23T15:15:25.434Z", "26.2.0": "2020-07-30T10:11:56.269Z", "26.3.0": "2020-08-10T11:32:03.858Z", "26.5.0": "2020-10-05T09:28:28.294Z", "26.5.2": "2020-10-06T10:52:57.685Z", "26.6.0": "2020-10-19T11:58:51.088Z", "26.6.1": "2020-10-23T09:06:49.393Z", "26.6.2": "2020-11-02T12:51:48.628Z", "27.0.0-next.0": "2020-12-05T17:25:36.412Z", "27.0.0-next.1": "2020-12-07T12:43:38.132Z", "27.0.0-next.3": "2021-02-18T22:10:12.636Z", "27.0.0-next.4": "2021-03-08T13:45:07.422Z", "27.0.0-next.5": "2021-03-15T13:03:35.591Z", "27.0.0-next.6": "2021-03-25T19:40:14.718Z", "27.0.0-next.7": "2021-04-02T13:48:08.735Z", "27.0.0-next.8": "2021-04-12T22:42:40.429Z", "27.0.0-next.9": "2021-05-04T06:25:20.635Z", "27.0.0-next.10": "2021-05-20T14:11:32.036Z", "27.0.0-next.11": "2021-05-20T22:28:53.422Z", "27.0.0": "2021-05-25T08:15:22.255Z", "27.0.1": "2021-05-25T10:06:45.629Z", "27.0.2": "2021-05-29T12:07:32.808Z", "27.0.3": "2021-05-29T17:47:41.422Z", "27.0.5": "2021-06-22T11:10:45.964Z", "27.0.6": "2021-06-28T17:05:52.481Z", "27.1.0": "2021-08-27T09:59:46.810Z", "27.1.1": "2021-09-08T10:12:20.978Z", "27.2.0": "2021-09-13T08:06:52.926Z", "27.2.2": "2021-09-25T13:35:11.055Z", "27.2.3": "2021-09-28T10:11:27.270Z", "27.2.4": "2021-09-29T14:04:52.750Z", "27.2.5": "2021-10-08T13:39:28.790Z", "27.3.0": "2021-10-17T18:34:48.971Z", "27.3.1": "2021-10-19T06:57:35.874Z", "27.4.0": "2021-11-29T13:37:33.736Z", "27.4.1": "2021-11-30T08:37:17.431Z", "27.4.2": "2021-11-30T11:53:56.173Z", "27.4.4": "2021-12-10T04:43:07.375Z", "27.4.6": "2022-01-04T23:03:45.763Z", "27.5.0": "2022-02-05T09:59:30.457Z", "27.5.1": "2022-02-08T10:52:27.518Z", "28.0.0-alpha.0": "2022-02-10T18:17:40.784Z", "28.0.0-alpha.1": "2022-02-15T21:27:06.535Z", "28.0.0-alpha.2": "2022-02-16T18:12:14.610Z", "28.0.0-alpha.3": "2022-02-17T15:42:26.002Z", "28.0.0-alpha.4": "2022-02-22T12:13:58.045Z", "28.0.0-alpha.5": "2022-02-24T20:57:23.432Z", "28.0.0-alpha.6": "2022-03-01T08:32:27.771Z", "28.0.0-alpha.7": "2022-03-06T10:02:44.172Z", "28.0.0-alpha.8": "2022-04-05T14:59:58.490Z", "28.0.0-alpha.9": "2022-04-19T10:59:18.076Z", "28.0.0-alpha.11": "2022-04-20T13:30:57.932Z", "28.0.0": "2022-04-25T12:08:13.124Z", "28.0.1": "2022-04-26T10:02:43.385Z", "28.0.2": "2022-04-27T07:44:05.905Z", "28.1.0": "2022-05-06T10:48:56.944Z", "28.1.1": "2022-06-07T06:09:39.314Z", "28.1.2": "2022-06-29T10:33:58.226Z", "28.1.3": "2022-07-13T14:12:31.880Z", "29.0.0-alpha.0": "2022-07-17T22:07:10.497Z", "29.0.0-alpha.1": "2022-08-04T08:23:32.552Z", "29.0.0-alpha.3": "2022-08-07T13:41:38.671Z", "29.0.0-alpha.4": "2022-08-08T13:05:37.436Z", "29.0.0-alpha.6": "2022-08-19T13:57:51.950Z", "29.0.0": "2022-08-25T12:33:30.722Z", "29.0.1": "2022-08-26T13:34:44.375Z", "29.0.2": "2022-09-03T10:48:22.188Z", "29.0.3": "2022-09-10T14:41:40.625Z", "29.1.0": "2022-09-28T07:37:44.449Z", "29.1.1": "2022-09-28T08:05:35.813Z", "29.1.2": "2022-09-30T07:22:52.324Z", "29.2.0": "2022-10-14T09:13:52.849Z", "29.2.1": "2022-10-18T16:00:17.113Z", "29.2.2": "2022-10-24T20:24:08.895Z", "29.3.0": "2022-11-07T17:55:49.680Z", "29.3.1": "2022-11-08T22:56:25.803Z", "29.4.0": "2023-01-24T10:55:55.534Z", "29.4.1": "2023-01-26T15:08:40.772Z", "29.4.2": "2023-02-07T13:45:33.354Z", "29.4.3": "2023-02-15T11:57:29.584Z", "29.5.0": "2023-03-06T13:33:37.167Z", "29.6.0": "2023-07-04T15:25:49.915Z", "29.6.1": "2023-07-06T14:18:29.131Z", "29.6.2": "2023-07-27T09:21:34.309Z", "29.6.3": "2023-08-21T12:39:22.094Z", "29.6.4": "2023-08-24T11:10:39.885Z", "29.7.0": "2023-09-12T06:43:46.809Z", "30.0.0-alpha.1": "2023-10-30T13:33:15.273Z", "30.0.0-alpha.2": "2023-11-16T09:28:33.442Z", "30.0.0-alpha.3": "2024-02-20T11:09:13.072Z", "30.0.0-alpha.4": "2024-05-12T21:43:31.813Z", "30.0.0-alpha.5": "2024-05-30T12:44:11.295Z", "30.0.0-alpha.6": "2024-08-08T07:43:09.144Z", "30.0.0-alpha.7": "2025-01-30T08:28:36.475Z", "30.0.0-beta.2": "2025-05-27T01:23:16.337Z", "30.0.0-beta.3": "2025-05-27T01:27:50.947Z", "30.0.0-beta.4": "2025-05-27T07:04:30.097Z", "30.0.0-beta.5": "2025-05-29T00:30:12.972Z", "30.0.0-beta.6": "2025-06-03T23:50:51.529Z", "30.0.0-beta.7": "2025-06-04T03:35:45.198Z", "30.0.0-beta.8": "2025-06-04T07:53:14.518Z", "30.0.0-rc.1": "2025-06-09T01:02:48.033Z", "30.0.0": "2025-06-10T02:15:54.720Z", "30.0.1": "2025-06-18T22:31:32.310Z", "30.0.2": "2025-06-19T10:46:21.717Z"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-environment"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}