{"_id": "jest-leak-detector", "_rev": "203-fc91b4f9564d3336c43b7315f3501786", "name": "jest-leak-detector", "dist-tags": {"next": "30.0.0-rc.1", "latest": "30.0.2"}, "versions": {"0.0.0": {"name": "jest-leak-detector", "version": "0.0.0", "_id": "jest-leak-detector@0.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "56069fb03c1bb1172575d018b3b7dc6ff80312fc", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-0.0.0.tgz", "integrity": "sha512-eXRUbsGLfQ9FmfBsm5MsrUM03w/8V4qrkIW7IIEyKoj2qMd9wjAWKp+UQJ6GVJeD2Dnim3dYeokh/aAvieG9VA==", "signatures": [{"sig": "MEYCIQDnUV3x2vc3p2htOYKhCIIABFpv2GQYmHhbZjB+uCCqFAIhAPi2JXH+1j+9YEjaza0UjytKZqnRluUllddpTPRx9NNg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "_npmVersion": "5.3.0", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector-0.0.0.tgz_1510431291828_0.08395390887744725", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.9": {"name": "jest-leak-detector", "version": "21.3.0-beta.9", "license": "MIT", "_id": "jest-leak-detector@21.3.0-beta.9", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9484f3521fcff74e2de93138ca48bb1b0c1f85b9", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-21.3.0-beta.9.tgz", "integrity": "sha512-AAr2/jzeNzoWvVGBh0vGskgACmYpBIFFlBh7bYVTDoKY2reSIp5z7sstlbFYAxNj8FXrvpdTxuuVcCOsVsqvzA==", "signatures": [{"sig": "MEUCIQDlv3j77lCMk4USwYStgM50PGbCW1kG5njtbHVS2nSfrgIgf1RZpJ3+/ujLnYPfneE8NeYiCgSAa+RYsw0gUwvJyQ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"weak": "^1.0.1", "pretty-format": "21.3.0-beta.9"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector-21.3.0-beta.9.tgz_1511356659267_0.046100664185360074", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.10": {"name": "jest-leak-detector", "version": "21.3.0-beta.10", "license": "MIT", "_id": "jest-leak-detector@21.3.0-beta.10", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1b715bb7e9e58ad2ad18a32058b3e10bfa8f1199", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-21.3.0-beta.10.tgz", "integrity": "sha512-MLxYuahElHJW/BUs4H00qNsybJUCfyySYd89OzNHNyXic5n6Ga2DOHaOVIz2KE1dRpIJZg2zFgaAN2lZjVo8Vg==", "signatures": [{"sig": "MEUCIQCaWopuZhUt6AX7SersuySmu3giQysghn8zV56N7p3nIQIgTM/bx3oB95XQqP4PHtmX3xqVjg52xzSx8/4XtKXOxTw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"weak": "^1.0.1", "pretty-format": "21.3.0-beta.10"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector-21.3.0-beta.10.tgz_1511613569715_0.5435668891295791", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.11": {"name": "jest-leak-detector", "version": "21.3.0-beta.11", "license": "MIT", "_id": "jest-leak-detector@21.3.0-beta.11", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "da03fc03df33cb31cac5c7b27f0455501b8e51dd", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-21.3.0-beta.11.tgz", "integrity": "sha512-/e+U0IZsCuNi60XZRus9tU4f8DxbsHGhjQLAwlp5Jnc/f9eblsV3H1X9QQFppSwhNf5nBljmDfKpJxmLM9Ce0g==", "signatures": [{"sig": "MEYCIQCRyMYG/1dSCJkTih1oQggdWgwDM0EhDGnot8jhKf6T9gIhANtEoK6OjY585QHek22oMJZsTMi8YtG1/KlxiaW7rtK5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"weak": "^1.0.1", "pretty-format": "21.3.0-beta.11"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector-21.3.0-beta.11.tgz_1511965884931_0.2720079473219812", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.12": {"name": "jest-leak-detector", "version": "21.3.0-beta.12", "license": "MIT", "_id": "jest-leak-detector@21.3.0-beta.12", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "75d1589a75724bb50bbac117aa323bccf3624630", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-21.3.0-beta.12.tgz", "integrity": "sha512-04oDxrDA6F0l9q3MYA3vvGhp6FY7XX27hm3iWx0DswnARl9G6Zh8Zw1KFy98nya8fcBO00p/UOhlYroTdsnVPQ==", "signatures": [{"sig": "MEYCIQCXabRQ4um2OvxluoQI6IvV3ZsXisWRCzEfJ9cCB/OKnQIhAMXTwbfhFhITqBBgyYavM/1lHzs/h0nAgFvZPnKn5cm9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"weak": "^1.0.1", "pretty-format": "21.3.0-beta.12"}, "optionalDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector-21.3.0-beta.12.tgz_1512499720101_0.32035309146158397", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.13": {"name": "jest-leak-detector", "version": "21.3.0-beta.13", "license": "MIT", "_id": "jest-leak-detector@21.3.0-beta.13", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "05301ba87d9233eb2f7bb79e32e4d6050e1d17cd", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-21.3.0-beta.13.tgz", "integrity": "sha512-XWss/NHswRuES8oTLuUybZxZF5sR0qCLVtbY9WOMyfo+xRHHDE5Z5MKlWjYJvpCi5aUIF2w0feMHcEdEJ0MlEg==", "signatures": [{"sig": "MEYCIQCw4mexkfx86jcomlwD+MbLFPHl6HXX/jOCDokhsx16EgIhAOhv+1BI89TzG774BVfotB5kONEv164Pq+oBKYFhuPje", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"weak": "^1.0.1", "pretty-format": "21.3.0-beta.13"}, "optionalDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector-21.3.0-beta.13.tgz_1512571047343_0.7726311071310192", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.14": {"name": "jest-leak-detector", "version": "21.3.0-beta.14", "license": "MIT", "_id": "jest-leak-detector@21.3.0-beta.14", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5798e6ea1ef19622f03de016e0d8f9bc178e0da2", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-21.3.0-beta.14.tgz", "integrity": "sha512-gWhdzGD09ldLHfbu8kY6Mw/uYA/YoIBx34nDwL7ezNMvP7a9pYGqUIWXrU2ccl0I0iUGsx25SLRdk4c1lY/4SQ==", "signatures": [{"sig": "MEQCICWKDupzO8CkODQUhA/+8esYCL0x3BCmAfnH6MateEXHAiBoY50FSrVm01nBz8YujSut7/9MSoOdHRDJONJ18GKvDA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "9.2.1", "dependencies": {"weak": "^1.0.1", "pretty-format": "21.3.0-beta.14"}, "optionalDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector-21.3.0-beta.14.tgz_1513075960259_0.7564278484787792", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.15": {"name": "jest-leak-detector", "version": "21.3.0-beta.15", "license": "MIT", "_id": "jest-leak-detector@21.3.0-beta.15", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "750906e8ff97a1859e79a1796aed861d720474aa", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-21.3.0-beta.15.tgz", "integrity": "sha512-Od4oM3XqT22Wv3Rr95oA5z1Ob6o/jeBu9rwNHRokyCmA58FwzyOPcLNvhaZgpsvwNaDhDRwcOqxWq9mZoWwShQ==", "signatures": [{"sig": "MEQCIGmP2qVoClBTbARFiloI+DokqrEb3X0GAC4Whzbxh/LZAiB1jN7pLVZw0iAHzExyqTQpXIb0ZoPQu3aqnHrj2NSRYQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "9.2.1", "dependencies": {"weak": "^1.0.1", "pretty-format": "21.3.0-beta.15"}, "optionalDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector-21.3.0-beta.15.tgz_1513344463901_0.06916486169211566", "host": "s3://npm-registry-packages"}}, "22.0.0": {"name": "jest-leak-detector", "version": "22.0.0", "license": "MIT", "_id": "jest-leak-detector@22.0.0", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e2fee3674f4c2d62f538c4935a8fe146764cec39", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-22.0.0.tgz", "integrity": "sha512-Zx9937829uwTbv8UhxEIoNMcW71B/dAO034VpRkl5nthZW13KjesgowICl9qS0I5rZj5uawQYHk86/nZTVQ0FA==", "signatures": [{"sig": "MEQCIHrq7xLZYYHLiTWuhqFzb2NXB8qVcSXH0NfWST5bpEzbAiB08qgP4G0Rgo9QWY6TKTMB3MSBxkzFyMQwlTbmW+rtZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"weak": "^1.0.1", "pretty-format": "^22.0.0"}, "optionalDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector-22.0.0.tgz_1513595008270_0.7206950678955764", "host": "s3://npm-registry-packages"}}, "22.0.1": {"name": "jest-leak-detector", "version": "22.0.1", "license": "MIT", "_id": "jest-leak-detector@22.0.1", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0f1a8ce716ad33e751ef73c3f795222762ef41c6", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-22.0.1.tgz", "integrity": "sha512-X175D/mqqdYBatSU/u+ctOEtlUp2UETmLHEvxJvP0pjRZ+9mZ/CBFJ+GB6cOTS1a1gN6gT9YMpw3m9suKiuvdw==", "signatures": [{"sig": "MEQCIFVN6VVZhRkxwvoSwA/Qgc4JvrLMwiTjGq07oWMnhPpcAiB8h5KT5tlZ44QDdZE3KTvQOGEWvFEX5jN/1go03q4fww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "9.2.1", "dependencies": {"weak": "^1.0.1", "pretty-format": "^22.0.1"}, "optionalDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector-22.0.1.tgz_1513628968730_0.5216033312026411", "host": "s3://npm-registry-packages"}}, "22.0.2": {"name": "jest-leak-detector", "version": "22.0.2", "license": "MIT", "_id": "jest-leak-detector@22.0.2", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "bfb503131903a3163b30b5a7ebceb4a6272bb45d", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-22.0.2.tgz", "integrity": "sha512-u3gOMM6Ad/VvYFAoem2eJ742ufRw5Qa8x0V+jwyHtv7aZHgesPl6TAsKThGgsEbaC/Td8tgv8zgg/Nin7PfTog==", "signatures": [{"sig": "MEUCIHZxn+pjvr4Wa1BupNllo2/WnhfyvjAj0dZmAgNp+CpSAiEA7SG0LPHLbVDuePnUq4ACHkNGW2Scfllc+noRO0Xt4Nw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "9.2.1", "dependencies": {"weak": "^1.0.1", "pretty-format": "^22.0.2"}, "optionalDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector-22.0.2.tgz_1513691587802_0.2096081234049052", "host": "s3://npm-registry-packages"}}, "22.0.3": {"name": "jest-leak-detector", "version": "22.0.3", "license": "MIT", "_id": "jest-leak-detector@22.0.3", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b64904f0e8954a11edb79b0809ff4717fa762d99", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-22.0.3.tgz", "integrity": "sha512-xyVdAmcG8M3jWtVeadDUU6MAHLBrjkP4clz2UtTZ1gpe5bRLk27VjQOpzTwK20MkV/6iZQhSuRVuzHS5kD0HpA==", "signatures": [{"sig": "MEYCIQDrwARRJo3jMUxt1SMSLqVMsYfW2k1y8/uC4HowX05gzgIhAIDmxNSRS0qjt9RsDT+zOJtmNHvrVPsdNf+bqJL4kmod", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "9.2.1", "dependencies": {"weak": "^1.0.1", "pretty-format": "^22.0.3"}, "optionalDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector-22.0.3.tgz_1513695539384_0.7089802836999297", "host": "s3://npm-registry-packages"}}, "22.0.5": {"name": "jest-leak-detector", "version": "22.0.5", "license": "MIT", "_id": "jest-leak-detector@22.0.5", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "277f792b6a71fa3a412ddfbd5d14aa190c29bea5", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-22.0.5.tgz", "integrity": "sha512-vxqiSBwoqLglZqw/IFWSdYOw1V4oMNXJpLySsQ+yKatM4I80uXJalDNoezqZvrQVX9zKuVpxIUqYGXDTF2mCYA==", "signatures": [{"sig": "MEUCIQCMMTPS8J1OSPvWnpAe7blDWPjEWzSq5nQY2RW6ntFFBAIgAuprFczT7yWyQK6zR7SKWu32m63hvyl1b1OKCVPqsYw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"pretty-format": "^22.0.5"}, "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector-22.0.5.tgz_1515510596540_0.652998790377751", "host": "s3://npm-registry-packages"}}, "22.0.6": {"name": "jest-leak-detector", "version": "22.0.6", "license": "MIT", "_id": "jest-leak-detector@22.0.6", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e983e6fca0959f095cd5b39df2a9a8c956f45988", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-22.0.6.tgz", "integrity": "sha512-Ll8C0iH8Un+liiOnnp18WTVScvhSr6GUFrhFa4p46peFWg8rgh4MuJvG7A1Dwpa00ZHV5W5kzVViFaedDWxJMw==", "signatures": [{"sig": "MEQCIH2eFui7vm2NSZ3d2qoPMoWsWGrQR/GqZYSgVMiZlkzfAiBX/+ROmImX0mmjLgnV3vPv6hKRU7oEkvsP0DvLdDr2lQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"pretty-format": "^22.0.6"}, "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector-22.0.6.tgz_1515664009617_0.332408215617761", "host": "s3://npm-registry-packages"}}, "22.1.0": {"name": "jest-leak-detector", "version": "22.1.0", "license": "MIT", "_id": "jest-leak-detector@22.1.0", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "08376644cee07103da069baac19adb0299b772c2", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-22.1.0.tgz", "integrity": "sha512-8QsCWkncWAqdvrXN4yXQp9vgWF6CT3RkRey+d06SIHX913uXzAJhJdZyo6eE+uHVYMxUbxqW93npbUFhAR0YxA==", "signatures": [{"sig": "MEUCIQDQHG9+ts7lmFngVT6B/nN9+mDU8wPh8lmEyDBO7WZX5wIgEs0etxF46vpoYoLcqCUxkwghRY3W1e4KaIFPD58hDJE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"pretty-format": "^22.1.0"}, "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector-22.1.0.tgz_1516017442875_0.778854004573077", "host": "s3://npm-registry-packages"}}, "22.4.0": {"name": "jest-leak-detector", "version": "22.4.0", "license": "MIT", "_id": "jest-leak-detector@22.4.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "64da77f05b001c96d2062226e079f89989c4aa2f", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-22.4.0.tgz", "fileCount": 3, "integrity": "sha512-r3NEIVNh4X3fEeJtUIrKXWKhNokwUM2ILp5LD8w1KrEanPsFtZmYjmyZYjDTX2dXYr33TW65OvbRE3hWFAyq6g==", "signatures": [{"sig": "MEUCIA6VpnDRiTLabMEUJ8TaRPxhcjEMe21gC/qC/jyZEqkxAiEAllIIvJ2OA76YbeR7FO4aQ0jxj9gYgkXnhGdGdmwILOI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3192}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"pretty-format": "^22.4.0"}, "_hasShrinkwrap": false, "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_22.4.0_1519128215800_0.001725729619564742", "host": "s3://npm-registry-packages"}}, "22.4.3": {"name": "jest-leak-detector", "version": "22.4.3", "license": "MIT", "_id": "jest-leak-detector@22.4.3", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2b7b263103afae8c52b6b91241a2de40117e5b35", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-22.4.3.tgz", "fileCount": 3, "integrity": "sha512-NZpR/Ls7+ndO57LuXROdgCGz2RmUdC541tTImL9bdUtU3WadgFGm0yV+Ok4Fuia/1rLAn5KaJ+i76L6e3zGJYQ==", "signatures": [{"sig": "MEYCIQDdNGUtzxakXq69W5u9F4kxif9+UO1hgd0gE/9/Y2anIgIhALFGTV4yV+Xehf/Y63ZqKmWj6074IZj4+jjkdaUFIbbs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3177}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"pretty-format": "^22.4.3"}, "_hasShrinkwrap": false, "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_22.4.3_1521648547529_0.5540752035668366", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.2": {"name": "jest-leak-detector", "version": "23.0.0-alpha.2", "license": "MIT", "_id": "jest-leak-detector@23.0.0-alpha.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "541e24980ddd7ea6d4816044bd277e0875ac8209", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-23.0.0-alpha.2.tgz", "fileCount": 6, "integrity": "sha512-Qjgr5GBvHLgLu4E6Q01405uac8g9eYC7PLJyAjCtsZf8yqQDHSdPUi7dVlv8d9wbWNbDU7q9CUAL4474yy0nKg==", "signatures": [{"sig": "MEYCIQCSyYJQjD8ajua4HoMJqVghR+TQdbTAeHyGdEt78udFWwIhAL3hOZUFy7xiTj091lTzz/KQbG5lf/T9t0nlsFHpxKJS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3193}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "dependencies": {"pretty-format": "^23.0.0-alpha.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_23.0.0-alpha.2_1522060850969_0.2517340469350817", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.4": {"name": "jest-leak-detector", "version": "23.0.0-alpha.4", "license": "MIT", "_id": "jest-leak-detector@23.0.0-alpha.4", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "97ac8d5d8e455bc80482e13cba3e52de9dfc127f", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-23.0.0-alpha.4.tgz", "fileCount": 6, "integrity": "sha512-0lH7evbbiPxpzERWhIDMIWQ47c44b1mSHIpGC8X/cuwLr3MNeBzcjnbEquy3vku9GgOFJssbocjnW51iCT1ZNg==", "signatures": [{"sig": "MEUCICZYnaaV+NpO6TLllV0ltnuwua0uGHA7qJxGNbBMuYJBAiEA/Pdj2qESXRyx8TOh0107X3iwHAMhK34QC03mtcMaJlw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3332}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "dependencies": {"pretty-format": "^23.0.0-alpha.4"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_23.0.0-alpha.4_1522067503697_0.5295415817413851", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.5": {"name": "jest-leak-detector", "version": "23.0.0-alpha.5", "license": "MIT", "_id": "jest-leak-detector@23.0.0-alpha.5", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "0f35047e4734671c676e7e95661cd0e8a6c4fdfb", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-23.0.0-alpha.5.tgz", "fileCount": 6, "integrity": "sha512-PllBKkFkcMZMFsufAk9bxXE3X9Hr/xVr/zGXNHDrrkubwp3i+hpZrLjraZNdEyfsb5W/DzaH6vSe2wMMr2NXfQ==", "signatures": [{"sig": "MEUCIHcL7wwhSWP8EC0/mvn62tMISYTpdksRjfkwF1+NlnvDAiEA6mWLGACPcx3/8i/mObFXpGahsNOlhrAMPaNEGE5VIjI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3332}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "dependencies": {"pretty-format": "^23.0.0-alpha.5"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_23.0.0-alpha.5_1523387902644_0.9019640396711217", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.5r": {"name": "jest-leak-detector", "version": "23.0.0-alpha.5r", "license": "MIT", "_id": "jest-leak-detector@23.0.0-alpha.5r", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "57c436ba7b3f2758be3c4e6cbeda15a298a07701", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-23.0.0-alpha.5r.tgz", "fileCount": 6, "integrity": "sha512-aHtQ5QWQnxIe7/YU0NIwmpHIILHbv0ijYjOkeHeqYiPRaz0r+/w6WyCGCoUmsQ9g3YUpTeu+CDhEDqoqdkLZlg==", "signatures": [{"sig": "MEUCIQD/ciXAAICmbDtcjPpI7lAnv6KJdZhvPxtDSGScnMb7kgIgGJoOjgrpxUxSSEv/5/Lk87FBnn/F8zw3+TTZvjK3b2k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3334}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "dependencies": {"pretty-format": "^23.0.0-alpha.5r"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_23.0.0-alpha.5r_1523425971823_0.9793751584531092", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.6r": {"name": "jest-leak-detector", "version": "23.0.0-alpha.6r", "license": "MIT", "_id": "jest-leak-detector@23.0.0-alpha.6r", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "471671a75ec53018320e3f9c073f38ba6f3b93a9", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-23.0.0-alpha.6r.tgz", "fileCount": 6, "integrity": "sha512-Phu5mEzvLpVop3oWyAIObXLf5T6zY4UV8tovjtEa3W4WUjxpdsG5HxcV0dGeH1pLBpLUSWS+j7SChN8puHpE8g==", "signatures": [{"sig": "MEYCIQCAR2+tb2hjsV5/waB570ucFFQ8HT6WsiOTmB2pL0+QsAIhALVtSR282iKD4lOJn10aipgzJ6AJbrEwpTq6t5l4odih", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3334}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "dependencies": {"pretty-format": "^23.0.0-alpha.6r"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_23.0.0-alpha.6r_1523516497542_0.06894040864197226", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.7": {"name": "jest-leak-detector", "version": "23.0.0-alpha.7", "license": "MIT", "_id": "jest-leak-detector@23.0.0-alpha.7", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "3724962feb77dd153ce618e01dadf3897956e5ea", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-23.0.0-alpha.7.tgz", "fileCount": 6, "integrity": "sha512-FoxgIlgk9S6dETvWvNxfTndX21b/Vchp4dtbAodXQv4k1Wmp2hF4P76sI/lAulYN5o5065QbEJydonBXwapccw==", "signatures": [{"sig": "MEYCIQC74X40stkAeO06H86p9IpuMybssogL63IslTpUcFG1zAIhAJmLutJ87P4OaDk7c96LIpvw8zqQQGImi3d6kXeAb4wL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3332, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1kMmCRA9TVsSAnZWagAAeY0QAILw5BAVq5YR6CYMMz1N\n7fw4IEKTYa1zCFcKZKUTwtxxVEhKVCRHqZ4sj9XuB8J6YkkhDZWP/lETQugB\neJ885fUSPBfsVnkzSg34rV0AfXmcSjTAdAJaKW0TC7zSnDGjj9iuX+1urbms\nbccCCGVZ3YMG0j9DeiolxaBpyGvnV43Hmg/kVOoa2yFicqmsdXmJEi3s7L/6\nDRbIqwxSNuUc20HCb4CanZd0ePxLMwkIrAZ5Y99tFF9FrnJek7de7k/yTv84\nfYlSNyEd4tBdjEmACvwEl++JR/u5/24+flMkAXeVvs2cQog9tIHVHrd52Hh9\nk4L5rQ24pakQR2brptHTjitXNUXa2BWenBqILUCo5EUisjNrHZ5NMCx8vrsL\nv7qy9EV3Bmu4LABA5JqU3FyJL2RmStO2tFI5ne5tDuoqOoCVs6XFET/hlkOx\nYzvDTA97F5p5iUY3eIqyXAnd/srgN6r1A9apXSnHLq27q2bJhZPzSGxayUND\nZhSspqfFJcLPr3Dxv9uMB7xq6fP1huLQrp3POVW9g8tAxibGajUaPEewbLpu\ngzfIgTd8b+2Tvw9Xd93rsbv4pLuAp7l1sF/14PVojMKYF+UYVlq8BK04rKes\niTZ/O8HPRuenTOQTcE2VrLk92gmTTgPxx1gTx87G3YxR/dZM8zWz39GySZzj\n/HfI\r\n=oVWx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "dependencies": {"pretty-format": "^23.0.0-alpha.7"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_23.0.0-alpha.7_1523991329300_0.9769443683930439", "host": "s3://npm-registry-packages"}}, "23.0.0-beta.0": {"name": "jest-leak-detector", "version": "23.0.0-beta.0", "license": "MIT", "_id": "jest-leak-detector@23.0.0-beta.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "5472f4d915452945b5f83eb1632bded503d6a73e", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-23.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-CplzQXYlILicGSaa5rfLb0ic60Dvvi4DAEYTqZNsnaKiHKQalPGECKQQ4bAC8ckhMXSxclA0fBTPRFZ+hMf6dA==", "signatures": [{"sig": "MEYCIQDmOdHRenpkklLelegLHzBZ11hCwOC8ZLnYxVR065u7swIhAPz2CDxSzSlBqwl56fpo/3kDx3nI9kx4IIejctc7XSvs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3200, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa2byyCRA9TVsSAnZWagAAxX4P/1U0Bk0Sep4OQ9PXj7tp\nFqJ5IoZplyPMfSmLOjcmcw7WhGs8X6bEzupHE6yYqrLZQaKsmYQ/tzXGF+gz\nF7EAGJQDaxXeIc10b5lBOvUqU8n0Oee/XoznAAQZmXYnkXXvLXWs8OZpjw8x\n8bJYmgAapI1RuzjA3Jm45PFqvkJPRUsQDjIok+sfpcA4s6QKc+nF5wUx5LS9\nlC0SgNtgIrU8MaZ2BUK+lCF5hOMoqKROnuKWH3EHs9ysTLxvS7JJGccIfm2x\naqTnE6JU1zCO8y1pZYWf0BEFXZ+nAQ3tAlXEnLwnsGsJDcLMhBPHy6FgAlFT\nWLlOYnunrhBLaYaMBfJ/mkFRDfRkIqETuebey2Tolq+R3Ob0Zl+Pl+776sHs\n7d8jNK6TR1fuG2goh8XQjwIIMC25psJyLs+vmETMsqVZJSFwNf6te1imnA9t\nFtfxgWeYpX/p9Fv7LLQCWJrWZpK1QKLFHJ1o2m3/5ZxUeq9ynZ5klMus1uFz\nozC4lV2sf6jqG1NGaEPYPv0oWf+hxEL1a1qieDDzOGXCUITaKRVfkdxX1k0A\nGWpkQDrmzr5lJ8w45du4VMHI/IayLRSd5Wbj7qGfNKcVCl50stD34Lrar1ED\n9n6JUAK3Gx8LfG+bW+p14OBK1q5uvPy0MV05NYg+etqjEVNDUULsnkRnt1BG\niJxS\r\n=sysZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "dependencies": {"pretty-format": "^23.0.0-beta.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_23.0.0-beta.0_1524219058223_0.5521582365203235", "host": "s3://npm-registry-packages"}}, "23.0.0-beta.1": {"name": "jest-leak-detector", "version": "23.0.0-beta.1", "license": "MIT", "_id": "jest-leak-detector@23.0.0-beta.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "b8a3e5896e4da96dfc6c0feea70b096f41abe79d", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-23.0.0-beta.1.tgz", "fileCount": 6, "integrity": "sha512-S9ZVbsjD3YWC6tIlT4saICq+Xxb5FTJ+5f1ft9Pslo51C8DV3gLu0jogzAG13x34zWpg8w7UxvzC7+MhvR7T4A==", "signatures": [{"sig": "MEUCIQDZdAp2ODsvxxlQK/P6QnoFLdlK3DDJaIQsaXWzkwTcvAIgO5sC8uLcyzY44brjGB+CqvHjiHbib4iGNkXsITRIH4U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3200, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa21xgCRA9TVsSAnZWagAAACYP/3YhegU5TqvQpXrOHi0l\nrJlUHLkcp809wTG1KEnx8Vgs+acmXa2OxclkPI6IZV8UZVwC4xdGZnUkx7Bm\n70wDto31DESmO4CIHDBNDzWb2KOiunaH3rASgwsG6MiM//IDMIEtRQSPiu6T\nrL8/kcM0hSQ/Jvbv1gIBWcHgEY5aYsF4iDELWuroKEHaO5cOofjzhPFQfAh3\nXWuMaO/DKWPEJTRcVaVJyXP09Cum0pXV3aYeqvagyRWjTdCaZh4yLmBPJng4\nvrB3Hk9/amjd5vCxRV4oGRNS+rWBMhy9nb/pfoHVF0DJH8uba9LP6ITPXhwc\nOQh6NO+hqrZY0JdUX02nIVrJors+JmiZM8BS/94/JNqVG7ctp+jSwz6S2iWh\n2qHHfHGADXrunjbxRHWIRT/v8gwO1FRWCFqUm72dcB8Cc8BoB+WkwY19DqMQ\nTiB+C3x115emKqgKlRNOs84LHwscm3e+9yF6dlmCKL8LSH8Lye9ueXSO4fbO\nLeV4T9DL+FqSAK/giq6Tuaz0Alx84v+8idk3BBgK3QZRoFR+Xwy4gheqwNvP\nlLfKsiIY2gOjeo5pQlKInqGVJ6xFexCfox6UNXx4viIHdHbwsZovu4o1jYcg\nLwcuYCjZUwSuPPgoEtISYO+fGMW6fa6QdO+wgj/JhyWiZcyRVKEHE/KQzUwG\nABfB\r\n=xVyM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "dependencies": {"pretty-format": "^23.0.0-beta.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_23.0.0-beta.1_1524325471818_0.19109281577106096", "host": "s3://npm-registry-packages"}}, "23.0.0-beta.2": {"name": "jest-leak-detector", "version": "23.0.0-beta.2", "license": "MIT", "_id": "jest-leak-detector@23.0.0-beta.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "70829447af8d008cc7dd2f79dd03da16264a62d5", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-23.0.0-beta.2.tgz", "fileCount": 6, "integrity": "sha512-inKAsXQNy6ac8ppyue38km+9ngtQNT/8pHfdNPwl3siYQN0hqtnArYFrYgnNgdPKjytQDHn52mKP4s15ho56Yg==", "signatures": [{"sig": "MEQCIDQnbSMB7Gm55+5wXrmhr8gNdiG/eH5V8aWxCKSNFx5IAiAoYMunv+F9CmF+4VQ+htZ4XvHSOOJA9kQPVIGzQfCnDQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3200, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa4kH4CRA9TVsSAnZWagAAMZ8P/AstRt90YxKaPuikdt3A\nTKNbIVjlSsVc7q7furbzOzi8pmAa6Wuwg4rfnsjDdCE/nAELw9dXQF2pr1Oj\nJ4WZeyWWILPUZcLISj78I9r0+5ya1x+iszo5QnylTJRm2aoLCh6d7/XXHMeF\n2h4QwYF9UMOo/5cJEic6U579czWuDdhIp3npRobQ7Gje24VWH9m7WO+zPAb7\nfr6bHwwH/f4O0gOTZi/saMrVKhxDE1VrSKiurUrb1zWuhVXqvoGgXzrpqfam\ncVZMZ6w0VaBGJkmHSXe2kvKM5vxnRaudLX2qHZbne0lyxHkN21edIZ/Fq8on\nUt756kaMLRUXH8oz25WHpQ/LqYjCfp0oU9rAz7iwbH0ImHBUxUvTboE/BhyT\nEFIDnWu5bkPcPMvvY7+6mfMCttJGcVMqMfVSEDgegRcweA90J8r07VZRI7li\n7xUJVs3yHZh8LV89xfEEmmUlTxRTihQrXJzIpXmoo5Xq82D3kwED8L4Qlogh\n6uebz4/vuTWNrGG6g8mcNjwSLI/AOeOdvYGVQmjiZfvWnu9irOg468IpzOzP\n9bVD639Z5Je33/+efvW0XhusoRt8n9O4omNZBrPNf5IGCqM7v8fnkz9LsZwU\nHShpmg42uu2NLfCZBglJzLX5r2TP+ahaL+zXrM0hOFL9SAaxcPsWR3ysHvfP\nsB8U\r\n=lA/m\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "dependencies": {"pretty-format": "^23.0.0-beta.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_23.0.0-beta.2_1524777464463_0.10313587217578335", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.3r": {"name": "jest-leak-detector", "version": "23.0.0-alpha.3r", "license": "MIT", "_id": "jest-leak-detector@23.0.0-alpha.3r", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "f0577070db8f7c078379da6972f11d44e49e97f6", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-23.0.0-alpha.3r.tgz", "fileCount": 6, "integrity": "sha512-Rd1w1wlfikoZSAbnsmBAdHH3/RWcRjTflCGnSbPdhiTHr8sRUsIAXF20AoJnE1m7EgFydvXQzH2rrn/GlVfYlg==", "signatures": [{"sig": "MEYCIQDx+BzqoOavtbsT/6JsaRm+SGfKtBy94cHjGur6k3JbiwIhALkhi4WebOq+EEifa/ho6/ITHWVD6EkMoCnFG8sFOdal", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3204, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5xXBCRA9TVsSAnZWagAAXdwP/iLtOJTe2CzHQ02YnAqt\n+KcLhXSgXahn5dLmoUF1aP/CEyY6rSd+PjvKsWgZwfeDLVdi5MhIxUmS4q9C\nWJmmol95OPDMPy0E4h/SeykEezzZLtHfK5FF+xOuS9ee11A73BzdjLM6M28v\nUU9B5wYpUMcyhQdZVk0aOK+r7wXxrCcANEDVTPFqUzgO/j7mwrZ+P7khUIAk\npw+YrVCPeNqdx0revT2Rr6PcsUIhoSX+4Z8ihhWJJV2bqDh4MZWS+FRVQW9K\n8U4p93wjbVNdEhWMUAq4GUp38rfOAqAAK+CaFQkjQnkB6BhlFZVBFQo+GKYw\n0EUKsXce1CbciLxlcZb3i1Q7ZcHfcxuwzjUnrA6a4C7UEe2vGGYSisNnIpKj\novCL0o6cemT1ZZ+yp9ajieVjfeYCZPUz0mQchtCIZnIgXpyQ2ueXZmnkcvMM\nzjREOJrSJqfRR3gHJrydBAsoHegf05fjQm8s9QVVKbUuy1JG1nAn260Q5+Tz\nAsJ7eV92ouOsYMb2EAkVie70ifR5pDo0qmBBC/k8wY30jloj1niKGLlMLTTS\nQD73+zf/OVtzv7MNBE7RXS98xEqpGrn97XEOM6nCqRGn7XnZ4cm9ZysUDh+b\ncHX/wFbYsiHvC4equrySmpPjPA8OxyT09MCV5qc7+K/4QVtojn+mUdefyOb+\nnVKI\r\n=999r\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "dependencies": {"pretty-format": "^23.0.0-alpha.3r"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_23.0.0-alpha.3r_1525093824710_0.9138393204811659", "host": "s3://npm-registry-packages"}}, "23.0.0-beta.3r": {"name": "jest-leak-detector", "version": "23.0.0-beta.3r", "license": "MIT", "_id": "jest-leak-detector@23.0.0-beta.3r", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "52e31eb531e5bb4b652cdc29c07965686398014e", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-23.0.0-beta.3r.tgz", "fileCount": 6, "integrity": "sha512-wyAWdLSxynmke5xoje67jTrl5MXtWak3KvHy+TzpI+Wea57QkEbxRHqO0mDGOmu1VSZIxFyaRn0XNZAq7yoaew==", "signatures": [{"sig": "MEYCIQDuUbI1prpCv0vR3jEPRfkJC29pHJWXkykkumXkiuW/IQIhAO3ScCg5/vflJtIrvXYJGsKuHxF42tK7KhvBQrwLOXg7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3202, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5xbVCRA9TVsSAnZWagAA8tQP/R6yrFiuSyckgh2hYZnk\nqzlfyXHPntIHMjgg66Ut4VsnvyFc9QnYDOdFcEYBSuAzfZ7yxE3JwbVqeJ6v\nDSjNyeMxr2cBPuxDnCrvDMHhuiqdrpPA1uRT+ZeR8CmHZESUR5fIE+dGcIsZ\nXIm2qeVAAxDKgDdnu8ld27VM/8IUqxy1j6k9p0yL5smegVlHm0uXhRnovJty\nhe+MSl3N8sX65KE1hanu1i/xVtzXD8jBQKmHgtgbogJIzp7pMJu9nuxlg/2J\n1sJI7hPZL/L+FigLdlQaOdjDJwaGwOdECLZfZWevpQ5toFxmw6Tp0qYKQxdI\nLb5t9btSWyHXnjHs51HRdRmPymBcZNPDamJGhWgjfFI8mLQasHcNvZ8FFury\nSudxfZ2IhRU75vtX+iCUrvETWvYBmDCk94YFmNg3ibzizP5JyA3dU6YGev26\naxgSPalKxNmHqXoPHO92ZLzw8Um5O4V3H1LqQe0IMMSe8SCdBrklAbtRpDiw\nsSdjYd0lgolcmoPDLx9dNHR8qZpV4T9cxskZSQTY7D70RMebLGPZmmu7vgYH\nR98qt5oIZjm0uOg3+hp650IxcP8l4o7byhZxcWta2W7Q2D6VDty8LJORQBej\niVduWz0HTpJbRcIaKkR3DuynykFmxEGFib4pbgdr78I8cuS4woysU09bjO3+\nJn6/\r\n=3hnn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "dependencies": {"pretty-format": "^23.0.0-beta.3r"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_23.0.0-beta.3r_1525094101449_0.36730914228166567", "host": "s3://npm-registry-packages"}}, "23.0.0-charlie.0": {"name": "jest-leak-detector", "version": "23.0.0-charlie.0", "license": "MIT", "_id": "jest-leak-detector@23.0.0-charlie.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "f95805df72613287fe85e1cc191656de8c527e7f", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-23.0.0-charlie.0.tgz", "fileCount": 6, "integrity": "sha512-IChRGQWhYKrQneotX4qbmeDSPwU9nj2GyBh5E161JCblEdjI2c5qTgHyp3KIllYNCjYY/khWtu5V1juPZWtRAg==", "signatures": [{"sig": "MEUCIQDDvei8yzjQfVkEb20p2tm2pWBg+H/+6Rw4JGXGH3/y0AIgNcjLdGyqayVz/D0qHhCzZGGQl3YpR9/Ad5yIqYvaT8c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3206, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa6ZliCRA9TVsSAnZWagAAJhEQAJzsImloPoNmSmhSWXar\nuqvVhduygnk8HPy1GmZbUAsRA/ZGqXKGUzHjUXy/b+LfecdsBsbMsmJRXbzy\nDfkqM7fF8RbG460TJGAsCTkLkv2IY+kRdz70R4UufKiwPPnGJBaiPeBdzn+A\nAE0zBz5gfH7xGKWvpN1brSsOJs4HGuaPmz3s7w8/v5714GEzDadlOuZsJyf2\n2umUg8rhI9ANf4UX8EEtyRyLdTSt/7kbT5oEIZ3qCSW9NKWTu6NHvB34SiAc\nFG2Ja7PjH6X9knLN7XfYrqd2blLkFqz1DHbI446SAuNxAMEH3giSEqW9A5p1\n1Lt9uNMq8BtrO16oFEU1hQaZ+4vA6QWRX4g8qtw/TBtRoj8fu0fYbyERrR7p\nqosg07KgwkToYL1D6kB5sJezqK6ZQy+wb3oOsOG0kgWgnf0HlzEaSH7urc5i\nX2M4hSkgRnEgksirryArF10KHC6qUkS5fy6Ttus6DMWCquuaxX0yDb/VlqnV\nKUO5tVbv9XKidNVwA9cm8O1kZiVkDn+Go8ZX9gU9e2W+djD03aN2AA9guEWu\nHz5DpR8WA8tCjUOmv+An0/skxHYMq54+7I4RPkmgnNmmMvJDTkc4zUBm8Rw+\nKkCxD6YIVxmWSl3PqDoyNfqNkBIj3QiV/VT3y0MXSQGIiuMpOGwFWaIBUizO\nGOcC\r\n=eezC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "dependencies": {"pretty-format": "^23.0.0-charlie.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_23.0.0-charlie.0_1525258594161_0.8154849615784503", "host": "s3://npm-registry-packages"}}, "23.0.0-charlie.1": {"name": "jest-leak-detector", "version": "23.0.0-charlie.1", "license": "MIT", "_id": "jest-leak-detector@23.0.0-charlie.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "1a89f816da34f422014bf59ae02bc87f17a34d53", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-23.0.0-charlie.1.tgz", "fileCount": 6, "integrity": "sha512-M8m/JjLe2O66BqpHUVLAxagjB0J9Cujcf2c6KLWVH8QZHV3CB3VI2lg6ONv3fe6XyiKaGH/Kz5+MBc7GARBaAA==", "signatures": [{"sig": "MEYCIQDZ07UpIPTbpaLfMK8uHFUNB2/R8eWNPAbYZaENMZVDiwIhAKGFdybDdnKA9TcfBkKPwuXxisbjKBPWqavgsQX6r0A+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3206, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa6vwwCRA9TVsSAnZWagAA7P0P/Rgc6l0Z4zVN3KK3a0ta\nFThqBdM0WuPTpgcUN42OgIQdHQ5Uc6KIoeie66zUuYGQo/9owzYUKFYtZGeU\nFEsIBt4MvwFKytqBxQn3E/p4NNXfGrB9s4uo4XKUJXoV/preuAbqt1jmSTIi\n97lXHNKMrn6iATFZwKKHaNS2PaWZzUZdo7JwC+hNheGoOVJq1F27wqyAmCMg\nI2BH65ABJM7S9smnkIVOUzqtGTAZCvcaB11j1g60uaNaLORqyID4OHcvFoB4\nCsySFu1tuwfdkFVNdPGFfd0Nx7IiGrtzpYxenM66pMfhS7JwVlb7T765czpA\nOUlGguyOyLXRi6J/sT/WlDpmATd16XyGFu4CMMU9jowP2E0du2v/vWpZZ4Kv\novbM1GD1TRpLOHxpDdrSmYigtbx13y+yBk5bXFyVPmMBXdFhJsQydcEPJtE0\n0Cz4ESLs+RhHQZjCQ5wfpO2H54SY1rza3xEQ+7nbrluKIMmU23GTU7vdMEdM\ncvF8hUBJ1v8/hHOzrayBlx0KZxEpS40jaNAwvIDsIlq0I7I3bK7eE55SBuuO\n8j63OU4ESVGjbsv7nNu9Im4l9ClbQ68icJk/kQ+V3k1Hrg+NF4/lY5rK1lf8\nrJJXl0UlByPbKHTpyLZ6v2hIbImgbW/DnpddjXt4ZMmNgNk9tspFTMcleyX5\ne4Xt\r\n=03Ql\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "dependencies": {"pretty-format": "^23.0.0-charlie.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_23.0.0-charlie.1_1525349423877_0.7309258460767256", "host": "s3://npm-registry-packages"}}, "23.0.0-charlie.2": {"name": "jest-leak-detector", "version": "23.0.0-charlie.2", "license": "MIT", "_id": "jest-leak-detector@23.0.0-charlie.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "ea32c2d2834522195c342aeaebbf448117551be1", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-23.0.0-charlie.2.tgz", "fileCount": 6, "integrity": "sha512-oxVvqaCtCisdH+GNvWkNAq7Yqw01ST768EicUjS+yzgophfL13SVg95TSEJ6zmkoWyziWq0D4ElFmYjQa6Tmjw==", "signatures": [{"sig": "MEQCIDGkIyd8auiE5pwj2KKSTpxa9uIOqen3+ptdANsn8/i+AiAvNX/GzHBpGthSAA/6PKSayfZyRGW039le8j6ZC/8fEg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3206, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+q2qCRA9TVsSAnZWagAAeo4P/3NrEWwtZP8+ZGsVS/Ys\nEonfgGhgjTD4De67vkAY07dSnOKwSyzOCIn1U3FlRB/1YITqnyr8YJxOTwUW\nYSXHNHMcu/pJmQpxgUGFa0cZCbsPUq5eSb4XnsPO+TBEqCrfSe0OeljESfN2\n2Ut1oy3aY67hLcPiTP31Ec9CtwN9WZCylR4PYeHuQ3V3Mz2kpRP6+iciZwNg\nzry2F+gJbpS/3kJGreOXKTefzIWEk5E/w7NOvZ929Tydthr8iszv8NxzJY5Z\n0eJhM+DB03WXqkEycBg88hqYZfzfe/3o1WTcYDcglR+JAIEva1xY6xzZhrtr\nn7uYmGKqydr7gWVBFv2dnQII+W37xQBQcy8C7e8DDVxROoksfZLMD7BTPoIA\nybj1MEdoeTLWdgFEeRpAv6u6faa18XVUGV0QR+tXkQzls3zbra++IKNP2vGG\nmWWxITRt/XwezgDWPNWCa1PfhB8HNOaIiNV5G6LZpaUf+MNVSSJfD73ke+VE\nW43NHjfbm4zMpllvUNoIj3W3HGXy2ZZ243rq7QSXf9cSeiLPAjGINGzPcSr2\ntq9wnaNOvyMbeOkms/KhuB0phnlXpz+Spnn3ls31/kqnKqkQovxMXQfXwD7P\nvBoGYT94wqaCc2asdg6VrJgAxc+hZe9KKtW7NLQXLbfo+7HYR6ymD2teagWb\nhq62\r\n=vMTp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "dependencies": {"pretty-format": "^23.0.0-charlie.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_23.0.0-charlie.2_1526377898039_0.0840715555856153", "host": "s3://npm-registry-packages"}}, "23.0.0-charlie.3": {"name": "jest-leak-detector", "version": "23.0.0-charlie.3", "license": "MIT", "_id": "jest-leak-detector@23.0.0-charlie.3", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "f4e37d99466986208414584e0e397d416308f134", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-23.0.0-charlie.3.tgz", "fileCount": 6, "integrity": "sha512-U2TQFmjghuCme63gdPx7kwpEZ/2YGIP4VN9G9ZTvDkxnJSMP4QjCsOgdtCoM68mn1SKcaAhrDxxOclaufWhvkQ==", "signatures": [{"sig": "MEQCIFcnhm4Ayhm9dw7oVkOLd+lhno0C6xmPBbxklaf77brdAiAgYEUr6QRMRTqEUz0Gz0OMlwmHNE0SGuBnP5KiKgqFxw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3206, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBDA+CRA9TVsSAnZWagAAQpUP/RL9T+8pDuXJWa/RjIWS\nHugJAu4SQNqb37YD9YazEZ1xsZh7WC+blwrSqVBjP2G/BaCNJiaBAHXf/DM/\nb8I3OdTk9/yRudR/p7MQafRwAp/5ODXlN8z5fBm+DT4vulMgjVJ+K7GDmW4d\nOMrLJlOyvbG1dSHaH8Pzbcf88dIAdK9ROtFwEjJ+fgZYI9vdll1s7Ci8AzyN\nTnx52AnO3pza+WmH+AYLi9+rUVcM0cSEjCpj7nu1OKcEM6o9dm9jV7cvbWlp\nevZKCsMOj5khKt2cgdviqSkcU9/JZfXyZrgmJausAAt1dFp2pWJvQ0QD2p8L\nXj+St4Aotd4fXsJd9MzYgVH4q+g/23UINf5Z9dXIpZWVmG+efJxiCKD86NAN\nVT9JnZQqKJgoi9hlC3rZ8e8na6Fyo5PzB38Inb37aC9xUGKOLnayiOtSRzZd\nmlDbMW0JBphmoDFmhdoXZohM8bWeU4DMkfa3PMwqmNA23uSZtYg175EPutoJ\nXbSxmZG5N4yoPEyP9hRYeg7djYV4M8AnEFwowH7OIIvM+HkCnIyelC0CYatR\nJePtpj5/knXatHGhn3ncDBsAuBraZcn6UG39j3FHcTl1VkQhEmGz6NQSVuDx\nkkQ5qRR7u8m/tzeC6u+Gn96TRThMoGm9AjA/QLuxQX6GzL7JmxRVJgsgzuX2\nIyRz\r\n=2B5g\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "dependencies": {"pretty-format": "^23.0.0-charlie.3"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_23.0.0-charlie.3_1527001150297_0.06291428879785799", "host": "s3://npm-registry-packages"}}, "23.0.0-charlie.4": {"name": "jest-leak-detector", "version": "23.0.0-charlie.4", "license": "MIT", "_id": "jest-leak-detector@23.0.0-charlie.4", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "3efa1e25ff312b85cd2f07b3d3cdbff2726141b1", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-23.0.0-charlie.4.tgz", "fileCount": 6, "integrity": "sha512-7uxCGxv7CU3nVIEKt6QD/NGO4U7UUfLKAuQ8p9xtooRqmF2Q+edkRrH/eN6RHZY3/FH4krL8D6OIiKiWI5riFw==", "signatures": [{"sig": "MEUCIGdC1PG03TeUuxhvfpCBYAx3Dug6IWaKz8+fAmNOELViAiEAqTLVp9TSHb8Xi4Joe8i6MU+phHwcRWtCSBu1RRBD7DM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3206, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBUWaCRA9TVsSAnZWagAArCYQAIUm34Py6d5d7u/upL3+\nXzCjP9JHlHi3PXyp9EGRvcpgPbukXf8WQaYw3blDnaAniTleGrwEEcwamqcp\nceADJ9RlWng2ecI78K0ePjEVJmuOcTb+1USrYHhtxMv9Cp5NVXnLBsQaEHwV\nvgE3oUFI53KAeopX0I+ZPSaex2gA3Vn1WKPLdGCwvsK7N0QO+OVlvHoOrdvZ\nI7M1dkfPAnp2Wx1hjAMB1gzIDfboGBiqPAj25uUcA78+p3YrDtUi5m1zgyy9\nB9M6UiOi/5aiScL8gvHfjvmFxQ/oOXRLziSg1hub4CnoFS7AewghUobAQcRC\nF0tCGAC8/z2oJwYJpBPXr/gBJuV/AjCQPTTliHiew0cXzHyVB4KJjmbd59cX\n6A4v/P+E/UfA31JI06EjvHE1qBbCjCLHh5kgQQ72JIO08a4MEkcftiwrBDei\nUwhON9g7X7/5pIGe3zNYZzTy4CxCwtREQm5I4sedii6aAqJtjGBgSti9lFk/\nu/EPpYGzPkgGkdQYl2xaL0nhmjE+5qM/HvmKIMx+LLHnXjD1d6xMDWW85HSR\nWGtWqdgZqcTWNp9Mf3ykuGPYdDQ+L4PYlmQvWO9FriAdXWN9vehm9VbcbX8m\n8qX+ImGQceOA07qvsEez4WkRTG5tCpq/J1TK7WbDRR926qh0Xf8WgRC7ceFg\nKRdU\r\n=/mnl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "dependencies": {"pretty-format": "^23.0.0-charlie.4"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_23.0.0-charlie.4_1527072154436_0.6521587099876587", "host": "s3://npm-registry-packages"}}, "23.0.0": {"name": "jest-leak-detector", "version": "23.0.0", "license": "MIT", "_id": "jest-leak-detector@23.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "ec93d755b21e8b2c4c4e59b8cccab1805a704ab3", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-23.0.0.tgz", "fileCount": 6, "integrity": "sha512-e5VhHhNMos5p+JVoFtabSTSqNusWZyw+cN7UXAlZgnOAnSsFoBUujmb3Gt51gP7sT0qDwZBs7xSmiukkcvxchQ==", "signatures": [{"sig": "MEUCIQCjD+6JBB37lhfrWlhEj9xF6t7JcDMFUojbtdlPyjBgfAIgNoJoPjerqNngqvxlOLiicUtv4yJit+HmoRk1V5I862I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3186, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBvXLCRA9TVsSAnZWagAAjGAP/2P86KPMoob41WxIB5In\ncaHCXTMS9Tn/4zqVUIDoj+B1FEQTgJiB0Tz7szN0Wq9o/pWdouCCnnZa2mri\n1E/si1U/DQMnyTICGNrKjJ6ZAnAwE9wC2Mni9wjQKjeFbnWD7qsmGlRFApwa\nINjV0PGNxoukQZikMt2Pf5TG8EalOl+9rW3gjh5AllUoNl6Ph1Ub4Q3CZPsO\nTqECEma6Y8z2qomOISKRACL52RBe6AZ8XpuWE0UPdFeZ+O7T7d/fjHEBQh/Y\nLKJdS8Pbh6TldpFKcBRsyrY328fs6e2I7OP2W465OYTw2/4IMSccfOPIs+4A\n5QW0NSJa4XMtFPwACoLkziwCi5iT7jOjyQpvQJ7qxLs+s/sbnDMxFmkGeHTv\nRX/Syl8JhxUt3rDCxs9G1qY4lda8ckWMvKwgvG/AZMgOaDclcN0JJjIKfL7a\nuZZ4RZwPd41eEJHhbVK9GE0kI6182HFQZ3iDxEO+751jwkiuU40YVOXtSKr8\nf1rP1dCkBatR/00nbrwJAlRP+syqooLlmHWat/vJlPF7KVC3+2uAGdUdocw3\nBNcGeIX61NiplleKcXquEPytZ5Y9gF4MLhqWF8tpTVydjxaqUZQjAlQLf0Gc\n3dfTir1n39qqYkc6Oja86mtJZRPwtNvmw82vddyTzj4uCjz6OnmMcesO0qgI\nYlNO\r\n=kpWQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "dependencies": {"pretty-format": "^23.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_23.0.0_1527182795111_0.06868685481293046", "host": "s3://npm-registry-packages"}}, "23.0.1": {"name": "jest-leak-detector", "version": "23.0.1", "license": "MIT", "_id": "jest-leak-detector@23.0.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "9dba07505ac3495c39d3ec09ac1e564599e861a0", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-23.0.1.tgz", "fileCount": 6, "integrity": "sha512-uoMRdh1zLtGBoyNIq548kJn529aRZxlpR43QV0Sa1rvcmBLrU0f+UiQVQss5N36Yj+DZOqlcj+/UUCo4JR0ZBw==", "signatures": [{"sig": "MEUCIQDJa0YyFBQuuiyvu6gNpwuZSVj4ck0lRNrL02xHMDzbbAIgBxq7zM1yKibBamYlk8u1H89k9ITF3LZPFa5gqFMbP6I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3186, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCs9DCRA9TVsSAnZWagAAVrAQAJNvwi1TlRtWbar1v7ex\nUkN45I4OuzXfx1V4z2aCCGA5XSJniyKToSVnlDeWlWS+0u05e50IkqWIx0iM\nw41mO4kh0+7faTw2ZSH9O2OldRu9EbxT7DXeT5J9/KMA0UVwdE4ZtDM2TNcf\njMMs3+2RcCeuLa3QsgLjfxnRtfG8LLQ/UWGGEQnv3z1J+j0W+GoRO7SjUkU9\nXUxIZPRg/3uk7lcDaIsltZzAUqKfT2KiOe0+dh5Zx1eFKekO9+OoNOFspOt8\n40nTYXjafeMbnJsx4Dal9GjmqZZtSfS8fazchtNxcUJAkwli9cJjpM4120m1\nn2obaky+mjP+5ZP0dnB0JmlUGZ43K94t9amg1ITRTRaPx3m5vM/bAQrBN9d4\nhwyyaFJih6FvcbDxoMieoGj/ouT3V3pNkCocAygUC3sUMUhKdH0RISxKlT9u\nUe2Tu1mOiOHtlrB6k0nZu7fBRLLhgBBBtp2XXvZ2qkozBxMUmogcGOPHN4ji\nCuSS8kbhdX4xYSGTE7tFI1KsQFbhQbmjCRe7mPTjgE5tICR4QYmZ6kcsq4/K\n5g6AyEjyLxwWOhB8XwuGVsjMXsl5rpvHdSTdsKaTQ0MaFbjWRCLV/HUEp0y1\nOVRq7m2PweGcxkSdu3P0hNQ0zuSV8b1flTsVfN4Kvxt+XzOxC8TWnuaXUsev\nTol/\r\n=8Icz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "dependencies": {"pretty-format": "^23.0.1"}, "_hasShrinkwrap": false, "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_23.0.1_1527435075158_0.8719232054711878", "host": "s3://npm-registry-packages"}}, "23.2.0": {"name": "jest-leak-detector", "version": "23.2.0", "license": "MIT", "_id": "jest-leak-detector@23.2.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "c289d961dc638f14357d4ef96e0431ecc1aa377d", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-23.2.0.tgz", "fileCount": 6, "integrity": "sha512-RBQi5MVRb4YKSOort9Ryl3K4DUaEJOBbjxYJ8JM0pX48cPD8SbVAT+dzUWRwEXY2Tf7LVFt9D2H42eeXMVp08g==", "signatures": [{"sig": "MEYCIQCRgpcTCZboiOBK9bogMFQAFdNq9r6UVNQ0DBgWhgmYYAIhAJgS0IgwRSu4hE/XV/yS2e2OTSqo4NsiVfnV61sS19az", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3186}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "dependencies": {"pretty-format": "^23.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_23.2.0_1529935521517_0.5134360772971662", "host": "s3://npm-registry-packages"}}, "23.5.0": {"name": "jest-leak-detector", "version": "23.5.0", "license": "MIT", "_id": "jest-leak-detector@23.5.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "14ac2a785bd625160a2ea968fd5d98b7dcea3e64", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-23.5.0.tgz", "fileCount": 3, "integrity": "sha512-40VsHQCIEslxg91Zg5NiZGtPeWSBLXiD6Ww+lhHlIF6u8uSQ+xgiD6NbWHFOYs1VBRI+V/ym7Q1aOtVg9tqMzQ==", "signatures": [{"sig": "MEUCIQDdEP/GIXCJvwCf1MLQhiSlQMX3fbz+or4CF9YqkmUPDgIgUZxg66QdiMYVsXu9HElR4rIpNztlo5nHhnlHtJa4NHA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3186, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbZh2CRA9TVsSAnZWagAA2xAQAJOqlx5JIo/suMQx00Cw\nmGl9XTY3FPGD4HlTUAo6uq9UeJD77V782+QcKmL0rNxUV5HtK5xhOq9g/Pn1\n+Q+FI5lYy/zv5TJFJC9fv1ksFSMsj+MQQEb0AbuW1OKviFERXl+QaI1wzaYA\nsUEEQbzh8OHxsA9Vtli6gxJghOC0rVPG0Dl5k77zvngDeoAwGBwOyKk4Yuta\nMe5kH9mSY/kV2/LOdGjKU0ALFPU+YsKYju2vPkDr+e1YOuMKiBqyofntzQg9\nYLevVe6JCYNI81dEc+2dUbGEqDXtJCyUn2gp4uxd8zCojNQBkneeoCt06bkt\nZ4wPefcc5nAdgaUlTWau1SqCd+w9G63pKLgbP+FTgYRqWsRP3PyZD9xcjuHM\nOF9K5uDGB2wsV6kfwvQ96GTGgQRkTjX1D55rjV7zRt3+iurJsu+Q8uweMH94\nEx839+UTbEiME1xON3xgZbEXzP/ekZncKld1aYl2M4xmcv0YLYXlDNJTaDn8\n7VNwJaXdkwF7CoT/xM3T5ZsFAIwP1ri2Q8YDwW3cLtAQmuZS2qsf6+he+svf\nVbkdoNl0Ggv/xvMXHbF4ENAGYgEDpY90y4GeYE/CTv+XvMKOEnwCzTPMr1w3\n1acWF6rMED/8Qng/wPFDpbUc7woWOFuVl0YLpmMWvHmioMWQ1E10d1Hy7L78\nW2Ws\r\n=Q3p8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"pretty-format": "^23.5.0"}, "_hasShrinkwrap": false, "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_23.5.0_1533909110139_0.48034173509082123", "host": "s3://npm-registry-packages"}}, "23.6.0": {"name": "jest-leak-detector", "version": "23.6.0", "license": "MIT", "_id": "jest-leak-detector@23.6.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e4230fd42cf381a1a1971237ad56897de7e171de", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-23.6.0.tgz", "fileCount": 3, "integrity": "sha512-f/8zA04rsl1Nzj10HIyEsXvYlMpMPcy0QkQilVZDFOaPbv2ur71X5u2+C4ZQJGyV/xvVXtCCZ3wQ99IgQxftCg==", "signatures": [{"sig": "MEQCICuofr4SEnbc+kJ1r4HGZG0iTIaz59B/BJrh4aXMroCqAiBRAfkyyUgUSyUv5XlKLUuEq1BABe3NBpgnAL4OdR2I6g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3186, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJblmbUCRA9TVsSAnZWagAAXm4P/i/lKU2zXWwakoq4U3TI\naOO1wDOJXZEklrgNqqSPswAh/YQ57QkOx/iCMZrtgkAPfzCtbKe13QAFUghU\n6d0jlPQF6qI68n2YlqN5PKu/SEptjXa/eqrPMXnKD69MO6KQrJxY963QcCNk\nSV0zdrxY+9CMUH4qnyTIas1T/vl329B5E8w2oJWiSEQrMz19eUkWFIEgjohD\nMBMmWfxwn2gEn84B8crrJ7scBy468vghAO8GOYIIn/2ZpWGm0P6MxPXHlsb2\n5LkgXevPMtSDqQO/jHjROqGsyBbOi8p3Mn4DKNCa00PKsSxf0VT8WRBzeMey\nq61tfFMloSlleqokofrh8ELfZDB6QnomgoRwLr1w9bP7HPoRvX82EKillNQC\nM307Dx15v6M3hu4f3M/Jt7KVtJYYAWXI3Eb/3DQIDHE0D2TMJcIFSNyt7x6a\nrCIjEOnxRhtNR2NRsG+0D29wLKAYwRUyb58RryO9PRfbfN4Tly5Y90CCL2rl\nPLIqNY2WiBGWT1GGziWa8rruUKuReiY6iY1ahefJldaP0EQ3Rqhs+H+E2FuT\nlLum5PL71LdYpp5i9IGozl52QGZR3EA0seFgRiRY70n3C6/9/4AKv823Pzi/\nXJwrKafommVSD9lbo7gCteBTLvQzVo0uKdXDurXHMKhsPwYIqjv2k2uZaHBM\nJdrs\r\n=vGgr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"pretty-format": "^23.6.0"}, "_hasShrinkwrap": false, "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_23.6.0_1536583379824_0.9161422563500399", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.0": {"name": "jest-leak-detector", "version": "24.0.0-alpha.0", "license": "MIT", "_id": "jest-leak-detector@24.0.0-alpha.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2833ca78e9dda237a7c0f4c89f8f078b1621d576", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-24.0.0-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-aWPwmR3y/bNKV0HUD6F9x4jIVf8AjbBn+aIjW3wqQDcp6Hxhsv6n5e9ULQmGM7pmon7b3qFfMNeCjD91WxEAPw==", "signatures": [{"sig": "MEUCIGZzlVzb9boRGFwPs8XxCzLD0aswJwL8u+dj8L9YKnvHAiEA58QjzHWrdEk1oc/Ai+Kp13AYFfTE6vnITcWzgxLgxXw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4358, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbycpECRA9TVsSAnZWagAAebUP/2scgy0gBzR6EkjYcqZf\ngafzStkSVhlSq9T3TpuImb9Vz+gJg4hArWzEGptWnSR2W5Or+NTTfMkpiTtS\n9N2BYBK1gSKBJ0yHdS7giYj1mb6ADCA1d5FglgXTTmRKWqk3FqCtFgrirO/Q\n8MkAeKyda0N2BtDMYZdnWrHcIBWgHK/kIycf9YiBTOn06OrKIxdb7bOhxrv1\nsAI2y/dxPNUYnWs+OjIstikziS+FBX4kav6SpvjOCNpu2LDX5vMj11ESyNNr\ns4LpwLGeG+nwe674VKXs+C0puOtGokAeyXLbcOLGWY0J2gKjflDkB7q8s877\nhe6XICIC0VVw3amrWoNkcLapqzQ5r2ZOZZlacCLFsAyxLqrFVrTXbwu4hNh3\ni94WdVjDOjcVGQSvsFqB88xgmrYd3jtOBRUDaGNLih+EAaDWahHYoriWcLgM\nAsLls4cHNz3TSG4MLZKO5sni1hWizrWo6lbyls3DgRRelswaCD20aOVkJ3y3\nNLyoxmwOW/ZCYiEDpomWvzCKjVCGm0bNN05da3RQrRXLXT8gJgxR+ejWxjcl\ntcIjByv24cKNV9tZAh3un0SLJf0C+OB/Ud045+ulcgESX+VOVIBGBlRy0GMS\n5JalbMM+r/gvT3E3nV5M5vy5KHGVsIGqU29X2EGUK5lKJrPuiDHF23KkNWg8\nzaqj\r\n=IUFD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "gitHead": "22f67d49ffcce7a5b6d6891438b837b3b26ba9db", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"pretty-format": "^24.0.0-alpha.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_24.0.0-alpha.0_1539951171642_0.25183372172240315", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.1": {"name": "jest-leak-detector", "version": "24.0.0-alpha.1", "license": "MIT", "_id": "jest-leak-detector@24.0.0-alpha.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "52354c5a5cc7866a90b029b1bee3e19b37ac019e", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-24.0.0-alpha.1.tgz", "fileCount": 4, "integrity": "sha512-PkMZNr3fHYmXV1YC8k2qOmAQOPKRZezLR22dIaCou7UsPo/ABzocu/AaKkoE7CKlqvL9nR/s4Gti+pSucLCFEA==", "signatures": [{"sig": "MEQCIE0QSpMOPGo/6pcnitJ4uM6Fo/7bXOOTvLdASrLzUiPJAiAQFa5UF+yb509YzZFKisjnVctfXlLSCp4eSkHkcfm4vw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4358, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbze5oCRA9TVsSAnZWagAACA4P/AjrazMPrQFZkTixBhOf\nDlY0PCh0mZzWeg89zvetAzxQKOtDB/baQFGRPLOxXG3ifzxXqejCJeIV6ydI\ncNYrc6FgPn9lTib7SqgCUCUSfnBcwRIb0nPKUJ73a7uwdfS9g55aUrfF8+K8\nx0muQC5Zxgy/Dh680hozJfSwfFtxsRmhKA1IrK6DHrsXhXfztpIlAag+UXF4\nWwNd73+7K8osltPbIPbVKMsJJiCyQ6oFmPKouR295HxvExgLiPx/HjAT67Yq\nukGFCSgEuQOczQqACMLPfJYmH0Kx75UmXyaYQOKSmLbQASjOb1QK7CpowrfI\n2M5ICf/fzLlXdXkaf2MGmItrV3eftnzHIPjIOxaSFT5Qd9l/A0n4wknwc+Yy\n78UsNSShklEqk4Qla1H5m9kSu0ocF5RJlOs2CIASdcbil5C75qEPGukun10k\nWHxaz0e7EC3o64C2ZMex7Sj4fqFyQUaRgqPl7PnDX8VSKeehoONhizcFcMNx\nxv1PFTTuUoZj6XQhVGWqY8BlihDugxQQBkX/o100z+IEvjnwYcWxe6ybzLsY\ntOBqhJhREOTPRN9/zbOvL9ZHGTvhRTS90Y/oAqKHGLQzQEcQcPDn6q0iP6gh\nidMV08wo0Kiij/2vRf0PtfqqOEVLNEOi9H7mDYP1uEtoHAE+TX+/ahuTKz41\nq+cm\r\n=A+yZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "gitHead": "4954f46708415174c48a58f296a605fbe1244a31", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"pretty-format": "^24.0.0-alpha.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_24.0.0-alpha.1_1540222567403_0.821493747771886", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.2": {"name": "jest-leak-detector", "version": "24.0.0-alpha.2", "license": "MIT", "_id": "jest-leak-detector@24.0.0-alpha.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a288994d86c619da75f9e7d143d5cecd6f069279", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-24.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-o3bWxt1XAKXCu1dkUOtaKcg4Dfv3XiBNImat0Q4uVZrvuEHw2c+mUQl80q6gsWveC/L9Esv6/VF6IHnq8tmmow==", "signatures": [{"sig": "MEYCIQDp+smb+Q3iqydRMGDbsUGmK2Toey9e7GVTriNfp8vT0QIhANsYDtIEVq8l62Y4HurnKZAO3McO++n90LhoLqxr/Kn8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15129, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb0aARCRA9TVsSAnZWagAA3UkP/2gKSv9j5qWuxj8/sGB9\nLuvDkUJon9N78ngvU8bfujVygyiNEOKaMyBP4wJL192DsrIYxtQRdp64vwB0\nlij5mp348IQGWE08Lkw4jjRNNxx98IeWGMJjZDJLrfCws6wxc9O4P+NB54i5\n7GT5TUbpQkxp8wAtYxfAlSBjCcw/sLymXF0avGhsfhyAfnBCzn2ey+rKEXYD\nJrwc2JUznpkw7/kuUnFK3E8UcvVYkmtimsMDt0+98zsc78sxsLOFs0foeEvp\n06vprvbVqb9piDiu3veeCHb1V1uGWFWwsy6Xu+4E/RzdVrS7WfvZldkWFOSh\nAxAPaO/Mb8rXha3JRyrCamJXQgeHBq1mqVSXQMKmNDW3O3vocD1SY+r9zKIN\nc4W+BAGkrqpWuEPQ1lGYT4oZRaPOZz6z2F+DwfRKEscQojS+NzBhWx4IM5Bx\n5okdUHz55uembz0/zIx6CAsc+PLOlS6ynqHoJI7X6lQt5VP812RYm/cByRXx\nCcc2Av0A+KKZsgLOXJP4R8GXhaFvBVWZys57nMUBIWJy4AL6HnBeJ7pchWoS\nE3cT2ErKPACHmY6tUr+Gbnpkxy6MrwlOP6lYCTLajMwDepKwESMoOBYhJ7XT\nXqcKV9iO816oklolbk2TViFnQkRBPlxOPBjepkVtbZQlP7q9r7M7MZk6n05E\ntsB4\r\n=/gQY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "c5e36835cff4b241327db9cf58c8f6f7227ed1f7", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"pretty-format": "^24.0.0-alpha.2"}, "_hasShrinkwrap": false, "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_24.0.0-alpha.2_1540464656687_0.37848502329530587", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.4": {"name": "jest-leak-detector", "version": "24.0.0-alpha.4", "license": "MIT", "_id": "jest-leak-detector@24.0.0-alpha.4", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a14886af34e0f54c5ae84a58a07e96c353d1c190", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-24.0.0-alpha.4.tgz", "fileCount": 4, "integrity": "sha512-IsHvueFwNqIc/VWk7V+yox21vGnWZz/6idEqcQAm1aZaVaydmMz3hB/YmvyhiE+qZaQF7HlyvbdS+FJkbaD1iA==", "signatures": [{"sig": "MEUCIQDzwht8s/mRsqNDsFUmPCLSQ4DxWLMPf4+L0gpKUbsnaQIgAqZ3Aau4JTAVikHUqY6Y6jTgnOruBUd4WlxU27AwTxU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4397, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb00HRCRA9TVsSAnZWagAAUhUP/j1kETveaLVZFXM5ult/\n/pmIOYI92jKGHXEW+QM3qmLdmv4e2m3MZ8IWhgzA4Q9yUZATddihEpqXJk+Q\nQON6JgDGCLuGMPKR6aW6OVoT+cmIlBqalFQDxswD/XUJPc7mhIEB9hqArL60\n7aB1syZnvoQ9u7K8A9/SmpkyUGlmAM3ZYcNVZR6fe1m4QWlBYMwNcV+3yWMp\nNFli4xn98zVwJM9D6HmHc2vuaWAD4/QpRG94LGKjoBJB5lSIXQzWlyRTdFx5\nhVyTY8cPXji5Z7kw+SCRJn57AuHaCM3JSOkLA2Bk8kXpicFy9O+8nCAzFPpW\ns1HE48VWWVDdBrAlkkXQnszyRf6fRjWFZPNjfF/rg6vo7u42O6NJT3zy3HDp\n5Wu2qx+2nVIBtxytetAnWIrAd3256qZwIx9AMlg21+Ivxj1T1zqIt8NmoNOQ\nr8xaPj8uR1Cq66/x4e3P8tRdBs5muWwCfpVy86lhr/+vgtJz2cQfJclAajSL\nIt3BmBWnQUg4DAFme/rcb095PXvztfz/IBylJ0EUrm9OzcfPeKiVwgWa4NpT\nQVU/kNYpWe2nHBpOeYzD5q+TAcjmRKZVoyzeW1ranUJchJDAAeFhftFD3elR\nV/evExsHmdczkCaQL/LVkBYvtg6qx9pa4jBVmC6eSK8pFZLAgN1COG+g7jMa\nAxye\r\n=emm+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "e41f0bb257c6652c3100b97a1087f9f812fbea0d", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"pretty-format": "^24.0.0-alpha.4"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_24.0.0-alpha.4_1540571600877_0.30316093279907674", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.5": {"name": "jest-leak-detector", "version": "24.0.0-alpha.5", "license": "MIT", "_id": "jest-leak-detector@24.0.0-alpha.5", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1cdfad981d4a90ee18ef67f006eda167581327ad", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-24.0.0-alpha.5.tgz", "fileCount": 4, "integrity": "sha512-uqytiI9Cv7WPe4Lwk1hAiRyhIEZ6jWvSWxCEmZYzp81m2o/JO5/rP7CbTCK0RbMDjVyLp5Fv4XR0dDGMESF7Xw==", "signatures": [{"sig": "MEQCIAHkjwZjJ1ZTE+sEXePPawIrC+gHcM3fiNXLNPINZ8ZrAiAu0c0XPSc8RS5oBp7lzrCfpWZ4ftVi2Yjy0Gfe4zi6sQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4397, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb5YfTCRA9TVsSAnZWagAAAv8QAINv60Jb/Uk0066OR25f\nc7wlx/D5RmHR9wPKnxtkD/MPhtEOZ+8l6XxfkCCiXn6bkC+J0Vjedy+A0h0f\nfJZaEY5Jzb6/ZvupJkBPgokBDa0SFK4hZ9JSKTq3f0GT3gKzgwZnK4g+Fg85\ns/CzsE/vyRrk9XJ0r6aYrZGEB93vVXrmnIqvMAdYizsE4dZVHZgLCr0ek5aX\nuLxDsHdWbecz3j361joNj4o2lp4i8RmfGYitIlyUwU733k3y6JJL76+8VkfI\n5VQL3N08Fz6h6ZDUMro6pATvSwt7e6s+2jusa0N4phe3DQI+9LWCuA95AOlU\nfpj4QAiMQeVM59xvixzDssiK7vbw1E2NelvMr68l+/Q5iEwkxV3/m/oCfj6y\nSe2g29A3Q2FHqal7R90EgW7C0s3UIdttAK0x8dw4waE5KmyvXK8VYYk/8Xog\nXMRztOjP/l7C10s9xMnwHcp3xXQ8jfquPtDujkusZg7RmV+3rcKxHwtGbGGz\nfrOdGP9ZfDjAzMW3DDDgeqW7+yOgtkp43XdPF+yS5dMlCl4bPp38IFSp3szq\n+jHfY6aPamtwOEFC9n8emUkc48cIKctWM5gWxakF9xa2DePmz+H0Xh5CgZ1h\np10irIZFHAxONHEoKi6PPTvCXR08yBFjZ0WEAVevqp2e/nVKBWwCL/OadGMF\ntAu7\r\n=aek2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "2c18a53e8ff2437bba5fcb8076b754ac5f79f9f8", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"pretty-format": "^24.0.0-alpha.5"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_24.0.0-alpha.5_1541769171032_0.8937505538062107", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.6": {"name": "jest-leak-detector", "version": "24.0.0-alpha.6", "license": "MIT", "_id": "jest-leak-detector@24.0.0-alpha.6", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "01f95a351f6689f7162224bbdcde3ccdca6b0a8e", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-24.0.0-alpha.6.tgz", "fileCount": 4, "integrity": "sha512-jFjDN9F5H47lE8TgM1+K4cR6ep3/cKwx+PP/qnuJpwKeKEp69DyvxXYriExbq54eo3Ed5yq7yH+PsAf6dwllew==", "signatures": [{"sig": "MEYCIQDAmxXSFHQ1Rvuu//w1ab+jQzlMBuni0xz3ZVH36B/MCQIhAO2EWvZAS5NuI1Y5RR6p5x6RlD/nEID3fLfEK1Q2SPLj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4397, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb5ci8CRA9TVsSAnZWagAAfIgP/AuPNX10um8R+LJkIgN/\nWnLbDswEIqiGx1V3q0g8kjTc4X0XfUvp2cDpSSR27kZzz/Et5YaSsG9mb3tV\nhVD+h8q5a8X8T2KFwjJ/CWpiXc+4yS+RgYTLld/4RFtHM1KEZ4kYNLpUQTcB\nMDHRYKHbVxhS8FQRPS+T292+8N2rvTIX24lJvyVJH8lEsb9kfmwsi1+SsYd4\nf++Kj18DKP6UBV5KQz9gIOe6sibpfPafy782BPlhtK+O9+lbN8m8Jx7D1hjI\n5BauqlUFZAUjZivVhQozCgnIArdQZIVLdoq26vMdvKHm+0UoZaWYUzPqIJrv\nJUouVW5Xj3D2uo4ibgxuyz/Kp6/owax9MYFI3MTM9ABQBfH8puMlQZX5KJmW\nSwP34UdA6yipxVJVz6jQ3oY2lITEBYI3xSYQSIXphw+60o6Io7y1asQVJw77\nb8R+ddc+3cbi+vd9BSy05Sbz+xnbg5XplPnDCC4/cpZTMi6qPk64Mnbzq9YP\nl4EUAxzfomvtD+57WXwg3qkdxgKhcIfGmzlEYfm68yGBIBrYxhLrWLtEwCUi\nrhvyXmEjDlYpX095ved/eIgwaDV0cTLZsA+gyB+C6+GM7NwpCXs7/xjJ1eUv\ncsaEx6qLdd+Yuer5o6ZC8SkHLwDAmo/lX1AIsEkLjlGO4tKSwF5l5gJ7N9vh\noiH8\r\n=ZpNC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "49d08403a941e596eda1279c07a1eaf4d4a73dad", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"pretty-format": "^24.0.0-alpha.6"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_24.0.0-alpha.6_1541785787343_0.32344839783139157", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.7": {"name": "jest-leak-detector", "version": "24.0.0-alpha.7", "license": "MIT", "_id": "jest-leak-detector@24.0.0-alpha.7", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "86055a2ab389effec3b19f56ba3a46dabbb07a0c", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-24.0.0-alpha.7.tgz", "fileCount": 3, "integrity": "sha512-4rQ9lFa2JdSgzk319VoQIIb2ayUMQTTxiXiptVw46kbO4XDJZ6DrkhgqWBNWUNFv3UMIxvOYY6kg/yHSaS7doA==", "signatures": [{"sig": "MEUCIQCres1ru0sqbPm6jCiIx8VVJ26EyAsL3yRmY4rBCOq9IQIgIOHGM8rbtzkrtLoVTAFxCjCy7VoBVttslm3BXOjcx9w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2045, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcD+M1CRA9TVsSAnZWagAAiDwP/iI3gXFRs6rd12rhQIYu\nydBEy7bBfU9FNFSRmY+tB+2UMIdRJuKZ8V2+GpPVDmLoqLbI2bkocGOx6A52\nWPZm8pLkmD+5V4XWwkKhE2UGRWslYgPillO6vOA/Wc7E09R5ojDZeo8bI6hN\nVl62LzZCjrI484dlKVHMRXCqVBh6xQ60R3GvH65yJinGy2UqXflmUYjtld72\n5bGxRxjKbz3kNqQWwFZN1Vd3bRNErBH3A1PqM5zxsasViDncCzQXosgqZtJ4\nnMdrHf0LfTyG9fnla5wgIcAZ1Yxn4n5fyrbXQaVxPMu6PKMYpKg5YX+6axoi\nHXJBuSsfTRq3e9rO7xb2vndiy4odTKxDgB8iiw12pCampJRYyV28LnQUXhiW\nNrhqAzLvUFDELiFCPw/nhB3XNAPX1QK8JMg0EJ02y1m0TaEgMS2yC0Qqu+w+\nqogJq/TTNm3bD8KuOuPm9p6VcKVefpZ2YKNB2UVE+tZCvUOiYMe6OdXJJb1P\nDvvyzrClLr5pAVNJd4oBuOJUOtq825H/gSiko+lyWjol9b6fxPcBun75fJUU\na3ea6ytiNMLOXPOSRhxFVPBwLBsYoX1vg/uRIX1kEPuK7RqrfSA5V759iE5Q\ncHjGmYkdh1HwgLLcfFstjyauFhuMKJxy1NWpasq4/sL9+mzh1ryR+q2dA8Tj\nRAnA\r\n=2p3t\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "gitHead": "4954f46708415174c48a58f296a605fbe1244a31", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"pretty-format": "^24.0.0-alpha.7"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_24.0.0-alpha.7_1544545076525_0.6354136329106927", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.8": {"name": "jest-leak-detector", "version": "24.0.0-alpha.8", "license": "MIT", "_id": "jest-leak-detector@24.0.0-alpha.8", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e6ca9ca85e4fad05172731ce07a408e3febe4fe6", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-24.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-J5JvErW92vjMa66hpJ5TJ7xUh4uY4dhO5vD+5o2oqbACbAjz3SmREwFu1b90IOTuQ5fvpJfrKP3LgiGcOe9/lw==", "signatures": [{"sig": "MEQCICL9kRVrZvBFdPShWv+WqKtHtEhc0FhyGaeBFPNQxAaQAiBoWZ8jdvzEDYjy6S9syGKeRsT1L/W9z7I/Cbf5nNZfuw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcErdvCRA9TVsSAnZWagAAPJAP/1cv36r2EYMVqukKeTYa\nZOzAhQSUPBbi7SHpPOFpEIbY5Q0l35MW6+UH0m8msANTsvLIUDWeAKSzrccs\nLrwr4o376QvqlO9fP3JwifRAREVezzRD1+wSmSUSocPZDsphjFW394n+5Rxc\nE24rPMoOXaWRHvtTvx4/wWeUKv+icbVOdhgiPCjj6AL79VZ4MgV4nt4Zil5C\nKRFaHwFkvxDI46l06E8irC5HKaleUjv8FhI1uQVXWk55AkMcOrccPToN6b5x\npyUsa29Ne07TD61Q1go0MFzMk/MvcTofoAJQ3XINyMEeJsEWhECdvdPVpj7P\nBBsiU+fXFuzfB+5zS16Q4ScPfmwmPbgAe3/O2vxrN23kETKGq2mkwi7SKakn\nX87DE4FrlAzHspgBgnyDPj/ZOAkHe6C9S3fhPRL9mShZxkYf9fN5QZRWZG61\n3S20Nt0oc9Px9RzgSrXqYOdoPNw5w0AslXZ+Xekd7GvmBy4PpmwiT8Vriuv6\nAhROG+bp+E0oAF3M6ueOubqaclNWyDYqRg7itiQkWaf6IjQ7va+QQjqvQllB\nPFaQ35GLUe124s5KdE4acv0JQU0a62d9+ssgJlU18eEIRB0dvr6cb8c3mDdd\nDbFPd2eBAotBWwWMA8ZPtmWZXDzy6FriUsmX+enXZrm27GQvoYoS4x8tnCXj\n/+5h\r\n=dcZm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "700e0dadb85f5dc8ff5dac6c7e98956690049734", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"pretty-format": "^24.0.0-alpha.8"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_24.0.0-alpha.8_1544730478949_0.7035607273888576", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.9": {"name": "jest-leak-detector", "version": "24.0.0-alpha.9", "license": "MIT", "_id": "jest-leak-detector@24.0.0-alpha.9", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "02f6dc2d54d1f694524f1cf2bc0291109ab30e38", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-24.0.0-alpha.9.tgz", "fileCount": 4, "integrity": "sha512-1IvLErlcj3YmGjVXmaUFXQNdKXoP922kAiX3Yu7cWqd0TyUhXbv/ZBufmach+FF5WmoF2eb7o3jpzZj+EdDRyw==", "signatures": [{"sig": "MEYCIQDcrciAEddEpZYQlJhY/A74wG8l9YOTno3WEF9eKKv6tQIhAOYkkxAGc3IG5SpjJ76muCli6GCL8zwUhl2quQjnBVHa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4507, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcGlR+CRA9TVsSAnZWagAA4d4P/02KMpEv/Ac2LN6XG19v\nKdKNRvDooNKV0IXUCfqd92FNMjKkiDzLPJzEcZQE0HQ1OFgSU3ACBG/agVmB\n72FjXHhk33hjQ4cVhEozSGdX18qbPO2fxmJcpmtxLaxDMnGxH9RF+ZAmOKw2\nLFKTZMJo/9EQP3DrN1Jw/BUM70O+py+vhsIaSzEFw1jzh02tmFgUQ9wFO9Ck\nIvIVvIWWoXZ/2PgWkaeTg5MzoUNolY2dHDzT7B6PJ+xf0FQyHc2Jdd3TK5bq\nxL/jw1kw+gxI6vSgjFocavCksy2lPpi1VTBEe+/b2bzLFd290g9Xh4GV0sIh\nB9Ky/nO3Y83H2qf28M3D+BQ95+Wn4bLrcmooFmj/IdX706Uqfa4L2yamsJ65\nKLK1L9b8Mcuqk7VscOv9Yp/ZumUFXjf+WEtpvBwPVizTC01CJt6y20UV9JuI\nx6gG9S6ZLRX39YZRUkquzs1c/5BNr9YJqFvIuWv8NLC6po2mCpNVXeTgw2bz\nmZjEI1CVHpbMK+VyG7jwfgqrtPRCrQSMak/HSr9LNpY1kJms01ipIUYk6VpQ\nQbQo3aONMejpPmgSVqRLEoBandybcuYGjjuO7PHQmPRaO+ZV1AyMVL+1oTCk\nY+bNxEdygeSMua3UzJRau6j3x+P2Eyb8FnmhbOCQNb+0hRjXeprOx238tQuQ\nupd8\r\n=GsgT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "c7caa7ba5904d0c61e586694cde5f536639e4afc", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"pretty-format": "^24.0.0-alpha.9"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_24.0.0-alpha.9_1545229437662_0.44809567472415845", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.10": {"name": "jest-leak-detector", "version": "24.0.0-alpha.10", "license": "MIT", "_id": "jest-leak-detector@24.0.0-alpha.10", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "47177fdece1b15f1f4aa91c937920841a0caaef3", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-24.0.0-alpha.10.tgz", "fileCount": 4, "integrity": "sha512-dveL3Bjf+efozqu+vS0x735ZqJHiL2+wq6oZtC/G6ia9vrw2cReyh0OaYXE7qmLWxlCMBnTRVh2IdEA0ZIPhBg==", "signatures": [{"sig": "MEQCIEomiba7FXV0KtJkitNGBHIABsXd1Tnl0PNu1yTTyGhQAiAh7lJ78qLKCYV1BldyrdS6TeF2MY8jBWlmOwyp0shfmQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4515, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcNik1CRA9TVsSAnZWagAANJAP/3GveM/w8bHVLTTzpw8m\nwab4kKbH3dSoByDmsOAHxLj5/xT0nUHKxxd4HTRxXXSJ+60IulRcBRWbcmKQ\nBMdy7cOb+/zKdbnzLBUvmetW320KMBNsx8Iy/ew51V0rIrK9FPGXLkGW4wP7\nuuhTpjkwBD1kFWj0fd2kuTNXQok6xZYhfgjJdpuuDskpp1Co8snQYyQ3ecsT\nS9tGOD5nFAITjAlSiDsWuZva1e0I8d6Iq5m/d1oU5O9zKeHEM7Z/SjCnn0KI\nCsZo9S1vjOk9Z8lXnrLONobXiUVVhwQJrgha1mBQBkPHmBAjpqsGgox/BOtO\naayI19T/Jwr9m+IEjTPlrSZtUwpivIIMZ3Df63k4efoPnXi0MGPSjVH1ZxgE\n8CT5Nsqt6/2hQdm+h3wIa8VL30lZQV87aL1zqzSpndunmPOwvmxo/wPzTt8Q\nSeeCKTfIca2mlftlpbOYsK/eeI8lzR9CiREyinpSJKimZCpFdLWc0gpLVTUH\nZHVocAnzFHPgjQN3NCJ0DkDu0+pex5e0jaGQt0NJr2xEx8FIK1sUQDPM7aRI\nbLewe3dBweRgLQR1qW2REStlwLtmXMdSjmvF7nrLCroBFfi7NSP3bHjbqvhW\n99nYVIQk6Od1wcMerRUOmEFdsyuPlzjo4zjFbeYEzAbrmKDNl5NfcGY9/Vo4\nCCqX\r\n=Aq4M\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "722049ccd66947d48296dcb666bc99fccab86065", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"pretty-format": "^24.0.0-alpha.10"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_24.0.0-alpha.10_1547053364864_0.09181431779569404", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.11": {"name": "jest-leak-detector", "version": "24.0.0-alpha.11", "license": "MIT", "_id": "jest-leak-detector@24.0.0-alpha.11", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "777e9cabae91d421fcd1d6fb34eece20c9c5a8c4", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-24.0.0-alpha.11.tgz", "fileCount": 4, "integrity": "sha512-0xU759al/bybp/6uJVaipolgg00m0SI2qcevVvgB15gNdTgXTq8Du4RpB8x0Ayik37AS48eSihfIgEhtjRbKTQ==", "signatures": [{"sig": "MEQCIET3QlLDGHT49kc1oM/jf0m5jdc1tIc8Op0OlPlOW9/zAiB74eBfxDzoqvnq4vqiYMUWpsoLxovqUXuMPwY/9i7hwg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4515, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcN4/jCRA9TVsSAnZWagAAUT0P/2UyyljFeG/Nd/Jh4To1\n3RBdkgSeUGT1Sys4U9qWzkdW9ti4Uswmbu8Nqu7fLM9eCUpXIU34f/GLAnF5\ncXd7e7YWaD06QGduvMRx+LZ7qJCAbLKu3Ye+WHJajdMP4jEVtCltcd20NEXl\nOLGENzgyQlslZ9yW50eN6OL64Hh8CRLFjSj5dkdHe44O95VebpiQT5ebVpQn\ngH5rf5bxPqHH1U+qmXDCHwyc5LgT58MLoFjc33o4JxIsNwR1MwLILWD0DlOM\n63+uK4ayB/QWJTk+r7UzYYdhnHkAeCNTnoaqNdSTBtUSb4ckTELixBzDgiNQ\nhHkAS1H4JT/sF2pM/PI3izbD1+SCxJ1q64iJxA1/eqFSdZ9J1VmBwWJ2DPX/\nxfbehJEML73s78rRDx4axDyA2dCGi1ebpS3wbERk0qNbCmbEEvnyJCCbY5Fr\nKmoFJQJcWLDyxsu7Bsg94wSyWKWVR60MJAMAQz4/fIapLXvLVNU1iB1rfK1r\nBAaKu/RSQYeHbD+yS7Uhb+uJHBSyrHhKibUkeWFFbNgHJkTJF3PR8Usq2VUL\n8X+I7Nqj8qykQ285tGWV0tTAN2i4kx7ourI16Ww7/NFzJz2CyB7H4Iw2TuG5\nQYUUC4y8kE19tyrbdIIn42GTiCSg59N7ziRcTGAHVE+0OWi27/8xPpJL+F+h\nKm7U\r\n=zrK4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "6a066c6afe2ae08669a27d3b703a6cf0d898e7b7", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"pretty-format": "^24.0.0-alpha.11"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_24.0.0-alpha.11_1547145186600_0.7001143061950086", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.12": {"name": "jest-leak-detector", "version": "24.0.0-alpha.12", "license": "MIT", "_id": "jest-leak-detector@24.0.0-alpha.12", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6ef3b8902b81561ff3108918c0af0d5acf4758d3", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-24.0.0-alpha.12.tgz", "fileCount": 4, "integrity": "sha512-z6b0zryzPZNqygppBxcyiWSchWVCclTwH3vMgYsb6/SIadJ8RAn6Dnj8tQJ+nE71YoB+LyNDMasJJwUk/qFGhg==", "signatures": [{"sig": "MEQCICf2l0FBSZm0DliDPpl6tpNoTHZIkklCRxYnhizadGILAiBRI3AqoYf9MFoQXkP+Ca7qLrBFdP3KlBUXXxklH/vQ3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4515, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcOK9JCRA9TVsSAnZWagAAHbkP/3v7skZxihQ0H9D9qVfK\nOW8t/F6hKMM5g8TVmQqSP7ndz7oWeSJWb8gqzJU70eX7jN5XNQGLtNbZmwto\nIRo8YCSf9WWz7PvAG/54SWQUOUaMnVcMOJ/bviIsw/3X9DRui55AKLxsl4dG\nz+DusN/4ppAHZ6Oe34rCncHl8Ckgxu2290tPXk63M0F9braszwEJbLntgORZ\nJmuMLCOMDrHPMp2wUXXvVJzFRVHMwiQr3AYbhO/wkDJFPWRzezDTZr6ky2GJ\nLnd07oTNOhguUD8WevIxKJJmEZX5HXTmCjKZcBEN+bamaBHHVkw93SDiVuof\nlsycg4x1f+oANvPsRneO6w7WgDxLUzmOblryf4W1rHleim94qEXQwt7N9I9A\nU6u1W+nqQisbvXvtEwt2AgyDEPu63ODJ7xAUpCm8JgHbdT+WNjV6p5RGDAC7\na8fI+1u9/Q9dHx3h1mrs+GRWi1wtptQ8mpWJPQ4EH4FAIsUMe83pu+FLSaUV\nmVUOh3ap6szNffx54e/qjC8V5xwX2Wk6LQbuLQcPlY/e/IbeohWkwv0ZA9P4\n/mo3YTYJK09lpuw6SQ92aLlgfTp3zJUDTPMuE1OCHN0TOe457BnhtAJye5dC\n59eVlBJz+ngVFSRjnVqCBOOVz31JX0ew4pV3adTkVUQuTly4TvPQg0bK/i8P\nr6x6\r\n=FOvA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "4f2bcb861d1f0fb150c05970362e52a38c31f67e", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"pretty-format": "^24.0.0-alpha.12"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_24.0.0-alpha.12_1547218760877_0.4930704220737647", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.13": {"name": "jest-leak-detector", "version": "24.0.0-alpha.13", "license": "MIT", "_id": "jest-leak-detector@24.0.0-alpha.13", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "dce06799550ceb8f8476c4e89344a1a269fae652", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-24.0.0-alpha.13.tgz", "fileCount": 4, "integrity": "sha512-nhkSsfk+Xxa33O6Nau5wHy6MvTYHtYDQS61ouNLnzMxRXA3pL5/z+6HNOjlUvP8oqZNbeFlG9JiNnKR2GFrevA==", "signatures": [{"sig": "MEUCIQCoK7ZIHrkJzRFScJh6NZFf1vnjd1SRPkFgRfw+Ztv8fwIgdySUQJmbfT8eeyU2FGkVAoWdtjsD2KGPg9y9Le8Vphs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4563, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSIUWCRA9TVsSAnZWagAA/I4P/04ZspUYlmKdO65wGcmS\nXBWWPTwCtSg3Oo48PSqryl5Rc88taBlb/79HICajHTXFUNQi8OVJ1QnnZp2g\nT3Y5OQHvm7ljlGLQY3UDSC7JDGgzsBsQ0LhHkEZPHjgwB75gwa7RjcUv+nBg\nJD303y6uLbe+RWrp4Qcfcyj2+iOluF8ru9HnFrTZrHXePFDgH8R61j+fkcX2\nGyegfWDrTYTgLUrGw5wupG4z2aAs/Ht3MXJn9VROitYnKVj80EugjlovpV3n\nNLXs9Kr/jE2mxwZeR8JmID0xrg60pJx0Ojf/Qy3F6YfHThV9OinpmGyvAgBw\ndDYBIPTwyW9MLl6nGIDphJc4bK9gRSo4lNNog86cURip9X7MB+P99iofH+fB\ni3aokXV6Dck10RWnJI3EDAqPxfwLh36gJii6KvegQuDL6M5moTTc7/0PTyhG\nHuzy6httRg6sqknI7fiwu43Y+i9qqyf8AWICUY3VN278YsaHyU5EXCo4s4gl\njqNuZOiIFnwx8zbyjL81URL7juL+i3HiAKNp72vUtW+crt0uH6AQeGP9tp0f\nPM3jVrKf8O/85TRuSeoPeSy8wx67MLkvLtvr1Oj8AD1nEv/yVaZIEG6yq50v\n2s1RCtgymJlIhI6oc8i39jLTKGZ24EhRtCeO7UYJf4dJIaR1x3YcIkB6+cUE\n/ObK\r\n=y3vn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "6de22dde9a10f775adc7b6f80080bdd224f6ae31", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"pretty-format": "^24.0.0-alpha.13"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_24.0.0-alpha.13_1548256534108_0.19192243998582703", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.15": {"name": "jest-leak-detector", "version": "24.0.0-alpha.15", "license": "MIT", "_id": "jest-leak-detector@24.0.0-alpha.15", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7dbf31512429e7372126212d12c934061f6367a5", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-24.0.0-alpha.15.tgz", "fileCount": 4, "integrity": "sha512-hD1gDj8M82k0d2V/vaXqcAgOhLzixXRcFqaVo8YcMRV/sh/Z4fHME7gE92DY/K7BC10sIotfJ6eQ66jKWo+stQ==", "signatures": [{"sig": "MEYCIQD8j2nFkYVBeplCX88Qy9nz1lxnEKJbh+ZOkznUQSkiSQIhANsWzf84oIPde9AJjbshmVL0vNss6Q0Vkuq3y7o8gsrb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4563, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSftnCRA9TVsSAnZWagAAaoAP/jS9qhE+TcVBL0NBU9ZA\nIhF0Hm71YcGCpfOj8RnTQ79SKjACg+Dkux9QrvQCk/GiO5UsSnRxsYe4G50s\n7PPmOLER2pi9pV6Lfhlf0dsdawMZirZzdyVZQTCQXwQVZgLS3Xd68P6g+WNK\nXrbdIQnnyKLesvJTrxmQ0hSgRkChIF3XfBRo57Zw19xb9EOq1JqjVHiuXD/U\nErbDBeXUr9qUcXslNfwO3j9U8InDf73vf7TPb6nBC+6qSfHbmrRJcBahH6Ci\n+ZJySj0Dcp+O2sWqPh+cZMAR41degkkrglSxdw/9Ad4xQxt0ECbNiB1KSSJ5\n2S+r3SbIBAdYoXJMx+zuj4+agRJ/2O20d23rwGNgUhhM/5m1GYxwJdvXQu/e\nVbihAGLExAat3GW/2SMfjTmaqBQF0VM7YBauOm6qiu4IB7mogS4oznMtFsBN\nP56Ip9Em/QxdBoHBsHHsJiXMqqPd7YfVn6ogMTqsM6rrvjZ380QrQRFJWdBo\nFt0mwBArUv1NdtF/PruslUj3bJqQWuvSKwxwRkW6MRgL3nQmblf16v1rKhYu\n17De7F1UVcQdXZsnW0s3qX/lycoxFB+qmSUtWN8O9Jpp7iK1CDueVAikkPw4\n8qT53LW0rhN1LKDEPGrYYwO27CeA5cLSav4EJz6GatByoBpa/zgXI3pmM5ur\nlhi8\r\n=ZuAD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "28971c5f794330e8acc6861288e6daafcd32238e", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"pretty-format": "^24.0.0-alpha.15"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_24.0.0-alpha.15_1548352359247_0.388604027743368", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.16": {"name": "jest-leak-detector", "version": "24.0.0-alpha.16", "license": "MIT", "_id": "jest-leak-detector@24.0.0-alpha.16", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ea59d8ff0b5b04021ddae3e1e14052fc83bf4795", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-24.0.0-alpha.16.tgz", "fileCount": 4, "integrity": "sha512-0w6lLR5+eu51GYHdYr8Vp66SL/7Yq4elgSFAAvbKcHVhWaAKmSK0A63tCLvd8a7nc8Nmrnirm50Hp/ISVf6Dxg==", "signatures": [{"sig": "MEUCIB0YnrLS5ou3Haph9at0yiw4v1fq4QTwpuK6A240NMm1AiEA7ecpWrcuRMP8eU8o+e+tKmcCay9y7NprQ43jfdX0frQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4563, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSxIxCRA9TVsSAnZWagAAZDUP/27t1OxJBGqkpP6tKvh5\nbB4PcuFNiuvhlPr4AQ7z6rVJWCpS+nmdG6yfwPCyGl4eTETaCmfqMdwBDwLU\nsQEuMU5RYqIrQTIsurAdUF+ApIIPdCQikftPBHS5WQeHFAocVTcUd4oIJ8tj\nkr6iKQPkzyk43g4NIch33bWi2+RhoC+58UQtC8vwPEnDEzx5TUigu3WMen1Z\nrkTM5UDS599SMl0YYXXzacfw4pCyeTHBnbvrYHozMJMjOx6W05TbKyVELlNs\nXU2tV/nk1hy9xoa1+CONkzrQNg2f7sJRv8byeJEnZXW8i2kOc/SxkRf5vaVL\n+k+Drsf/obKc9qWj2H8fBeAh4VsfRPKQvP5F1nyfj8cjS3pOj/itC7UYfhbb\nOtRSc4W7gpCvkzRJSBWNAVf9N5R4IBHYeyI+R3FUQnvvlYYH3p2snLcnjK88\nLLnrwWagCN9/Cj10+I26kArdBk/rfep5aV9jj9vsY1H6q0FhsqZYlCEyymck\nTEcal7Fsg2R8c/SfWtipRikecDOWpu0w9lDWaHKkTBwvn7DvSn90CeoOukh3\nzKoyjDWxZ33jWflL2L8lrsVO3fYoBa5WwAI4rFHGM4RJHzAWWh/+wIYUrukx\nnkh5zv8EjuNmbGKOcP0etcgDAWnPJd7xCOTW/48grhI/rebU62lw1smizvVW\nT7Dk\r\n=7DZg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "634e5a54f46b2a62d1dc81a170562e6f4e55ad60", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"pretty-format": "^24.0.0-alpha.16"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_24.0.0-alpha.16_1548423728866_0.3215148764422191", "host": "s3://npm-registry-packages"}}, "24.0.0": {"name": "jest-leak-detector", "version": "24.0.0", "license": "MIT", "_id": "jest-leak-detector@24.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "78280119fd05ee98317daee62cddb3aa537a31c6", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-24.0.0.tgz", "fileCount": 4, "integrity": "sha512-ZYHJYFeibxfsDSKowjDP332pStuiFT2xfc5R67Rjm/l+HFJWJgNIOCOlQGeXLCtyUn3A23+VVDdiCcnB6dTTrg==", "signatures": [{"sig": "MEUCIQDgd3fJVHxDCxfzFQpRmWkzk8AIEQCCag4CAj7rRw49fAIgfC3aHsnxpZT0vlR71HscrX1wqgXCgxsiwSsD4nFr3x0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4545, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSyWgCRA9TVsSAnZWagAAVi0P+gP/+g/sxmNhdD3zVo2F\neZUBJ4AB8DUdqWd2KLKsI8fRECZH0aNX9Xm+bgNe3iG2S2qnEQTbHrCyuhNV\nn6TQoIgARMCtTufeHBn8sBKq48MGbYsio1/LBwmXTKcPGSbyAbpZ6J29Yfi1\nu2FWFHcr1fn6zWSVDkBoXKOLPCV4X93p1xjJOrgfMc4NARc1ibKj0L19n6LS\nTFfVgvECjrHSo1CfnTNvIy/oyG18ME5gq0eOpkGSk2q3Cl3d+Bqf8b2gshlD\nEXq6lvykYotBshc4wN1HlUUYKkx7IOAQlUNkUpGstEVyYckB+wpmq52gT2cd\n2t9tbrIXMjzGxNlbVz0JGp+5v9PCiStAbJL4YKoeSsqfww6X5nyHzepn7syP\noRlL/RvzSFNKobBKIlD0tk5Fnz2MSGMIFnvN09Sb5gIPEt1Mr9LKi0vV0nVY\nGu0lMH4cEykT8HeiA6667AFsSOPAvrk20TDiWLWmJlcNBnYl5NYSudcY+aDx\nz4DPPiHmGtPf3P8MOhrRXM4Ww431FGRXfjGtKBQgp9O04Hwxmw49qWcbnpxZ\npiQGx53tbQaMf3WpLj0FKrqnCC0DnEzYwFAo5kwXL6gsUVT2PdRSlre8lXBZ\n3BHX19BpxkuofRn/mVyvSqmXakfDbqhRQIE3jHf0B5yqmxM7LqjA7W53ymV9\nQlSf\r\n=+m5y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "634e5a54f46b2a62d1dc81a170562e6f4e55ad60", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"pretty-format": "^24.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"weak": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_24.0.0_1548428703973_0.8321149417176044", "host": "s3://npm-registry-packages"}}, "24.2.0-alpha.0": {"name": "jest-leak-detector", "version": "24.2.0-alpha.0", "license": "MIT", "_id": "jest-leak-detector@24.2.0-alpha.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a458525ae4a623c064f03010ef72fb96e22c5743", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-24.2.0-alpha.0.tgz", "fileCount": 7, "integrity": "sha512-7gcUTsiXfof6sv6janwRLxLL6rj30El/6o6UGGz1QIeIEnhEdLS1Djw9sIyeAi6IpcmTS4Axe9Esiq73OI/FAQ==", "signatures": [{"sig": "MEUCIFXaCC4zONW1lnF/9GN/3q0EP0oQFZhpcc+gyNnjRGkxAiEAtgt4yM8+UEY7QNeEKEBFBw7tT9mc3l+4Jv/eem8RIAY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5513, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcfovnCRA9TVsSAnZWagAAGBYQAJlvQnWElZzvjHFH8hOU\nC7TAQ4Enitsi95JblMQpOQT3ttk1Q870ZX8PlHt3n+YR9WuUGDYtfCnk0B4/\nhrQIez1t8YbOkQtgdetgHzkriQjEAvQWLVu4Md1MIpa3b1QpgbA3szLovOui\nuQyFvn1Psa8jdvl9ox9+FpI3+jb5fxazM0/PUEhwkJbxxBHmlpGOixKq1Tr2\nlpPIKzKivB/re3W8rqnEOoAO5azYQfW/ytb0AUQQ3kzfZPaqlX2WHlJ8Quh3\nj5ASBRX+xsWDOmtp7YFGkN8LY32u0p7tk7s97ItIwxnYNZzNUa1FD1PWvNpD\nZphVumb1/VEtHxJXCVDfnUJIJWWZNojm0q7YBWMctyfUYzegol2/ZCuiEBCs\ncUQWBuShUMzWL2REPYa4gMcdnGsuMwQgUja+w4t+B170rZMPvTu6sGyTHTp9\nAO0GCm8JGENfIJRJ0Xg7W7fb3SsPM/po+PZjv4euwszE5M7tsB8FPz9tvXQU\nhrV/IpHxhFOt6a+yJtwlvc8wkPAzNkMNpmZDElFV+rC6gko9yQeLOYux6aYm\nLfs5ZKFFRDtiWhiY0YLFKL06qAbVg1hVyiTN6rhUv0Q8YtkHt8IyoYIL2hKq\nOhhaEpizoVh7XoLBQynBQTVVk/uzYGHWfBPivqW+LXXcKubgGXAreLDHPg/F\n3V1b\r\n=MlBk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "800f2f803d01c8ae194d71b251e4965dd70e5bf2", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"pretty-format": "^24.2.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak": "^1.0.1", "@types/weak": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_24.2.0-alpha.0_1551797222638_0.45978151592929795", "host": "s3://npm-registry-packages"}}, "24.3.0": {"name": "jest-leak-detector", "version": "24.3.0", "license": "MIT", "_id": "jest-leak-detector@24.3.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "20216c2fb94a67d90b19c34e18880974bcd901f2", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-24.3.0.tgz", "fileCount": 7, "integrity": "sha512-NUwLCYPVMnSo7mHaXY8ahKbzmPNBlRTPvmvoHK70Y2K17COFNfVz30wKhsa3Dpv3rmcnk2XaPq77DKjUAsyVGQ==", "signatures": [{"sig": "MEUCIQDLSGx5gwATrgBh37EwVbvmxikhP/Bg3rLMJJJoFE7sdAIgOlPuqyVE5Omy+f4vEeOviKq6zg89UMPE3ObPqS1zCc4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5497, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgRXDCRA9TVsSAnZWagAAdWIP/3w6/iAv4rZmjVTdZhXm\ngGufB3K4CVPxjehX1ZPBj2hFdHlf6YJnHXcQxypC42fPImidFTPPM9Omdfxc\nUWRTpX8khZjkdlEgRAvmXfdBuVpkWxa1Q4JMW9RhNqZkwwVSVcGmlVQNC9vp\nhB8CsSixeFiwjWeqk4bd0iQ8/p9/cUDx9ULT0OVafSfVDu9pheeEpWJiMv7Z\nHvyzlfqcxP1NzBiard9C7mrs/VAhlFleqRfjt8C+Y93f0bFq7zyQoMsbOAK4\ndSgm2nvJLOW4MLTzsW0VG5IDU+6e2MSJZ8Lkpi5XaNIqKL5ENgdXNYIiouB2\nz9O1FDRK6voTBSvIX0CMG+IgKT+GP4URKE/kQMXUYFNs9ZyXZoxRqlIQ2GX9\nbFS8Ei11sLe1flB8qOnOkofmMcH3BrHfXoZf30gqGWFpzA6nHIsD1Jva7/IT\nYLWQzBvgesgwVNs6N6jaXhF6lS1A6sHnDy5uQdHg+UiviW3gUMc+QFOIXKqX\nwSsHAZEfkaKgbNezy6gdSSZiBU4qebKZY1qgHP34zk1wthxF+TDWRDW9yvzE\nCAcqtubU7F4k5Ylw4fX28NTjczJzw736YrxCn6cSdTAiADNxt96FvI7MHxH7\n+MhZJ/Iy8L9tvNv1uap9keT0+bAKsjtXfw7avhdHdDuySvmMg5Nl+8LI4D2P\nYhQ+\r\n=FF86\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "3a7a4f3a3f5489ac8e07dcddf76bb949c482ec87", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"pretty-format": "^24.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak": "^1.0.1", "@types/weak": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_24.3.0_1551963586897_0.9929013571710745", "host": "s3://npm-registry-packages"}}, "24.3.1": {"name": "jest-leak-detector", "version": "24.3.1", "license": "MIT", "_id": "jest-leak-detector@24.3.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ed89d05ca07e91b2b51dac1f676ab354663aa8da", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-24.3.1.tgz", "fileCount": 7, "integrity": "sha512-GncRwEtAw/SohdSyY4bk2RE06Ac1dZrtQGZQ2j35hSuN4gAAAKSYMszJS2WDixsAEaFN+GHBHG+d8pjVGklKyw==", "signatures": [{"sig": "MEUCICRAerhrQF5xCWjEqAciMWOxfGl6GJaxAbeMtm1vGpWoAiEAlOVLCK9o4RDxIgIppwrZ43JVwJpiZkzNd6nPa+vuL1E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5497, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgaVbCRA9TVsSAnZWagAAxmAQAJ5CZSbmjbcpIaGgz8kE\n1FCVhBxLgh7m/xXMQJUCba8oBDSHxCbalpsw3QsolEWW8yEjirLpTwEpgMaC\nXOPFLwH5y1CJz7Nja3kbnTweBD4iXt3E2wWxiylmOTQp/f5DQr7CxsEmeqTm\n3FJlYHCv6Nj35JgmulqIUpyfeWbFRzvvP0L92bIPpclD6O0yOQnlCX9PO+eQ\nEXjzL6FcMYzzVvEUBao6Dz/SdpOPo1ztshwiDBXc98sjI6+mkxANQwyOGiyU\niCM82zf1TzUNYFLlNEj1MaZ9zAz+V8QI22lzUExpBT2m2PC96vHM/52ewKVV\n26D9otGyBHlUJr8q26zZZeHPBmoRuS3eAFKBoSnr5NqbkFjE3j2D4zgKTeLO\nansF9UOdLW2+uni1T5Xg5pBmkIBWvYgbN5TeSelhtmcDcm92P7HwZfQqjo46\nxj9B7sRmkyOwZgY9ZqjAqqGergVDANdDEMKdw7onovWPSNy1+jXyvGMztNbT\nUIc7Vkss7DDMsv7yhFHUtgX4hQbnazWFSFRGrANc296l9m9sBqDqFvn2tz6z\n4A6VETx1/PMaZXQmnKTGuodyv8bDRfD7zXiUR7iPTX1452xi/1PbvJ1jBLqI\nxxa6vh4V9v0Sxs/lAISQQ4Jka6zs83N1C7mm5VWlM3U9fuXgOiBXbf+JJfKZ\nhitd\r\n=qRv7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "65c6e9d5e398711c011078bb72648c77fc8a8cb3", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"pretty-format": "^24.3.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak": "^1.0.1", "@types/weak": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_24.3.1_1552000346594_0.7148455331135479", "host": "s3://npm-registry-packages"}}, "24.4.0": {"name": "jest-leak-detector", "version": "24.4.0", "license": "MIT", "_id": "jest-leak-detector@24.4.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f255d2f582b8dda7b960e04a42f7239b7ec6520b", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-24.4.0.tgz", "fileCount": 7, "integrity": "sha512-PAo0y19ZkWZWYmdoPAQKpYTDt7IGwrTFhIwGmHO1xkRjzAWW8zcCoiMLrFwNSi9rir2ZH7el8gXZ0d2mmU7O9Q==", "signatures": [{"sig": "MEUCIGar2Upy08WoVOeNzQI0r3RUH6VGEdUJo0Cvwyb7WJ7mAiEAqSo8HfGar0lA4ahNeCpYDu61x3hhst0i8i+5mpceXNw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5497, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJchnd0CRA9TVsSAnZWagAAsA0P/RvBAv+LFLHKmicTl/HY\nQeU+9+90EiOb2ZB5lWKS9WEMgkffP9403i394k52wuE78hsDWX/7WsWwwQDa\nM3JsCQsvN8EoGl05ZMTYvibaXYrf3ZEqMkD+qKq7XDFdH79cLbNIQ1RdqrM/\nxPaHl3ww/EkmwtY+xD3D6e2RXRLVMc6iQWC+gaRI6QuGX6cred3O3hg2rQ9/\nSYT0VzUB3tiMmpKZmdi7gWpXUXbgehkPYvu2bJPb0YkaQNEdoKEF9m/WKLib\nSSgmHcib/D2nZ23sx0SkDOSbMy9ilY+Ko6u1c/GswqZpDUwXh+C4eaEoLBPK\nv6lI8ueHsdgYeroZ1OtltEdeRK9MMlb0YfahrQjAidh4KDBSt3nuy/jc3xYv\nNnkXOYlW2ldqPCxEkrfLQz7eVc1mvg+v2VpjorTFwVV6GQF/uZ+7B88KCUhH\nYlwpzpjiAMXn0Nm+zchRkOrY8+bwkpTPqVt3enp9QAghYTZVMsGmJAdO+Hbs\nM08q0U7+WAQENBvjiNT89zrVkiMS3n6RNY8OXUHbsHLWQfFgKJQ9QuLE23nv\nNV7KEVpbIq7k/d+nHf0D5EKsi6OZRY5GlzMLn7FezraDqTZGotin5allWrWV\n66Vd8gOyZdHu7tSIGXPYEWrcfqAfv99HvSZfJEol2wqe2yZH9VzUFi6TP0nW\nhkyY\r\n=23oA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "a018000fc162db3cfd0ebf9f23fdb734f05821a6", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"pretty-format": "^24.4.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak": "^1.0.1", "@types/weak": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_24.4.0_1552316275680_0.4205919419179618", "host": "s3://npm-registry-packages"}}, "24.5.0": {"name": "jest-leak-detector", "version": "24.5.0", "license": "MIT", "_id": "jest-leak-detector@24.5.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "21ae2b3b0da252c1171cd494f75696d65fb6fa89", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-24.5.0.tgz", "fileCount": 7, "integrity": "sha512-LZKBjGovFRx3cRBkqmIg+BZnxbrLqhQl09IziMk3oeh1OV81Hg30RUIx885mq8qBv1PA0comB9bjKcuyNO1bCQ==", "signatures": [{"sig": "MEQCIAhLtqimsmYuiU5dHPWSeNzbABLyKWy72QvNjJt/6tG5AiASk+bWNtCxPzYbeBMt1DPsGA+GrkGN+YtPVRlprr3CCA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5497, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJch+AWCRA9TVsSAnZWagAAr1cP/A92LvaMqdrb9VWovfPL\naeDQMs584VXYS4r2W9rLiyw5f41FPub1bMZQl7spurSfwhRbtjxiGQwcYOk9\nBfnAwEKPL3zqXMD4bfVSojoiXZjwVZalZ+AyeY7T4zNsOjI8lfJIIyXBLHxl\nGve/01q97ajjFBDmpm1OFxeFlzAt8Eac64j+FHVvdt4gVGwVdV3QXASXKwk8\nefOPys38Q3G6vpc6K544Grc+CS2+IreJ4qiWYIIIQYrwx/spG6wynszjm2bB\n3igZODPb+Z/ZrkPxtfrvqlXnsrEvz3wxBS9Jb60kPoghcjcwuo8OG93P2Hjb\n3YecZ4ecRbc8ThAxTeXs0pJEVJc+g8kgs6Z5jU+VMNql01qR3cDW8h76pJYl\ntfetXscdFA79GrBSlCqvCbYhoViVXRqicVQNT8k+kOdDigTawdQE9NiFO/51\nMc22xd+l4ovuwssFJQ2h3YbaR+QX2R1rsJmjDr8rbhSGPm+uvpFNLHoIBOX7\nuSz9Y6gwFZTL8B2+JmHgxkodkEU82eHn0Y6UjlSSaGHgE+GWWiX2S1uQVXzx\nqhrCp+WlZJoEJ1ACRldZe90rTJXnf9LYM2gaEq6EGdVK2pYoPeS/ghPKWhxV\nP8oeSsMBAwr+Og+Yo4ydaFRSltm89ECjCKWer7UMj1UrCpfLqXjIH/d10Y4e\nai9w\r\n=GZRV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "800533020f5b2f153615c821ed7cb12fd868fa6f", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"pretty-format": "^24.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak": "^1.0.1", "@types/weak": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_24.5.0_1552408597578_0.3661115260090264", "host": "s3://npm-registry-packages"}}, "24.6.0": {"name": "jest-leak-detector", "version": "24.6.0", "license": "MIT", "_id": "jest-leak-detector@24.6.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4e85938b754a7164271c1f8e3875f0321b37c43f", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-24.6.0.tgz", "fileCount": 8, "integrity": "sha512-lBYsv8IyBjH4bVdMxT4tZRKwBMLIMl6tKyeQ9htSGkTatmnCI8cvRal/P1x8TJsxFvTo0HLhBUQdmkGWNMu2qg==", "signatures": [{"sig": "MEQCIHwgtNxgW69Vq2EwH9j6NkL8VhOrHcJlgpBNxE0jRMzEAiAD5VDEa3ezNpvoH1F2RNkfUhlocULZrOg06JX9nVy9lw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 163858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcopAYCRA9TVsSAnZWagAA1HcQAJtuwQ6+rA05MLrda/F1\neL/r1+shjMiNK3Do774TO8nlQOS3IDhboBJWEtoYfbWAQfr9ubtVcYDThHWt\ndCvxlS174/w7goSJDGruE+xh6eSSkEc8HhUavvpyv1+qU4sBAfAXBMuXbZy1\n0twv1Mah30ZGyO5yULPjFrcRhTqWKjGcJixw/uYbu3ZfvQCWA9rFZOqgAX16\n5AWW6SNbF9Yjz0PdTSdMs98EExDnROu5z+CUbtEyZDYVN3860ZDK4TTz/ZOH\noDs05XhfkuDxA4EOGvs8N/ATFQTPEXwc243nTyZW35mBrL6TQL+I3CpM9cn+\nm2F+jd5U3dy2t5mmYrj/VOsbnDDMU6WWn6XYmXhDioTlpDpJDg1tfR9Ef2dc\n9AzOJT1tMwzR0+xr/vKO+LLd2DkNR1RNf0trRCNrqdxYvJXf9KdENl5Dqvbt\naby/C+nwY3HsQ0ZqbhBC14GB2vMTfbnQPvMSnM2W7+PCL+7JhtkevclM0ulT\nNh+FxlZBdUg/dZD4ElutG5o35LNViGtliXWsMH7CvU7WORMv1tJVGWzIXo2J\n/gpmU3GpqMOMycs31ig6yMPlA2XTBSQCvXI2dxeWY5unOfxUadWGcqGfZh45\nztUDJbt36w5Zy9+e1PgjQBsh0LgMFri+kQVOYE2+q9IrgSSMtTj+VvU54W/5\nGKWu\r\n=jh5b\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "04e6a66d2ba8b18bee080bb28547db74a255d2c7", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"pretty-format": "^24.6.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak": "^1.0.1", "@types/weak": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_24.6.0_1554157592277_0.43721455350613225", "host": "s3://npm-registry-packages"}}, "24.7.0": {"name": "jest-leak-detector", "version": "24.7.0", "license": "MIT", "_id": "jest-leak-detector@24.7.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "323ff93ed69be12e898f5b040952f08a94288ff9", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-24.7.0.tgz", "fileCount": 8, "integrity": "sha512-zV0qHKZGXtmPVVzT99CVEcHE9XDf+8LwiE0Ob7jjezERiGVljmqKFWpV2IkG+rkFIEUHFEkMiICu7wnoPM/RoQ==", "signatures": [{"sig": "MEQCIDeXXJgECTU8WPoxbXvdBblj6//p0bsZqNRdmsWQ5g+7AiAtCg9W37fgXrdLo4bFfO/02TfAFoaD5+X5oO30piF6Tw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 148528, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpC6vCRA9TVsSAnZWagAAWnEP/0XaukvrnDIyMaKAHFTf\nNMibg5uVvbeFxepvKSO9iMZBJFGAY4Jf/10NMucKC3R/1IjgghfuQtse+GYx\nQJNIluDAEarYge8hIoPrrd0bsoSXMWokrT7ZzF37wcHD57ZMv+xVzhOQqFQd\neNtFK5yQOplkgrlsGJibzxsQJtnJRzMajZSCdVR/TaCUwE2EJUe4eIc0xlvY\ny6jwrlGn5IWwM3lkM/5hmtrur1U2goTyYEoQAcXuD7aqkeoo7LfzQsUbcAdn\nS7CJw5NjggpldvZYUXcjIyilqCuJK00ptW2/PnJahHD8sTwWhmRy5owE1Pk3\nrP8F18mEqvllK0mNpDWNKsVUefIJoE6RsbmXRkFDBfGU+1W/AzQN/rfn6T1o\n+QYEcXhpmpFMsTHyzIRGD7QbLLJQtegrFajviZ8GSJRTNiQmH7t9kQ/13mfi\n0GEZUyephvuAqmyS/31T4hEP6PbIKNmJxo28zqt/3S+rIuuG7pV6Xt3nH9Ql\nW01UtbRX4rt3YmIts4i98+/+b8ycgybP3mRZSFYd1cOEeZMjlO80/yLcBWhz\nS1nYPdN1TYRoQ+zysdNhNOx5dkZpRyLZcQ4Cx4XJ/jWEi2ifB+DiC73Cjqm+\nbNa+j5AEThScDxklF5eWZdS0ZU5bAXHiBmCBoOVyWfpBajZ/w/04rHyJNz9w\ntv0q\r\n=DjMN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "eb0413622542bc0f70c32950d9daeeab9f6802ac", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.13.1/node@v11.12.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"pretty-format": "^24.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak": "^1.0.1", "@types/weak": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_24.7.0_1554263726417_0.19646922240280396", "host": "s3://npm-registry-packages"}}, "24.8.0": {"name": "jest-leak-detector", "version": "24.8.0", "license": "MIT", "_id": "jest-leak-detector@24.8.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c0086384e1f650c2d8348095df769f29b48e6980", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-24.8.0.tgz", "fileCount": 8, "integrity": "sha512-cG0yRSK8A831LN8lIHxI3AblB40uhv0z+SsQdW3GoMMVcK+sJwrIIyax5tu3eHHNJ8Fu6IMDpnLda2jhn2pD/g==", "signatures": [{"sig": "MEUCIQDbj+Hn9tIjq9TogYiQ6aClwHnRlHC++/6XO9FQFQNqmAIgP+Fu6faSrhA2nL7j+057Xk+R/2DQVDfF2Dd0IMg/8TE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 150774, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJczkQ0CRA9TVsSAnZWagAAmPMP/1TecyXvbVrFxI4TDqZB\n/WHmnc/xMfSWoARvs6q/3Xkvx3H5xpIYaXcEeA+m+mPWsea1yagtwVa7w4tl\nRdD+x54n4dFyFVnX/CRaGEQEQ09pcSoK0Mks+oEJYwajf0M9U3O4IAtRjsl1\nw1dF2J3/xemtt95shBtKzXiSq6DJsd7VbypGtu+aqYNpIAMnn2Tlxk6qoKN2\nOMWvyYVuJ8j915K9IGRH50J+QtMHvV6nF+bPH0saTGJ3JGnjuhBVjDQJOU49\nTCgO6T61btDQwPIEk7YBZbVGBUxd101e56OG/UXHEboagXQ3EhbKDHxaWaMn\nkAoG8Z449laoA3krI6HDTVaoPSEptKkASQgyFmWcw7/3U5HgE9mWSiZus3XY\nJunh26QAZZQEmfx+X+KkHCLhxdrpcFWJw7n6a+diUF2OD7CupOp4AnwxxRTk\nbMzdV7rsPNU5Ywsk8Rc9MwfzzTqaXSw8bfmcMD5XOgT0SJIpC/yf+HfnVwkh\nPFD59SmrGpwI7Wv7/H8MKw/a2d7PZuwEeurNw7fcM1ZhkV3OL2b9zJWcrMy0\nb3Kxc5gOs+h4KoN8R0QQWghhHM/ZWPKaVmUFdU4YOAoTnyh+lGvIy+xOsdzF\nYqKhpscEeUr6OGwAgdxdrOS+foOoLMXjTvPHxel587bBc8MwV2Mw1P+BVID3\nJdTp\r\n=uw2r\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "845728f24b3ef41e450595c384e9b5c9fdf248a4", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.13.1/node@v11.12.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"pretty-format": "^24.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak": "^1.0.1", "@types/weak": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_24.8.0_1557021748064_0.04208709540129685", "host": "s3://npm-registry-packages"}}, "24.9.0": {"name": "jest-leak-detector", "version": "24.9.0", "license": "MIT", "_id": "jest-leak-detector@24.9.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b665dea7c77100c5c4f7dfcb153b65cf07dcf96a", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-24.9.0.tgz", "fileCount": 6, "integrity": "sha512-tYkFIDsiKTGwb2FG1w8hX9V0aUb2ot8zY/2nFg087dUageonw1zrLMP4W6zsRO59dPkTSKie+D4rhMuP9nRmrA==", "signatures": [{"sig": "MEUCIQCX1I4w/hXV1VrAvICvlJHL1v/f1ySo+otfOXgSoeqYwwIgOU6sbppuxxaHL1Xszl8XN/9TukEr0ENAxu/LWdnVYCU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5511, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVkVxCRA9TVsSAnZWagAAnmUP/1IzQ7R25lOiJg/QcO9b\neqCXuon5Ujmx6301oT7LFt6cVFcShFLsXv+tE6dvnRvRuqVVUrpbeH1L9EQd\nqzmrEesGCWC8fkNoF2ZR+MkWJ7vQVAH7qh1lmdQPDbIuqG/OfMC9ed+a137P\noqGanpT1llMif2nKR3vPEZSjVDGgO5JmM5ekPRzPiuBsXo2NAxZbZWpanSCY\nUrlRwkXU7mNnp7yzo0NmCi2crncQY0xUjGJVgUj/FKCgLpS9lQlYGOGOUzoZ\nipOc8agnuIZFHFU0srliRO3IzbxLY84IbvdbKUyQxWpkE6YP+qbUbfVx4P5E\nywnjaMAL54OjZCVAabkfsJRNj8QNgjw/ySKqJl3li6krJYBWmhiA6LWd3G6T\njQLBZuh1iiN1LteUw3s2N0lRuTroq0GW3FbbTApHl88L43omKe8dfisXjEUc\nuBT/ocD6P/Bam2+AyZG1Nit76CgBKKBZ2ZkqTlCzJYnZ93JL4vAg9wZkdIx0\nEJOEig20DzfHwGJ2H/8TW/UxDKxx3E+uoIka1tRylUvmL36a68XPoSf4z5nQ\nCoZ8ajhu4d1pNvW1Ub2IoGBI99uKuTY5X5BD61XjwDOPAufFH3TH2gcO7nmW\nVsLHVGnIIbnieBam6l706lXNsW5DSarH3JbWiNLxtHqiSCEfL7Y87jReKLr9\n25wP\r\n=hsm2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "9ad0f4bc6b8bdd94989804226c28c9960d9da7d1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.15.0/node@v11.12.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"jest-get-type": "^24.9.0", "pretty-format": "^24.9.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak": "^1.0.1", "@types/weak": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_24.9.0_1565934960406_0.2429720595373943", "host": "s3://npm-registry-packages"}}, "25.0.0": {"name": "jest-leak-detector", "version": "25.0.0", "license": "MIT", "_id": "jest-leak-detector@25.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "04a6430780cd120a991b357cabfb392d78a0fd49", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-25.0.0.tgz", "fileCount": 6, "integrity": "sha512-QlPqlpi2i8gFSNrXkarCJdrFMZCQdy43bw3KHv4plsJWTGBlMM5F3U90UEHoSzX3xiMhXRaIvV7i89kEMVtdbw==", "signatures": [{"sig": "MEUCIQCvps4z0EbLJQN9jjMdtKvNuY3S9mI53Wh2GyDM7AkDtQIgAzxu8ykeMTOJgCfcG7UvqVRQnUAp5rqFaK8fb+qzqgM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5382, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXgrZCRA9TVsSAnZWagAA5uQP/icfKKW+oGO/UYAzdv3o\nkX3+SLCQoqs42OPoF5PSSpu6zcnj2vdya7FdzpmlZHyzpi2hfek/Va0hGv2h\nzpRIGK0v2vimvCrfqDqvputQ045IGfYz3hALAra5Hgi2fFArj2w9L5u0hucL\nFv4myJDKrxsxiCm7aArH15cYI7DrcMFuMQJn4I4lXbZypBGKfweFzw496dtT\n1kvWdNqxkdF/P9TZ5lIGLKj6UZNibvs9R6/qg+iWdJ7SxCBglhUuxqETt5wK\nGlUqW1kFpuozEHCRqKkWjQC2YEufVm9LLstL9+O7ZEzk5f3S+zCHPlhdN3zg\n1OBVPaQEBioJA9MqKdWltSe3XZh3AJk6cMiDjPfj/URH8i6TGRMmjnqXg2iN\nWlRUNsV+HUcQtyumITrvqM+y63seAVQ9Ybtx60H3wqQgaMkzp/WMEoojSdQH\nq0WRoqI5WDxTm2RG0Bgnk+tOTOsuj7AHQC9G7x80RrtusnTlJwxzANSA7/FO\nJA3B5E0cx7CAL/pavymkOCtdmlKpWu7tsdAt3fLBrOxn/uPxzlUfznpaLYV+\n4XYEVqv9hr/vYfJjauvbmhU7f91pdT2hM1Fc0oaiHy9vLEsHteLR6W6k3ztM\nSmCS/i9u0U8qZolqdSTPtMG/qjh2cUml4T1UL+dOhEMTt2k6VAeTHAmOxGHY\nk+pN\r\n=nMs+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8"}, "gitHead": "ff9269be05fd8316e95232198fce3463bf2f270e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.16.4/node@v11.12.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"weak-napi": "^1.0.3", "jest-get-type": "^25.0.0", "pretty-format": "^25.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/weak-napi": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_25.0.0_1566444248715_0.16658724296382088", "host": "s3://npm-registry-packages"}}, "25.1.0": {"name": "jest-leak-detector", "version": "25.1.0", "license": "MIT", "_id": "jest-leak-detector@25.1.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ed6872d15aa1c72c0732d01bd073dacc7c38b5c6", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-25.1.0.tgz", "fileCount": 6, "integrity": "sha512-3xRI264dnhGaMHRvkFyEKpDeaRzcEBhyNrOG5oT8xPxOyUAblIAQnpiR3QXu4wDor47MDTiHbiFcbypdLcLW5w==", "signatures": [{"sig": "MEUCIAoz5qsFJs0xQAn6R/fY9Rck+hF8o0BXZXu2TL2okKEwAiEA1zAIHV8zoudOxi5BV0LnOzXe5NS07mcRkBcIAO/51+s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5650, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJ56RCRA9TVsSAnZWagAAmgIQAIgF3A/N8gLLgXNR14xU\nEKvC7sU51MZWsY3rqi9w0mUkdYqO9ve1eWJa06krSdDs8V/zaicTaMymc8YI\nR5BiOGH1ZwYi4y7QwKMM9TY5Ydvdyb6tIPUwQp4AewmN/Q6g23Sw/G2v9u7o\ni5VQ3DWuoBAVe3Q/udBGpCDhrT8RYUEQWB/vMYCn3vsgH15nKdJbVLK9KHUw\nxnnRT2ETcQ4zvs1fN4zt8DZJSegXz0itc0W513ue8qaKZkYma7kO+OQETf3k\nOS7uohe6knQh60sqSAptEpBKVqB/DQlJVSu+WHsVSd/aFQsJvoWXsmHCcrK8\nxeAKVJPGbC9MBnGJBtZfGxyJH+ZqeYMDQHoV21Rv74kbncCqeTC5+6ShalTO\n5U0pjHY3MGwAAlHBkJ6L1QULcw5sTnkU1CxktXcn2H7ytDwF8G4+yPNZacYS\njAhebT8kvDrDxnlVZI3DJ8JRcQXYj+XKNCoHAB9wX49XbQSjQd9/sB83ycrO\nf0YXYPRK2YqAmaRl2xDj9PZtOGxoaiMallddTK8/byyYlrCeTqOfdhw6gN19\n9p6JppBpihH3XI8LGac48deQo9gdUeQxtBhiWA4FFyAXkbyacKRXZliWEEjX\nm1Bb1zY2Jpz8+mxUhpj0N3Erx7U+Rszkl0/b8vZ+fr6cQowwX1b4ixn9Gm+u\nDS9O\r\n=ECgi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "170eee11d03b0ed5c60077982fdbc3bafd403638", "_npmUser": {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.20.2/node@v10.16.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {"jest-get-type": "^25.1.0", "pretty-format": "^25.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^1.0.3", "@types/weak-napi": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_25.1.0_1579654801281_0.9055572471428888", "host": "s3://npm-registry-packages"}}, "25.2.0-alpha.86": {"name": "jest-leak-detector", "version": "25.2.0-alpha.86", "license": "MIT", "_id": "jest-leak-detector@25.2.0-alpha.86", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "117ac7d97634eab81115e2627d7394a00d5c6730", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-25.2.0-alpha.86.tgz", "fileCount": 6, "integrity": "sha512-i1oqK2yhw1KHulLMWctkP618lnpIBHvpU8jFnJ0/T2y3DdsuVXIB5DKyrzOV5BK8T9EtWFSLzDKou/9iMjvDNQ==", "signatures": [{"sig": "MEYCIQCGV6VAQsgdpGWV43er/ZTE07yjeG0n5wc2EPwGyKBMGgIhALgQ19NWBRoUYRRQhBXV3vukI2ofEeSSoJIMisPyEjRa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5712, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee5HxCRA9TVsSAnZWagAAVDYP/1PVBwCpTYyH4Mtd3y8g\nEFFACNnmdRJO3bHMvyCgGwM1wQm0GN5e0huvM8St0yCIMuxGQ/qyoe3vFLQL\nphtPsETR9Ie1NwdLvSXtWe1k9AGDYq+DVsXy1pjyal/8CHbYkxDsBiVzXq2E\nnhjwx/s/S4epiH4tYsJh3MwymfGm2UQhTjDduRzNR3sB1EJJdckqWKo/29Ca\nX5jFac4VxIcJK5/3jNOEizc7Val4S5ibwg0sDVncjTaOnf95jim/reADvhJG\n3qL0ehCXjjJNAbV+ZCdHHA2iDKpxd0MDoZM1uAHMqpGYXv7lAvDTQa7yHaui\nzZeNTqvUrTTbhbxSbDHqJ3TRCjrTMpoz6LeqDh+fC+cTeFJ1CEPY0NZYBSZt\n3OU07CqAex+8qNxj1k4rvBBVolP7KUE60Ff1nrDWAqfE2YvQ8jAJwRl52qr8\nF6Vhg0co+o6MdFljQm93vtRCIkLj1QjM1S69NokKsUyvIC6hUVCfoNvGItXy\nEUYIFSCR7zcliUTSrTY+42zsmeBEyj6uuFQM0DEybiKX6Y8nhJUULaC39gTy\n/FXio3iivanigAZ/V5yOjEL/pVyXHqEwC/**************************\nQHzSna0UXgATSKh0Om7Z0lUknWa3I5E1BjRayLqhqZH7bYOkHTtLSyDqXQ/V\nT5IB\r\n=T7AU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "cd98198c9397d8b69c55155d7b224d62ef117a90", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"jest-get-type": "^25.2.0-alpha.86+cd98198c9", "pretty-format": "^25.2.0-alpha.86+cd98198c9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak-napi": "^1.0.3", "@types/weak-napi": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_25.2.0-alpha.86_1585156593215_0.5050632512038147", "host": "s3://npm-registry-packages"}}, "25.2.0": {"name": "jest-leak-detector", "version": "25.2.0", "license": "MIT", "_id": "jest-leak-detector@25.2.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0106b59877e79f180642e047ae24897f2b8fdb12", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-25.2.0.tgz", "fileCount": 6, "integrity": "sha512-q9T+0roWegOMjoeGO4uxmnBSqvm33CXq6H+Eu2YmJxvUOiuVhfqMKekZqQS8SRxBiPZHXqEPVHgM3tDtWz0qIg==", "signatures": [{"sig": "MEUCIQCNnNkQQC4fq6U9Tdx4eqPc08F68pjzpIIiuN7jIbyfOQIgcaIhYaQQE1l2qTOAhxswv2ZByQpBRgpp38159jdtVyg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5655, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee5uvCRA9TVsSAnZWagAAO9MQAI8PvMDZchVm+F6Po3KS\nxRGqZoRvw0dS5t02thIzGhvuGo+2mPqatm8luMRqFnBegd9qqUr4sVgqX0Mz\nueAzllYQAGVBDESMcCD3qRo+GwQWelhpAuKIdPIL65phFzuiwm7bQ+y1R9gs\n9Q8EGjZuSoOiMaUdDGbApEoebQcZdymVH9+YqeRu9FtuSMAsR+GV9PhTU2Fc\nbGkeXMSHGQ9A54A6OpUmB5wqrKMuZ4UnLlxYNF12du63CFNAaXyw2bbyCAbE\nWmqQd66gM3q4pGhiL5hStZhuLadZVEYEdktN+vE3OwhDFmnaGMrl3/4Lxcsi\n8B0vYMc0VdklRWHstWs0Q8e91K9u/ex4olZH/ppX9AYjFzA69tku+cfb9hw/\ni5eVCArBPdL2qL0GOtTO/F8H9MTZOvCFRUXAR1+zAJjtE8mdTM/2BEgOSTED\njDnfUV1zFvfiMxiFu8c0by4YTb9ilra8Pdfo2pjMwSKK3VTD8tD53ObBsomn\n6V9rCYva00Ou5nQ6D+tAvIPZ3sMXbp1kUYHyd0xNS1lMPUlw9hdwIFsCa7dl\n991/sXKYKR2Mvh6UW37xZ8l5xQVg7Vs3ZF1zduYM+JU41XhHXlH10KA6EVCr\nTSb8gLETsNGbGYNOJquhZ5SP2MZyg5231KPzuDN1NkdHrPAMHEMqsp8BSxav\n7/IV\r\n=/JGV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "9f0339c1c762e39f869f7df63e88470287728b93", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"jest-get-type": "^25.1.0", "pretty-format": "^25.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^1.0.3", "@types/weak-napi": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_25.2.0_1585159087147_0.3907724384732467", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.1": {"name": "jest-leak-detector", "version": "25.2.1-alpha.1", "license": "MIT", "_id": "jest-leak-detector@25.2.1-alpha.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8a10566d62387dc41e8df06904b9b451c75f6534", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-25.2.1-alpha.1.tgz", "fileCount": 7, "integrity": "sha512-3szingJo7x2BH70pUSXKvfahXqqU75ohE06G/1EqoxdEjq1cWKdQx5ODI8P0+hJQENZT1f14z1JFZEFSxGBgtQ==", "signatures": [{"sig": "MEYCIQCU8OkUVyjeCN3ldm3NCplHEVfgl/CC3cMaMAftdMWW+wIhAJ7/J+HHrSjAKN+pnitz11LrplCHOIaQY7SzwbzWWP/q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6275, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefF+yCRA9TVsSAnZWagAAllUP+gL+4eDWgIsKlz4fplp4\nGgsXBcHZAuwDUQSV8bK3ruMI4RnI4HD9G5N5dox7ya52zMEL41iOkFWh4lzJ\nT4sqTBxEi8FHTZWzHN9OHGozJ9HDj31ZSkOplIZ1O3TOXbxd0/yECwN8hqCe\nvhsDgLXiAjnyBvrfW+MKWSqAHZI6Ys9bLzGwcgCbFmf7JAZcNB1QgVZPRQmj\nUCZka0B2eRGLV7CLAQo75yHMn1CcUyakiqhYaBOh7z7gV6L9q/0alF/OMRoT\n9s1Di8FJ9/rjP+h1TaJ8WZ0qcvusz+7Q4ZEqdPLw1we13v55GnUUZy6d7Z+M\nHtqbxy7Iwa1kKjxdxDzlgjFrtEUpjOZcJaX6MIDZk4dG+U0+mRfgIXyqd9JE\nDfj7mCq21+yJV1oyNQyfH4w516vXtQk2qzUH+/JdKUixSPLA/pcPXGtrVHSX\nxAOX6jM94vY4QJDtC7QqRJod4Moi9LdoCSzgZIWWNBFDsL6xZu6sKNqw+NkM\nA2JdtD5qfq+f4fnrrDqyqY8uvHb3ekH25O8B8UZHQU7tcrLaCrYZOdMwLiJc\nIKHfDqOzkuzXT15tfB2bglUEoFI8g35+V1zYyx9qlMPBXZtQh5ImMK6tdSFW\nCmmd6lVr0UlV5KhxehkmudVQ7JBR9ZxLumCgKRN7Y+NI5iVrt+WuuiZikxYx\nr+ok\r\n=GsQI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "5cc2ccdacb1b2433581222252e43cb5a1f6861a9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"jest-get-type": "^25.2.1-alpha.1+5cc2ccdac", "pretty-format": "^25.2.1-alpha.1+5cc2ccdac"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"*": ["ts3.4/*"]}}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak-napi": "^1.0.3", "@types/weak-napi": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_25.2.1-alpha.1_1585209265660_0.003057960316840802", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.2": {"name": "jest-leak-detector", "version": "25.2.1-alpha.2", "license": "MIT", "_id": "jest-leak-detector@25.2.1-alpha.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e974b702ac1cf9146f2ed25b95e4f6b192335cf9", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-25.2.1-alpha.2.tgz", "fileCount": 9, "integrity": "sha512-eIptsJlaSFi4mMCO8e9CqUSa5kajK37RkFKNfQvy31PsuED/aT5ire/5Vw/cnpQSIVPNFNcmlE5Hgc1DFvcdog==", "signatures": [{"sig": "MEUCICspgQUS1k7y5HsqSHvTpfQpklhHHr60285pBUT9ydE/AiEA5ntobIfsiv9AaO1pyUSoeuYjRqjXs6glA2ZjuIlu3/w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6995, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefGN+CRA9TVsSAnZWagAAr+sP/R+Iaq0kxQMoGbo4qlZo\n/ObB3zXALUasMrBfEyQCNHOB+sFF9p2BLYXA8+U6Jtnk633EAJiQ67brW9f5\nHH/Vz6C3igy9O6J9QBNH2VeOhSGfgZezPtS9/anCzfA41zLI+knYz8EQg+WJ\nAh1QcQA0YF7hwg6RmEsfXLfb4+Qf7vnL5jwY+gfsAatxEsHuT8gVoo8vLmXO\nypnO2n/SWHr7jVE3BRySFN5wGSyv7wdB+IUnobaevk2YyuXiJgyLeWMQKksO\nZBJYaXi5RH0sK2+h+nZJ4ueAMDZPCPITz4j7RGfpY3vuuzW0aHWrF1PPJARH\nHoT1fQwbUXh4a571/yHtz0i4mEnkS75IlKhUhMR90vwdoDtgX/oXUaqtczk6\ndyiO4xQyRpSXsoFeYgqzoIvuUfOgyk+hJs6g1fLhOV6tfyUvvsBDzjL8v1cR\nWI8oyG0dbVmhwpP1CygLsi6K0j8KZsHdSNy1IKoriVn6b7lbYwQ4ne4UV+PW\nsIo/q1Q4VNlCoNKmAAKrfC3O3oALBMy7yNtU5K5Vt37JoiiWjIThgYX6YJJj\najh5NxUnLDRqTu1L/JNFz4/oy5kzWtQ+mbn+cmnSdD9mEP6642QfY2qJQBJ5\nshKNeFvxXkm4EyeS+/klYtI20DCnto9yjj7di3lX+jmFr9bsIpSx0MoBt84/\nFcg5\r\n=Aqcp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "79b7ab67c63d3708f9689e25fbc0e8b0094bd019", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"jest-get-type": "^25.2.1-alpha.2+79b7ab67c", "pretty-format": "^25.2.1-alpha.2+79b7ab67c"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak-napi": "^1.0.3", "@types/weak-napi": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_25.2.1-alpha.2_1585210237727_0.9355926288656755", "host": "s3://npm-registry-packages"}}, "25.2.1": {"name": "jest-leak-detector", "version": "25.2.1", "license": "MIT", "_id": "jest-leak-detector@25.2.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "77c55c59c32de9600f6bd9aab9540538b541b253", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-25.2.1.tgz", "fileCount": 7, "integrity": "sha512-bsxjjFksjLWNqC8aLsN0KO2KQ3tiqPqmFpYt+0y4RLHc1dqaThQL68jra5y1f/yhX3dNC8ugksDvqnGxwxjo4w==", "signatures": [{"sig": "MEUCIQCfG6p/jSJjgiFVwsb0lthFc26brw/250f7j2uC31q3JwIgaOKETI8dueBMW/BSZ5YDJG9BP7XiAhvsBOR1gA5+QwA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6173, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefG9fCRA9TVsSAnZWagAA43sP/0Dl9HFGkOY4FCldM2QK\nsrsTTj/KVg84ksmaHEZLsCFhuUQVUlCuWNkmX/UXHo6AdK9Y7kIQgqfrDatS\nlij0HpbhQtbVGL+vmEuurHetXFm38CvJI/UwegptTjLxGQhVThg8YsIp2aOa\nBQQWvrUJ3q0TqmSH7AbZ66lsQypl4mQzr35Rd5uqnxD3jol8GDWmioQOImgv\nfm56pb24MHU/LJ/aYzT9JG307D0hk6i3joPeXi6ZT5/oprDfXRGwNrV1d7/W\ncqRUsVr3WdlYyswQaTMA9mt3uG3KzfjcTHQk6maxfcklGDSv63/gs5GGeF2n\nS3LEmqbTr6W8+lHOz1I9URkqW6Zb2PqVJC5psWYHp7TPlTtisb9fxCpVleM0\nudhSSpwFYSb2P0nLQqPHZSHUl7e9ue9WXE283g0r0svkfT1qMB9s9l69AWy4\n6zQu3bBKJzad8f4jEMak3z6hKP2KSkINw6QwLGR4tkSEtszS8I9ptIQCZX6l\nTWWKmwWytWBL0+82qXFxsJiC3y78V+hDCio9N4HvSl2zCQw0HI/vTANXZ5bw\nS1b0sQ2moUDFO02UzgXErJbFum75Kgn7LU1wXYQLIakyEtw7IKJou+pbse1a\nAawtG4sgAUFTjj3IrVQllBw30WNFxBEfBE16/g8jv9MxoZBwrz/QgYVZY+3Y\nVOKo\r\n=7AfK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "a679390828b6c30aeaa547d8c4dc9aed6531e357", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"jest-get-type": "^25.2.1", "pretty-format": "^25.2.1"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^1.0.3", "@types/weak-napi": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_25.2.1_1585213278913_0.7615758527941849", "host": "s3://npm-registry-packages"}}, "25.2.3": {"name": "jest-leak-detector", "version": "25.2.3", "license": "MIT", "_id": "jest-leak-detector@25.2.3", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4cf39f137925e0061c04c24ca65cae36465f0238", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-25.2.3.tgz", "fileCount": 7, "integrity": "sha512-yblCMPE7NJKl7778Cf/73yyFWAas5St0iiEBwq7RDyaz6Xd4WPFnPz2j7yDb/Qce71A1IbDoLADlcwD8zT74Aw==", "signatures": [{"sig": "MEUCIAy6Mk1YDGyT/CVu04ROfXDqldx7AtT8dEdEcaUhgC52AiEA0qwb4yKjr8IfBfeM5+/Wq6AqsiY56Lr6800ldiVqwFM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6173, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefQ+SCRA9TVsSAnZWagAAhKYQAJbx3+HYbHBxHxOeRpqP\n/LJlLkHBeM9EglhxCGRFuv7qP0y7jNqxOiUWa/1Qdx8X4HlEd4R9ggnD6JgW\nCa8gJJYPi5HklMldH8AnZPclE2k/B4yeH8mwNnqd64E0re37hrZzYH9cTv8W\nRgLyepRiYOFKctdS6FwmZlbECoIVkwU9c1Zn6vmnaqtRo46PfjJ/dhriIt2G\nAmVILZHpCdK3fURVeEAtRzKZztZVNyOG0mZPkj9o06WaZH5KyYYvZfuqHmGb\nTTzAJWA809u7HSX/Al2BFyCKtEchBFvrf6wAc42eWMaEbBZjS9AZGwj1zC5C\nCkxH2UdRcoL3kNG06dHMYiyWrBnm8CbiCwfqZKKFTptOIdSdsbuKJD/8Gt+F\naKCbxndWsgxtI7oFWrjD/C2E/4ADhOt3/cirta1j1zrldP/LKC3oUB/779BF\n3EGr40V3+i2tic8MrAuCURy0D9E1/io+O2SxsQ2GeJI5ZEu6T2WDX8lLUjQa\nJF7UK5npCY5xdhMSL0jHwGNdQefb9/9cWd7yqc/4f2nojqu1s8mXojcM88PE\nOZDCRonefk72QusNQCNhajFtyR32eRxPpJz8eOmgtF3GVdA9K7Czmi+ZBbDw\nxigaRck0+c/834YDoZJfggcgCeNqrYBkVvj/c6VW9/PjUwnB8/qXuRCp+0kU\nqVnC\r\n=+PAt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "6f8bf80c38567ba076ae979af2dedb42b285b2d5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"jest-get-type": "^25.2.1", "pretty-format": "^25.2.3"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^1.0.3", "@types/weak-napi": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_25.2.3_1585254289799_0.9087661208135411", "host": "s3://npm-registry-packages"}}, "25.2.6": {"name": "jest-leak-detector", "version": "25.2.6", "license": "MIT", "_id": "jest-leak-detector@25.2.6", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "68fbaf651142292b03e30641f33e15af9b8c62b1", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-25.2.6.tgz", "fileCount": 7, "integrity": "sha512-n+aJUM+j/x1kIaPVxzerMqhAUuqTU1PL5kup46rXh+l9SP8H6LqECT/qD1GrnylE1L463/0StSPkH4fUpkuEjA==", "signatures": [{"sig": "MEQCIHRXQoo0hEuW39EIppzDiuhLsY0JwlqJXmR6ISMpC1atAiBwTueS2F0Y6EH3HX3aQhoz+RFhym0jJv2/KhDlSZjJvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6173, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJehb6DCRA9TVsSAnZWagAACOgP/jNh3d8AqRBqSOmzXDpR\nRvK2IS5+Lnk3Meq3d7F0tN7WTN3v1vASkZTVUwihHbHvaTsmBgZS/6ORM97D\nFAo1pKNhA3LwVFDdgu9qpE0BCO0BpjHwvfnBb/i68v6XaIgg07sowieFa9Ho\nPW1+rVeUklJljSPXsMWl0j8Q7hyHBn7pUqmf1WF5b2qetCKo5S4/+OH796wf\nywhU69GDjSsM+IFlSuskBPFFsU97g4KFptGcCHuzEEQyfsU8CM68dIZst1dt\nbOWODROIc0ccqkUz2a1TzDq99aFWG+8Hgw9wJSBv1LwNLmXZXCNIh19Atcps\nhlC+kEWxXgjuGF3y7/wZwInNFTBYug8VU8kPYu/nG6nWMth9NQi/r5lQb2KL\ndlUNFPhh83J36fDejDnA0qhgPEf0q2RxkAv347LtRMrrW7irjCpZkvbQ/T9w\nveHH6tMseRWPnGku+Cd50gxOF+U0QvqAL4jy5epXhLYu6CG52co1OF4mlbSk\nhsXIY526fbMY/V8t74UtD1ucIqTM4UVhiFX2xOG4V6bk18E+oil9dXtl0npN\nvDht9ty7AfBlJxuTbmbng1PNLYEdD/0162jRDzfBtXALWSk1RrEnZ/2nYVIG\nmsBv5rX2kS3DiiI8loxOUxzXCwsit6OOOrA2Ebdmch222GY+A8GJORV26YAn\nAA3k\r\n=WqK5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "43207b743df164e9e58bd483dd9167b9084da18b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"jest-get-type": "^25.2.6", "pretty-format": "^25.2.6"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^1.0.3", "@types/weak-napi": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_25.2.6_1585823363445_0.6725136320126586", "host": "s3://npm-registry-packages"}}, "25.3.0": {"name": "jest-leak-detector", "version": "25.3.0", "license": "MIT", "_id": "jest-leak-detector@25.3.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5b6bf04903b35be56038915a55f47291771f769f", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-25.3.0.tgz", "fileCount": 7, "integrity": "sha512-jk7k24dMIfk8LUSQQGN8PyOy9+J0NAfHZWiDmUDYVMctY8FLJQ1eQ8+PjMoN8PgwhLIggUqgYJnyRFvUz3jLRw==", "signatures": [{"sig": "MEUCIQDw2M95CLVjZim9WB5l1SfUpAKV5a0GlKp+x0glrjTfAQIgBu76ThtLWGOAjTTjbvzRIrUBQoqkn0PTUIwq8Oay0eE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6173, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJejc/TCRA9TVsSAnZWagAAP0wQAIKdMJIKet3sT0NULw7T\nbcJRl7dr8CUaLluzUWk4GIJ5GDalBUB0XRTAV1gp0owxIj0swG/9G4A+UNKy\nXZKQicSssycSKzRI7IudFh3DzdU5NK1kC8ud3jfj39WGGxTyDdKEYg9k6UOC\nRRgwIdgYgPEuhEQlFb9YANDVu1RhL1XlKVnbl4+5m3vAUePEQgYJtW7iQuPh\n/ZCoY721vmWjlsA7kdR06k/BkHJo/AvlZveUj4+DqdmglZ5hq4+zWXmzGpu7\nEBGmMvInvb1QyrAwCkZxfWcUm155GV3baxdXQ1x2PEJUBmoLRMEpB3m/5thJ\ngWkcTpavpNODXK2XcdvL/Ry7AgJOtME+gZFaAe0Bleow2N012oSUI8FOs3tX\njbD1qCB1PIdkJ8N9iwFkWFpbVclhXH26Xpxs/FOKbHPAdyXGnXRR97D1Hyv4\nEIdlic4KLn0c4n3wqBexCQozvHx4d70QsdEJrnlJrKzz1ZdXBgjvfV4X9P9j\nC2bPDc2sUakHTqFQgU4XfzoUOU8ccTs4J/Z3pi+KiURihzUgE3WAsxjNJPtb\nXvO1qcABz5gpoNkai2sbvIP9e6wJWHYtu6Xc5/I7O0CNA7FBI8gD1itu83v0\nEeRzyclICgQrFjkQD8ehoUxuGFfExz5+dzNcF9zmzlvBqc2p60F1YRBDX3I5\n2vx9\r\n=nRRa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "45a4936d96d74cdee6b91122a51a556e3ebe6dc8", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"jest-get-type": "^25.2.6", "pretty-format": "^25.3.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^1.0.3", "@types/weak-napi": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_25.3.0_1586352076749_0.2388338192243653", "host": "s3://npm-registry-packages"}}, "25.4.0": {"name": "jest-leak-detector", "version": "25.4.0", "license": "MIT", "_id": "jest-leak-detector@25.4.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "cf94a160c78e53d810e7b2f40b5fd7ee263375b3", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-25.4.0.tgz", "fileCount": 6, "integrity": "sha512-7Y6Bqfv2xWsB+7w44dvZuLs5SQ//fzhETgOGG7Gq3TTGFdYvAgXGwV8z159RFZ6fXiCPm/szQ90CyfVos9JIFQ==", "signatures": [{"sig": "MEUCIGhECINd0Nqfh9Sw6/7XDrCysIsbzpuOH9siaojKyhV8AiEAr6DEwtQaQW67d0GRCSVKATrGwADGUvCFeGu6gQmzdH8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5849, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJenMenCRA9TVsSAnZWagAAKHwP/RAgjv08wrd8u5QP8hrq\nQ0DCSyacj4yo1nGPou5arAJO/Uo3uJSrnlThjNGJKPOPMQpwClJgLOLC2lk8\nAy/hEy9O8Terh9NchCLGVeBoGVuYZHUMArJUfIMSFqZiznFAXF810XqNpOk8\nF8CEls/fRDpmE9S1CHF7NrXx+uSghPQPEV+9OCzVd9f68/BhxWB9BD5msid+\ngVWphDKD2eD31j6ABlYp0rZlRxQ5ACbmN/SbsLQWOIWrWFk/tKOUe3AjMn+L\ndGDDzbM3e9Adg+L8k1jusdqyWTeZ19dPpnEN1BOB3FXUmT1CZxWZUIuMoUhE\nfwQy9/XE1b75qPb0IB1q+Jo0k5DDnTlPYgB31CZ1/J3HvCvQDGoqzHw0R2gQ\nmI3vVa2IJNYyxdaLpWffFq1LxJdfJjV/1ycDQQwRjpKz+ndz0Ou4BoRTcTdW\nu4bafVyothE5bcLiTEW0EXmxzD7mvEs9Qaz0uNaCqOsJQY4cTjckplLnEzB1\nB2LStd0Pj5/uMdwaC+NmZ+KJ23KZKhf6zUPjooUPvRm7Hn7hReruMmNXqTzZ\nWN6Qvsm7WoOzfuxFkgfvcnoEn6KgqwInr4e3G43zmkC2SDsYYUX3oPj7gRxR\nYx4YdCAsSdVNNsKeK9iRM2hbjPn2rP2O5io+xO4fQVSMhJUzbl3t/2Iw6qxU\nyJ7T\r\n=D/x9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "5b129d714cadb818be28afbe313cbeae8fbb1dde", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"jest-get-type": "^25.2.6", "pretty-format": "^25.4.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^1.0.3", "@types/weak-napi": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_25.4.0_1587333031452_0.0550412427408562", "host": "s3://npm-registry-packages"}}, "25.5.0": {"name": "jest-leak-detector", "version": "25.5.0", "license": "MIT", "_id": "jest-leak-detector@25.5.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2291c6294b0ce404241bb56fe60e2d0c3e34f0bb", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-25.5.0.tgz", "fileCount": 6, "integrity": "sha512-rV7JdLsanS8OkdDpZtgBf61L5xZ4NnYLBq72r6ldxahJWWczZjXawRsoHyXzibM5ed7C2QRjpp6ypgwGdKyoVA==", "signatures": [{"sig": "MEUCIBKUZKxoSXBMzUlAaOxNY8UEEmsaidLPTvmQiN/P5dIaAiEAkRDYf4XOtnfh3t7+4mvD6SEZrOZOtXpi/wlcysiiNP4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5849, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqIfXCRA9TVsSAnZWagAA22wP/1Ok1A5v6JXL5c6L0Qok\nzQX6XO8D/258s7TXgGk2W3PjoEJUYcDGbr1gqwj7yUziSomkdeoAF/I0nyO5\nfHNmRXaPI1h4OkJqkz+8sZruNwMQ0SrpS4cJTr0PCsRNerl4OV/Il3UZsbNS\n3p8xAdTb5Aqx1+jnD94TXgdp3hRsQTfoVowZZW5eezskIY3yNGtzHwsnWwJo\nQOHhapwpFCgaY4XuZpHRG+dmv/qHoR8qc9vRAcCyApZrOJY+sP4Ep2tBEVNi\nb7Eofdb8iRTn/SuvMfMKeFZrNXCbt9XVlvASLdBv58RKLWHaW3UYbAOkBYZn\nd7Ue20yl/242kc8O/SUeI4+Xpbp9QnCeVWhm55jfV6dz5Ju9xic9YfljZCuj\nxkO/zhQ85xspjpdQ4fKniJWsaVvdIwKWsYeNOoDIVi1vpKhs5+K5rmQg+3Kn\nJxWoO03kPEpgFVVg/0Lpwb+di7q2NoOzsxReRyIykEMxcBOebn8Cxw93CC5O\nnPCJk3KsLO2KMjrehf2mPRHHmu4Vi6wTcsLaHyCAnYa8UqtsD/kDA5kUpLmQ\nEQD7jIdKsymwkLl/4MeIPSrB/Z0GOWH5B7FjgiE+YISUnuCaF8PCSiqtBP76\nA4PjzPAZgW/ndxKsWhtun3s5RvQa1NfqhpIOL6J41Mo8qYOz9+TqV85N7V+g\nap0E\r\n=GzOr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "ddd73d18adfb982b9b0d94bad7d41c9f78567ca7", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"jest-get-type": "^25.2.6", "pretty-format": "^25.5.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^1.0.3", "@types/weak-napi": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_25.5.0_1588103127132_0.6022272457411171", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.0": {"name": "jest-leak-detector", "version": "26.0.0-alpha.0", "license": "MIT", "_id": "jest-leak-detector@26.0.0-alpha.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6369ec4de20288f2594eadca71c34befdc3e8f4c", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-26.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-rSqDgarFPeklhuskrr+ms9XUCwAHeOPGmCwFoJ3TBB+mTWPJm9vdsULJfnaHC6ehDTXmRnPsJ/q6I9GBqyaS8g==", "signatures": [{"sig": "MEUCIG9n4xpA86vfTui+yPD5+uGh+XH1nnlHEsJpEFCGOEiRAiEAxOcxcJvu6k9je9zrMg41c5AKfSduobF5iCLSk50seTE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5396, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerWPRCRA9TVsSAnZWagAAgPwP+wWMHWxcbW+zacAufmF8\nuufEGHYtcldtb6vQ7fbWBwsb2Kz8opS9DRozIgf7jym+RDsztXP/v5t/RpJt\ntr1svslRizA/ohURRhfX+Q0YnqaqWIFYzGG2FhKqGq/Lkz1r+8PNz4iTnRrd\nkNFdkvEjtfTuG8CvJnYft+4Ihi/WL5ONm7gEX3PMkWnwcg0NLYlHL9sy7aIZ\ncWkbIghxJtJggTGmt3kSNn/xBVs8RqbM8rMWW1SkmLcKA/QTZBFVrkEf6ojS\nmMvYKfkVDzzXYB93yoLDsjohbL0nUTAkyFNP0XuvOiu0UJqd9WuHUIYB5OvG\nDOstgvCSMc/NU+eY4aKsatLmJ9yUTbwxhWa0XdqJAT9lHBuGS+4kbIyf/alR\nwgBt87j09Yxg5m31XRISPJjpH1x4idyeFV0Co/fKDmj3OXiu/mG/diHGoxym\no1H9nNkV2x+copMsVcWL4NDRSEcF5wy7wAzjJMH1rQXoBJx3+5x6ZC3GlsFj\nOKvS76SqNxMCmj0uh70o2KkO6AxYTQLEVnbm+HrVUFp3oC+UUuG8BhqK0r2u\nbm9Ym2KFvzv/8wVSYx0sHB9c+W0ywvoOGBqPOFXPtb+xxi9GgKLjnY7ZTOZw\npAMJYimmjtKoSb1htevyhxbM/bz14BXbDa6qU1AKT5+Z3G27LOpYkTMdReJD\nlOid\r\n=GWAq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "ba962e7e9669a4a2f723c2536c97462c8ddfff2d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"jest-get-type": "^26.0.0-alpha.0", "pretty-format": "^26.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^1.0.3", "@types/weak-napi": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_26.0.0-alpha.0_1588421585370_0.18739739744932127", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.1": {"name": "jest-leak-detector", "version": "26.0.0-alpha.1", "license": "MIT", "_id": "jest-leak-detector@26.0.0-alpha.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "037ee6ba1873ac38672b02fdf14e0590aa2e860f", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-26.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-+Y2EegSSNEK1O1GEHIEWIy9+cwp3JNjNEZwJqn08VmBzGHQE7oJuC0uomul26xSUKOKI3PH3Kwyxe9Zqu5k4GA==", "signatures": [{"sig": "MEUCIQDEpuDa+cDkaOIAlWjDoIhv0KdY5b+V0Y/BW4axGmVQUQIgCsdjvMjrCZt9UBgN4HMrXcMXwTSk9MP4zF+hQh2PUCA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5396, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerxHnCRA9TVsSAnZWagAAFLwP/jhXPbqxKBkMzDLej2Qf\ndWMpeIRFOyImM208BSGtT10X6T2FITD49AtnLsxkH6PQnXb2N8wGUzpHYEqL\naVozAYEkg9jk3WzowzZ98oP8XorgiCef8siCHVSZYJ1x32Pd9JDN4vN2QB8j\n6anfH6QSxM6ogSWdMvVQGTurIbohunydVXinLWix5+yqiTTmCQJ9M7tN1fB3\nrOxzn0cbyQNy0hT/e46r0dI2c5TRrBc3zMNLLtDLdvMeAsV+FSqNGERfuxkr\nLKafbZYDo/TWZNFtunSjxsW/9UEkJuNOPmOQ6ueuYkEpQcyG42hjwpoivKcz\n1RK0x6+j4durZcZJ+0LTL/m3KdK3MKeMF0gnyZVmNK5EXUeQIB8Fw/wGHAau\nqIA7AVMUaHFj0GY47lAvrDj4wbgUfvhOLVpd0sQXUJSEYS4uBRDXkvF4WVB1\n7RPJXsH9Y47VZgbKpop1GtM7dVngprhDqVtxh6b7I64Ei1Q0y4Y8kRe1/aI9\nHPdzPhD4OOPBNWalA5DFrUJIVGwgiQW1rJV4yWC/XCTxaX62i9kcv7vy/jXd\ngsTMh0b+04WvWobsXj02A6vEop/G0igyBt/PAHQ8zB4hZ5iVI9ckDRY5laNM\n900fLQgZwCBpJYheN6I3DWA7J5tS8QBfyR943SQMqWlB+GpYtpuDM8pSZ1hs\nFk0k\r\n=0Gz9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "2bac04ffb8e533d12a072998da5c3751a41b796f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"jest-get-type": "^26.0.0-alpha.0", "pretty-format": "^26.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak-napi": "^1.0.3", "@types/weak-napi": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_26.0.0-alpha.1_1588531686863_0.2532068942417147", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.2": {"name": "jest-leak-detector", "version": "26.0.0-alpha.2", "license": "MIT", "_id": "jest-leak-detector@26.0.0-alpha.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4eab0d2c466cbb0903d5b40073f6e487b7eaad7f", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-26.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-g1Afbj48CJ2Fayr/piv5Lg/iQNZEI5yccVPrLpHh7v9IhNhnUebufzID7GXHfjVgrQ1Lnz+UHg5nwpcPlQxcKA==", "signatures": [{"sig": "MEUCIQDrBeg55uWiyoGqo+DmPNCZVhVpCyX/rtl1j8BsUJCcjgIge/HJHtP58JmsVaxCWhGCjFtZdqILK707NX5U85cQTE8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5396, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesD1PCRA9TVsSAnZWagAAH74P/2X+49vds9BottxaQPFq\nQ9u3fJmCTzFOLiF3Dbd5Ga6k4B12c3qofTMMjtY1ZK8U7Z9wO8W3N8lZyJpl\neVUUUVRLcGw/pLbwXCAoxXjPE+GqXxdIDs/yc+9RpLfrx/Ig9FXD7MNm7ckS\nQWpAUWdX+XxPtFhEEEERQq4UOj6beFd97U2C4BKf3hyr7IwZU8r1kJxsGix+\nfWuYFPp8/xhCfLcRQKp+9TLI8a8o82+HnR9rYVmDcu8rjJOB/a0QbrtyCTtb\nrQ4j1gefrXgFgQjrQ+mM7ZTFmFK5rzwq230XRweAnk5rQR+bdYY63PQ7OevU\n/LBgyt8tapfy2/8WyeKtiJDM6zjUfLA+zuWMnP6epdYDxtI4A77CKe/u70T0\nUe2jbn23agAOMvlZsquTT4CQzPfzJMShwC7K0koO+/aE+a9jzdb/+viTGxvI\n5HldYU03TsnA1PWSRD32asp3qt4UiO0t278xN9HuWAixDd+r7DGz+vIoPxOh\n+5hoYsFzSS7x2gru1ssgRK2p+M8CIs6maAv2etVZuqZyExv7kssZPCpOn1NM\n/Xrlv2tozoRlRFY3JwM0+3axjynzHt7xU8OoDw60LPuK62swToff6Hh2DEfl\n519MKSs6v5eBW152B0w7QnHjNN+wwIcztQYu+rk82qxkymS2OgJ0eBejUoKE\nGFfx\r\n=E4e9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "68b65afc97688bd5b0b433f8f585da57dcd1d418", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"jest-get-type": "^26.0.0-alpha.0", "pretty-format": "^26.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak-napi": "^1.0.3", "@types/weak-napi": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_26.0.0-alpha.2_1588608334620_0.4505145299190645", "host": "s3://npm-registry-packages"}}, "26.0.0": {"name": "jest-leak-detector", "version": "26.0.0", "license": "MIT", "_id": "jest-leak-detector@26.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "460e76179bbc0499f470d459802b45c28e048d11", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-26.0.0.tgz", "fileCount": 5, "integrity": "sha512-illOYTJmW0zM4EY42dtv+sXUzVlhq7Mmf3JREm0H+IxFikhfMPiNWOKHOhb2+1j5uWZ1OkF/wLhMcD6+SBi7kg==", "signatures": [{"sig": "MEQCIC61Xv9lAjlDrKQgioKnwhldqEjG3GR1O8FA5naCLZRlAiBL1Kq5PCDGmJQR4nkekb7NjzPOxwYz4Pnu3UkgcC3QnA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5372, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesFaHCRA9TVsSAnZWagAAYHUQAISqDcCnNYXMJppfbWgo\nG+vdUXNldPx++VRgzGVe0vdafdrinxygudrZlDimnqyk+6aVhnMQC64Esw77\ntCjHPm00C3ncV5WtZee5IZtzJ2F+vJHSHee0pjqkbHfEyFa26w0pyovRgs1V\nmQUCMlHKb9Zj/CXHEFlHyhSzTMB2Uol21EEWv10rqxslvbVmb9BAwQ0JYBiQ\nC2gTcPbn55BpDwSR8sq0WyaSwAfi25tbxl+WCTRf5E1wFdDFfeW6ValKgghf\nPYj+QkXqui5EIym8obz389HPOHCHDn3G4wIZIis/7UvOS9wZBSYktzu0BMpR\n1DsiaYaxXMT9DUd2wiRjDwU7/D7kc0gj3ShN6WhzS9fPgiD7FiZtH26GWJOh\nr+Oss5ukpx94o8hU4CruJd7Nm17vTTUSPUyHYFq3sihSN5i1FbgtT1lZwBMd\nwMaKBbQsBdLvgVvgzDtZo+61dzcb9gs7BJfvJ0FTY/4UQuAySfG1jKbQmGLB\nUntspFK/uIpPhmamuBf1JHlsO26espVqTgIn8QrjOf/mP5CgiADAPOFazSQs\n9Lk1H0vXNlKEMoa+GrXbEB1ZYWZWwbLDiEEyyoMUitkBZIIshhz9D0XJh8ut\nTgG1jKVDHksT0R8B5Si65bl0/17KIrYpyfzb0PyLOltoj9m89uQRYgtToIDz\nMcBc\r\n=32UZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "343532a21f640ac2709c4076eef57e52279542e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"jest-get-type": "^26.0.0", "pretty-format": "^26.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^1.0.3", "@types/weak-napi": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_26.0.0_1588614791276_0.2772399943111896", "host": "s3://npm-registry-packages"}}, "26.0.1-alpha.0": {"name": "jest-leak-detector", "version": "26.0.1-alpha.0", "license": "MIT", "_id": "jest-leak-detector@26.0.1-alpha.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7867a07ae50bc66f5875bb8c9ab6a7204e600989", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-26.0.1-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-KF/Z7mXcepVPhBs/kdzcR9FlBn5xqyil10WqdLq+M3kmaFyrBvIELife1RxoP9Bns92QCxcBNhvspVcG+Bjatw==", "signatures": [{"sig": "MEYCIQDpIYVnT6PEljfVPvNAmJX3FUp5/cSOj1E38USdVAKkbAIhAO507RDk28mQfoOXDsMiwQR7VCjhgxet7T37/Zn7eWsh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5388, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesJQkCRA9TVsSAnZWagAATjYP/AhW/Pbtw4DFoxm1701y\nrQ4FMuNMxPzV/9j6fxNOcCfKyjRFqMY4p2IuxRlEnLXg8fAlBPJ9Br6S0pyX\nBmA3yxHTFCTxD16VK+NN6rox3EAd6Dxj11YYkQRqZM3vovfUAUA7ZmjxLPaP\nd7l4Gu8n2gAg8vLhpBt+SQGZt7K8ikg6ea+E/Vfy7wFufQwsoTKvnDBcQqF1\nwtOimzqthLW+5XXeGXoJw1ba0XQy5Kwj63Qvb5cWxKwUXMLYzd5tvaRP3Sno\n/jEohsulIJ/9S+92z9Pn4H1wcZRzhWhgC7SErkNrqeGUnvFxsBJnSfGBbfaL\n3oyJLhEMwK3gujwDpfThw+A4SXhBMH4TglgIPDEGEbaoc0BOqlPatJci7ZHy\nKFPRx40nAO3eYXg/j8nDfmkYe0qDGnXksmfcETOypqnL6fTsmAmcfkzZXRwb\nXpyJKEefThkWeBsXyiWjTEx9uYK+eOUGnG5jP5NRB6b07pVcNsVHr+0JWqjy\ntHK74skjaArJULv9OwGy2k0hCE6YBcKml5/xKUSRXMNIGBtdkzMK7xoKNsNV\naRGGqBVA0TCnVdYWkE4mNkg5r9/+MDJWbBqUyD4jiXaGjEPmkLOYhTTWWOG0\n9lQgenU1QN5GyeC7dEQ5/ZX6QDyQhM5FPfQmM8/PrrRVSkFd4ls72lM4OTXW\nglUZ\r\n=qkVD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "fb04716adb223ce2da1e6bb2b4ce7c011bad1807", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"jest-get-type": "^26.0.0", "pretty-format": "^26.0.1-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak-napi": "^1.0.3", "@types/weak-napi": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_26.0.1-alpha.0_1588630564410_0.15851117712805896", "host": "s3://npm-registry-packages"}}, "26.0.1": {"name": "jest-leak-detector", "version": "26.0.1", "license": "MIT", "_id": "jest-leak-detector@26.0.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "79b19ab3f41170e0a78eb8fa754a116d3447fb8c", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-26.0.1.tgz", "fileCount": 5, "integrity": "sha512-93FR8tJhaYIWrWsbmVN1pQ9ZNlbgRpfvrnw5LmgLRX0ckOJ8ut/I35CL7awi2ecq6Ca4lL59bEK9hr7nqoHWPA==", "signatures": [{"sig": "MEYCIQCG2ntA1Lkh9gYId7GC/XIJJisZxzj0vpysT4jutbXR2AIhAL3SqPciNnIN3zswNmE/QfJaiayuyb/6Yv7n7Y7KrQHY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5372, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesUK1CRA9TVsSAnZWagAAQYAQAKFM0mUn4ARlKR5hbcTH\nvFOKnj2qp1XWXxV0HyTdJ+xuMn8Cj5McFyxo0xgz7ubdlgWZlxz/OlTuB8l1\n0ESG3RaEbEuiVs5DRO55rIjzVbVN3nU8CDMgj9k8n1hH9wjnfbEldyC7kmEa\nexJZeTRILV5K1F4HNrRs4Fc6V660pc8UsQKLFA2hUEM6z/1kod2ODOXCoL2u\n9g8Rx3gviEQo6SUS9jD24FpSpqin0cPCi3x8bf1jFd7mHhHlNrv3q3wUTbBj\nxwwUxcBOcvxMm/vFXT1VmNHtDjgRQ7gN4e5JI7GWLwaXTXNZb3QTH57F+HE1\nIHHOh01dQ0tWXimBAxvJwOv8tuHOj+2aR7VHmXj65J5pRXXwAjCnjwjrSE7v\nBOUVJr9odISIhUgCSLNHem1wIAw/t/xfPxdTwox8AdQB59EzSdaM9YDDPTaH\nnY4ftwKL9JcZecKwhBRBu8tdwUyb2bqDO6spusme2vtqyVXS4Nvg6M5seb9j\nAGtfZl7et0db518uhsuQOmuQsPdC5YX8rodScrc6GT+Ot43WRgjnq8diaKCh\n+jCI3cBtnkCcwSe1BkrksDebD6ynk1CSZI9Dkpg3k5mQb9xSH0wsRsAZLuln\n+v824iaFWDvv9PGCKC5F6Jv+HR5iV7kU2k4sLz2XFvm9AMd5og3lpr1wpJa0\n6+99\r\n=veth\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "40b8e1e157c9981dda5a68d73fff647e80fc9f5c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"jest-get-type": "^26.0.0", "pretty-format": "^26.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^1.0.3", "@types/weak-napi": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_26.0.1_1588675253447_0.1900624182918773", "host": "s3://npm-registry-packages"}}, "26.1.0": {"name": "jest-leak-detector", "version": "26.1.0", "license": "MIT", "_id": "jest-leak-detector@26.1.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "039c3a07ebcd8adfa984b6ac015752c35792e0a6", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-26.1.0.tgz", "fileCount": 5, "integrity": "sha512-dsMnKF+4BVOZwvQDlgn3MG+Ns4JuLv8jNvXH56bgqrrboyCbI1rQg6EI5rs+8IYagVcfVP2yZFKfWNZy0rK0Hw==", "signatures": [{"sig": "MEUCICQGNCnG1OKwId0xAaa1DmGCeDak5KLpptEhHOMhcjTVAiEAooxhtCpgMG4twtnD9GDf1x2MeWqPv/Fl32J1LJeEhT4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5372, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8hyFCRA9TVsSAnZWagAA0ZMP/22aCAe59uAhlWyMbYpg\nCLWQihsZaFR0ZVspbsX9kEtWbT0tMMrlOsU+3DoQ8s4gsUNBNB0yqPme0h7h\nLTkWFBJdE8SBlqxJER7ZaBORbC84teGmIRPUTfcVrCdvLo8W/thbcF1D6baR\nP0/JxOcy+0iyLZKit2oj7ZJesyzZMvBSbMkNbpF9mqlhF6/jXEUp1M+Pt+n0\n7N252r1GOScx7Nit+a8GrCtkt9AVZeg6YLaA/XBs4lk+vJkkxETjnMHEGqdF\ndSx5UMcClRcYR+HWstz+wpUM4iWq8iGSMuvUERssBvgsDcb4IX7U3xNk0QuB\nQYb2ofX4ykVqGyMZKGtN3P+oG9bxqib3ho79ePmX+vzn+kPMvv6oDZHrhxHo\n7bilU84ifnhxJoswB8kuLVwXIHT+4Md+8BiJooT5j1z5n2SmuPzyZENvq584\nB31noU0rAh0s/iATmG4MjausOxVcdnPUYdyLEgSgZV1zVR6CYwwcwk3UdvPi\nfo9JK9GZTpzNSrnGGZqYIyP2N5dLAVYRzq91Wl8kGwFjj1P7e4szZGS3cLMO\nES3duD729v9Gt7mIo3lkd7CBbxq+0L5bnMmL40+4sSHXAQaHIoV6k1gDcgO3\n0sMtplfYtBVfEQnmUwZwFoeEB809lQ5hsio0jfytgcjgblMXJ7bBfod9FaFk\nTCUs\r\n=vDqA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "817d8b6aca845dd4fcfd7f8316293e69f3a116c5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.20.2/node@v12.18.1+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"jest-get-type": "^26.0.0", "pretty-format": "^26.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_26.1.0_1592925317306_0.13962412851470996", "host": "s3://npm-registry-packages"}}, "26.2.0": {"name": "jest-leak-detector", "version": "26.2.0", "license": "MIT", "_id": "jest-leak-detector@26.2.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "073ee6d8db7a9af043e7ce99d8eea17a4fb0cc50", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-26.2.0.tgz", "fileCount": 5, "integrity": "sha512-aQdzTX1YiufkXA1teXZu5xXOJgy7wZQw6OJ0iH5CtQlOETe6gTSocaYKUNui1SzQ91xmqEUZ/WRavg9FD82rtQ==", "signatures": [{"sig": "MEUCIBVqIMiZJQOJT1qHX1Ys6R9NS9uZRlL2nYPgHJCEHol3AiEAiX/d9Vgk+5mYnHBr2lp7pJoLEbkS0VJX+QmK2LOripg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5372, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIpzmCRA9TVsSAnZWagAAAN0P/R/DAcUvIA3gUjX1L7+z\nUdWxcPXa7Rbho2ZscCecui4FZyMm1u3tBRe6jn2wqyGRfGamwanulS0zid/O\nv5wWI9ssGchea9RnNl3QGJtGvTSDNWO/YTL2ZCb8yV5O4Dhj8tiK9Sa3rz03\nYzeGVY8XxwxZRRkV+dr62EVMLGMVwwt8fjb/qsGHbsgFhZ7uKjhoSi70ebqN\nyfVe6auH5REAmu0coSEK+jYkwcl82Pf84WjHf7KEC4ibUeXsZFe1QujiZ63M\nOaqmymKmJdYin5PNGeBqx9eLrTmyE3zxbIchiuyIi17a+U105aWoYgPggqgX\nMiHW4MPpgg+Lnu0rMfjuD3ueMRKAiN7tHqdHUfNLVwk5CCevOW3slR6mi7ut\n6F4as8sPAR+MmRiD08D/VOLI/Jb87NyZ6K/Et77gbnF6fV8FXR59c+7KoxN7\n+DEL7I3BF+w6OEcHTuOhD+OcjBJijQfnmvbMpCex682bbn0HtmcGWe31kpUk\ne/z8aZVnZUm7EQw1T0sT5b5Zuw835hSpsdQu7Km7ymdzMh7hUZYeHV01Pzil\ncEiEKmSOSdIqThrW76fPcS+s8CxPGc1uTrSxV3uC/g31Pk+QhDEsXzh/RdME\nkR0TPSMj1YOtd90fQTZuqHspT8Hsm0rx5TShFMwDFigknpu6UdVFWmHu2s0U\nHjFe\r\n=zCSZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "4a716811a309dae135b780a87dc1647b285800eb", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.20.2/node@v12.18.1+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"jest-get-type": "^26.0.0", "pretty-format": "^26.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^1.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_26.2.0_1596103909803_0.6538863969220179", "host": "s3://npm-registry-packages"}}, "26.3.0": {"name": "jest-leak-detector", "version": "26.3.0", "license": "MIT", "_id": "jest-leak-detector@26.3.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "74c077a243585cc1d2cfd50d231d373100dd6e6f", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-26.3.0.tgz", "fileCount": 5, "integrity": "sha512-8C2Bur0S6n2xgW5kx22bDbe+Jjz9sM7/abr7DRQ48ww6q4w7vVzEpDEZiY7KatjTHtUloLTAqwTXEXg+tuETTg==", "signatures": [{"sig": "MEUCIBEqZRGoD7Za1mG4TXrOluyMGldh3PuBIme8WSy7wNFPAiEAhlXFHuhAZ+6ejtqOLYE/zFveZWxxbFFX4Th4XqYhAEY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5664, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfMTAsCRA9TVsSAnZWagAAA+8P/ROb7jeqEh5sz0CtIpFq\ndX5cnQwVsJPdJbBoQZClpswkxt6aY0w5GsCpp7H+eVjWgYCdnCLQtHOua9Z9\nDuEDB6XlAuqpHYYdJ8fRMDLLFAaF3WwIS5s144ydkQdDwZL913WFKUTtlYAm\nEq16YPk1/IS3kp0APIfBHbljO3lUUpYhoih1etGpDKRTIgzJhKX7Z5Kf9tCg\nMwRYIOfqJHY2b7qdgPrqb7Ni7wMxst4hQmC/HjTqOjQIuK4EFXj9spdy+Y7+\n08gVN7pqlQobtDP9zAViRpFYJDMg1N4KsqlG6wqaHmopW69TxjnGm5jBrHdG\nyxDKBhzzui8EAiW5fcjTmS806Uf+aVoEnvwifVXGFI2StkgaVcAI7vtP+0bM\nYkhai8DMypH5wxnn7IZuclbtqlnh8ELkd+ACngiW+1BkvY/fX/zKzHzdL58E\nk62Y6wKPvYRRktIxpkg9Mt6YD7Qynf9dg6LVnlDn1noiwJaov1Z7C0Vwt7Bd\nqjlWEeRUL0XB9nXgRVBDu3v4fSe3RMOV8NMyM9AxwqyfuofSbcMDMMq/52lh\nP4JZ7BGZsur6JHnW/SSDTPevAq9bRk7zjL17penp3TjmyuoJUpOPEWtp7PrO\npVZ3pfyBk2bv1aM8i06NGHobNu6fc6mx3Fg2hQJEytZtBa03beutchQ3wY3J\ny2ZI\r\n=URWh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "3a7e06fe855515a848241bb06a6f6e117847443d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"jest-get-type": "^26.3.0", "pretty-format": "^26.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_26.3.0_1597059116191_0.3897845325931739", "host": "s3://npm-registry-packages"}}, "26.4.0": {"name": "jest-leak-detector", "version": "26.4.0", "license": "MIT", "_id": "jest-leak-detector@26.4.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1efeeef693af3c9332062876add5ac5f25cb0a70", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-26.4.0.tgz", "fileCount": 5, "integrity": "sha512-7EXKKEKnAWUPyiVtGZzJflbPOtYUdlNoevNVOkAcPpdR8xWiYKPGNGA6sz25S+8YhZq3rmkQJYAh3/P0VnoRwA==", "signatures": [{"sig": "MEUCIQCDERJSqsOUd777ACsDbeOOe/DhtqfHgvmNEIOate4eyQIgMAuOCJK5JocnsnFkCbqbt8a+6Dm3D2E1lEgBagvu/x0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5664, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfNFhmCRA9TVsSAnZWagAAAWwQAIHNVFDoBszmqQHIuJcS\nKlgzYBwSMPCW0YvtG741o/mKqiFS+Ou7L6SVToRu/zu1p6glEw8tWx4SlNzu\n+6HzqUiNMEA19yzpvWwDkYIWSdtrv2gjpgmOUolad7jl/jzKp7aw+xJzzmvt\nrBeSLabkkWBqR32qYVZhe4M5hMBCcaLHuew0MQVZFZniEgZucZ5rGMnPH/Ch\nhYLc+S4toaRueGdeh/3umZL0xVzyj92VruHT+4KAQjMk3vZiiCcfDouffzhH\nVscvQS0L50wPqeaDbz3DTOn/9a4j0v9RU9zBbf7lFjINfgEZi3YArdyhu7kk\nnTcE3q6oEu59lz34hizcxrGmYjlzgPPl0JkDeUOomSKIQ2wFVRauZw1mG5Dr\n1lqVQRS0pRgCgkt0X35bHiKz4ioJXWj22TlJwIjZ87Z9RaPbxNDx1B7iwbok\nRH7jf53FosYe61Osi6vLuJwGxOEAR6UccS/nc8KocFOHp2JbMkanC2OICdBj\neFsFO/Mdxk2T7ZhmyXEdVeS090BMUayuBmRO3/EGyHtH1FjiZVA9lSVVgoDb\ncMbz09EYzjWG+wr30iVGLsRnhONOa3X8WG1FiBGQpB44ELMgvTq/s4Sl46Q1\nH87UewoQcvYsCdwglGdJFeLbTxPOj6rAeu9szL69CxIvh4GV8qnsU0ODn2xk\n/kZu\r\n=RxnK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "0b1e41d1d93ce4d15646f4a39fd5a7ffae5f43c3", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"jest-get-type": "^26.3.0", "pretty-format": "^26.4.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_26.4.0_1597266021928_0.3281340925402103", "host": "s3://npm-registry-packages"}}, "26.4.2": {"name": "jest-leak-detector", "version": "26.4.2", "license": "MIT", "_id": "jest-leak-detector@26.4.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c73e2fa8757bf905f6f66fb9e0070b70fa0f573f", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-26.4.2.tgz", "fileCount": 5, "integrity": "sha512-akzGcxwxtE+9ZJZRW+M2o+nTNnmQZxrHJxX/HjgDaU5+PLmY1qnQPnMjgADPGCRPhB+Yawe1iij0REe+k/aHoA==", "signatures": [{"sig": "MEYCIQCQcbab1/FWKq9q/LTscPt+6q5PJlQpb8Il/Cf7fExEMAIhAPGU8KQM/+rgl9qs4oycsaRfI4P+UB/xE2w/nQI0ZsNN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5664, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfQQsZCRA9TVsSAnZWagAA0vYQAJsXxyBqz2jqz/2PP9NA\npK1FrYva7y0DoeyNZ5NyLHjt4X6UVYkFbZB9LTDxBpMK6NyqdBrkgvLeAxnN\nYo//STvSjsfIEscYMsDRdMkYSpyPis2YMvY9sper/8KLjhGq4vcj99OUuylH\n5XMiP6llt0AGuCUqMljcVy5h4epmCIfMO2reshnO+ZW4Se/2NMPz/xNqT+H3\nQ28xB4PB8Y0ecUS2qQ7L0MMH0pkwkzeXZyCzCuynfkaMfpi1NU5D8JgOFddl\n3ZJS/c9902aVnbox4iAD4PI0Y/De4nj0cY79KnJb5FrVALnAX4uhzmiZrViU\nnsQinIflkWXmmda8kCK//C4hR+jKfySCLkC1JGG0xKu61mYY/HYXM4UJRaXt\nYbBaWqZL5y47Rklk75n4DtSGVpFoyVOULQ3qH2JRc6pmMJpPrIxG78haMgU6\n0zZSYzgvHj+V7H4+B4YnPkXoe4oVo97BwqEEtEQ0ANI2KkAm3A00/jHkz9mN\naINSy7Uu+gBQXeRS64kxHPujeHQGPz/bONg/HDULI8/KpwuiLdABsJihzE9Y\nAbBHJ42fcHIhoOmw5jEF5lsaG9RgozXlNJdakE5xeSyZhmdUuvq6bwDqBfIN\nVre24PHs/A73Pl7Md2nOCVLdFGq3LRwdYqJtpmu42WkNOxeAKmewrh4en5OC\ni8e/\r\n=DG63\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "2586a798260886c28b6d28256cdfe354e039d5d1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"jest-get-type": "^26.3.0", "pretty-format": "^26.4.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_26.4.2_1598098200662_0.7894252680977207", "host": "s3://npm-registry-packages"}}, "26.5.0": {"name": "jest-leak-detector", "version": "26.5.0", "license": "MIT", "_id": "jest-leak-detector@26.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a5671ffbc6308e45ad31b42cb2ef0722488a4e57", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-26.5.0.tgz", "fileCount": 5, "integrity": "sha512-xZHvvTBbj3gUTtunLjPqP594BT6IUEpwA0AQpEQjVR8eBq8+R3qgU/KhoAcVcV0iqRM6pXtX7hKPZ5mLdynVSQ==", "signatures": [{"sig": "MEQCIDv8TcvchgIug5doBQJ4wK934P7rNmQVvJk1Zb2yM4DqAiAyYsiCoG9K/QinXrEjbTMIrZ4HKi1Lnwr8UUYYQa7llA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5664, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfeuc1CRA9TVsSAnZWagAA8MAP/i9pt8trQiDG7A1VpZ0s\nhySyszJn0atlxqrMyBgskVNzpBf+Rw5HQoI5QZweyxYQF92cL8anAFf/iPi4\n8MemwKhriySDarNZsWqRS0nIN0VWYNWed4E/vl+gAgQcjr7lfAeoDFGQ3qoM\n4IOBOBd3qXszaTYi6FuVMtqBQpUw9CV0a/+r9KuUbALf1WIMe7vtIfBBnDEZ\n3cZFzDeD9Z+FXEDZ2apUqWkfcuz8Gi4Xp0Yaxo8Bll/dSwNi5+5rlqjP4HJf\nLa3d188GSLsGNDWaaKGkKz79th+7bVk4LejqGak/RIaTD1wt4ABW9GJaSuSK\n+pDkJBaq7hKIk3R7o3XWCmJ4n2QY7dExxzNOrGP+trH3f6ZM2eajR9Xw+VPt\n9WDRgj6iYeVAaR0cOvjOr3EW8gzxMFMDLmpBGMlZfCDK6EGzE/A9R1RAMSEE\n3CJ1eDzYH/0j2+5Q2Om94ws+PNm/dg9+A4pqOZxREcsUl+2JgX4sQ1wRCbQb\n78WTOtSM+uL5QbF7uFInFyVgFNAD9s3nZLIltVVYOJvd3ZFjOpJUuv/6TJRo\nnGJbzn9kVmsq0FuXYhtzvgbD2Wh6VVIr2B76tyz6KXYmk4On4xtpiiiXlUfu\niNO8QTmR05rIAVzCyBtvK9W1HHnEIOz5kUBo0fU2+ptmF5IG1Nl/nvpxfbR1\nHxDC\r\n=jJT1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "68d1b1b638bc7464c2794a957c1b894de7da2ee3", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"jest-get-type": "^26.3.0", "pretty-format": "^26.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_26.5.0_1601890101366_0.5161866754922764", "host": "s3://npm-registry-packages"}}, "26.5.2": {"name": "jest-leak-detector", "version": "26.5.2", "license": "MIT", "_id": "jest-leak-detector@26.5.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "83fcf9a4a6ef157549552cb4f32ca1d6221eea69", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-26.5.2.tgz", "fileCount": 5, "integrity": "sha512-h7ia3dLzBFItmYERaLPEtEKxy3YlcbcRSjj0XRNJgBEyODuu+3DM2o62kvIFvs3PsaYoIIv+e+nLRI61Dj1CNw==", "signatures": [{"sig": "MEUCIHLzy6Ji1VfhSzvQmHxhJYQoNsbazZ+/6W4xcVrcUD7hAiEAxss6SwhGbCB6aqiEAMoxjNPvy8WGp0aibP1YFJIPeqI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5664, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJffEyDCRA9TVsSAnZWagAA5T0P/RbOK/f6CfG/uhtgfJ2V\nIKM7GCB+jXsBkNNmHpIIQk7bVUo4G8Ed4IgpgEB8NfhAEPjDviEE0NrE24b0\nmpohZkdVExioQcFDdvTXJATLqsSP4n2sYsJXG0FVrbfn/FLd++qboJzTCd6r\nWHcwaV4DR5+Kn4l23Iq9XYCnqaPI4NKz8ZR8vt3q+j9ESIDn4q1kicMFEge2\nuY+swbKsbrioMd5bmaySgohJXfigXPGOUp6Lfs+lNvj+R3WtLwv5JNKnHl1j\n4n4bcbNfUf6elI4bNQilMVoB/aUzUgD4HHkRhElzID0z4X0wuObJsji9sVXJ\nKYsGZ+ZkFzo6WoqM9qXTJv012BWzj+9GrSRfyr/dXYTmI90a9jCQyk7lzSdf\nQ1dpPRrcCExNTEDtJOqxnwcnRMP5GblD3INObaJl3H/jBqwSw4OOW7alq8Ls\n8OGtfcH5M93ETQu+CsZZLneWydc4AMl4Sn9I/1qvGuxcvtMPhanCyiV17h5O\nKDvy1bpiD7aoG3BUCsiABHa8THJg+ym/OeYGAIVU1KOYa2O2i5Wiu4rRzgTa\nozDJ0gNUUBeoev3XR0zm9dEdYiHoDuLRhCZHMLybVW5kjo8LXQ3m4BrS2z7d\ntJfyVW2pm4RXtOwiRf+xOnWpWJBfbY+NcG4LPTV9sxhqQ4IAmqJh9fydFjTO\n/gT6\r\n=Z46A\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "d2bacceb51e7f05c9cb6d764d5cd886a2fd71267", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"jest-get-type": "^26.3.0", "pretty-format": "^26.5.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_26.5.2_1601981571016_0.0772076063400513", "host": "s3://npm-registry-packages"}}, "26.6.0": {"name": "jest-leak-detector", "version": "26.6.0", "license": "MIT", "_id": "jest-leak-detector@26.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a211c4c7627743e8d87b392bf92502cd64275df3", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-26.6.0.tgz", "fileCount": 5, "integrity": "sha512-3oMv34imWTl1/nwKnmE/DxYo3QqHnZeF3nO6UzldppkhW0Za7OY2DYyWiamqVzwdUrjhoQkY5g+aF6Oc3alYEQ==", "signatures": [{"sig": "MEUCIFzhYWM0bAZWafJMcUuTu2ag/Nn2ww5fvFuGJprbN8OHAiEAyPMJHGfmtY+duNhLQ3r7kvVa09xBE6R7iHvhfLZqZm0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5664, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfjX95CRA9TVsSAnZWagAACyMP+wa5T8WzzmcBNK7rtza7\nthIRg/fqLbO3znDB1watAdj1CSK+myatoixKYFxgdrGVdz+ULXOmsp0Wmk5j\nHZ5ja6tciimdUCLgEytVvOCkoBTJk8bJFvim4r97oBXPSaudjLSvnSRuWfhO\nSuwmymCKoKJTIQrmv0CtLaCU+gH0IChwoAQpb+J7tGQj2ErSg2ULsDqd+caa\nXS3svuj7l0O6jXClq9tq1FUrmguFBKcDebNz6uZNgYoOuqs/OzSgpQkpu2rK\nzH+XIn6zfPvVEplKAZfVj7nA/8fVR/pJCa9sIYg9cdBwlhYpItIWXULE+2r9\nVQUTSikA19c74a251dknfjzcWpCAD2gWANO6U5WZy7Av0yKJqIDCseZZKz6z\n2R/vdAV8Q0GUfauwRYnz6qR/quipJhHDe6ppyWIFSFuyAxLfkEQtfqUJ6D8T\noIM4GEfdM/XPQFAo3EHMV6gi3CNtz6z5X5PmTdUaYLyXfceGAJPsSoXZllwP\nVLofyLIZzs/wkdEOsDPBNNxtumWYOGJ38hopY064YC5zpI6KBcZbBVRgprZG\nclPxf84YiPky91RsJxO/TZvsiqM2U/9C1X/khewhTE8ttOBNwy2VWgmTtQ2s\njPRyC6i3te6ShluOVUEzXyNso0Jzea1p/QxlDGJdB89BknI2eIHCEJfv4/gg\nXtoU\r\n=3qmJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "b254fd82fdedcba200e1c7eddeaab83a09bdaaef", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.22.1/node@v12.19.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {"jest-get-type": "^26.3.0", "pretty-format": "^26.6.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_26.6.0_1603108729428_0.2742252769342821", "host": "s3://npm-registry-packages"}}, "26.6.1": {"name": "jest-leak-detector", "version": "26.6.1", "license": "MIT", "_id": "jest-leak-detector@26.6.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f63e46dc4e3aa30d29b40ae49966a15730d25bbe", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-26.6.1.tgz", "fileCount": 5, "integrity": "sha512-j9<PERSON><PERSON>tJSJKlHjrs4aIxWjiQUjyrffPdiAQn2Iw0916w7qZE5Lk0T2KhIH6E9vfhzP6sw0Q0jtnLLb4vQ71o1HlA==", "signatures": [{"sig": "MEYCIQDimiUfyhW5gcPUnVvCBojuJaIieX/OdI/ygTxRB/AzWAIhAPgYssimaxlpAkg+nvFqI1gRncWHVT2FcOvPMABWw5KN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5664, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfkp0dCRA9TVsSAnZWagAATQEQAJ+ZEvwaCjhWoURJKW/s\nfTzeG643OTgYWE+Ji9/vri0pNhSlv88Yw8LegJok6n0gd5/u6nQTN/rOftxJ\nR1dsIWBiXrW4mRVgBhUX826GEtyMe8NiRIveMBkncOqXVHAJBtjgqt/UGiEb\nT14gcM5IEHxhstZe28HbxncG9VZw5KjRiPMuuHqyfso1wNyqoqzF5a8WFTKB\nuZzMhp2sOd3nP4HAheQh6l1eptZXElGdXW7YD4yMzsB9qgezXJup08pJNVpB\nOvvmN4qlcTzfjkpkuY7W1nw+hk7aNU2jqxv9Y0GIDmr2b9ZGpv9wMrejMrUO\n1VzC5VcKsXhVPO8cNB0u5oQxMWsb8l1/9CahykcIG599l/p//t0HqJkWRt5I\nJ7uU+IlwtsYHz4NuDFCqiRmQYEXmbeIprndqJWwuzbs+nukeRlDvb2qleaGY\nz/ROrd8p2aMYBVAdJ7gfn9b0UBywV5Eyy9waDrIWD2BgpPEpoFtbT34mnsN9\ntBmhueIxH45oqRzMhtw4boOBKU6s7ptnqfd7tBHzZuFXW0jjXvmZiqUugcWp\nsIHLp5u0axB3O/NzxQvlTDeUMk6l2sAsD4HBk6/RWe+GLPpfu0r2q5uT+RnC\nbx4qQJcp4L4+t1fzpfCU58UTBh+YZetnI3N5kHWO+TlQBi6/GK7sm1uCp7vJ\nDWO8\r\n=qbFr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "f6366db60e32f1763e612288bf3984bcfa7a0a15", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.22.1/node@v12.19.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {"jest-get-type": "^26.3.0", "pretty-format": "^26.6.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_26.6.1_1603443989785_0.7419525526308006", "host": "s3://npm-registry-packages"}}, "26.6.2": {"name": "jest-leak-detector", "version": "26.6.2", "license": "MIT", "_id": "jest-leak-detector@26.6.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7717cf118b92238f2eba65054c8a0c9c653a91af", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-26.6.2.tgz", "fileCount": 5, "integrity": "sha512-i4xlXpsVSMeKvg2cEKdfhh0H39qlJlP5Ex1yQxwF9ubahboQYMgTtz5oML35AVA3B4Eu+YsmwaiKVev9KCvLxg==", "signatures": [{"sig": "MEYCIQCFC8yD4wbVxfb40QyszSwqLP3qk1S+Rl7akfycCaHRbwIhALnHWMBtO3emoHqvPINf3Z6tx2nSe/DJShp6YWqDrvmw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5664, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfoADZCRA9TVsSAnZWagAA24YP/AuF3M5HvJ2RfgfcXdof\nqhFrsss46y48vxtyTWEcZ8wbXgkgbZwK3uaOBBk0Tp333iNcFFUcipxxulYl\nYnpjCuI6CPo6tSXFSOHc1m6QUfOPcGHOzNYtDCb4N4dVPXHQSahWPQ6vYIFr\nY8KcOOcdxjvF3nTgKgOXzeS0KXQ+red0wM+5Hndb2bGFD3xsFDIoMNkMpbZO\n66nrfzooP/uj0FlIG/KuaSygXhgcQb4/Ijt7ysKuXo6ps6Yr9EfvuFbpkMlW\nIkMxstZ10DKjjS+v8+PoSidipc7q3i2iQpj4K3AIzdk795f3LDuvcT0u9vW4\nUwa0DZEjtSzQVsIx6TdFB8GbaQ6Mkh8Slu3VTOGYWIlfK7x6WfoPTqKqggIh\nZrUekEEbXHeYwq4zvgt8I4buYJvyq0cYbRHheK6MfyClmx5oOgC0RrRqVn8i\nxCPj3Z7bnyF59QPgI6Lp6L9safJCuwqt+qC/CEFF+Y6yHJX0lhGcKqntTzSf\nq8qKwlH/ftoKOt1CY2qBWY++x61N2GeNuB2QW3IzVyRK1P13me2vWScb7Ceb\nP4U/nXAhZNvTzy6SXLxf5INqm5N3B8OWt2L4vUW0PvGGIfzNxVTa98Werxn/\nIWgJvcl8DKW0veGq+INWwJl2jbo4uI5pkgymQ0GmckvikjmmUMxZekR8uOO2\nZfpW\r\n=80V/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "4c46930615602cbf983fb7e8e82884c282a624d5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"jest-get-type": "^26.3.0", "pretty-format": "^26.6.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_26.6.2_1604321497443_0.09651401191425713", "host": "s3://npm-registry-packages"}}, "27.0.0-next.0": {"name": "jest-leak-detector", "version": "27.0.0-next.0", "license": "MIT", "_id": "jest-leak-detector@27.0.0-next.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "76a935c70568ae6d30f2ea7e43205e5acae1fcf2", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-27.0.0-next.0.tgz", "fileCount": 5, "integrity": "sha512-qDIezWqa69vyTHQIKGV3SKZKWrqAXUFgxfqub7okBy91Y7fAESz0S6cTJP/Ft4ksGcnjpZsOlfF49HN+uq3VgQ==", "signatures": [{"sig": "MEYCIQD4HOH4mDzeDR9pWYRRwZjBu9gZExU/TJ7CPhFWWkESsQIhALPB6tnhJnOkWjeJXdil72SDlGFwzEvsMh6j6wRc6hoM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5811, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfy8KBCRA9TVsSAnZWagAAQfMP/jj8EBKRm5sDTMbMXYO8\nPuYXjv05HADPuqZp67J6BufQk8oRpr3jHH6ZGK3tt9wfqUlV2LLwOV1rxbqs\ns+en2HqS8Va0XX4fFWS0pW82iycRGwSSP6M7O3/jBFAdp3hJ9ovklNdc25lB\n2l8TofV1O1XFNwXjQhLyfuf4i+5UpEc8+Q+XCziRKPBGUlFO0LnJ7hX2jz+K\nZ3EkKzrkqG6tWBMCOnx9IacfxptVxHWe4T9cU9Dqlv41FBVOucy0KxClMfUk\nYVNxgwtEw6Rqlx/GHLaAgqMVr1dylg8OjPlrhOyWzGHe+piBibWKe4tO8lrj\nqS/N0y+FFGWC/RBkNdz3F2RpYwaCzA/G381NJ4P+KBpw1KUlyIZeHQ67behR\nwoX9GpXs7VYwqvDT476lli+kBOgy6gCcV6gYnPv4zPzkOUd+N1gIiyfaH9BZ\nMLz3s+wtySKmCzaloEY8w1Ib3aRTuZAR/WbWvtbQG0LH9lzzOOP+9Ht1TeRK\n8VfttsHFJhMOC4Cg+7G8myfhl2l7Hv2UCdWB9rJ84toOyKcBUCLsAPSZWhxY\nQ/2n4FCfi6+jZg3meByFgAvrJH7DY3D8tPe9uo3hmdk7LW+Queh8YhsTCNGa\nhzkLb4wKi6lNZ8dF+m0W8m4K76zW7T2iM54qn7XWda+GfETyyEMbPd1kyAm0\nfssz\r\n=Llju\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "4f77c70602cab8419794f10fa39510f13baafef8", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"jest-get-type": "^27.0.0-next.0", "pretty-format": "^27.0.0-next.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_27.0.0-next.0_1607189121184_0.2210211847925465", "host": "s3://npm-registry-packages"}}, "27.0.0-next.1": {"name": "jest-leak-detector", "version": "27.0.0-next.1", "license": "MIT", "_id": "jest-leak-detector@27.0.0-next.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "471c8d81a5238f2659178e6ab5c144b2f2710a1b", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-27.0.0-next.1.tgz", "fileCount": 5, "integrity": "sha512-gKVc9o2Dky1V7+l1IYaD0Xs0tAjM6FzitjHHuvaXvW47w86JNbyZ/7VRtDj1Y91TPftULEFKXhHVYYpCWok9jA==", "signatures": [{"sig": "MEUCIDR6ggbtJ9Vve9wg9aOKF6e866joiu35Z2CEWkJcU+pxAiEAprC8+Yh/E5gQc7/6dYdDpEJnr9jgeEFycKh+7kFF/mA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5811, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfziNwCRA9TVsSAnZWagAAh9sP/2dDa6J26zZDHwNRakG6\nrdtB9Tr0tsMn4Ajws5uDz5GdjZLAnLca6GhfKsNh1LAuTwXdeIkwyogHcOuM\nEGjUEWUmhXDBpDA6xB9e8QqCuHCb1viGDnT1O/4o17VF16Qv9unFaxvMHncB\nrFQWWAKLjeShCdK/GyilHJ7HY8N/ue3ZFr2PMEDOtcgR/LjkPIWzsadgt2l4\nUU3sQ1w5SypXKA6S44kh7ZnLu+inFzGLenQjVn1UI+j0LC5Xs33eLY3vGC7e\nhxAwNEZCWph/bfKp2LGd+JX9VUobyZFo7UwVVN59ei+O5mN4WPHPEP2EoLzA\nUuMvLc7nNAG/rl5r9jE3TA6vUWFuX7uAhgFn4Y0Ih9qNePUiJJtsIYLpwuEl\n4QXha3aGIx2lmWn9meYwLtnzd3YzEkm3heDXo7m7APvQNHMuyY1QdFwTfpEb\nhraS4dJzExNAbLM694gmSLGWiUDwDOXTsBXFnMdM+DHkbRtYXD4C2a1tye0j\n/LgNPVt54JU4ML+BY4K/9eYfBXfmZuJTEkUeKNhgf6zwV6BwhviphT5jwla0\nhewjCpqyKbSgDp5T67dLLF4NZhHEpdt2I4T2vFh0AIJUesXrIYJlvfikpa1T\nLnKYkAy10EGqIHNrVUXtHY6zVaz8lLVaErZp1pMxImBdHIysZzvDgEWDmpRD\nwxLa\r\n=IYcb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "774c1898bbb078c20fa53906d535335babc6585d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"jest-get-type": "^27.0.0-next.0", "pretty-format": "^27.0.0-next.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_27.0.0-next.1_1607345008407_0.4020090686655433", "host": "s3://npm-registry-packages"}}, "27.0.0-next.3": {"name": "jest-leak-detector", "version": "27.0.0-next.3", "license": "MIT", "_id": "jest-leak-detector@27.0.0-next.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "56f810546d22f08a6355dba3654ab004e8c2b5a9", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-27.0.0-next.3.tgz", "fileCount": 5, "integrity": "sha512-2iKOIvSwt4hGsNUrpibCIR0Ti1BeY1rFDcKe7bJiK907zufTVkhSexGttg8CcOrEGgQQvSQnaRDeZ7ql8iyfDg==", "signatures": [{"sig": "MEQCICVLMpyW/PfQlG2R+Fx/Mtou2UM+YlpigcePcZJdWEVNAiAqWoUvwltEKElqhgr5oXNY8/SAa8FHaSD0wSdeUiyWWA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5811, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLuWzCRA9TVsSAnZWagAAj8IQAKB4l1LFUGby+iyyY99m\nqgyiCbIh5MRxnwApDBtMPKKVwqFVWqds7f562CYsoWSSIRAYR8WTcCM0EX6+\nocIszvs34kva3sUD4AYCisOHgXyU7i2KPZTbV4rvuXb3tETVq/oVqwAZv+Dp\nW8pdz8FXPPyp2c8ZY0JQxnqvKn8V9RuUsJk0tCakZMUhMnwtvgwKERqrOg8B\nfRtYMecIxnF8ONiH+km6XScAwTsp52m8X7m7IGxxUVK26u/m2t+DIZU9r7Sk\nCG3bEJT2lewlpX4nZpnKuJgI1KbPJt9HMOg2QAiqt2ALbRl4Wjr/2D4UUCnp\nNKtzeiPxkUieZA4NOAZbN0/OPAnMiSYOIrO9Hkgskr3Q6Ir3pOduwa/PgHav\nHJeSsOlB9HScBYd38jcAl9WHEK9plSBPouG+s0KelRJImn6CKVD61fZpaYrW\nTo2aLrsJxGF0LjCRgSPHucouLV5+0sbWim7nOBTYu5LZJ4oFSQSAfO444Nis\nUTyhnw6WVF0xUVoSKjfn4uTm0DNIBUBq1sIx7W+5IF+TDEA8kKXJvjU8ibwm\n9thXUNe165Kj92ds1C26930w07dmdwpc6RgqMXLIt+TsNxTXeyk2tp37hKT5\nr6eR2gwdRpFewJVE+VcJWf5BjwT0JvisPJ14D52uMBpj3Ax+TxwjfluLa+SE\nAZgK\r\n=kt44\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "2e34f2cfaf9b6864c3ad4bdca05d3097d3108a41", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.22.1/node@v14.15.3+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"jest-get-type": "^27.0.0-next.0", "pretty-format": "^27.0.0-next.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_27.0.0-next.3_1613686195337_0.6344335377842982", "host": "s3://npm-registry-packages"}}, "27.0.0-next.5": {"name": "jest-leak-detector", "version": "27.0.0-next.5", "license": "MIT", "_id": "jest-leak-detector@27.0.0-next.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0b486db866cd3f093b461fe9a868b16447d3d30f", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-27.0.0-next.5.tgz", "fileCount": 5, "integrity": "sha512-iaaDbxBdjRn6MRNSE1AEdd7BfMHNXssJ04GkBTe/rTkx6ibsg3wF9VyRgd22cXK7kpwvjCnrC1LlN2kOo8cFDg==", "signatures": [{"sig": "MEUCIQCHewjhYI7j2R/zIbrjxrcBSeFLtVg1ihSEcQsaQyDXOgIgZseCRMjcQrMXJnouzhJs+ZT6jljwRUDX7jd2j6qsYSg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5811, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgT1sbCRA9TVsSAnZWagAAiLwQAJZiNdUqRghSgJE1mDt7\nc8EtAUxmBHjZJcF88PRtLrdhFOd71eeb6L35AuBdh6fyKADsN9MsgwRkeeo2\neECKTg2p+PKD3veoAAtuxBdSvjSVDEWPYshsCK4JEUEMk/FB4xXwHyGcI1Vx\n/ft8M3WrDhWhOqL8pv2OQfAOCqbSiTC1yfEg5/YRAbGKXu7S3r8seo/9EwEJ\nz3+HJdxZZXHU23WwzX8BGrlAqV4JOLDjGrjzHoAuBx2TLwqdo9AoXIc2eJSK\nZXhHxWjj/riUvcFlAFnYq8Qiv9zCBz3YsfgoSwdLkq4eczf5fU1X27CK8mm0\n50CopM5uLw4KY6QxgE3Ai9c8hBpBW12MWk+Huj6XFsLKxA44s4lASqXJII78\nbEV0oR462czYjtBxy1U5sJh0tCKnsX0itcFi1oRCOYT2KV+2y7E3p6aVElSB\nJtBlZfuGbTpNQrSJNfgSiJE0RNyS3pzoryCSY79bE7RvBREC3ySuHWAJp2S3\n1mk0PK52/6thPSCy0YLhmSIrbbhDS67Jit6P/Kja/pm3UGGobhX81MWNMBsu\n1N1Xg+pEe6QqkNAC/Qot0yjW5QQpeN/7PImJV2bY0Pa1Hu1Rr7Ii36GqcuQQ\nvhorX5zpDQ5yO5LVt0W1LlgZJsN1WP24BCN3uk9Fu3FilWmczgb/trkFCnHF\nINO8\r\n=XX+K\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "0a2b94282170b6d4cc26c2d2003cc04ffebe5e3f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"jest-get-type": "^27.0.0-next.0", "pretty-format": "^27.0.0-next.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_27.0.0-next.5_1615813402646_0.41067135011600486", "host": "s3://npm-registry-packages"}}, "27.0.0-next.6": {"name": "jest-leak-detector", "version": "27.0.0-next.6", "license": "MIT", "_id": "jest-leak-detector@27.0.0-next.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fe6446c5875283bfea471874774762ea62246171", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-27.0.0-next.6.tgz", "fileCount": 5, "integrity": "sha512-ChOFeFjvU+q1GVIFdUOftNp03JTZ/qyhZ0bhwbw2yLLUX6OmQPtk1ZpFhb2oCk0dCRZaxwqgVFsnD6Y+XGcz1g==", "signatures": [{"sig": "MEQCIG9Z6Ek8zAPbtjp35P45vgXxc7Z7nzBIPvV6pWW8Sbq0AiBFMVV8G1RllIc+45GjFwdqtAhweHGRjiDBDUQtxhp3nA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5811, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXOcTCRA9TVsSAnZWagAA0CQQAJ+QEL6lz0jXdZx022iK\na9KacpYfTO0vI/7wjKLFzyAAK/NkBIp4DWE03vzzcmZ6/uA5qNDEAvfkpaFE\niRaxzE/yR3BbDneUVKrJI7/r+j9UtEO+Y+qa+hqswS/1Rfez7yA0WxxxrfJm\nNdxxGpvXH9Q9B/Rl3jMHk8Nya+TReEFL0Nzn4yl/vanpIAnNLjlDW8m9FqAo\nS0VTHptgDaEZymZxYa97YedUfmMOhhcllt3/ObtpYrnXH6Csacy4q97pbQ+X\n2rCDmnrrlxWQ+K2MJ/Ak3IvSb5YDdXhOR2sjkUiOn5HdY3mRCatN//Z0vFxA\nre2jPQSmKwJMxNx/d/m50XIO5BCTPCbwuQ/cphdpJjFzEYqgRtS3m6dqAOls\nq1bzqaNouB15/acdC3s14kukmEcOSFS/XCbQ2MwPmmw5KS7zhpRKBCDMx5dU\nAuJ1os6aB8CHv7E9Ocx1Stlv8xtn0WQW3nU8jBo/EGLmTnJer20e01t5/5ex\narLZBwsjhw/cg+Tm7dq99njFZwNDfNq7vVVRFO0J8u29kEOmKlfErRrCzdow\nBPIwHOrfqBCUOG5IELWON8yLxDb4MpGXuw97gqA4bIVPJIO+ozle7p/laONw\n76PrTS83ly8HG/ppyT8hhuHT81VygTeTNtJ04hVEzJhSJsWufC3RECbVS9NL\nrFv2\r\n=+EWW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "974d2f22b7deeb4f683fb38dd1ee3a0e984916df", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"jest-get-type": "^27.0.0-next.0", "pretty-format": "^27.0.0-next.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_27.0.0-next.6_1616701202766_0.09940628232786852", "host": "s3://npm-registry-packages"}}, "27.0.0-next.7": {"name": "jest-leak-detector", "version": "27.0.0-next.7", "license": "MIT", "_id": "jest-leak-detector@27.0.0-next.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "bf818b414143bef9ecb7b6a54e468afa58f1a95c", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-27.0.0-next.7.tgz", "fileCount": 5, "integrity": "sha512-p4VSUNWCOLkxcpDD0zFhW7BQTRjbUhQCGUpHfYd0A7/GO1G8O9hDyy00ruR2yiw4s5/oOibOBAdW2Uevg2QFFw==", "signatures": [{"sig": "MEUCIQDOuLztwK9xUYeFOAffv10z2dHh+WdDO93H3W5crC9w1wIgfTYPUw96Pooa1njUIGVAwVq5/bYiYkIPClLXRToHsj4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5811, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZyCQCRA9TVsSAnZWagAAIxMP/i+E8ueBESUpEtenkwFF\nf4UQeKJc3y4xFy+vFHMVkN+Twrcad4N6hhycflFUB7qCYImeFPS5mbsvelHN\nCKlPuq2YrG7jN9nscc53ztazNc80foU4Wzt/yAnCfeS3v5PiKJmFlrv3Jl7p\nI34oiCiDduO+Iyd9/7GAq0p6mmVdYSGrC4YP4GgesBKCYaTZXBcH7EQmKETE\nhcSZrVgRflDMpKYmSO6YnbNYxYZcn/UJQtr5KBWYDAPCiKkJDeZXSEsPt5bW\nty4dr9Jh7TtjyfrEdbDGtvBZVR9ZtjwnEeYVuzLiy5EkVfSpuPqH0gZ/pxqp\nJvhp/tuCQDEIlRhiiJ08MVCERxsIAnzvcXOhD1hVB9mJTjezgnv0Xqd3UWGp\nOSsLDD2zt3diprrK2D9mRlyMRwQoaBjGZt4Fa7EzKfq30+Ijnh3H1EvfEhL5\nANOprKYTC/n7Ja7sy8cYErf9RQpiW1b1CgV4O/5rDYEPHe4dqvCeZhTZPiV7\nrQ4jOkZljys+e5adIib2jVmvHfL1BHdImDbvCussCz0ws5M0N+ZDkjMLwHZd\ngL9l5HEADt82ARhrkjreWESZPOpvvYjD8ql6RqwM7gm4iSwYapWkuJdzyoxx\nma8ZZhRlh4aW7mvWRJyMiS/C+zzzcRKojz3DnKi1nRby5YEc7cEkzDTX5gvz\nUiH4\r\n=MKSL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "28c763e6be8f57bda89238b95dc801460c2d6601", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"jest-get-type": "^27.0.0-next.0", "pretty-format": "^27.0.0-next.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_27.0.0-next.7_1617371279713_0.8095241486801974", "host": "s3://npm-registry-packages"}}, "27.0.0-next.8": {"name": "jest-leak-detector", "version": "27.0.0-next.8", "license": "MIT", "_id": "jest-leak-detector@27.0.0-next.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a0e292a6737f2dd9ead8b73c4523fc8698ec1a23", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-27.0.0-next.8.tgz", "fileCount": 5, "integrity": "sha512-0c8J7U47zgP7OYOmpICG3qWtJJjaPv3dKEiHmW7bo0FSyq4ktyZHv+QsfPCx5+CfNZ/OVO5OKx7Eldvp47kZxw==", "signatures": [{"sig": "MEQCIFQaZQAU/JsP50yzm6LjaZkfHxD/j2YnEKdJcxyvqdamAiAilNPmt4l4XK5+TKjNsk6qeKx1msKNUMXV/zbeSO3Zcg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5811, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgdMzZCRA9TVsSAnZWagAAuhgP/0t19rQFey42MOlQosC8\n7kCKzx+tglv2wc3AuUnXatKTVKcBvcOQD6wi/DVKxjx88FpypWaUr8PBD1p4\nrYDYM64z6e5csye29II3VW02fV0P2zmYszFXaFzcAtYl7OqJD0kpyheJGc5r\nn6+JVVf1R1I04rYj3hzrP83g9NjiM7hZejxxFRcFRU/Jf5X2+Y4iSCpArApw\n9H2hPZvVUsOEGAnd3vLovyjoGr0/cEuuB4LlwRvUzekpItAdeaXB+2Ihnouh\nKojEC6cDMuUarbc4jI5gzfFYy5778qugKX90OazOTdhYgg/1k99vLGKVSnij\nBT0m+7YLnOD0tcmvy3Jo6syDUINmWBIwIBoFcrhRiGQUG0oHb8mWfF8N/AoP\n9E/aGuBlFnUtQFNGmIjF+9Uy3fgMK3R11Ud2b0lISMSIXPyLec3dc7zWgsnV\nh/SqNaNKsavIbZSTkxbp8Odc1HEUfwYKFbYz7khlt5gRJgXWGpmN546FmPMB\n3RQoJImgeoJblQTTYCbNSkmUPx24sD6HSi9ubhtMBBl91oNCe+wSxsI8QLek\nHp+/C4mFbYo24zN27A1blTUPCH0lOHZ82gtyrkpf/A9PIt8tmHOb0M6XGmZe\nL8wwNf0WQAqT1ZB/6yNYa14aH48qmr3po9xUyq/ajwBX6zkU6wDTZ+n9gxZC\njDVU\r\n=X2SL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d7ba5030e274b52f029179dfdb860349a36eea37", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"jest-get-type": "^27.0.0-next.0", "pretty-format": "^27.0.0-next.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_27.0.0-next.8_1618267352815_0.2557563876473816", "host": "s3://npm-registry-packages"}}, "27.0.0-next.9": {"name": "jest-leak-detector", "version": "27.0.0-next.9", "license": "MIT", "_id": "jest-leak-detector@27.0.0-next.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c2e31907e6aa2e3d4ebb6bc5428ee954fe3002dc", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-27.0.0-next.9.tgz", "fileCount": 5, "integrity": "sha512-0gUtjim26ku6XsW1BoZLIIDT9RI0Vj0zClDyjPn/7vzCiBoS0BnPSwHB95E/r2VxPetbmzjBSatiWaoZPvcZOQ==", "signatures": [{"sig": "MEQCIBXtqS8bXzB0z8lWKpL3YctQT0sfEaRyyYrzWDbrm0ohAiBKhvPPE4iMFBXtvb3G6p0XPh3tSLWmzmW1miV03jilMA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5689, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkOjECRA9TVsSAnZWagAA1QQQAJhqA8nkcZDmcy83cQoO\nMYakNbloRWug6EsFhEEodSBYDSF9Eqjf+k2/vNVhHQlZAOQcSSAbA2kPG0QR\nP+LjzPE5ugUw/Tj9dpvAAlW+arX2FdjbGOW/6Nu3EJ7fr/2czYN+42aE9q94\nicIZv+muMMKqmRT14G3FhYB+JmxwKP4t1UMBzNOsrXZtKVz91Qphr/56ihZF\nucbTqvAITO2LFC3kJbGxGMRwD5Ajoa0T5maQ05+Z2RkyyPvKHByddHMVZ0+r\nwXaHhQMJeSt/KBihovtCJilPJytUJ8fmGMvy9MV1DBNppuRzGOJU4FkiRdhV\n5N1ltNDkrzk47Q3xw+P29e83xX+9lLYP0KEZNQp9gL0cOnyOiOeAk6kMkaGE\nR89hut/W0GJwsN7wJ0fLaZwTsmIEtzU6okmhJ7AylrnY1DOVDMomX33t3JD5\nXrHdzriCW+ANOUpEntoGzDtvPToHllltGwY4kj+TBOSYU7sIXmMwgbEzQqbu\nnB28HeB6k+VqsCfH37R3pYIjoNz1ePuEdcIvVpesl/ljIAQ8wsM7XGz+oIs4\npNHeiKm5+T51RGmwmIthcrsIaiO94ToODCbX8tS1mviVunI1I45y+HumauoI\nrWlgJCAWfIMd48p356rRrw4hHqaB/HaPewXHKTEDE1kXS8hP1VNOGvr/zVCK\nW8pT\r\n=9YTv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d836f33f98845794b4eae8149548a81ddcfc6521", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v14.16.1+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "14.16.1", "dependencies": {"jest-get-type": "^27.0.0-next.9", "pretty-format": "^27.0.0-next.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_27.0.0-next.9_1620109508556_0.12220632321278502", "host": "s3://npm-registry-packages"}}, "27.0.0-next.10": {"name": "jest-leak-detector", "version": "27.0.0-next.10", "license": "MIT", "_id": "jest-leak-detector@27.0.0-next.10", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a3f5aa5bb8c8429c2102ace8fb0703110187c616", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-27.0.0-next.10.tgz", "fileCount": 5, "integrity": "sha512-4LSxtp9H9uKB8S7Cyux1AQcXRe34rOyJyt7hmgtcxukFg0B60cLbvoLu0C5KGnbltrCAnvEGWs78yh9MSD0Z1Q==", "signatures": [{"sig": "MEUCIHM8T0O6tbdqPnYBQY/cFbq4vpABcY/GNY+molRKO9XgAiEA7D8zrQVtaFY/f0E/lnQRg7S4SdEZB9kcY5tLRWBLI3Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5691, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpm4JCRA9TVsSAnZWagAAEccQAJUFYpuPHDuFLVFlZXAG\nd7tWlLnKbdzrmN8hA0gxZZ7BiobNJWHeXKLh12E9jSjE8mwfUsCeHfil8Zcf\ncxVBme2KCzMjPDS82OYWJichqU9yv05AY8ekjQt6WevjlKqxyfTGJMfTnTkc\n8CAEPFI52vM+5Spq23FXaUyNXbVI3azYQS2zD25WQ4kwMKDQ1c3razdxCasA\nkD6g7spdLzqZAmLTeycvEkgrmRw3a5YedzAA7JHMtuAsVJ/lWA9sC0F2fpLh\nBQOpm9zjHDJVANuI3mqnfuI8Jtv3Az95ao0wgg89ChnPgufmKUhzI2V3uuaV\nhX9quhze0UNIvKx9Z1p9jtx9L5QSC+A+AGTnUEyjbFALZxt/zGXYuUrKE6dx\n0qr4+x7XqDpLFaUWDa5T0O6kEem6pE/Nq0qIe7n4iI3S9+LNCKzeaE2qqP0x\nqYLxiESQuSK5QKh/JvmOgY6dWjNrMbu4h+O1cS4KibPVeeyrDZL6zBGI9Vee\nUTiKOYL47rlqVzgbhzqcxfGsNUEarcU239gNQrkDKpn4kzeoXlS69GvWrIo0\ny0jyeB4lF9LbhEvcigSi/V8DeHM7nUEjcHVcIhhPtg6S45zlH8dkDNT5nRK4\n4JTlCvCClvfMYAAH4rh2B2RIkV+Gc/7VcJBirUuN765edpUtKUpqck1UQ7wc\n8ifI\r\n=PKFq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "6f44529270310b7dbdf9a0b72b21b5cd50fda4b1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"jest-get-type": "^27.0.0-next.9", "pretty-format": "^27.0.0-next.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_27.0.0-next.10_1621519880270_0.00043924010557483406", "host": "s3://npm-registry-packages"}}, "27.0.0-next.11": {"name": "jest-leak-detector", "version": "27.0.0-next.11", "license": "MIT", "_id": "jest-leak-detector@27.0.0-next.11", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "82ad127e7911ffc7c397a0659a1b252d9be62002", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-27.0.0-next.11.tgz", "fileCount": 5, "integrity": "sha512-r1X5LPgWsqb25PmvDcs3adZ5eN0dLwbLexziIShdYNuWx2r8foI2hh0MSlwKoDoIr6xdqq5As2GUqCHkBrb19A==", "signatures": [{"sig": "MEQCIF4EHIpr+AQ9/6IPx5aV2NV9QVvSaOM3zntnlyjFAoaqAiBEg6W1G3VoXR1S6gd5MFBsR6LkXF1YK43YHhWJYonEgQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5691, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpuKeCRA9TVsSAnZWagAAPmoQAIenrx040yx6TTdhduHb\n0SmqxcF8hIPvuidWDxEQTtWk29vlkGXA2fnB6jkpMrw7y1qqfQ9HpLdsDWWv\nwaTRpRkySdIsTdZMPHCY6QjQ2pHIBrI+l8fAOtXjbUjdqxg9GbGXU0+6lg1z\nmb/GcFadup6r6lbrb616MKsg5+lyKlYWHWRZ9t88C+47wq0FEhzXeBizxsUt\nkLv3llvvUH4vsdc+PCZJzhcWfuIIEIa8W6UiypM1a7m/GnUdM18b6uG08zic\nmj8JBVySkJAhg9qfmT6lxu8i/X/Xl77ZLnTUUDysvTVtcROHwvnaBVnwvIeF\n4N59RNeg1G9aQrr4moCSkcFPh4PMuH7DTfaaRD4ZE7ry88Uq7WzmLEwcfedt\nxMJloDd6HDla9SBJhPQQJmNorTmOCzw+eNZ3SyROt5pODyimyomZ6n71ns5s\nfvDHaSs0eBu23MmdXyIHCm37BD4Xb22KN+8v0Mk/hQlroIEMTh2lTDQpj1M8\nuHJ0H7VOekmZOVXNsVENbNuMeFI/N6IdJVX+NFjN5Hl+vwZyFGXQc9l7fSXG\nsXVznjZERLt1WSkM39pVFLxAZiC8zBircmFyg8l/W2TmH0BuCVS0v6ZVCMGu\nsQvPi5vd9WlJMVa6Tv04MMVFYVkiy/nef0Mt82VFj6fHF4Sgc+hvMa2pgl5U\nGwsO\r\n=E0rP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "e2eb9aeee8aacd441f1c8ac992c698ac4d303f60", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"jest-get-type": "^27.0.0-next.9", "pretty-format": "^27.0.0-next.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_27.0.0-next.11_1621549726135_0.7682279813842572", "host": "s3://npm-registry-packages"}}, "27.0.0": {"name": "jest-leak-detector", "version": "27.0.0", "license": "MIT", "_id": "jest-leak-detector@27.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0bc0f351ec432084f215049273d162e35d3fb491", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-27.0.0.tgz", "fileCount": 5, "integrity": "sha512-hqa76nyWJDw4wXLxrGKLoADb9pMOSfa+Kf8bjDcBWaXJIT0yffheTpJ7+2ruzVdABz4i70oT6cAw6H3VWNbIOQ==", "signatures": [{"sig": "MEUCICPjOTNXWPePqhY6H9Or95fY88g4ldJN2ju4eHIpdcY3AiEA8IF1TTfefoPq4FozTLkqWbejtdSLX85BHHVfgRT3CbM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5675, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrLIRCRA9TVsSAnZWagAAbzkP/A4omumccphCC3aiixX3\nF7KTm7bYEcAzD/xi/uAAU2hfxSaAqXIJhK5bHZn2bRCeJTDvO3C1vf/xWkNf\n55uqKAqtfeQ+k5qxmwA77Oi6CWi5WZ0+gUeLnUcTGmlG381o1nLRViXn8MDp\npb6NtaxhKPyzl5DmTVtSWE3TKJX7K/u9+Y0VKJjOwz8cGZFLAcGXvXaBw3pW\n1RT14Pb5O68jP61Nry9orzuu/kfK0rnLD1YAUEkj7xsldWv7c3qwoYY8m65V\nP3ZZH69zfiXbLFEjTwITQp9rugFo0ykc/sRNSEAy6F/K+FlFsIKOVQXbb/th\ntKjPTfNoG8PzJ6VomsZlqh8DJUtcRog4ryzXMKorjykfWOkedYUEBYQeo9iD\nG+aNvbDQpxaS5H25WeECz8bE0RXwJKFXenO5ewL665NeazLrYmnV3GfhKs3Z\nGZBJT2d4j1gR59BvNU3ZSY3F2Ip3RoNtZno/g5pl3fijHhQCA92OfEOuOJ5c\nXWnJZ0rjjKWHLAhsgNfXxDWiFBW+p7uQL+d6k2fj3qZpWJ3f5bE1+1BEdtvz\nzKCKQm1+ymoRmfVN4xzfmrhkQuaiEizngXy59A0fd5/5/jeKkyVK2O7z6Pgw\nv9UygeZsS7vpzUxy1Px7wRUl81G5zvfFltN7s/1KWsLC+6c3v5feGBa5Wi3u\n0t8l\r\n=v+a9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "be16e47afcc9f64653b9a47782cb48a5ca243e65", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"jest-get-type": "^27.0.0-next.9", "pretty-format": "^27.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_27.0.0_1621930513587_0.29703923582920333", "host": "s3://npm-registry-packages"}}, "27.0.1": {"name": "jest-leak-detector", "version": "27.0.1", "license": "MIT", "_id": "jest-leak-detector@27.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "eedeaee7c0ab553db4d8908f74967329624342b9", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-27.0.1.tgz", "fileCount": 5, "integrity": "sha512-SQ/lRhfmnV3UuiaKIjwNXCaW2yh1rTMAL4n4Cl4I4gU0X2LoIc6Ogxe4UKM/J6Ld2uzc4gDGVYc5lSdpf6WjYw==", "signatures": [{"sig": "MEYCIQCphuxxiQGXrj+msue6zlqKGAaZtXOBJ+ow47mhQoJnbgIhANEUwkvOUVeIk3UVrGb47Nq8DCy29ulTApvO18R7ud4s", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5668, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrMwrCRA9TVsSAnZWagAAN44P/3VwZ6rOY3K7DgDfIgmh\nG1qB0Tvz2Zb0XwGXyZXVyjcWkIjx1xY//nnWXXdzntGR6ymXDq5BlIS8wCzZ\nBcxbgmaKdDbcb2usFKZvdXPKUbNzxHebKvDpsuirIJQWkIvgNiJlqLfA52OX\n/kvrTKXjhcx9vQleFQJ753K9Icpmy//HmONYaAQ0SPxCgjykO7hofsd6tOZx\nhv1vpfy7J3PbZkIsuLu1+BAE10QTVN0lHoBFaytxgoMVLyoHSjPmXHe/c48g\nzPpZj4u0OPGltmAnK04Oxo77OVvyrtfS0CYE2O1XmGEq4txo03j6nCF5XU6O\nWlhr6IfMuTZEJ+bBf2qm2qUtII/SS20Y55O2pdyhfcc8NHigkahtEzUhaiHP\n/OSXqdjaUOhBMqBSE5iE8MjHbVYxmoCeZKB3lJ5TGKnM1ydSxXVnSshRql3D\nwMRnl6wOYA94xg+5SWnAUcjb753P5xc85+dC+Z1LWtZ+TLQ2F2n5czFNVu1n\n4b8vOs+/Hf4XDkUaQrhjVtcrzdjbjeHhPA/U/8E14d5slrjiukUAnhZH9znw\nG/gtpav05JpU2cWhalYwJaX1HVjQpzbqJsp/lnaza5hHqIZow9I5d+APPPUB\nbHzlP39Ln8nXqS9N65tTEsNavvcgbzsCzWQaFR3ExNGBsONFOper1NhW5PgW\n5ga5\r\n=bMm9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "2cb20e945a26b2c9867b30b787e81f6317e59aa1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"jest-get-type": "^27.0.1", "pretty-format": "^27.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_27.0.1_1621937194984_0.8984216443841397", "host": "s3://npm-registry-packages"}}, "27.0.2": {"name": "jest-leak-detector", "version": "27.0.2", "license": "MIT", "_id": "jest-leak-detector@27.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ce19aa9dbcf7a72a9d58907a970427506f624e69", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-27.0.2.tgz", "fileCount": 5, "integrity": "sha512-TZA3DmCOfe8YZFIMD1GxFqXUkQnIoOGQyy4hFCA2mlHtnAaf+FeOMxi0fZmfB41ZL+QbFG6BVaZF5IeFIVy53Q==", "signatures": [{"sig": "MEYCIQDUSKxseLb2rGXwNsxLHScJfQBhcD46/NvX7srJso44GwIhAP6AS5lyr8Pkljb/zulW8McajfDQwwTGboAzZf3F+iNV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5668, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsi52CRA9TVsSAnZWagAA2YYQAJzUPGYEJverGIcaMY0Q\nVpkLIJetRd5Wcy2K3f7hDh82oh5QRBnp61iSG/O8gNUTxp7qzUO+UEEN7eRZ\nEiTFWz2ggGexLGN5GQDilHGjrzW+R4B5cEzPv3MPSq7dlCeVTdnnnt0TcFd7\nXx5IFkYPJRDnueQ0/uKRoGArr3CBa38xeEM6yqYSdEGoNyUDTMR7ZDa0Q6dF\n+2bfUf1c+SfwUq19JWtvE40LjtAjyBXayWYG9Odr4CDQSuqO7JjIxaeMRX5k\nGXkiK2HpNbJzO8qVkXEd30NdPx/eQpmpKOlObyO/FbRfUBhHJl1h0DzPYWs9\nEKtKzmHwzQKy0rgh5CDlnM992tGfEqRvenlpGVUHBk8r+UrYQOScDQ5It1Oi\nB77Pqu8c85Bj62YhgKiFLTvMt3rzCKjFC2PG4FAfm/iT/l3KHNXJu+OfMojO\nS/4JegiMLrT/gBmQUnAZ0hFBgj36dgeMnWlj1e+0jg4fkHr19uS51GJLGLzx\nKf5IEd1I+Ydtvn926iFPNEtj9Ptp76yTb+8r9GJ7qnhN7GJePL6ASwFvSZ7z\ng8uw7SozgDGLbEiPuW9Lg4IxcsL5miLylcuD3incfTe/vMtKt0k43gxzGKs7\nfExCvsEejlR08JNHR5BGL7noXnLDBu96T3UTO1vlcUF1yUN2qE/Isn4hiUB6\nwo1u\r\n=22Ib\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "7ca8a22b8453e95c63842ee6aa4d8d8d8b4f9612", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"jest-get-type": "^27.0.1", "pretty-format": "^27.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_27.0.2_1622290038482_0.2780832277576102", "host": "s3://npm-registry-packages"}}, "27.0.6": {"name": "jest-leak-detector", "version": "27.0.6", "license": "MIT", "_id": "jest-leak-detector@27.0.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "545854275f85450d4ef4b8fe305ca2a26450450f", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-27.0.6.tgz", "fileCount": 5, "integrity": "sha512-2/d6n2wlH5zEcdctX4zdbgX8oM61tb67PQt4Xh8JFAIy6LRKUnX528HulkaG6nD5qDl5vRV1NXejCe1XRCH5gQ==", "signatures": [{"sig": "MEYCIQDjsCDyI/3Hvgw2Gas0njL8dZKz2DK5maFj2HpuzdLebwIhAJi1OMqiTqozaEUejfmk8LEPwPz7VJQUeKP6NM1Fd5uv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5668, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg2gFmCRA9TVsSAnZWagAAFNcP/ijfoAzWmrzW5/gyVnh4\nRbeoYHpGivCxfYPEtAFgCkmaG8QM7AIBr5CxKTW2vIQCpe0FRXO1/tmDojXW\neiTYIruH0tzpOEwN39R644G9I8IIzCtBlXY7zJLMnwnJ8AB07kkMnYjLpPoO\nGneMZknOSBo9HXGzyKl40QOIu7SdAGCjVTAGaMqN6+fQrLdLcT7RHweSr1zV\nw0LuOsDUsb5/tHzNnl+dCDhgtqXGsvCtZQntE0qpMFoBVWq6u8vvro8zuFJo\nnTJB7pbUfcbiK6ZpHuoRS+4B8phfPZIqZ2AXgjWFR8Hq9rgGeLLYT18l0nSd\nMy5fljEiDrmPG1lxROwkCNtB8jvt0z1VSV20V/M89iZVNbdQQj8rrJu/xG9r\n9nh/1OiedN9VeNRHd7NfLNHBqrjrVJ1WXr6ZkxoK+lPPPYIhoBE8STbpvD+i\nH3nAbPekwZIoME8jA0yWsMKHP1ykgGqIdDMrEX7Af+X8rDLOXa1iTZVbSJrO\nfudpdwpoL7WCi6R+1YXgYQ/HXRlde3aQLJHk31QKJxbQVpv+BnPL5JLYq4/q\n0/6LLERUQP5z5AZCtYHN+pY8AbQzKOdhEptcmnDV1lbuZdH8E2rk0MmkoSxj\ngQHqFF2tO+ao4DdhT5LZQPaHe9wsMzKPvt91vjX7jmwwTo4FyXfrkZvSlHb/\nyIGI\r\n=kp29\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d257d1c44ba62079bd4307ae78ba226d47c56ac9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v14.17.1+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "14.17.1", "dependencies": {"jest-get-type": "^27.0.6", "pretty-format": "^27.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_27.0.6_1624899942374_0.658439947632641", "host": "s3://npm-registry-packages"}}, "27.1.0": {"name": "jest-leak-detector", "version": "27.1.0", "license": "MIT", "_id": "jest-leak-detector@27.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fe7eb633c851e06280ec4dd248067fe232c00a79", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-27.1.0.tgz", "fileCount": 5, "integrity": "sha512-oHvSkz1E80VyeTKBvZNnw576qU+cVqRXUD3/wKXh1zpaki47Qty2xeHg2HKie9Hqcd2l4XwircgNOWb/NiGqdA==", "signatures": [{"sig": "MEUCIQCoyt9I8W0dHfXZO7f7vNGCXpiJHKQ0tfLAYAP05Ru7WgIgV5QTTZlq74s8c7KEqdFZSV0F1O4/qHmFoDDA2JA14/I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5668, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhKLeMCRA9TVsSAnZWagAA5KcQAInC9FmGPRSkbQZT06uK\nT2ykdD3FdsQH6dmhsL0y1mhPC88ZU8yJkaqrE7H0s4lVLaLMuCEbwcl8P62P\nwbXyI7NfZQBw7o/DmpJg5aeSnVW8lhMATkOOIbAxyOhCvW8XF3gQCZRxf9/L\nIXwYgNXp4JPwSxkPCY99jkyNpNw8nczIYCErui5l5okkSjhpi0P8/F4N9MxR\n5+Dd24jO457woUJZioxgGYLJOiHYnvUkVKBGLn248xKd90vyrYys6UtCvMe5\nKZw4oB3m2VS7wHoVhHuE9JmAZ7oQ267fKTyqlTAL3rcMiIsV+NFyilbes4Ma\niAcMEfGd5kLQU8kdiICxcnMrtG/Z1eu56ZSaqfrQMJ69EbN26U2AIzrscKQy\nyrEFE+DF836Ucn2SqR0gHKPk8T+dyU8eivW/lZBug1/uOHioESBML/JqUwCS\n8UIJnyTfKp9BDM4WhvAjjpNbeFB+MzohZUb4m/hMxBHUkzj9ymof8zTHYIP6\nowPebelpQ4aMxsD1L287xn74zM2ACY/qbR6h2XHIMKW2Mx+0TXYrhpFs5ySF\nGxTWhPBiipKwUYajo4W1sAeG+xhxn6lsYlHb/YicLo5epNv0q2sKqHK1eENd\nq+8F19qFEITClsRb4Bgu3L2m+9213Nw9YpISGLapKVNmiN0/6vjC31MKqvUl\nW05b\r\n=c+9C\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "5ef792e957e83428d868a18618b8629e32719993", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"jest-get-type": "^27.0.6", "pretty-format": "^27.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_27.1.0_1630058380521_0.16750967650541826", "host": "s3://npm-registry-packages"}}, "27.1.1": {"name": "jest-leak-detector", "version": "27.1.1", "license": "MIT", "_id": "jest-leak-detector@27.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8e05ec4b339814fc4202f07d875da65189e3d7d4", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-27.1.1.tgz", "fileCount": 5, "integrity": "sha512-gwSgzmqShoeEsEVpgObymQPrM9P6557jt1EsFW5aCeJ46Cme0EdjYU7xr6llQZ5GpWDl56eOstUaPXiZOfiTKw==", "signatures": [{"sig": "MEUCIQCtnidg3JDo0O/x6uocsufDni/woPmjHb23UYg3UT0a3gIgYCE34LCTBn54555Xsf4jEhxCdVTfnHTfrqX1Td4FGr8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5668, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhOIx/CRA9TVsSAnZWagAAwOIP/1TQ1mdyBzVkblCViIEG\naEarmDJ6KuXvmlewfVJQch5xAabPBWrp1o5fQNFZWXzMAXZOHPZXCvER4/60\nDtTBmRKna36ndssCU/aDR0lAZm2Y0RbyinpFCp0VyR9vDepJTvRDfeg1vZJj\n88enugxzxs1CWsfIyaQrWq4fybxWsMG+HDXYZNNSx0UBQhgPP//iGVxQKkEA\nH9LMELV3QtImzrKzEGR0Mulpf1pD4piDCnIc4O9qvUrZsKknoV2S/04QDJ06\nwJ5D6IYEFzVbTbuJNPyLSuUGGYGgXTMfVKybzMoKbBrzKdAXlkYIQPGMLuEy\nVH9ducZAQg/5ebwEpubeeWF7w7mcQQy8zIeQTA2VWln0FV/6N+ipT+QfRtvl\nq4S43nU5hllt/eOPzi2nmTH4vWtO9/iQgeoMbDSjTI/aAgRsjDZTNSV2Tdd3\nj00HH2UW1kSC9CghAzVDLrWZ3DcqKtHQY7cW/GiYNR213tSoBCWPDLTlExZQ\np2NfKPPOxXC2YJrCKlOErD3SSK3TlCWuw1g+RLtJXRkqjc4o1ZYZl2vBqd7Y\nzrT2yswzBgrHqXT3RQZp+ZstCwgj9CL4i3l9TScOlTTsvJI44rbz9M5SZD1J\njeJ66J9j8erXvwgiJyPz9z3/8t32hsqDjHkMxt/Mj8Zz+fbdHbfca9uFg43v\nOiNp\r\n=gFot\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "111198b62dbfc3a730f7b1693e311608e834fe1d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"jest-get-type": "^27.0.6", "pretty-format": "^27.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_27.1.1_1631095935451_0.36586777165116713", "host": "s3://npm-registry-packages"}}, "27.2.0": {"name": "jest-leak-detector", "version": "27.2.0", "license": "MIT", "_id": "jest-leak-detector@27.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9a7ca2dad1a21c4e49ad2a8ad7f1214ffdb86a28", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-27.2.0.tgz", "fileCount": 5, "integrity": "sha512-e91BIEmbZw5+MHkB4Hnrq7S86coTxUMCkz4n7DLmQYvl9pEKmRx9H/JFH87bBqbIU5B2Ju1soKxRWX6/eGFGpA==", "signatures": [{"sig": "MEUCICjk5+ETq+7FZX9jCvLbT+aYnZ6s5URUDhmb7/zzH7gWAiEA2MntcG+c8MxWa+Z1YN0b9W7ZzUwnH8+QevLmvNDkK1A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5668, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhPwaSCRA9TVsSAnZWagAAHkkP/2mU+Bi0CEbnxCLjPepY\ndatZgoFVQBgQIzazLRLO8AbPpGO95agBDMsHuS+Cro9JpgCiPliRn6CLkyfN\nqCIrhPTWQFB1fw35/gwlugomtab7ow49qqlQ3XlhLh3oW5DdxWfuga9JOi/l\ns/V4udUSlWiienotIVDc7oSqYXFl+w0H6UJjRIALqK2STNtY8LWKMYP2prSI\nR4lqeNCzQAuuHZoCZwf7Q+FyudyMngq+iBNLP2OU/p/6DScbPPLElAIvDa0P\nM4UoR/s2ohIApDoEsMSXIZZt+Vh2xcZzC6s1UOuyfcmH6RUBLsVRu7RfmaEL\nsdnd27fcYV0/u8B36lvDLp50oIpgaJECYCFBwoLQkniWsdR+ZOfj734b1uyL\nQDifdkhV4YkcITFO/MOqgxHVPdPIntRFAd7HfzBZgKGGjJTTj7TfT8GcaJ9e\nc66iJWH6pyIc3RU88OGW3wCTbLGb46QYtHNU3v8fdgEroRS1m0Lq+akVHcXD\nwa3gJwWqLws4slpcUpGrJ+UHgAu9iJGVYcBqfrSf4eP2EloR+0mE4aaE4PMn\nmD8bFzbvCOw6dGXMQ3gDR95OC5+gtzHn9mSGcIWjB8mVSvenTWVMxIJvJODF\nkfkYYfn6Gol32P8ZRhMeMEggfOReVf2Xg+0fzz/dYtUKukH8P5y4wBafwz0S\nA/tx\r\n=lMex\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "b05635c539f8f673dfed5bf05ea727a8d5d7bbe2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"jest-get-type": "^27.0.6", "pretty-format": "^27.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_27.2.0_1631520402016_0.669330555277609", "host": "s3://npm-registry-packages"}}, "27.2.2": {"name": "jest-leak-detector", "version": "27.2.2", "license": "MIT", "_id": "jest-leak-detector@27.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5af54273efcf5114ad406f4448860c2396319e12", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-27.2.2.tgz", "fileCount": 5, "integrity": "sha512-fQIYKkhXUs/4EpE4wO1AVsv7aNH3o0km1BGq3vxvSfYdwG9GLMf+b0z/ghLmBYNxb+tVpm/zv2caoKm3GfTazg==", "signatures": [{"sig": "MEYCIQDNtZQqEn0HnX+z2LSDc3gwWr028sEFowX9t+zUYyNoKgIhANP65lZoakA1XOQvRDL7bwgxoTHPVfG7PG3IE7xIats9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5668}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "f54d96fec55518640b900d6994b2c4153316d1ed", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"jest-get-type": "^27.0.6", "pretty-format": "^27.2.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_27.2.2_1632576908315_0.9244383911615486", "host": "s3://npm-registry-packages"}}, "27.2.3": {"name": "jest-leak-detector", "version": "27.2.3", "license": "MIT", "_id": "jest-leak-detector@27.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6c60a795fe9b07442c604140373571559d4d467d", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-27.2.3.tgz", "fileCount": 5, "integrity": "sha512-hoV8d7eJvayIaPrISBoLaMN0DE+GRSR2/vbAcOONffO+RYzbuW3klsOievx+pCShYKxSKlhxxO90zWice+LLew==", "signatures": [{"sig": "MEUCIQDyS/h4iKliYJ6mvnSeMgAKOW22Z/tw9S47gtzC87YICQIgPoiBkwq52/UdXQGk83YzJDaXUOkjNQuWyHZu19UHJn4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5668}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "ae53efe274dee5464d11f1b574d2d825685cd031", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"jest-get-type": "^27.0.6", "pretty-format": "^27.2.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_27.2.3_1632823882532_0.14914233673919663", "host": "s3://npm-registry-packages"}}, "27.2.4": {"name": "jest-leak-detector", "version": "27.2.4", "license": "MIT", "_id": "jest-leak-detector@27.2.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9bb7eab26a73bb280e9298be8d80f389288ec8f1", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-27.2.4.tgz", "fileCount": 5, "integrity": "sha512-SrcHWbe0EHg/bw2uBjVoHacTo5xosl068x2Q0aWsjr2yYuW2XwqrSkZV4lurUop0jhv1709ymG4or+8E4sH27Q==", "signatures": [{"sig": "MEYCIQCHCb9b+EOJMn06+LhFIWAKJoo+rSGCuvsOc7Cl5P7lDQIhANEgZHK0MM9X2hFHF44tPbknyLF4ZBuZ7dCwdaqOTdKY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5668}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "5886f6c4d681aa9fc9bfc2517efd2b7f6035a4cd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"jest-get-type": "^27.0.6", "pretty-format": "^27.2.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_27.2.4_1632924289929_0.7007365369230274", "host": "s3://npm-registry-packages"}}, "27.2.5": {"name": "jest-leak-detector", "version": "27.2.5", "license": "MIT", "_id": "jest-leak-detector@27.2.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e2edc3b37d38e8d9a527e10e456b403c3151b206", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-27.2.5.tgz", "fileCount": 5, "integrity": "sha512-HYsi3GUR72bYhOGB5C5saF9sPdxGzSjX7soSQS+BqDRysc7sPeBwPbhbuT8DnOpijnKjgwWQ8JqvbmReYnt3aQ==", "signatures": [{"sig": "MEUCIQCnGp91s2DNue3iX+pkgBW5BBCBBAscQThsNF98pLHfeAIgAbBeroG3SOht0uudag2ldBU0yXZfuvzCu7bl48I9SSk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5668}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "251b8014e8e3ac8da2fca88b5a1bc401f3b92326", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"jest-get-type": "^27.0.6", "pretty-format": "^27.2.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_27.2.5_1633700362170_0.6778463004991719", "host": "s3://npm-registry-packages"}}, "27.3.0": {"name": "jest-leak-detector", "version": "27.3.0", "license": "MIT", "_id": "jest-leak-detector@27.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2a881226a08068f6c2f3f238a65a788d4d3e787e", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-27.3.0.tgz", "fileCount": 5, "integrity": "sha512-xlCDZUaVVpCOAAiW7b8sgxIzTkEmpElwmWe9wVdU01WnFCvQ0aQiq2JTNbeCgalhjxJVeZlACRHIsLjWrmtlRA==", "signatures": [{"sig": "MEQCIFjiRRgRPrNnYm5S4JXUNp82uZdS3aZz/LlqoWrrWPaLAiBUSFIsfm25Li8p+nigb5rxb/1MFXlvyn56m0DKWiEIPQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5668}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "14b0c2c1d6f81b64adf8b827649ece80a4448cfc", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"jest-get-type": "^27.0.6", "pretty-format": "^27.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_27.3.0_1634495687107_0.5501133048460964", "host": "s3://npm-registry-packages"}}, "27.3.1": {"name": "jest-leak-detector", "version": "27.3.1", "license": "MIT", "_id": "jest-leak-detector@27.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7fb632c2992ef707a1e73286e1e704f9cc1772b2", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-27.3.1.tgz", "fileCount": 5, "integrity": "sha512-78QstU9tXbaHzwlRlKmTpjP9k4Pvre5l0r8Spo4SbFFVy/4Abg9I6ZjHwjg2QyKEAMg020XcjP+UgLZIY50yEg==", "signatures": [{"sig": "MEQCIG6C7GCSgYiW9BazwO3XnCE1jJZaJJxFd15S/QY/2XRaAiBMv2DSUWa2bibWv0NY0HiKopWEcAYXwewxjQcpNyhPaQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5668}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "4f3328f3227aa0668486f819b3353af5b6cc797b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"jest-get-type": "^27.3.1", "pretty-format": "^27.3.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_27.3.1_1634626653601_0.2634762492497389", "host": "s3://npm-registry-packages"}}, "27.4.0": {"name": "jest-leak-detector", "version": "27.4.0", "license": "MIT", "_id": "jest-leak-detector@27.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f1379e537a8e6c87e7775f6e4d2b7052f55385f2", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-27.4.0.tgz", "fileCount": 5, "integrity": "sha512-d7QeqzIOVQeMI6VROLPNeYagcxPCvqYD6A34Ol9D+vPzs72omGXsGbuuJrChD51zuA4ESXcLYZ81L9JHr1VYGw==", "signatures": [{"sig": "MEUCIDX41FkLBEhJx4mG58XLzu/2O2KkDpWnyfdYJXVZNU18AiEAgUPYT5+rdYGeS9v8x7eEIcM9wBhDIAYcdABdkXLt5AY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5730, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpNeCCRA9TVsSAnZWagAAXgoP/AtS8MJBpfDn7fo1Depx\nc4ADWeDhY4pNHXXl2b9DHVod7UjrVf0H/LojZCtdTfwsYej8JHGO2n55PUif\nnxLNqcXVFNbOVHcx7hbEroW1i8m8STB2QoI2wiXrZRLHnmct0E/VqBNdwF0D\nBg+Afn6Tb3w0ethp5w2kc+Nlsw4YpCZ6Wmsp8ex41fzy2JWuIpeqboje+ZxL\nsNviR238/gAuFa4ydCBgevk7DMQu0Fdv7r9Kf6Ludhg58FQElfIntSZtuoWs\nT4/VgtcgoUJNevU5CA8TXq/4SMJK1ZJgFImE2imPC3Js/XL81kjMkgUXVZGq\nXDBX21U1YcLNwRsp/8vdKQtOM36wqzK73GG1d/OpsQnLQZIg3Hzjmre7nfiu\naQwDhdFI8Mj6xP8f+zSgGqquQFHvs+58t5Jh/D4yQugXfmheZb8qX983eSd2\nAKYs3Tu9Flys/B156uogpqoL83NPlK5ED974rAmGx0mfsZ/t8ZhBmRnd0mmD\n30mhbsGnAXg1mOcsU49gs6a/Cbxkd4cJ11qkTwr8M7k6h8H0UMvc3T5BsHtS\nCdkFuud0KCc7kruGqp5AC8iU+e5iJ+0mKfcW/je12nxp0K8/0oOLN3yoPlX3\n7zkWtuyoHX5AuLbMTtW+F+iRaEsKzb3YM19M2fjpIcF4wfD79jIluvPlYhgk\n5Ti9\r\n=JZIw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0dc6dde296550370ade2574d6665748fed37f9c9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"jest-get-type": "^27.4.0", "pretty-format": "^27.4.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_27.4.0_1638193026283_0.33211183467656125", "host": "s3://npm-registry-packages"}}, "27.4.1": {"name": "jest-leak-detector", "version": "27.4.1", "license": "MIT", "_id": "jest-leak-detector@27.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fea281b9300e3558a5edf9aa9c07368c9de1291d", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-27.4.1.tgz", "fileCount": 5, "integrity": "sha512-jiCXCUAetTgOIXM/996b/NzVHEH6+pnpcOBqzyvZypOKBetcK+JlZya9Az15zhACKZ8jcp1jAuPnQHsqQ0LMtQ==", "signatures": [{"sig": "MEUCIDEU6BMm+NF9zmSoveFv/ax1/6gumUL9BVKDShcIySfDAiEAt4/cxUG1xfEMQeCJev151iBTNiY2j73Tme5jcFq/0S8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5730, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpeK2CRA9TVsSAnZWagAA85MP/1AeczSGIESIvTw33kaH\n1S6UTPdncCVCJ7hoQQpCMiqlnRaW03Iq9+nXeaw8e8X7NqQzVVITL2p/zCNq\nMenV5z2fBN2BK04DjExHV/OHuubFxwqHv9gyOpwb+F8nw4jRgtzVe2JkdEJ9\nDukLcE//zgqg5Vzawigu7Xwbl+6U4xosEKZnEi0uZkyiuadA8CYCLEwAWh7o\nXppyp5TagXF745/NN23w2ywLEpN0OaYo2DQcWp4j19RzoHhmeUDqJVcRML40\n2P3+cTgEuE3d/42vLQQdd/IbqCbqS18oDk/9QlwoxzH44xBPUlnu9ZfHvcDg\nGCz8h6d3tIacJvVMK567EdBGlDgKEXcXeaaaBY47fDJ4Dmph8l/kQUu3z3J4\nahioYjYhQfV9pA5rfFwyGFtRzhgmG/XKGdhkEuKFYRJBJmqmUi0Z5DGkT2D5\nNWHvrlLnTi78ggoDOxpPPS5hsK3knd5emQ5mHrYV7axiYuXG9pWF8l7S+S96\nt/5kHAyqt7R7Yr6Y6DkyJ1RsNgvfJz9VeCZaOQRIBEiadJyUKnTqwXeaiVl1\nGjxjU/9uHsWqyHq4hhcGt/JlsWy4kDVzCwEPtxRhZEc++oaXvVODof/tmswO\na6lZsQlh1oIndeFkm8NWZDEgxSM/rIswxUoFExxffTxtOEuStyD/1PtNyYLW\nlNVQ\r\n=Gw+f\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fa4a3982766b107ff604ba54081d9e4378f318a9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"jest-get-type": "^27.4.0", "pretty-format": "^27.4.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_27.4.1_1638261430420_0.08899788959877508", "host": "s3://npm-registry-packages"}}, "27.4.2": {"name": "jest-leak-detector", "version": "27.4.2", "license": "MIT", "_id": "jest-leak-detector@27.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7fc3120893a7a911c553f3f2bdff9faa4454abbb", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-27.4.2.tgz", "fileCount": 5, "integrity": "sha512-ml0KvFYZllzPBJWDei3mDzUhyp/M4ubKebX++fPaudpe8OsxUE+m+P6ciVLboQsrzOCWDjE20/eXew9QMx/VGw==", "signatures": [{"sig": "MEYCIQDNYEGV0ricQVmlQH99rU/3vvSf2WtO2jbWcaq5iHWkogIhAOp13NKgJAIKr8pnyeGXz5uxS5MDJ74djCl2dEe+8FEM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5730, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhphDGCRA9TVsSAnZWagAAlY0P/AsI+yuu9zU+8OaYryBF\n4DBAd2xY5jf6RUl3HSZK+Kesazp8pWBd2IQ/jKSHYlI8Gil0T6XDuFxvUFVJ\npNvSQfLyPKJfLVQuURQxOSxvOioTYmfyP2LvOqybiACHyF9YuSZqlQs2idbh\nv1kp9+jSDuoq42x90zxsI+APNUfqYVWJe59ZF//+LgfDAxX4JjT16JMqVSBf\n9OpiSwX6TQSSwOfQPPMFg9IgowxOf6jqLudJN/2sWHGu999rEo9ZSsSECPAI\nk0o9FgQD7owXQBvn5TwNiNod5RfOftwNNFJq4ApRszHVvMGKxPoNxo9DZqVq\nskuJU1fZa6xLXWf1oh5wtiBn5rSvUQKtkmw26ZqWhKhlCnwPl5CmYQnlBDv7\nDzkGvVS0QMBK23fpIjKa1ITWG8IYaRsjXLIhNFksRi9MBNK74OAdyvOnWIcg\neSX6faahmm79D1HGu8P1OZAdzqRuR1SmORFAmphmG/YPfKxJoNP8zu7DHGl4\nP+igeM0sUqUPYAu4JVToVwkmZ0HA06QiVNqEOvJ/qU/WTQzylyx7dHtj9jOg\nZjQ/A1i33+SPzkrWfsIpjsm7TYrnMET3KJpEbPd2JWaZPJsnHqiC/mRaHxFB\nsYrHR7c1dIFw+/E826Q7Igy+Cs5vGznRZvnXaeulWp2ZPSJN1z81o4/ZH2ZI\npASQ\r\n=FvWa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "7965591f785e936ada194f9d58f852735b50ab1c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"jest-get-type": "^27.4.0", "pretty-format": "^27.4.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_27.4.2_1638273222548_0.3249475975112144", "host": "s3://npm-registry-packages"}}, "27.4.6": {"name": "jest-leak-detector", "version": "27.4.6", "license": "MIT", "_id": "jest-leak-detector@27.4.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ed9bc3ce514b4c582637088d9faf58a33bd59bf4", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-27.4.6.tgz", "fileCount": 5, "integrity": "sha512-kkaGixDf9R7CjHm2pOzfTxZTQQQ2gHTIWKY/JZSiYTc90bZp8kSZnUMS3uLAfwTZwc0tcMRoEX74e14LG1WapA==", "signatures": [{"sig": "MEUCIQDuIArQjBrF8gpjyMbmm3rW1qptFJIUZSSsYicojwCj2wIgWayNjH/VkJKoxyypVVoSkuOC+qmVPMkRqiIlnw5Llxo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5730, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh1NJHCRA9TVsSAnZWagAA/88P+QAGWZ5HHt5rLwUe7Ejf\nPvDteG6so0ubLZraF6gnxKfSyyE5O+OgWBT9JGj3JOJ2NEXBc+KP2ypacXam\nPBVgp6ml9Tsu2IcH1v3uqHG4I8syCfZwMYw1TMtoYT/O3a/hs69MYR4RrIBG\nrOy4gqG8BllayRNueJinkYKTYLh4YlS1/e5iaX9qPYlS8wduDvr9eg2t/oB+\nWV3XBhRBRnEEFtSAwgPe1PeNI1g8iL0A2qzocKyxaRzs8VaGuyYW3M7YWTal\n/MpBPsPiNH/fP/3ICp18Szlm19+Uu17he8QfnqtHDEjGYbi12GomSM6lchNk\nM5yECNCABg0DcStSBnoMBfLAc1QGxoUrL5gA2jZRmOgQ6fgM5Eq8ES7Rhw0e\nifnkaduIW9fKyyG6Zx1qDZg5XcsRQK7aebveIv5YckQkkw/1s2Bq7thmwm2m\nNmq2aI0O+P2e6/i+Lr3Hl/VWSEPT3Cx84uqsiq0yJlewciS0L73UgyfbbSsR\nlkuJUCMFB/ZzBtkVnjY3+CxMqEH2H5AWOo1FV/96A7SMabzXMxjZraC6q1vb\nUnOA95a/52lvQH/gWY20+UfBak/pfdBmZyRbA28CKoXVe4SJ6/zE04y/3Oau\njDPfrCHR/FHE7G0Axu0Sndlx8FAVV+A5QE+zYv9V/A15q5erTNaL0HHP2IT7\nZ0G1\r\n=0Loy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "644d2d3e53536b0d67e395c0f35f8555a67beb1e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"jest-get-type": "^27.4.0", "pretty-format": "^27.4.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_27.4.6_1641337415543_0.9663330274793114", "host": "s3://npm-registry-packages"}}, "27.5.0": {"name": "jest-leak-detector", "version": "27.5.0", "license": "MIT", "_id": "jest-leak-detector@27.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c98c02e64eab4da9a8b91f058d2b7473272272ee", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-27.5.0.tgz", "fileCount": 5, "integrity": "sha512-Ak3k+DD3ao5d4/zzJrxAQ5UV5wiCrp47jH94ZD4/vXSzQgE6WBVDfg83VtculLILO7Y6/Q/7yzKSrtN9Na8luA==", "signatures": [{"sig": "MEUCIQC2i/n+gA4uoPFnmjoPFVHeVtip3d5LP520wKVaC9yfQwIgPJMKK6VCOFtRB/kiWikTell4jb/ac/CCPfYJtZni2zk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5730, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/kp6CRA9TVsSAnZWagAAMiIP/jvbQVp38JXDmMHI3Cnz\n7mvqnS/LeZVPMf2LdU1cUZziHO2obbmgwRTp2jGc3QLNXO3GMBYLS9aW0YJB\n4W8nGSA5b97MSbNR4nkDX3aAnPjp6Gywg7bnLR1qQNHukV9QfpANdb3IdfRM\n8n8mo9ercw03G4wzvQQMO3WHVAGKT4MLk9SzG6XTNirkTWKEeKI8XsWvOhsR\n6Hy0jq0wecA79AOV3isFCkcbthuQl+Gn0ZJvNFcOZ2MfDS27PJ/5W9vjiW/G\nlQz3c0AHh+1hAwHzm6RFqZ3Yqj4KpQNo8hrH1WjntY/L0dzfDsxqxVVS2mNv\nKxXDorkTCyUX/w/JeSFoBLzEik63A84XEElP2i9K9YoI8A26Z0xNH2efNm0X\nigklrMFNOv880BHiYCSk7o9wt7zcalnIlaNakvK0TA9GqTeEQ9Dm3kJDOEvc\nJ/hPIcjMI+v8OpEDlbFp+E4rN1JbVK2ZF7xw+bctMnbvsMiCIwkp9qoGkv/+\nacdBJQEjWlVHQ0O8mosENLR6F1381X/KZdEDHR4dPW87542vDXEySdZUCyZ4\npn0tnDLx24ZFzYlKpNd9TPv4DSKNqSJu7QXjbn0OrGfIzf+VIo/4sQgZHLl4\nv99NlMjtPBv0j4h5Kwh/TqDTeCCHozabgzepNQ+Z0wZVs5A+1/3a7/eUtjD3\nfQoG\r\n=kyb0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "247cbe6026a590deaf0d23edecc7b2779a4aace9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"jest-get-type": "^27.5.0", "pretty-format": "^27.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_27.5.0_1644055162035_0.22460884036512674", "host": "s3://npm-registry-packages"}}, "27.5.1": {"name": "jest-leak-detector", "version": "27.5.1", "license": "MIT", "_id": "jest-leak-detector@27.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6ec9d54c3579dd6e3e66d70e3498adf80fde3fb8", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-27.5.1.tgz", "fileCount": 5, "integrity": "sha512-POXfWAMvfU6WMUXftV4HolnJfnPOGEu10fscNCA76KBpRRhcMN2c8d3iT2pxQS3HLbA+5X4sOUPzYO2NUyIlHQ==", "signatures": [{"sig": "MEYCIQDYxu4LkjuVa0OS9QmBmIejcUevE/4f8Ya+0NSVwhKqUAIhAOqsi2fAf6DCuVFj8jZtih2dA8OE+cbgs1EQj323ylli", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5751, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAktiCRA9TVsSAnZWagAAdHQQAIKZ2ZjuCUeKB1fuFeBI\nKVd+QfsbStG1NSuTw6Az9SXLBBDAbeZduzunxnvtJhaxXXPRaDQzQkoWHOnX\nLL3nnZARvf3KTbD9DcsrYiqG2bvtdhUR4olYs+sN+rPgFFWD4Mr8ohsRcGgB\n0onWllWhjA6mxxfr684s6XxG+rMemBKG31jerfRkVQdKoO4RUsCulftwOqG+\nyS5SAM80i25u3vxnKByPVKvqzFh4WUQdf8rmgIc32C8H+VugAvO1d9UNn52e\n73hriNZVNZYuBmfSFrLNrLEhCm+azk55jDb/9KuiwbsVwcd8icRCNniHxH/4\nMYK43FlrOmMzRgkGnC4Jl9EU71uveqVggcKAoOWjf2+sY/i4NnqYVXrd/R5I\nU7nqeRspalm7XcPK6P9BYpocQZljMjp0fL9u3yUIXTgXwDXzmkotBXQzPUc2\nAX6/0Bsfchgq+wFyFEkPln4HC6bphSXDYbuhBwOX8ZKItxRry1suXqF0gCUE\nvHDU7EfOl/RAEubgNVhplFiiNe0q/NHNDmQCG8NAvoB6xPBO/Y9kM7MrUb7J\neDeYfmsGXXkeTfZbdbwFQd6vI+lK5w2erMQ8LTfdHaOT2fYGgOQ8W17bjz1o\nzf2LFpB5Ge6ij9JIIDDq9vZorZsdpTcZj4WTDUpbKAvH/ReVRcNsyF721f2S\nGUrS\r\n=3QvT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "67c1aa20c5fec31366d733e901fee2b981cb1850", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"jest-get-type": "^27.5.1", "pretty-format": "^27.5.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_27.5.1_1644317538299_0.14022434165941355", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.0": {"name": "jest-leak-detector", "version": "28.0.0-alpha.0", "license": "MIT", "_id": "jest-leak-detector@28.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ce37cdcb4bae68355bc5af4dfd14e8db3fc316dc", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-28.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-yZ1cuMYTW7MJonxoyq9ekWBS3oqP+SJyC4aAOcU2yLDiCWuufkFH+kq5R/NDlQUU+BT7e0npCEHNdV5iSOnVsg==", "signatures": [{"sig": "MEUCIQDoN8yEQ3byQFKhTSKQqOVCJ2Sy1PISR1Eh1WPu3nOkVwIgZ9ygQR+pNyexPzgU45buYlw4HeOhKTzsw2+O1uCF+jw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5722, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBVa+CRA9TVsSAnZWagAAv0EP/AnbpAcgx1gaHY+EcGBK\nilXsZTQP8fGHFOUPFFST0Z0DKm0r0CJJe60ZiXx4yrmTh9Fh6Xdg4MXkgpXg\nfJ+athpJGcWZyyvqE8qwvLAFx3juazeIp3SAzOsYjmHcL1AAWOquvbYk93ad\nte3I1i9pOyQ7ea2zGvsOP7clhuvhg+VtFX33Iqhv0fupHcAlSotjuZeTfj9r\nSzX91+9x9ZAEboMvolBeFHFHTatJ1LFy7JMzCEcmJnqmBek4dMQl8GJAqHiL\nj9nlUhrJ/vB3Fb9beXxIgQp7TSX+b+F2Cdrk4BgxI1dD/YgjDQz7xDJzsbN3\ncHB4KkKlooqNJmE+AH6lkROcEjYo8TEzqv+VwgihK5T7rUTlK3nJol+kA1Eg\nHEW/mkIbLh9DRMNeDBjkLbZJHuom4OdaweJsRvIGLl1nTL958bKWCKw6AdDc\nw6I8B/uS1mLIQUo7hRUEd71qA1NYzQBb9fcz5dv3LPQQm0+UDRnjtKHmkstQ\n2RLWmH6VG4OUT4f6ltFdPteCpAjTUBQzeYDENL9/Cj2SIYZVCdktFZDLPqh/\nf7mKnG2OFGcLlpMuPYIA4A6xDXtwGiEv9NFWF2QhL2mFxTkfkXxUL7uVV/ve\n2NaHGWz7wd1IGRpAlwnGWes72XqQNKGBtZYvvanjd6UsK6oQxpXIPkmk1sQf\neE9o\r\n=V+np\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "89275b08977065d98e42ad71fcf223f4ad169f09", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"jest-get-type": "^28.0.0-alpha.0", "pretty-format": "^28.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_28.0.0-alpha.0_1644517053870_0.39726898140809186", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.1": {"name": "jest-leak-detector", "version": "28.0.0-alpha.1", "license": "MIT", "_id": "jest-leak-detector@28.0.0-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b2f2992d67d9ad57d16fff77e6c3a62f8cc063c7", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-28.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-Z6QavmZ3UiCoP8wpWkFY5N+7XCZ2nfXZxVIcIWezoV2eHRvwJ73tZCh2tDne0fIwW7rxcg56K6SSsBMknXGrpw==", "signatures": [{"sig": "MEUCIFctJ7kYW4TtlDYxxdxWfUvuo5UKaM87y2tkODa39pLYAiEApglFaiFI+Xl9iRObxq3Kh6BdJjH9b3Qw9zdLA5saVHs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5722, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBqiCRA9TVsSAnZWagAA8tIP/AiAW1+igTDxbL5Z5b3S\n1+7EDGrPBRo2RVSZp5dAWoS/W8LV7PlcDlVl/OD6Muk2fQCANI6+MWOa07v8\n17QsvQYv++g/GdQPb9AKKz5iVYu4VOB24YvY/TEu7YKJHoTCZx90VkTApbPS\n+i4k0VtdhFIBw5Bzs681nLg/g4hhqFtrn7MjDX1LgXWq7V0FB7ru9qfZ578A\n8Td3lUSnCiOjtAnhstcJGI6ws0SRUWN3yAaWyBrPqx53mw4NEVuP71nxqak8\nkbHDuPWENU7MmJNUy9LRy+1TiG4DFVXPennV+3nKd6KCkXDTaLkzVAPXAheh\nq2VgSfVH0NuXGC34zSj4iAB8zc8r8FJnFnPVCz2W2umNSxqqIx0lPzd2c+vD\nJWUxMdBS91mH/jMpBYTSRDRMxlispzdJxgJ3kUaggkiPMINLF4RsQdB880Gm\nJttFZ38ccyCI9297gyATDDgqQ2sTScvkmp7WsyJwrhO2WBLUfo3wa5JNuOsA\n/ljZ9mdcGYVSFeun60/njQqcVVN4CoCbEza/HoHuYQU43lCdKGDBgr0T7ylZ\n3wS2Gdcy1D+AvnQcthTwNE8/CdldLUNZUMp2kga2jTVhpdDfD64KmpXXJ5Yy\n7NP41JGOwdxFkDPScm33Cl35ipweY6FVaPINVzDJo++st4arwomA0H3Sr2UW\nKOcT\r\n=Je5p\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d30164dde1847166fa0faec98d20abffd85e6ffd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"jest-get-type": "^28.0.0-alpha.0", "pretty-format": "^28.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_28.0.0-alpha.1_1644960418029_0.27208779250040216", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.2": {"name": "jest-leak-detector", "version": "28.0.0-alpha.2", "license": "MIT", "_id": "jest-leak-detector@28.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c43ea2e800261491fc44e206caaaed4cc326ad8b", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-28.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-SqTAONuxXOCf/cj05mn7tjwo65MlC/elhtguHGRf0/Iml1fe0Y5+nyGJS50XLupgU0JOghXDhtDOplKt7OoUyw==", "signatures": [{"sig": "MEUCIACFwlp7Io2Bkjx2aJUhxA2yGFuiat4OidxDYm2B3mwtAiEA4mHuimmXt5q0zixHDPUAnDMQEl8R9PgSOA89iy3qYJg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5722, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDT5zCRA9TVsSAnZWagAAPWUP/RmyWgyIimd1RTjQWtf3\nzgprLDKcNaHkKORVmyY/RfulKA6P3Oi/7l9/pr2aLAlf3+aInahep3mX8Urn\naEzbb3lmCSrscZN/BYSM4pVLj7mRedFBw1RC5+KJYwUx7g9VKtbHaC9jDO5s\nih9hR8scM5gDiUPKHNlTqIwO4UQUk5aBtMK8xL5c7S2sQDZE0ZG965cTgsyV\nZ796aPyj4VJZ8L3y0+BpZl7alX42C3g4/W2j1WiUQl6BqRWNWOLFCcTMpVEJ\nzvgUs2WO5D3tdlCgnJRUCyDBknEYX7cISxMHXBQY8IVEXjLcA+kQzQ06ps7s\nffJWl8ID+XW0T3teWBIYLF3cQDXeXgwqH/5eT4CTxJGgv2dslfqkJRhxIDYo\ndGo1Sgbr1zcxyOplu8Gh4vhjag5iXtMYHYMMj3ej4SLkMqmabuRq8nyww0dj\nwjpaAMK/BiFx00BCqfC3+Uhzc5pdaz6iWSYiskf5PJErpR6A/5k4i4mUtcTn\n9RBnyfO94h/0qNhkl1yXtJy9W9fGhZ4W748Ll8hsGg2wyFpsQtXzWdpTEWbK\nPoTwkGRW1cjx5Atm2xtlLOmVhM6QEKBpIw6r1x+dSPvlVJ6BLTStzV76YCTW\n7axkiS8/Offy52zCbl9da2nWONzgvekaRbUHSSb7n7wkJfjOZOYC2iw2HmsP\nG08p\r\n=qnzE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "694d6bfea56f9cb49d0c7309cdbfff032da198c2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"jest-get-type": "^28.0.0-alpha.0", "pretty-format": "^28.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_28.0.0-alpha.2_1645035123464_0.03673521839617688", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.3": {"name": "jest-leak-detector", "version": "28.0.0-alpha.3", "license": "MIT", "_id": "jest-leak-detector@28.0.0-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4584559a6cfe14f0538e594dbbb958e5196110bd", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-28.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-eJhD6nugpSYQolmT3+fC9dzFfd6k+WHc5IDfSITde/RkUTuuauyfr40UJYWI6kxYnFWJ6VucyM9gsbVZZK/TrA==", "signatures": [{"sig": "MEQCIHGNcmKREXEYIBOQ1YgoCeORuJtuLeFQDBde1qYgPngBAiAFRCF+r6gWFsA5CpX2fMBYqehp8ezJ4Lax+Y6i/xo7kw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5722, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDmzeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJag/8DH1TQ5cb4eJhJDqx6ouGptC2tJ4xZGFgGyKcRqGrVCh/FUlr\r\nnCudP0M/jNtA94mZfzsWt9q+PWVuKFrnrOlAvajI1wlHuYsABoBoFF0JKvxq\r\n0xvBfwWpFaUDlShWP+aQSIhk98UvkRov1MIsc3cB5s1ZX4oU5317mBAkyWiB\r\n6qPVRpxPwkHOX1rwS7jiAqcyfAYaq+UwFquK++fk6d6iqgN/9KhTXb63jNjv\r\n1c8xrmZueDk8zJ1GqlM2D6uXdeq8NP6YDX8mQKJFE8o+vcDdQ1dWhLcs5GAw\r\nR+4edaUWQ9+MkSRGZqbNMdAn+9IWMRhtvvlIKbc/jkzuP0dJTt0UPQ9eOFLT\r\np7HJ2xXF7VG3zQ060ofUwuOMfkNlLAM12mWLRG385ISY3Dz87+CjqGS4HGaR\r\nlfvw5AKY6YZa6scoouDW19R33UmAkhgLQLw3mjwmfhHUB9amhUuNNxTSzxeW\r\nsxHIJKh9fswoX6RcVL6i04+uiPnypldUIKuO48m7FIgGp6WQdUuiXdmKRubf\r\nsc2xxC0DS4l/redasUPL3KKsBqKoLmYCxXAJzZNhglVBFIcVbQp9UNhN0U+G\r\nPldrJIyvC3nTj5o+OLlCU+m+G6/v4BGYfinyo/mOiee+z198a9Eg43DmTlm3\r\nE7usCPq2/KWCFSdMgFOKLynuNM5zsdgRr/g=\r\n=Cgjj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fc30b27bd94bb7ebeaadc72626ebbdba535150d2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"jest-get-type": "^28.0.0-alpha.3", "pretty-format": "^28.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_28.0.0-alpha.3_1645112542487_0.6574124498844902", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.4": {"name": "jest-leak-detector", "version": "28.0.0-alpha.4", "license": "MIT", "_id": "jest-leak-detector@28.0.0-alpha.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "22a7e63620aecb93be99ffa501c2c8d7d6675803", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-28.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-TynHEmI3q2bzwzp8mFHqG+0DgemSUJ+apU1mrWDB06huGa6GfTaLi15JNVJ23ixEfQVZ2t19/T6QX+1TFNHzAg==", "signatures": [{"sig": "MEUCIQC+5bEGDNj/2lzuTYCHWPrA4F/3E58CUlRKCGM0Ic8DOQIgZ+JgREaMa3AcqLSgk0JF2ZJ/Y9xbQ0M/3PVcnPhrpo0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5757, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFNODACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmprOxAAnRDBfF93NJuOqncEP1OGcHwIa4gfkQOgPkkDEghZdCmHnWPa\r\nxbYYCIACzgbQrlCX38OPAz/vp1oEPzrN1cmLQD8JFIP5/AyILVg7QdE0lRWq\r\neM97KVCRQGRxUBEnmuyHT0dQ3GgXS+7kIz5NM1vc1+yfQt0RnUNJAQF+iQNr\r\n3g5Owu13TGAs6BJidmhLDrKrF1gPuxB6MvTmBjRMNCBrUztNac0RhfI8GIde\r\nCKXhYbMQeBY2XCUS5rZf5dPBzaxgPoC9i3041JJd4Tn4YNM3yseTFSghVWO9\r\noBSWSz5FdrvG9SbAAGnXBUHvbeuPt+HOi9zBNTnHcjolhc5jfD2gaRxu7p21\r\nQePSTjaWM5AKPOrYQ32RvW6Je7wzbRmwKO1O7bcnC0DOR03rQJUSxgK4RKTC\r\nrh1BeVEVDZRXIvcla1oIyIebKyOv1sb+Zw3jFheq18Dwqh9FDZP29+iiYyUc\r\nRNF//VyWS0aPWd5vtj+dJUUKPzLE/TqvrVZL+tkfXkORM+hwfAjnFKua544O\r\njx/mGUcyJ+ZdwANMz91VvALji9mD4Tgqe41P6qPiZkrAoAGvzebV/V4WjDHz\r\nq4jSCDvs+c4KpLDngeezyJyyn7vR92+rvh10LOcDzO/K6NBuN9ujjcPcu3VW\r\n9vGnybnjDqtLadP2hOEBVLMY0Z8WqABMmtc=\r\n=4yZA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c13dab19491ba6b57c2d703e7d7c4b20189e1e17", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"jest-get-type": "^28.0.0-alpha.3", "pretty-format": "^28.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_28.0.0-alpha.4_1645532035800_0.07695938172835204", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.5": {"name": "jest-leak-detector", "version": "28.0.0-alpha.5", "license": "MIT", "_id": "jest-leak-detector@28.0.0-alpha.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ad5020d2a3d32a3d2bf7c079a57bf2f0a8769995", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-28.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-5elMSzI2GDHhsCV7Jf39uBlK9wv//NJd3bouzZobwlH+EQ4MiPxPCqczUq4O9nIYI/uBisc6CLmMzW1c9NstuQ==", "signatures": [{"sig": "MEYCIQD2Z0Y9otbe/aZQhKOeHW3Cc24xRSmbeVVhzWsOwrTQIAIhAJNX6MOvt1hJmzUyNTepx2UOz01fixGGpEe1Cl0EkBSc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5757, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF/EvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq+GQ/9E36cEUGqlSDOpxGf/i1LbFZBFYjqeaKQkj2FZxDV9XHmbznB\r\n8nmH1UT47tLUFtjf1th41uxGoIQti/dI8Z8/gb+zSLqqmTvHVm5aQMTkRB6w\r\nWL+uZWk1rO7Y7FqP6zX1j9rxx33808bs+RoLIFRkx8f3dbBaoZR2kyG4cUiD\r\nao8wEKxVsLvZHKaMBog082ZFObo2x3Dw3UqaHoIAfu1dGv1YtawVznA3rcN/\r\nx1w8IMqnRMqCIJb7vAvD/aNx1QogHBDHWqcI+NB1JAJRwPhd+G6Sp1bVFPML\r\nT3lacXtOPqODHnuWT1tvYljtzXuGJZ48DMF7Hz/FheWZOSN8V+nWhQxjFPi4\r\nAKVzkmlXov4mBITi4Kj/AHKTmsuF03mv8tznVji+3mY1kO9ZSqXHsJqgGVkZ\r\ncbNiHNL1St12YLxLhuqBSamB6J3uyW/vlSLORwi/BYyuHAJJqFKnKYrOvTTb\r\nUFfJ4kSzJqeHZThIsAmwQCMLh0MkfN3dxj4ike8V+tDi24cQ3kq/uqsDwXip\r\nxzozN2elhuTmAKB5d0BZqzn+vhY/4Ge1kVaWETLJXIdsaC2rF/A8YnP6q8vA\r\niLWPCF4QNF0RAMX94P4Joz3HdICjV/jlqD97EX6pbGpD4dNDCuw9rEb5d9ZB\r\nn5ZyyCnHU0cWPC7o4GRYY7S6AnBoWMVIIsU=\r\n=i+1Y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "46fb19b2628bd87676c10730ba19592c30b05478", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"jest-get-type": "^28.0.0-alpha.3", "pretty-format": "^28.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_28.0.0-alpha.5_1645736239205_0.11936722181771109", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.6": {"name": "jest-leak-detector", "version": "28.0.0-alpha.6", "license": "MIT", "_id": "jest-leak-detector@28.0.0-alpha.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1d6f578683d961fc2ef7e4748e6649fcf4abbe58", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-28.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-1FalV/xRfoCd8vwzkpO8c4eM14BPQoSB86zdUWawzsY1fsWOj5H+zhyBgrmbvceYnH/reg8mIJiqfosIqZN86g==", "signatures": [{"sig": "MEUCIF+ZD4rK3uukCNZBTxtJSOxvElOjPXWzo+HnOdzj2zOfAiEA2w+tw2hcq3EhtGeDQsvXmnzRnQiNyGOPwjGunkgZAIw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5723, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHdoYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmraaQ/6A3Sylk3tCObxcBh9wHfMtTpsVD3tE+0Nw6zER2JXWujDQUvx\r\n+kayJ2YBeAjjfWQT38sDr1B1CSNzTRCf98ll/M3HyVitDrUoK7ApRhCco/C3\r\nGhyB6yBQ0hdY+MoNVl1HnD+fK96TbSC5KxG8EO1GoZxclgtj7yByfIjdYI0P\r\nj/ZSNBEDWBKU/JA0GEntxSi/V2k68GMMHDMTT/VOrra51qho60v0I7hto5WO\r\nMAipdOT095P8E4iZXyAqzJZGN3YSs5wMzHWLPKy35p6EI+lQD93n23Wqenqz\r\ntdUqhNG6QjDGq+RbQmIoI4NorvhN+72afOPaOfsQf2ZzWz207tyUeLu5o8wd\r\n7KH0kw6a6gcij78zTqcCdQI43Yxs9ppxMPG+LewbvLTv5tzeS2SImQeIiw13\r\n49rLXG1YtV47nUoCdIKQG09dqIgkuYWptV87G2sii5tHXmibBf+xWXPjQIq8\r\ncOS5Sx6jvAe+QDDyDETzH+rFyS/qi5uPYxXOyXBzjatjeFSLbhQBR/yP2XNp\r\nj4c8E8TeGhv/v7ZvHeKgH/K7OA/bS37q8EkNRiweDRTnZ2V/tLcP6aUQjOgD\r\nGpjGaZ9cWwyYPN8xwWq1eEGgrZbTT3U90TzBjb+Q5Fp52zHtUQUqddf0KmCv\r\nhLnztr+C7TesTdd32Ny1QuudMDJj6gTtsu0=\r\n=6uae\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6284ada4adb7008f5f8673b1a7b1c789d2e508fb", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"jest-get-type": "^28.0.0-alpha.3", "pretty-format": "^28.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_28.0.0-alpha.6_1646123544508_0.9455637602875739", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.7": {"name": "jest-leak-detector", "version": "28.0.0-alpha.7", "license": "MIT", "_id": "jest-leak-detector@28.0.0-alpha.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "644590bfac51ff9facb4b6beb0f3a933aace89f1", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-28.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-Aujz/Bq2MjXEs1myhmbKPyBcu7ynR5Ts3DT9IvAbBJCLhRN1n4QkjqLPaA30xaUmxgDUAmIBcg0x1Y8a1n+0qA==", "signatures": [{"sig": "MEUCIBpmEkJ/HY71sZ9DVaiKx36+uXjY0dMHFt275jqbUzTKAiEA+4syhWyrOxTntP6x15yRdSFDnEQBTj4Ti+QD34S5oiM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5723, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiJIbBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpusQ/+NJ+4AapLV9rFIRhDtbRcERuPtO2f8863vuhuOCJuRi+scJuS\r\nESXEHU5S14toLMkfnUhSsjHeaBfnjMeQmuLNG7k0y5aQo9ybEN1UShbNv/tv\r\n5RyFOFtJy4f/unp2gtQW9u+Tvb2UW6jqFJMAtRs7n/vJ2JcBKDXrtyQaNKsF\r\nEcanqo+FecY+bKKgU73wZKKXRqMF5jMRZ+3M9RRKNlEb01oq00tdUIClLQWh\r\n9nqmdaldxi/9PkKvh8E2+rVWu0tuxIBfLQ21NKjsRXbCeFPFHDGerZHMAXl4\r\nzEQOc2WqCbQS/B5N5iwCSOsPgjUME6IPpzHy1bA+5C+wzWL1oAIUCgIQXpy3\r\n0wlULWgE5A9Q1GgC9AMudX1vF7P3m2IMeqgVjWAIxZTOQQwii9rsXPTAhRu5\r\nz4y/qEK1lqnNWJn98FGh+WQhkNpMlxBSklP3D8Nn2Wlw1lp452c+zJ+fWQHC\r\nbCuos5/Skpoand4QbBQQP/Zz5ZIqb5n4lt22w1YoywfHXBbqLx/zDFGpJUN+\r\n+jsaVqTp9jC/++jJAlcAXMxF0q5MI/nuOp/hpIVH4xCi/ypm2vTmeYOSxA/l\r\n9tMeAjUP/DKyvvIFtjJsuNjVmRvMG9AeOkfGvpz2OjybgctYNGMuGD0c+ZGY\r\nf16hK6rMD6zstbP1MfMXXJTK4btKM775VSY=\r\n=Lgcd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "06f58f8ca70abc9c09d554967935b58ce85c48d6", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"jest-get-type": "^28.0.0-alpha.3", "pretty-format": "^28.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_28.0.0-alpha.7_1646560960995_0.8833269734110556", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.8": {"name": "jest-leak-detector", "version": "28.0.0-alpha.8", "license": "MIT", "_id": "jest-leak-detector@28.0.0-alpha.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "02fc035ef0e300864c285ef8bab0bc16cc7849eb", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-28.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-nnZ6q6Sjyg+4BQE8PQIBYZJ+4cDnj34nWuj+PN/ljGWnxqkp5J57ktXakdWjMHzhXAR89eJu6eeQbKsQwD2cww==", "signatures": [{"sig": "MEYCIQCjTvu+WbSlbwT5dAkfaDl8fjTe1ChexA9HD0WQk+w2XAIhAOwSPq9QdahD/QFOsnw8ySBmwvrfN2sTjkiMIC5tFwCM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5723, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTFlfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo48w/+Jp14SGZzAObSTRu+XpYUNFNaY2qBiWkneiiXqO3yG5B/Ttln\r\n5TXICIWaAMd2Gvcrfr2V6EfZnQ3HO9vdiRSvWxvL+6vACnMMBukgUKAv086T\r\noEbk9Zt9tThrlo4U7Xtt6sZBUf8az2Hab0EBDIjwLh3q0TtHTKLdj6N1cQAp\r\nlzGFGvwdHj5S+gX5lq+wOnnKw41PXYPWYOlwkcpJyPOocuiyC3W4RC7Rx3mw\r\nR8unRy1kTeWxgyaSYU9eYOzj/c6V7Z3G5cIsiuxGK5IBcs2a24srVvE5hAs1\r\nAe+/GHU0ukUdVHrWD+lTB+o3/Se4Wq+t17GYiNowzw2L04kwNe2a+pS9lavc\r\nMXNy/rX/pn1PJdqANr6RpyxBeHzZMFsYHDtRlpYwVvsQvaq/awdJ5JMEeGm1\r\nsbAbQGK/ahRYB2+jREe4bcyv7atgy/O4VF6LQG0edc0cb2tQGhi3iyicvIdl\r\nH7uM0oJpFjtwETT4JdngvCOUlS8vpDVUTUnNJPfB6RD5gstC9UHQTjLCXOmy\r\n0weMflDo88uH+CYSYmnq1OVWnFK3HPvZ9yFDoc1XwnHU3wzCCECA672WQXdH\r\n0Ox0xm0tQn2CZWt1WYgh2/Sov4ECE/tN+hyZtd87jYhWCs1p/ATxUQL3CFC4\r\nxk+dtcdJrULIAHnR/N8dtfZJCRjS1J8NFw4=\r\n=fciR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d915e7df92b220dbe6e124585ba6459838a6c41c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"jest-get-type": "^28.0.0-alpha.3", "pretty-format": "^28.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_28.0.0-alpha.8_1649170782835_0.1709129432474692", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.9": {"name": "jest-leak-detector", "version": "28.0.0-alpha.9", "license": "MIT", "_id": "jest-leak-detector@28.0.0-alpha.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "845a0b489756354b2b4da3d503dfbacad01d3208", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-28.0.0-alpha.9.tgz", "fileCount": 5, "integrity": "sha512-RF8OQtnAWvipmDHiFwdkDg8AjHtPrpMEg2IjVa5zgpZD9v2nHfTvD6l9IDW28sPudRurCTRq+v9+erDDmF9RQA==", "signatures": [{"sig": "MEUCIAdxRuZg2acDu1iAdBQ42QuTMjadHU2bdcIsAUKO/cXRAiEA8bOPyKP7zpICBaiGJmWvlMv6oCxcTVqCSJRlEY1QgdU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5723, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiXpYDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrDLw//VJHzWuY6NKmouxtpRG+xdTn6lMzzH9o3Oxd7xw5JEczhU0fW\r\nOeFSm4hiOBqYaHOwyNZSXlbSf2f5eLjmEWHyohowEirghWLXNUvBwl44Wxzo\r\nbpPjHdgzfXPd6Oo1ILIxdwvQGGXAWgBAHH5e0avr3mRDCVwC7ePfTBAgYcfT\r\n+pDUuCHllo26RjiHvPzzTNckzavz6kMxrfutrVL+SvXdjRYxDHEgVeu1T5LM\r\n+WJBu8ZqBASubiOVMOnexRa4AVGOD/rTVmk4QLszjytwjTcE1Y0nNHiygrBs\r\nEUEieQ7oZ+R+PPKh9hin64lMOvsFARNdODp5h43N1ItpGPh53e0I82PtzQlc\r\nI30hiQ0dJXnm3UwdanXrEGcpHFNVChq+eNHYs8zJfA0KpC+cm+1RlNDh+Nuk\r\nqLVMhEuIY2NVYe0tf/1GMkxTfk/XwHKGUjoM2yLxkM7qEryzGAFEU5kf/b3d\r\nK07n0pUgebcof1jthZQFRWPAxJUrB6Baft8suC82B4n+CY+PibAx3rYfrggt\r\nz3d8FC21ypYR0xJzGBhokM8PzxSitZ+O3ZQNQ4Dr0cm67eM9vgfV36tVbrrp\r\nLDcFCLxNy+L9KgYL+vGGclFRA8x0JeQlDVeb+hENySVryFrvaCT+ZGkG6Tjz\r\nlOnmXfqlu1LK6j4vjwGXsPE131B9UzFp9gQ=\r\n=QtBZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "7c63f5981eb20d4b89a4c04f3675e0050d8d7887", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"jest-get-type": "^28.0.0-alpha.3", "pretty-format": "^28.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_28.0.0-alpha.9_1650365955479_0.6682784375742681", "host": "s3://npm-registry-packages"}}, "28.0.0": {"name": "jest-leak-detector", "version": "28.0.0", "license": "MIT", "_id": "jest-leak-detector@28.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e4ba7601a083cbeda784af540ed694bf206c9910", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-28.0.0.tgz", "fileCount": 5, "integrity": "sha512-P4KWylb4x6Q/jctJtGKJSD3PkUjgSIu/JOpstWwgYFvGfNKtAGXEfIY6nlGSBTCajSuPs7WXUNMQXiYqKwivgg==", "signatures": [{"sig": "MEUCIQDDMztA6spCldVtOl6tdbtiPY6TnX33DUsR0GYayD0S0AIgTTb2NpcpYlBygRAP3JWD5SqYcs7oUjYg0Yspib1dhD4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5699, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZo8pACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmriAA//eI8loV0pklkAWkQHEhhPYfrDa+z12b9eIE13qSGPHuKrskGH\r\n4v1PVGZ0Ln5CNf7L4uPRJZDaAmVkh/orfE65kyW+Ycjvq8zZq/XUw1Wm+d2p\r\nVbpK7HJUF0uoNiLuG3agWQAU+UFKU/rtUSNu/4/pMTJcrTsuuQ2sTHUGHVD1\r\njG9BQon81sqxMc74cT4l3mjUdLAYd7b9kHxCLOvzUF//eqjrqmnE5Y/PI3QD\r\n9lb7ZDfwTTj34PR1RA+W0LqPdftUY3I5/Pp961DUbG9cJq/hJF7aG62fFcmi\r\n70rnrI2xt6eq57rXmDvt/cmvUrT2MpLiEESiMU8uHOjziu9uN5J18QGZMHcZ\r\np09NGOgCdR3w799PL8+PjOqAUTCrEPyz6jTowrjG46qH+LK01EhftqZa7r6E\r\nw0SxfjHU3gfDOOw1ceWmdvmrWZZH+Wp6IagEJpefcPeZ/MIBixtp5crHB9rt\r\np6G1UBvMVh7/Zz1QBbPEu+AJV9r7+CgzjHij0ni3VBNIRgIxP0emYWWI2Nm/\r\n62OrJv3mioCKjctYJ4FPokWhKISGsMNPbj0mx5Ud4IFhtf8BA4S+6w+HAve3\r\n7wGM0ekwyepRk/EfjDyjFU4/cHRZ0+IEKLwlO7MeXzC4bulmrarCGNKFpL/E\r\nPt9Bwva5yehNMnfkw7WV4a5Bg0eiyYdxb1E=\r\n=QdgA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "8f9b812faf8e4d241d560a8574f0c6ed20a89365", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"jest-get-type": "^28.0.0", "pretty-format": "^28.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_28.0.0_1650888489290_0.7675242188033564", "host": "s3://npm-registry-packages"}}, "28.0.1": {"name": "jest-leak-detector", "version": "28.0.1", "license": "MIT", "_id": "jest-leak-detector@28.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "26e4737145ee6b2aef871eda2f9606bd63fb33ca", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-28.0.1.tgz", "fileCount": 5, "integrity": "sha512-g+0cRjOeNy0Wy/9LfgzyyVjKNnkFddEXisac+5arM3JwdW4hjZJR+5lwiIuMxuPtacldLWhu0pN63KV+Z33smQ==", "signatures": [{"sig": "MEUCIBv3sbhsUQV9Ez27vGDBf4hw9dav/IFnb7xxMwgPNqZIAiEAz3Y+sKmBYD5tcFJULz04mOgYURKABk5GsEpL+0Wz/DA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5699, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZ8NAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoa3A/8CpT1jt82eejAaNcdyK/yEMcPYN9XOynx/tFpYcd5nOGaHJVV\r\nxO+4hpXxJAaVDWk0boeAlg1QDKeg6bnbKWUii1IVSkMLeqXrvhsRY9A9VtWp\r\nmYy3fYkrWSiHLpsqKM0dKcHXI166x7QqdzfAfgPEBf2u8mZS1I5G0wkH2qbu\r\noLJALPCDv5HgrGgMzKr3L85J43k1gVTiVWnas+2lw9HOWi6W/WXpDVJu9Co8\r\nRxa9znWky+XZ1NL1/geQaPtue7wD6HJQYA+5LZ+6hi5bbtME6Bt/aDpNN9k3\r\nHG+Ntj4pvxhkUwE422omd2V5GbuEUwHl2l6Ztc4ibmsmbpqfAVo6FhhTm7dX\r\n8dYgRxn9bPDh4FcPPrWGF8hb7Y6IWK+xXLaHLR8AU/+c6slIGranzWMTOhF0\r\nSmhX6JxSjoCwecroxT+HeBikriyld5zoWQGD0ql2f1dBIOKiZRG838u/xR2I\r\nSXQkoSXbvKzzMBERZYM2BvpRwvYx6zKUv2saMEI2XcesmV5ZExari1KVhq9j\r\n6aCloK8/5jMKm8JDrQSo4CcIS2wJz1pPTySnfvQgrfvMyB2Mw451JL4pPym/\r\np+ZMSzt+LnnJ/9G7Lzbo2oVvkIpkooaZ6XN3g3wXfd9H2HFiQ7lds7kx9iEN\r\nn2ghcKIBfSF5iCUxO8qWYBR4IqOXX8m68ps=\r\n=i1hL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0a08639e4299f07becf1020a761adfec83536018", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"jest-get-type": "^28.0.0", "pretty-format": "^28.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_28.0.1_1650967360367_0.1938185588332797", "host": "s3://npm-registry-packages"}}, "28.0.2": {"name": "jest-leak-detector", "version": "28.0.2", "license": "MIT", "_id": "jest-leak-detector@28.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "cbde3d22d09bd690ececdc2ed01c608435328456", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-28.0.2.tgz", "fileCount": 5, "integrity": "sha512-UGaSPYtxKXl/YKacq6juRAKmMp1z2os8NaU8PSC+xvNikmu3wF6QFrXrihMM4hXeMr9HuNotBrQZHmzDY8KIBQ==", "signatures": [{"sig": "MEYCIQChakPa88w3U8D7Ylm0rGoXs9B9zgS0GMqqx6/n+/XEKQIhAPsjdpUSp0icxg7/iuNCf6NSQCLR/3PwkVnS2ZITGeCt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5699, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaPRDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqcdw/6AvCSUa/ba5sxWs1zrK1+Z6cmpWl2kdjYZJ04UlIcjAjXltsv\r\nkGTiPQzT+msJrrrvspEbMbTrJ7Q4e6SSR0CmptSn2xDOx81wXQdqtNZcnXym\r\npviXAPoEFAoT3MFudYtCTUb4obwdBftpLbeU2SUB8Hq44phzcVf2xuSTCKQA\r\nVyyE4TCIFi9OTRbcZHU3RAXl+g/0oFnt/00SDGSJ/Vm9CcI3cpcnnswDnTr4\r\nt9rwucvVyPjvNLSxuSa1Qw4SqprEsYx6jr4FuZf9E8zT7gqu1UBsoLEVWC12\r\nalmnitd4vY+xqvFC43BeqB437ep9NHLabsNJMLXUHBMQvyPi9ezFl2HP/HCn\r\nMx4FqHRNSxkRxAA4LjvLqwFh9MvgOX5h+MZD12lx0WweutXykZ+9rQsd+KgS\r\nXTkMlrM7438xlXcU76sVdWRAbX6Ug5LVvIailm1T51zmWDVNLM0XmsCa0CEv\r\ngQ6wqybOYnzC7K0cVQItFV8j1CYiSXLywpy9XWn3DnWLqhjPUXXhrtT8oGTO\r\nP9WnMMypbZ+IkmruCedLgj0HQBDU1JoKyfTOMSKNxbECazEwii5XNLLw9shn\r\nFSijpIwZ8wPIIiEGQ0RtSapKBbuMXr2eLcPy9fG/ivPYjX4hPek3GX0GDteQ\r\nnRgIZButeibHeeJcKWBycs1XBmUZMW0ePX8=\r\n=FQzL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "279ee6658d763f024d51f340fab6a37c17d94502", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"jest-get-type": "^28.0.2", "pretty-format": "^28.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_28.0.2_1651045443634_0.5518026567073293", "host": "s3://npm-registry-packages"}}, "28.1.0": {"name": "jest-leak-detector", "version": "28.1.0", "license": "MIT", "_id": "jest-leak-detector@28.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b65167776a8787443214d6f3f54935a4c73c8a45", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-28.1.0.tgz", "fileCount": 5, "integrity": "sha512-uIJDQbxwEL2AMMs2xjhZl2hw8s77c3wrPaQ9v6tXJLGaaQ+4QrNJH5vuw7hA7w/uGT/iJ42a83opAqxGHeyRIA==", "signatures": [{"sig": "MEQCIHeiprOKNQXl5juYH6/JsMOKg/Dkuo/vaxlj9kb2Zn4SAiA9HkLNlRqB8l5YNnS6pIZZ3xFu3nqDQoAoxTUhviaRlw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5699, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidP0XACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmobbQ//YOQVDJe2oiCaarBcB8OAqXDVbxAWQprrShbpOTNpxkb5AQuC\r\nuxneeKg6YLizA8f0aa18jKlECJqWV7yyGpy2OtM/J4F9LwnGQylSEvraM4Wr\r\nC0GEWQ2cjj1wy3hnberUX/GZb8vTyFqXp3k2W1ZkF+dnYmJCpqz0Cc7uoMBw\r\ncbMuYv2K0bOX/bcjgkz0H+1Psb5/********************************\r\nfkdJpTWxaWmn1R+HLYzQYkVps+sYJBik3IKmdmlT0DLXqdgRQ/4R43oy4sBW\r\nRk+IuH8LwW3u7LKn33PZwYFSjdpjNvaFY/bm/T0WuBkjcC75OfOGwjt3WohU\r\nQA+BnAwlY/rA1SyzCNXY7nJ3lhFsSJJn8UcbLRPwVfnwspt4CAoGyFN67RJ6\r\nJazoVFkEsGNMndNoMFENyUY8frwHQFHrBIOcDJSH2vEWqj4T6W7ozAjfC5GU\r\nRKnz+dQiRrvu5heLdkL348TBmajZW6rfoJ8WDi7vBWIG7GXvuI0EcbnubqZB\r\ndAcbS834ok5VnWCYzE3HgORMOFksYQyAtKeBxhM9Cpvn9yBGRolehoxrRZMt\r\nsTqn6oqyHF7/kWUcfPMRO5Jgy0agPlP9rQzcHghUVc8E4E0gpa0Sv3Xie6GX\r\npDY1j8PKZXk9cPENxraO0lucGQOFN+yVjAg=\r\n=Es2p\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f5db241312f46528389e55c38221e6b6968622cf", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"jest-get-type": "^28.0.2", "pretty-format": "^28.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_28.1.0_1651834135143_0.42548637130281186", "host": "s3://npm-registry-packages"}}, "28.1.1": {"name": "jest-leak-detector", "version": "28.1.1", "license": "MIT", "_id": "jest-leak-detector@28.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "537f37afd610a4b3f4cab15e06baf60484548efb", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-28.1.1.tgz", "fileCount": 5, "integrity": "sha512-4jvs8V8kLbAaotE+wFR7vfUGf603cwYtFf1/PYEsyX2BAjSzj8hQSVTP6OWzseTl0xL6dyHuKs2JAks7Pfubmw==", "signatures": [{"sig": "MEUCIQDrtDxS5UggBIctlBc9LSw4XicadUL/VsnzMC59QVXQlAIgZid2JS1vz5U6x0MiTyLKNEX/VeuKFIwxyU+Cgulg31I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5731, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJinuuhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpgsRAAncr7j/go15asGEMFlKvL9zEHvGfsh86DTL14Nv6gHBE8w2lj\r\nnIC/RetLiOKKIt7cw6HSU6eToD1I28XwCsVGiEIOWNwMqHRnTTXPDMiSg66c\r\nr4s/4tgfSeme5X8fCeWkUQ7+Qwcry7HLXZOaBghKwRv1wrQYVry47ZtAVK7c\r\neItWOYak9uSM1tL3ARM9F2ikw2I16aXDwg96RTFwhWjsfq/KawuikYs8GHJM\r\n3C69c18l5YPG688jj1qrV5cziXr/2Ny4rBBk2smvH+BnkMF3P1xBaDj7J4Bs\r\nFJa4BCXnzb340jNA/P1JhXTp2FiKF/f2Ygip925Ad7VoLPhOk7R1y1OQbd9B\r\n0482a6gZvwf/iMfnYrE19jPhfKI2OP9dIKWVd4corNkWP8qEt9cWXxSvPPWk\r\nKq1Cy5UUgWTuDpnhovuglHiE6MKlkv2efIH05w/10PME1KHEvxUfn8kMoHRT\r\n4qKbcRypAevrihJf99W7xKNhEnaEeocG2xpd5XRnjB+f/viC6La7KCTRDgLT\r\nF7eNtdJdNRtlJLTh0EaNFVuaMRn/EUQ3Pmbys82LyPRjpHbJxM/8w67/ZfUg\r\nJJJF726qBfAc6FyI52AXcMm+Vy0gsLG/SB6H6dw9qBN/X05ZORTM4CjoSZHA\r\nWTKjDHKxWRBLmQjY8xz9JOVPBekUxyRlcjc=\r\n=HvK6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "eb954f8874960920ac50a8f976bb333fbb06ada9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"jest-get-type": "^28.0.2", "pretty-format": "^28.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_28.1.1_1654582176799_0.2682918613447216", "host": "s3://npm-registry-packages"}}, "28.1.3": {"name": "jest-leak-detector", "version": "28.1.3", "license": "MIT", "_id": "jest-leak-detector@28.1.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a6685d9b074be99e3adee816ce84fd30795e654d", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-28.1.3.tgz", "fileCount": 5, "integrity": "sha512-WFV<PERSON>hnQsiKtDEo5lG2mM0v40QWnBM+zMdHHyJs8AWZ7J0QZJS59MsyKeJHWhpBZBH32S48FOVvGyOFT1h0DlqA==", "signatures": [{"sig": "MEUCIHklJ5+DIUZF4wCtb6vMU25ytpoPYybO1kAovNXHBHuxAiEAsyLbKoJjlvqEyAUAnUI0QBhW3uSzZuttIfmsyP6wtyo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6534, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiztLMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmorxA//VQQBva4fk2hBBdsoek7oZoyIUSlCICHqBQfqs03DuMD4h9Ce\r\n+8N+Jz8yd/5uf7wWnrJX7XLmBZUSSGnPu7ejbgCiGP+P72ne6+5kLNMvpZ8C\r\nG88CG9VuqGMBLXvBXYv4qOAqyHzIWYnae0Dwgv2kut6NcwlJrFHRHCTVOPjz\r\nMfs8LuVNdzcYNkz/PHScBtH29GKZ2WU75zU8eZB3KkWhHIzPi5bvuy5xsPjC\r\npoFsHqD+kT0lAjov+nHYAWY1AXwcVkJ5qytWsGW9LNFLPN8g6SXGGdF+nPrR\r\noSF/4w1/hd5EjGi4fbAPO10IOK0e9DuDTDkjaalKDvmDxk3gZsC1Ado27cOM\r\nKe/jPmY7ZmWpPSl39Je/MZ3q4+SvUL4nYqXODvA0lagCTkY04sWwpMt5eCZY\r\nmm09jQ40mzM7c2uUADLUX/QuF6WGRoQcHSneACjZgR51gaRFpiwko5/ODQnc\r\ni7Re/NXf8ZqLt6tH1c02EMteI8eMwe9bLJVNOclhDSAV8aQAHoJucfDAKEr7\r\nf5GNbdMXTqy4LegsusbPfysJdLMVVEf8nmlwqe/hYcQx3NFec+ocetb/cJgv\r\nBfU8iFsocx3573M4SWECxOuk6nAuWvhmVOSM0kgZpDQN3bD5L/yN6i+5l6ls\r\nV2y8Ab1s4kT63F2mIdYcaQGT32ccizPPdTM=\r\n=n61r\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "2cce069800dab3fc8ca7c469b32d2e2b2f7e2bb1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"jest-get-type": "^28.0.2", "pretty-format": "^28.1.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"weak-napi": "^2.0.1", "@types/weak-napi": "^2.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_28.1.3_1657721548487_0.9748144988886716", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.0": {"name": "jest-leak-detector", "version": "29.0.0-alpha.0", "license": "MIT", "_id": "jest-leak-detector@29.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "44216aff2deb727da6c51dc628355ebe45fece4a", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-29.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-iuJr4X0knUa70/aMyAU9++S5EvTeE6Mdb3ZCAzDGls4yzPtF/dalMh4jOimubJCfK8SU8DFaQcmqMEOmMt6yIQ==", "signatures": [{"sig": "MEUCIQDobfWXB+iivy5P9Kl03idrEOxC0H+bewcXUttyQzeuZgIgfCmMX8emd6K3HbOjh9ZbG1l0DqmWO0kcu7B7KT4oCpM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5594, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1IgMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqS0Q//VEuSkeBfdHLUfV7gy6+8BIWxsKWjOdTBgZX6pLA9fZDBZVZb\r\nTeOBKY4LgV8Z0fcbLYcGcJwFRGo6xUIZ4NXNxKXQ97WfVU4obTOE9j/S7Pvv\r\nbfF3OWL5cnymbyZ7up4T+2HHXMzLkLLhFSQgN+BjJPOCaVh+W5mPPLnTIHjl\r\nIfkhxR0SrwTClQz/6S+kk9s5UmvyNN6LS/GCYV+xAYV5/A28BCBByUm2IxFt\r\nPHH7T4conFEbVYbtR7epcGKM9G8/OlN8TTX1LcXExsDeLnXJQmTuTICOaalP\r\nX1HFD602JWFGKu2DBjEcwZMX3UlhcNHbjZ+eFBIKV9nVoVMZBdUHu/9+oaja\r\nSNCJXsFa/boY+Gf5xT+kyEZ7+JVRc2p04h1EQxYCo34G7cneTxFo4Wwml3jc\r\nLbn8lIZtCS84EW/RfWFUCXwRxUT98oxL2VBASPAP7l1xF/yXCg1zOTLZ2DBv\r\np5va+JISmVgcfswMEFTbUPWzYUlmexWVgfdd1eF9r870K5Q1j3s62GehtQkK\r\nvTU9PVnSktVMDwhxsWfEO83eYjGvdqOf1vVc6bMMjThYPdMJtlZekLJMGSP3\r\nE9QoFJWRBcVz9hWk2auFgEtd6An2YKnSTpCev9TYeAeOkKaPIWiuxf5DVA38\r\nrJwGktXXFEoiGvIk1vtcv6D5oi6ok4Mtbio=\r\n=zayr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6862afb00307b52f32eedee977a9b3041355f184", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"jest-get-type": "^29.0.0-alpha.0", "pretty-format": "^29.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_29.0.0-alpha.0_1658095628005_0.41166978032205703", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.1": {"name": "jest-leak-detector", "version": "29.0.0-alpha.1", "license": "MIT", "_id": "jest-leak-detector@29.0.0-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "cef3ec9c8775fce83cec1c989b7b55ee37f33b5b", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-29.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-jQtMLgrwVzWZIAZ+19+Ri0ZaDzaVey2Gepp95CJCBbbihWLUERZ85o6JwwfeN+SlmaCYxwkl9G6FwJHgjiMCOA==", "signatures": [{"sig": "MEQCIGoLGxdy0OtXDkZHBxRdHMKAc83rgk//KwylUe385xTmAiB9gKBnNNdyD2OHUVcNCGMjOBGhOPyH04T5bRWzl39p8A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5594, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi64ICACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpALg//Uq9jHPGb5VQo69qas9gUCT7kr35viXCKy66Nfoa3rCne2xWG\r\ncjWM/T9QzNnGaHy0NRc2c0m3X2RoC5flV83fBxaPVx3Ssg6/p+e7sHD9Bfhc\r\nWvm8QYWhUUrk5Hf9IFNWa0CO4hhyqWtHaQya+G6BICmcthiewRqC2a1Hs1oN\r\nwbCMQ1FY066/NTx+W/Mfo/D2sEciWaTQZmGERlqauCDjZVCcof3DekmH25p6\r\nBITgo1P5/FUT9tNKnKlD4uGwr6L+oNOreJtkE3oLaNNf7uAoOS4Cnkx+XM+s\r\n/9aSiOJLWDFTceeYBEDIcDYmrsuijply7J0756ehjBHg3zu1FZnUzKv4sicD\r\n15B9K77a33ecxwWqHu8yQ+8Dn5d9tZxi5RYgd5LWmFyiC+Gff+S1kRI5JAPV\r\n7NlllH4K1tCq2IV47KPlqimjNgG3yrY7ZPmADZWno+8iT+sNrL4IV+veKuSY\r\nk8LFPe7yip2vEBzawHu51FOZdtDSh0ipo1WsEI4unTu1w3TjrH1XUZxoR1IT\r\nafqIxvmGIhoWPJjbmZ5dABx31NTqGzuuIdnL134HqlMu+OUa/GWPRZarfqSe\r\nGphYHXvluSboloP5AZwZDSUMSeYurNFDjm7QqR4tHNF6iwsugOI0By8/jYd4\r\n19Ys4gdoktlbq/0Mp6evmdghBhQ2qvl1/Q0=\r\n=zib0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "10f1e7f52d9f876e6fb7f20c1903fdcddd8db8b1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"jest-get-type": "^29.0.0-alpha.0", "pretty-format": "^29.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_29.0.0-alpha.1_1659601410118_0.31478583420865247", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.3": {"name": "jest-leak-detector", "version": "29.0.0-alpha.3", "license": "MIT", "_id": "jest-leak-detector@29.0.0-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "dbe6479a77d5321bddff0dee7bdae54a6fa21171", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-29.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-biS7WHk7Ww/MvxcFQKYmZh4kGXIpSiCthqrtHjLc13wuE7kn1OSr4vHsAuvGesSPySqN+21CNrGxrP9RwVp4+Q==", "signatures": [{"sig": "MEUCIF2Llmk5chZng77hwHzz+O7+UXLyQVF9ENvvwczFLeOvAiEAmFjfJPQ2Ezp0DUZ73h5BTfD5c6ZheZ8Xl6wYfZ7/n6Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5594, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi78ERACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpGxA//UD6midiyYOXxZT1HNmFcOHe2DYfSaIilTo+Va7sDjGwvsJVq\r\nlGMfSkXbgBMFYh14FYLtIJ3EDJX5ti1iopVc/9+GaOX8QcWcImqdyyrPNMmo\r\nA8JM63EQXSpFX3U4H0EiL/Sp56pDvTI3eW8gGpk34/tzoV5icdmAssJgsSOY\r\nY5HJtVcyVvh2SqmgO9f4AieVCgdRZa0D1J0c/RNNK07CYnR+pXNxcvhui6Cp\r\n3QdDnTPN5ZHnOtP7c2kiL6aroHJ+0W2xSQExXX4QjGU2PP3oGhG3RWx5cm7x\r\n3juCVv0a2aIeZ0Ga6MLmva+Vo0fAXJr+e6UtDPjpEUEarkGaJHW2St06HvaQ\r\n8uUHYC/nRAnyu4hGIFheZ3LAL7v/IO+s5erYft5UAyihiPz6ZQbCB0gibcPx\r\ncd67awqDkrcldfSZHqLNH4pyjeDDNCgr8psHO0ff9G6mJt18kzetOqjAfTXS\r\nMPYjQEWc+zgm0xHMK/r7RVBBZYkM4LQbBzJGRiDvH5/eWWDcb0a/1MpAaf/7\r\nbVLjuk2Pfv3H5hJBdSxTGQgXOIBhyZVXNLaFcD2rOfRDHnAi3/QXYcmXa18U\r\njBHKXXmQP087ObMQzDrN9OtqNNCOXWsXU9usATxyt7j8rQx/OtvlCM5ldw4A\r\nSgDaFPnULWKMV1dr70pumAi5vldWGtOZFnw=\r\n=lVL8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "09981873c55442e5e494d42012f518b7d3d41fbd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"jest-get-type": "^29.0.0-alpha.3", "pretty-format": "^29.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_29.0.0-alpha.3_1659879697793_0.6313915076591035", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.4": {"name": "jest-leak-detector", "version": "29.0.0-alpha.4", "license": "MIT", "_id": "jest-leak-detector@29.0.0-alpha.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a07483a16736a126e14227505c5d62abe9c16cc0", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-29.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-fpNxKOAvYEddZPBHaxkQ5AfNvNUaY/hkiLrstMjUr223OmeXlIBd1vq2b8DpjNGFYSq4jxZ4+M6KyPASwfFM9w==", "signatures": [{"sig": "MEYCIQC//4hqaa9UWRKxJgqy+BSL4K6XGZhZN7wDTSyUIzj2bQIhAOBgor/SbFsK4B1K77EBHQV/CL2q3chqIXhgOQc9hTS5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5594, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi8QogACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqz3w/9GnUqd6nXmum2LwCWFV/int6X53W/zabPGvf90VqjVVRCIrv8\r\nlB2KwxML8DoAfhAcSrjjn3LG6fV87PVCjcXjijU99Zb7e/6/z66K88cb8877\r\njUTH+Wr+h7/VaXPTnk/XXs11gxC0oPBYNUwhLiVdhBCwE9T2iGNqUDrj3SId\r\nP454Jzsf7rzADyGHji4BK202bWuXd/vcHRO7ftGO2ijOABsS5IuzUZOO9osF\r\n3krdpH2+R3ByqD5a9QJTCxGjM8GLUjTgXbjbWphUaWgxs3IajKxz75dHzIyp\r\nRfzNs43YBK/ODCan0RJs0M6CaRPa3VOZAhYhdmbKt2DrQgsLT4t2caBanG96\r\nZOCJpLbpo6I4HxVzmUQX3RVWQEZEhGnUn/0J9NQJiZ2Y7G/AIQf8UQOCDrR1\r\nAIh7yGCbt87ZC6Wydrv1+VsmVxwMWZGw2CgY7NC9LhdYz61+xlAA6+PtRWWZ\r\n1LFKz0OsgJ9kNK+iKczMnyZ5N7kJ/druZqpl/rhIqLf3l80ofJShYqkrzV6e\r\nKSFpm+Uj1eosjGsLUB1e/OR03QCkABO1uhAXcYZiOeabwar4IqbSmOue/GfQ\r\nCf85EwhNQhjE2fL47r7sigd7OiB1rOQvX4dj2MXK4WQg9tS7vfzpOfroLFGL\r\n4hO6ts9zUAiKX32SXrLEGrbOuKGEX+PliKM=\r\n=Z/KN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "98a833bd4bc0bdcfcee5d4f04c2833400c4e2933", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"jest-get-type": "^29.0.0-alpha.3", "pretty-format": "^29.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_29.0.0-alpha.4_1659963936277_0.07894568810341296", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.6": {"name": "jest-leak-detector", "version": "29.0.0-alpha.6", "license": "MIT", "_id": "jest-leak-detector@29.0.0-alpha.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "00857a9abb34725b48ca9c852557d8481b318fa9", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-29.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-HlTUeX9Cj7LdKbJ1ffNFfq9fPFQRBXtYLmEO1NyaV8cmZtwLXwe1U5BIOYtvERJdW1I60ju0DWTgCxUlaFnNNA==", "signatures": [{"sig": "MEUCIAwuiMuAimlvUXZmwY91egALssWePTL3MEhw3i1GZ7cdAiEAt4ooZRHP9xwLwunEVSU9Hz69B9npK3w7ryLoV77s0EA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5594, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/5bfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpnSg/+IBVle008Y2B9rLymhVyjj/1lqfsALU8fxGi1plvtQKxRagR5\r\nvf/Imge7x8qr3MS1STYbwCtUg0C52zgFAZnPWjVh0DUx+kQi/IeIfFp2pIWa\r\n78Qht9MT3aBB0Q4SjQte1Uaop4VDw9tmvGW5UwutrcjINNBQHe0UowC/f/4J\r\nectz20j3HLP0SuDrtW/xfmulipw2nPoyGdcxjEn62VS/UuNcQ6R6Juy/1rWL\r\nPLEw/DjI1F+ZoxzlLxP1T6vbWEFts0BG+b86S9HZD8USAB77Dn0C7qlODHku\r\nO9jC30f/A0c+x9SRd2DWKPpkVIrfkLAS0qyIPd5rUqqxVDGcYvv8M5aDo+WY\r\njdFg/UcvlmnfY/hXH72lTdp1x1RkKdsw+n1luQJInyMdW3UVYHN7bhU+rOzW\r\nDBDEyfilST7VDZGHgd2XOXF/EZybtHEnqhNUKt9WkF2luXK9Q9jL5jMeufDt\r\nYpW6ExOS11R9Wi3wDDOWp/b37T5FqzlWILYMb7qG82oIF2WFzu3myk2lGt3C\r\nE4gDCST5izOg8pst4kGgsvhHXt6Z11QI0dWNhqX7B6Nz3Uy+BRV7RrdJnjrZ\r\nceELKI8Vc3QF4Wpdyhtq7ifsVgC1qJJTim5syDgJ0q+NKVJxXv0jjxowr23k\r\n1/ukaKRFlr+q5Gq9uspCeego64sdsF++UJE=\r\n=Eecw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4def94b073cad300e99de378ba900e6ba9b7032f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"jest-get-type": "^29.0.0-alpha.3", "pretty-format": "^29.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_29.0.0-alpha.6_1660917470964_0.058113805370489935", "host": "s3://npm-registry-packages"}}, "29.0.0": {"name": "jest-leak-detector", "version": "29.0.0", "license": "MIT", "_id": "jest-leak-detector@29.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7a6791cf61513550a8fc4a95f907f77db899204f", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-29.0.0.tgz", "fileCount": 5, "integrity": "sha512-kBjNS0/z2+ZV/3N7R+ot5fKD2W1fHkoxC3kH/fkb2z24YSPfR9RGwiNX+YLRG9r0gWsxQx16boxzHT23G6rFBw==", "signatures": [{"sig": "MEUCIAyF5+dV0z1dYQpuzQPs4+Z0e07rFJxXoDVsv5+ATIpUAiEAlTGsYoo6sbLrU4W2oe+T+yCjuJ7cLVJU3wrI2eJURDg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5570, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjB2wZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqJlBAAgd+huJf+9/SvGQFvGBNU4zcspbI64Dsy5uWsEbZM9oL2FxFO\r\neW+rut+OmX+o+xOOMv42ocyoiKDrhGQddU/el9NtB+MkMdXXGRjZNqVEyUCz\r\nzF+epH9b/Z1zpPcAgEMErKwGAxo/4da4KtayUcFYg6/2YArI539QRI1H8n9x\r\nq4Z+QnB8gHpae0YTdPADgYjIu04yp5LufW9LlK6wUrp+sISPU+GCr9wM+RCf\r\nCf6KNfmtGrltC6qXEZ8A/HnDSwy0C9XgIAYDZRMZ/OeOW0a7dm0+vW/r+M7c\r\nYlp2K62i7SSPyiRBrpGur/vEUsfMBexbYGb33sUYu7QT9/dLgA75TQsYxMdR\r\nxjz++256iq3MAjwPylzFpsxx7/MFYB+qiNp4o7xmYEppshBTtf11clMW5uuj\r\nRQuNYg12mY6nPUKnT3sLjxpremt77lIh7RV3OWWOq6baEb1TEPBvCXHsidoI\r\nkf5xK4JrG3UfxIGo84G8vKPS0UjE/ciMBCtJr+LLMhQ6bogBMSk1t8uH2sqj\r\n3IThc51mP/um9pMotUDo4f83dsjhvU0nQua1F2nJeYgowjq1u3l8YWlfC4dk\r\nN6vQVUHiyFbd1F32ZgW0zHvYYe28JA7PpUVxwjRHWhjYlDZWJ7pNquYrzfsX\r\no47eQCSVXgvgQbaCThfT6p6n4Vs1cZdpDno=\r\n=AnmJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "75006e46c76f6fda14bbc0548f86edb2ba087cd2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-get-type": "^29.0.0", "pretty-format": "^29.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_29.0.0_1661430809745_0.6662392923905791", "host": "s3://npm-registry-packages"}}, "29.0.1": {"name": "jest-leak-detector", "version": "29.0.1", "license": "MIT", "_id": "jest-leak-detector@29.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "****************************************", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-29.0.1.tgz", "fileCount": 5, "integrity": "sha512-5tISHJphB+sCmKXtVHJGQGltj7ksrLLb9vkuNWwFR86Of1tfzjskvrrrZU1gSzEfWC+qXIn4tuh8noKHYGMIPA==", "signatures": [{"sig": "MEUCIQC+Jbxr6v1+m+cNFE9XWHiUc/IxVHXUt2fSUEot87SaAgIgJqWthQ7aDqgymb1UeyPljUcUrR5AHUuGvCKebVA+Mac=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5570, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjCMvzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoWaQ/+JOfW9EuwdNsIl5C3EFDxLB0qRFHiKSea0u54qFjRPC12MRFX\r\nmB7HB9+zOeUX2Agu/iHiWFZFEATQ42QIP9Oc229l7eaW9pwSLxjJBl7uoq7I\r\n62fOC4sr3tKcNfLR9mA279vcMzvBi8DdwJ2N913kItXIxG5A94Eilde5vUXB\r\nkLkbD70K6FjEIlMZnNuZWNTRuGrwBKQ1q0MsYQk7T4zo6geLa+k5OBg2FlNm\r\nWlrKJTlwPRqWudvhvO4zz7ppdwBQENAdkJyiZQVxU/SIB06NofQhjFLYR2LX\r\nQ1J4tJvTU3OolVgkEvXAi/IZINX2H5jdRDg5MU0FkIyq/Z+O2pYj2m64HgIP\r\nOpt4IsFxlBtMoXirljC8ijju4gfwtEbuxC2uQJwuiOyGu47EiKhdLnLx5RLd\r\nuKkJBLTYKxTDy18Qqv8+m7yoAtrG+E6IZQTCXm3udb8YrPmhZ9tcrr8+4lJ7\r\nRNDwA1/6ZNyRiAvmeptSoukWILqc547hINN0r107t0gno6C4GadXRMGItTYO\r\nyKhimWoCcycr2+9sOT2gU53bNmBb5vGo5H6A9ICxFLzqgc+WeCn926BP57jw\r\nFw82Yrv99ZxtEVreQCspytYTYih9t1WsjBQADHLqxwX2rtHvm3ljz/iOqsG/\r\nxVHzBXkUy9dA52tYxHSp4JkdaYn5THG4jb4=\r\n=bkIO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "b959a3d3bdf324ed1c7358f76ab238a8b0b0cf93", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-get-type": "^29.0.0", "pretty-format": "^29.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_29.0.1_1661520883628_0.1798554589807131", "host": "s3://npm-registry-packages"}}, "29.0.2": {"name": "jest-leak-detector", "version": "29.0.2", "license": "MIT", "_id": "jest-leak-detector@29.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f88fd08e352b5fad3d33e48ecab39e97077ed8a8", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-29.0.2.tgz", "fileCount": 5, "integrity": "sha512-5f0493qDeAxjUldkBSQg5D1cLadRgZVyWpTQvfJeQwQUpHQInE21AyVHVv64M7P2Ue8Z5EZ4BAcoDS/dSPPgMw==", "signatures": [{"sig": "MEUCIQCDGGRQi4p6hnxUSElUgPWLrsgr7zj9u6kHsYMizxddaQIgKhNBVapvKh9dC6bbRxmPcVNy6dIlE3iiTCLuOm0Lpzk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5570, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjEzD1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoK2RAAhzi4Yyp/J02932L8UHLSC6WUFVwVCxmgGx9B+nVcsZXWHjVt\r\n62lRmmYKztSccNwgBA2Mo2qb24xc1kGldq0sJHLurSr5NXqA+jKgTEkYTxbr\r\nA6Oy/UQOd3UizR7VoFVKaiIqepISTIAbNsbTOV+gbiPbOB8yKbmUwpLWay3N\r\nrhTJsAzgHrRAfxYdNjcQlrADsoVB/m+8CDU5CL6raPdKXnSauvB7QJs9mStc\r\nr5A8Pdnkb59fhEAljtYrzLt5XyZOckJwEuVfsLe71Q1RORbB9nuRBomOKRT8\r\nYSCCUJDChVH7UOHoBo9EHbwBBqXa+lSxvhztnHS6psqRJ4jOFX63XVaXPsDO\r\npgRg3dcd9DcJofjFuThAKCfTo8p1a4TV3YgPVkgDW1gZsmyEyIsgacmoPs7C\r\nkHbWEKXjhZ8C822nrgeHPrNpmIXD0p1QWxe9z0sWNskDPV+RZMLgOpQ1U3rB\r\nXBYigM/Q5meStJk9jSk5W8IxYFOqHW65Uu/rgsLOAr+F7/l9KSdu9QGqVk8i\r\nox7eSYUFvn27ysn2P9pEqfdaV5BnGMMKzaCUmo1HQBFvgcyt43t20RuzXvPe\r\n/UfJ1A73d0i0/MEuFBQeH0OxtQ4pOPVPC4YZREOoN5WRNCuPfQd4kX+sTq4U\r\npGuAN410vlhSs/WSbCkqRHnar1fAimOjwP0=\r\n=0boW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "616fcf56bb8481d29ba29cc34be32a92b1cf85e5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-get-type": "^29.0.0", "pretty-format": "^29.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_29.0.2_1662202101087_0.652624307211102", "host": "s3://npm-registry-packages"}}, "29.0.3": {"name": "jest-leak-detector", "version": "29.0.3", "license": "MIT", "_id": "jest-leak-detector@29.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e85cf3391106a7a250850b6766b508bfe9c7bc6f", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-29.0.3.tgz", "fileCount": 5, "integrity": "sha512-YfW/G63dAuiuQ3QmQlh8hnqLDe25WFY3eQhuc/Ev1AGmkw5zREblTh7TCSKLoheyggu6G9gxO2hY8p9o6xbaRQ==", "signatures": [{"sig": "MEUCIQCk98o4HLkxoC+hyo6aj+w7CLwzLOlkRQrFZfk5vPH9UQIgQ1mr3IawivKEVeidVsfY6/n6OwAJOuTHfc36OE6/Sis=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5570, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjHKIlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrtjQ/+JhIWfzOUmM/b0VkNmOBH4J5I80Dgg1NKUqf67Dj0InRt6dOt\r\nzwCnrpA5NkDpoffHXRhMqEGfQMb5jTFFFSkrL9eA5+cMRQHIypL8fGcOOVt1\r\nvPFWSanuhMH5hSTXjNn6Gx46RYBJjOFv7KEKMR8NYh8Q11LxFk4TpJPYGTaU\r\nDe11dvEa/fRE7ia9/OgFTJZbF2OpTFfOPOf6z/K6ONQwfnqUWZr89Ubce03y\r\nDQmgd8EsyNLefY8lnW7dC9NHR7WpdTqIAuEmj682fN4aOE9mEW1XOb6r58qM\r\nlgXP5fW3qMc0S7pB2j8C82qU7fD1JbBdjArDhVfc0LKB5sbSAsFJd7dhqNuG\r\neOv01UNZuuK0oxlUs21lyMA0Du7TiuRgkIn272PZwTT4vIA3zTQxR6CGL00J\r\n5JkArBeRYaNc8sQPiMDNTydjuqevIZpGLexz8YLhGCcyeTyv9mjc8j6GZ6VQ\r\n/2vJBUmjMVhPWfV2CNcufHFsf2IeSoirBLdhMa+UpYIzVQuQkp3IqqUcJJCK\r\nzsr27xbQ6WRqqzL+eykLNP62FiJq5/kaJNC2h+auka4WxHg3MFoi/idgHjPs\r\n8IMURopnOKzKl74AKseMmXR0u8jslRLaNFO3NPC6Zo9TqKhGqWHJxwpRncS4\r\nbQz0BDpzcjeNT0Gug6S/Nla+Eh8q4NgjO/U=\r\n=T6bB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "77f865da39af5b3e1c114dc347e49257eb3dcfd1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-get-type": "^29.0.0", "pretty-format": "^29.0.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_29.0.3_1662820900980_0.038599840043517464", "host": "s3://npm-registry-packages"}}, "29.1.0": {"name": "jest-leak-detector", "version": "29.1.0", "license": "MIT", "_id": "jest-leak-detector@29.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6b7067d383dbf0804f21e16426d8562d84f19340", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-29.1.0.tgz", "fileCount": 5, "integrity": "sha512-7ZdlIA2UXBIzXBNadta7pohrrvbD/Jp5T55Ux2DE1BSGul4RglIPHt7cZ0V3ll+ppBC1pGaBiWPBfLcQ2dDc3Q==", "signatures": [{"sig": "MEQCIGOJ6cxR3rBRFPOHZVEzoNw5/SgNP97KBnw4q1S2kwb3AiA11OFb1OZ2l26tSV2MiqDltNGpebTOxKZI2SxngajHow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5570, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjM/nEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq1Cg/7BLZZkD6of5B2iGgqllWfxagRoDgcXVYXEyleIK27MC9RYJNQ\r\ngLI6UVInMG/+xUNqnioclmPqIan+fVMVYLtX3K+PAOtmoD7L7cS0DyGSIbkv\r\nYaQS3fjCUSUc3opvCH8+ej+kyKDbLR6Bsm+XBBvev4uHXaRkYlFabyCbaO9h\r\nrESm3rLUAzdCqGHgXQ9HdIWi7QO8d862mV8uQUxZsumYh7VkYTPDydGdVrfS\r\nVA0y+p6dDOAVduHUD9ZDJ0vRT8tngmQauSH6oN1C8xWjeTSTIK4g+gGtH/ap\r\nBhx7ns9RPHMYdahX6UTMN72c2v2i7QkXRryBKnRoLimgr6TOKo1HqR85tF8W\r\neEjDfH75X0R864zX9Fae/9d6OYM2QNTqFRzePt1Jxiq/PcmM4wWyTDIZYnW+\r\nz2zMoL9+nhHigHxyLyoS1TAfOa4Nbkc4GV5KOcAHjXUDY8SXtEsxpSUjWS9Y\r\nbYkyBnOEs7Ll/rjwEkt2dZucc/ydsRRuWlUvKdXsoNh6HqamvKNTcCU2qDg4\r\nV0JK//BD62jFwhH4DrnR9+QECuyShr6ZKwvWF0jHjdviM7Yl7Q1h7Qoqw8ij\r\nHPFaK+fE0XZITMt14fczYKC0HA/I/yjSP8A9g9K2azxF/dapbOLAsnbDMjfK\r\nPvcnturiSSLfefXfruu1DSajXcb/FfZKnQk=\r\n=tWCc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "51f10300daf90db003a1749ceaed1084c4f74811", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-get-type": "^29.0.0", "pretty-format": "^29.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_29.1.0_1664350659990_0.552664249729667", "host": "s3://npm-registry-packages"}}, "29.1.2": {"name": "jest-leak-detector", "version": "29.1.2", "license": "MIT", "_id": "jest-leak-detector@29.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4c846db14c58219430ccbc4f01a1ec52ebee4fc2", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-29.1.2.tgz", "fileCount": 5, "integrity": "sha512-TG5gAZJpgmZtjb6oWxBLf2N6CfQ73iwCe6cofu/Uqv9iiAm6g502CAnGtxQaTfpHECBdVEMRBhomSXeLnoKjiQ==", "signatures": [{"sig": "MEUCIQDqOqJxU/lcGCZrDil0KaZg96D8wslhhzb6S7Ct6IHQGgIgczlOfQgtYDHMEmMuAG/4iNip8db/2FzCODf+bLpxbh0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5570, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNplJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr2sQ/9EQNlgMyQoteaPRdDdY+7HFKxoB/nKRfbJAnhtTdSjpn3GCYq\r\noN9ndHv+89bL68mAVQ5JU+JminwtmdKkmrTmOJCuB7USzHy4OSaWQMj3/RI5\r\nxv+0vsowBgGtqCyKuPo+h3j7uoh55dDdO2fF7P7WmPqqEYuNnREvZ9aoLW/z\r\nzbOBZeUc24GydE6YtfvnvgY1X6mKJNiKic6rALKaHwrhVKZWwagaFFCG3+4K\r\noXnoRPK7kXN+R62ozmj2FmBZF+A5RQO5mljQAeheYWLSoIvu/yhdU+AdKTrh\r\npCTPpoKDJcX+hpLQw0OsOhVgv2GWx57dM6u1s4vsDY7H2qhNnJKNZp6on8lT\r\nevuRMVyIloGNausxth07DdnpcrKH53AU453JUnoHsvATDM1fQvxKR7Ouo6+6\r\njyhT7KNYcOqpNWWlpl0r/xV6GQWDRTWYmQ2EeGmMdSksEJgrP3Xh4V9SPW7+\r\nUTDh9MRjbDCCI4nIfszXM2+IjbzDw5HiaKHitJMxtW7m2i6QXlb15GyCWsMr\r\nWzPOBntUFTWK3ERp2G3FXKer0U1u4C9UpPal2TrQp76fgfkchG7n30XqC8AE\r\nx+vcRCc9Fv3lFr2sacXaYMxGaCCYUj1p9x6ZmK977tjV0gM2yY1qJ11OrHnP\r\nxlkUjsSUf29aWna2ToJ3YcAeZSErpEgfSHk=\r\n=ceDp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "3c31dd619e8c022cde53f40fa12ea2a67f4752ce", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-get-type": "^29.0.0", "pretty-format": "^29.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_29.1.2_1664522568835_0.00240027711739188", "host": "s3://npm-registry-packages"}}, "29.2.0": {"name": "jest-leak-detector", "version": "29.2.0", "license": "MIT", "_id": "jest-leak-detector@29.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7c0eace293cf05a130a09beb1b9318ecc2f77692", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-29.2.0.tgz", "fileCount": 5, "integrity": "sha512-FXT9sCFdct42+oOqGIr/9kmUw3RbhvpkwidCBT5ySHHoWNGd3c9n7HXpFKjEz9UnUITRCGdn0q2s6Sxrq36kwg==", "signatures": [{"sig": "MEQCIAiNc9rK67bTEFZEDfXXGiLxvdW6+K1UIZD/pUrQRpTrAiBNW4BZ7TcF20wJUxOatroML4GXeTU8AqqF3v7dWRFGxQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSShNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp2Nw//WHbwgttW1nQi/M/o4as9qM5NZFilV/VQmbbDOhvpU8YPIsx0\r\nHjXu+CwYKPdEDQ4zi6e/rJcNC1waUcvL6dFIz8qTOTIXQjkEe81g69NgkmAn\r\nvRcesLdn7eYX3d+1dpQofraiDJ8G4uywH7iPSzif2HcV7Xw5VHPJM5DXYPxi\r\n+FVLvIXBE9gAytP7/0ThgZwv7+2iA8RV7F3Y/RWHr66T36wnU1d5TdhTfEAo\r\nMEDpjwh7CzB+jS3+J18HmU7G3Ocqa9sB3cl0QReOpMJ1VLe8tO+5KTUwV1xb\r\n9yGKu+tpXJf0D68Dueya527pCwSes/nyboGoJhKIswuhIulSyNvB2rG/HDN8\r\nKG7p6UNIXtTdESoFDBdNTU0NXCPy869AIApNtt1rIuUOvudfDTR3XW92XVow\r\nUayW0ik+uXNFCukilmTw16mADy5btZuoJObfRuQ7LN8IIOErGbxcnVhtM46G\r\nCjktaEyfjljgHWayR2S3Uk7iJH4ZU0ocW/ZPc9oJ1gu+DHmTLHuezoI8/yZg\r\n0PmtjDlVWs2gtOKEA9C0nZlFIajKrpIIuX7cMHWElbIJi85aPi6sWPD/wcNO\r\n48GFWIHhf7Sj9CDqiAtwIGi9/KZGKznJusW5TXzyzPWZBUmf4hshjGi/Kkn+\r\nprf+9pUlnglvOiAcAeoWuNiaIpfS84xed+0=\r\n=R7uI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ee5b37a4f4433afcfffb0356cea47739d8092287", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-get-type": "^29.2.0", "pretty-format": "^29.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_29.2.0_1665738828837_0.06288014336921788", "host": "s3://npm-registry-packages"}}, "29.2.1": {"name": "jest-leak-detector", "version": "29.2.1", "license": "MIT", "_id": "jest-leak-detector@29.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ec551686b7d512ec875616c2c3534298b1ffe2fc", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-29.2.1.tgz", "fileCount": 5, "integrity": "sha512-1YvSqYoiurxKOJtySc+CGVmw/e1v4yNY27BjWTVzp0aTduQeA7pdieLiW05wTYG/twlKOp2xS/pWuikQEmklug==", "signatures": [{"sig": "MEUCIQCWSpfioLnDp3U/Ma9AKpOXDqMlgfWNsnnRrKOzrx7wyQIgAfcNBk4aoMGCLWi6mMhOG+rIAMpT5E8JunE4Q+BBzuA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTs2NACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpeFw//cVCDuYuN0DNWa5G72lBV9+azsaui/nJhBSpokkZVkwCi4stz\r\nf+eqhBN6zHQJShK/0xC6bdtyctVBGl+Hh05KuYdeLXIRDmPj63/lWVZZU4XQ\r\nbvTHPeg5KyHway2D1LGg6EneHtHFfxXw+hCtLgRGWLvvjtpnzZYFVk8Hx/LL\r\nyxOplPbGF8WOZIwq6re5Tlufr1LpMSVKWHIC6FkJBa9Lo1yrBOpEH64+TCbe\r\n5BAjkJq09Qx7/x71V9bxcrEmCVbqzMYd71FZpTeD5mQb9DPbs2y0TbICSecn\r\n9Q2anGDuu/u/LLU5w4++q38xmUF/EFYkmVkePo4R/z9tL4jMbkK/69yUYHnD\r\n+xSZCL0sJVOvXSUswFz4DeIrzJdR+38ZsgqH9KsZSy6wZ8dGl8ctfQhpCl3P\r\n7pAvlXko49sWInMrwvegqjMWEXj5Lc9S33JjrSnii8rDDHq1T2JlD8OYPrJZ\r\n9D/Fli8zsCtCV42h2IOBoHvT5iCNup9nlG1vIOR6IwDboBrKq25rHidRDMu4\r\ng0YsTA5SkcFtPBQxtOmuhM0c0QZIkoLqsON1uKb43msded/NIoIqznnYY9H7\r\nELfWlIKMYNcMLSeUF6/cP0ALO2vO2CwQ+NCXZUqG1Ne6MsU4A9nSD9UBqOv5\r\neF7uwMwN7n8Na9xaA4Ddp77GPfwcbpOcKVo=\r\n=ypbr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4551c0fdd4d25b7206824957c7bcc6baf61e63bf", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-get-type": "^29.2.0", "pretty-format": "^29.2.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_29.2.1_1666108813394_0.15014944177961542", "host": "s3://npm-registry-packages"}}, "29.3.1": {"name": "jest-leak-detector", "version": "29.3.1", "license": "MIT", "_id": "jest-leak-detector@29.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "95336d020170671db0ee166b75cd8ef647265518", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-29.3.1.tgz", "fileCount": 5, "integrity": "sha512-3DA/VVXj4zFOPagGkuqHnSQf1GZBmmlagpguxEERO6Pla2g84Q1MaVIB3YMxgUaFIaYag8ZnTyQgiZ35YEqAQA==", "signatures": [{"sig": "MEYCIQD99Ll+awyigH4Vh3tm4mXd/B9cQUHvhjVho2M3Cac4GwIhALgVJYHPZx85pE4BZrFuJOwzHzrPo307BMOcatvG25jg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjat6XACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmogKw//Q20rhPuok/5NBG5pEmOtysUYHkN6oU3YpnhGKIz7gwWMTAg/\r\nJWw1/F1sR5rbWsN/awIn/cRLZOIvzEb5+xAoZn1W+aBJfy/TZaExSzuAfAKC\r\nK5/SrwRooUniDtGohd/IrzP0V+He7emi1IUYZaZVsjf3eJiwxaTXp7AE9jvw\r\nRIkxYEW5DgKPooj7PmB+jYHh4PMp/+JhWo/6RzoCqIiGX6F/K+0xV5C150ay\r\nl8IREtE/Gyy+XINwm1byDln80z8OKHnKScMF61j0xy9YxQ+vTsm5W/tUo4dl\r\n1mpou5R1T0kRdp3Y02GfkX0XzbLiOwfxuSY2QFsQ7xkglEASQAoSYUiDR0QB\r\nq3cOvJYoULzOzXpWUw5s1jT5hTGNFt9U1AKF6orUqUVYnqJd7M/TEBGy3jwc\r\ni2n2i7+lkviYpBGbbhRuQwqQAc/B1YyuJien2De9T124/25574UcVImeLRGz\r\n8fM3R85LICfVQYzSSFNcPQm+OkS+gb6m1Fk1/qro75Rcj4O7AJMF3bIZ0TJN\r\npuVOmyrmvUEqrSwVlgX6KMZHW2JiQMIeg+ysF66JpVSD+UBmHhVOjfnhsnHd\r\nppNBB0MMKEzcszubY73q/q7uMRA2q0V4onrWy2J+jRrSPhXQP5oY9n6Thqc7\r\nqzJs0W4T8RGqbO3Izx89o3+Tel453fBYnF4=\r\n=0Ysj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "05deb8393c4ad71e19be2567b704dfd3a2ab5fc9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-get-type": "^29.2.0", "pretty-format": "^29.3.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_29.3.1_1667948183291_0.21150676685887793", "host": "s3://npm-registry-packages"}}, "29.4.0": {"name": "jest-leak-detector", "version": "29.4.0", "license": "MIT", "_id": "jest-leak-detector@29.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5f1079556c244cf6f5f281f5c5134ea2a07ad28e", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-29.4.0.tgz", "fileCount": 6, "integrity": "sha512-fEGHS6ijzgSv5exABkCecMHNmyHcV52+l39ZsxuwfxmQMp43KBWJn2/Fwg8/l4jTI9uOY9jv8z1dXGgL0PHFjA==", "signatures": [{"sig": "MEQCIAMMlJlEVdx9yHlnQxcBNVPFjhZvInMkyIRpfXwZzonGAiBCGATFZ+2K311wW5gSOhz/hE6XXibLX8+5180G0tj83A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8192, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjz7k4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpfyBAAgoWWfujtWRXGtTdSCSGEDkV1I2Z4XcUVJ2TSc5PClQimNwQz\r\nqh/RPZOHVTzo6l1yDWVtHy6tSI3o4ucjCLFTC+3s8C9GaM3v/31syVBiriWo\r\n0KLbCTKmGqjtbCD17YEODt4gJLmhLSYnj/xS2+h1EmZihMXpkt1tB+RJAZgK\r\nbyyPRtoYQnALDYnD0WhugZdQ/HAkXM9CDy3ifiUXME0f4a0eyMsBPQvphf+s\r\n0UwF48A0MxhZbC6J0Q7TIDhL9/qk9QYbe1f4xJH0JxvwZcAzqtsWLm/kSZRh\r\nk5XsSG0lOtSGiavnQgaFiZhNyIJUFmYSQPJaePaa9JAn+KfgTX2n1hFPwI3G\r\nT9Liy5Fep00mIdTXNXCLWDMyW3mUhPFkFajJBBwzB/m3jCD1tvxH4AhhTnHg\r\n37BR5Imib4Zhso9OSqP8W3bGKL+zpUGLpN2OVbguXx8P8bcY7MIeCpKMwSve\r\nxQal2+o0umrfDDbK+Qvn65z5lkZPMAybq2TZe6L/IPCSbD20lSK1ZBV3lbo5\r\nSwFxagNNb3y60LA9B38DHscxnc+Kaa+NQbm9OEkIZqaC8EE1R60/PuYA4t8f\r\nkHC1UT7MAg25BAAvhlVnMP1iJL7GiFizBKs3C7jZQEhA2sssvlqkQtL0i3AY\r\ncyg0+bcfwAhDIQoIggXfQYSDcX9TtFYpnJ0=\r\n=75xS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4bc0e8acaf990e6618a7bed1dca67760c20bb12a", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"jest-get-type": "^29.2.0", "pretty-format": "^29.4.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_29.4.0_1674557752098_0.70452065915519", "host": "s3://npm-registry-packages"}}, "29.4.1": {"name": "jest-leak-detector", "version": "29.4.1", "license": "MIT", "_id": "jest-leak-detector@29.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "632186c546e084da2b490b7496fee1a1c9929637", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-29.4.1.tgz", "fileCount": 6, "integrity": "sha512-akpZv7TPyGMnH2RimOCgy+hPmWZf55EyFUvymQ4LMsQP8xSPlZumCPtXGoDhFNhUE2039RApZkTQDKU79p/FiQ==", "signatures": [{"sig": "MEUCIQCT61YXj8MUOI0whUfJ0hqwgXdO2fVc7a6RSvdsv/sojgIgcQPHVJ4cJ4I6J2yq26JWXFtAdiyd9OOAQskONOhoDgI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8192, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0pd1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpVRQ/7BbovopCf93OMNmK4egL5LxCF4p2QdWXGaUVsH1Ccr7RQmrC1\r\nTbnve7MZxbTlWM1lrsCfyTuemGsrB35/NV05t03WRdtWY/yTpUGhLkEgF02C\r\nWhzfQPauQ2Xyj6oqTezpMWIp1vPZ9H2c/z8IMIc0WYOlpQv+W2VqjrrQoouE\r\nLcCeEAleL0r4He2NXQLcVpB4nVWxWUFNe69Fk5w0SGxjxYR+ttzhlM7ZtrAY\r\n7Hmla4yHVqWeKjI6UyFeE4HYrijKREl85bdduMbE3zFTls5NdN3PFejSua5l\r\naPL2U0EqvPnt3ie6ixd+j86ImRyFz60fa2YfR2f3K+kQz7yIMkpsQHqklIx6\r\n9xK5LZvwzLCBXZMyCnB3D3KB57NWZr3P2gOxo1FpTOslOYpFt5mAnyOxnEfK\r\njRGzl3o6/6lcht1C48//AeHYFWIUML/PeEObbjpK6ZibsQA2jw279kHzZS3D\r\n9SPsyepgnvHdCObzMyL0L18StFmn2I0Smyry3uWXwcvu1tk4iScw+HxTSGpp\r\n7tQqGSw9QSeaQYSIqbgpUfjZwoaYHf8+d7+gH+TvFRVeb8zlfdfAlv0Lg2PR\r\nmE8Jr3mhuFheD94LyAurhJ0IIb3vD1a5J5fjKz5uwaqEhKPx6LFB+fNtYyow\r\nzYQcDv3qn+njraDb5/J3NF3jkzQT3dYWgUw=\r\n=dOgf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "bc84c8a15649aaaefdd624dc83824518c17467ed", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"jest-get-type": "^29.2.0", "pretty-format": "^29.4.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_29.4.1_1674745716935_0.5527650799996071", "host": "s3://npm-registry-packages"}}, "29.4.2": {"name": "jest-leak-detector", "version": "29.4.2", "license": "MIT", "_id": "jest-leak-detector@29.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8f05c6680e0cb46a1d577c0d3da9793bed3ea97b", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-29.4.2.tgz", "fileCount": 5, "integrity": "sha512-Wa62HuRJmWXtX9F00nUpWlrbaH5axeYCdyRsOs/+Rb1Vb6+qWTlB5rKwCCRKtorM7owNwKsyJ8NRDUcZ8ghYUA==", "signatures": [{"sig": "MEUCIF6bB4+/uVMpUfOyZ3fiC0gvt0GFARtXLdTlreVdcf06AiEA9/8IJQpoQmPyt/e2BwhjdL1zJoEsZQgjDc9ZPZMiBcs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5627, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4lX5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrU9A/9Ff75DuBFwwy2RTCxD7frazwoaDDUZ3VNrNrAspIa1NasfBdL\r\nWniWGIyZXcKwL0sTwoInHCXj8ykzghjPFvIoaVHc9uWtopuHFpos3C4tvKbz\r\nFyf9ZP51dwX0tPhBV5ZVYH7Hnwg/qXaG2lWmkWbvNBlVyf06gT+m17F+kzxM\r\nr5RpJiKRzW1C/hp/esU90ojD3ZvtxRoEQPtmQfYMSJ0WsBgQXR5Nh/qGOD1Z\r\nBaZvB+99ZtX/qVogMw7GI6b0BuXVM0Mhrann16rM/Hj3bu18b3Ke6nNvDssb\r\naETcd2N9FBjM6Y7vLVUQGxAk5TGDOm+pqykKjzdVkHCIa4fgGCgENn/hnmSC\r\nCJv/NVfUhuANA+ShtV/vYur+UIlgvRpIgoJy7wrqm9uEmmzoUOE75ow+pF8c\r\nd5GabIqJPUGObSaN5h4RCZLwoxVVRGz54fRTaXet3r2OBiTbLbGgevazCt7G\r\npaEO+I0cAT6cX+5tzB2i2CEENSBspIQhiVOuri5zZeK2YAX7OSu4dAw5EZb3\r\n6wE5zaFidAPQbC2vJUHPvLYn1Th+FLPYjkjbtutHXNlyExHqAhYLCgcY2FPB\r\nOPXuSDFp146BVg/M9KAX0/5B4bYrPNatTu5bdRBkrOQ+T+jT04uBEhJcrJY/\r\n14MKo7iuh0myyKu+qTqFk9HxeHmESke531U=\r\n=vmDv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f0fc92e8443f09546c7ec0472bf9bce44fe5898f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"jest-get-type": "^29.4.2", "pretty-format": "^29.4.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_29.4.2_1675777528794_0.4510979104286752", "host": "s3://npm-registry-packages"}}, "29.4.3": {"name": "jest-leak-detector", "version": "29.4.3", "license": "MIT", "_id": "jest-leak-detector@29.4.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2b35191d6b35aa0256e63a9b79b0f949249cf23a", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-29.4.3.tgz", "fileCount": 5, "integrity": "sha512-9yw4VC1v2NspMMeV3daQ1yXPNxMgCzwq9BocCwYrRgXe4uaEJPAN0ZK37nFBhcy3cUwEVstFecFLaTHpF7NiGA==", "signatures": [{"sig": "MEQCIDPU+PGC4fpENisMKWeev3AFns0XUZzASlFPMeOP2Z4YAiBrv659YlnZOD1IVIpyQWYQsx/MSzn4+menxSHIL0qBRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5591, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7MiiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr0jBAAljk34pA9C9MTOnTUrqG9Aurp9FwM89rsZtecivaHh6adP+Lg\r\nL1UD8zilbYZw8y8365hF/JN4zeFheMkHSEVII4bZxKl8CxIas1ud9wYkrh8Z\r\nxY62eqWJwlrWrrsiVYeBfyXtm3qhD5EmCQl8tRZiw4GYo5IJIGE0XrQopCJV\r\ntkV/Pv9ptMEd2UTjDMQb+9Lty8Xd1pDrM64+4EKxiiNGgaD0cRIX6M4//4Nr\r\nFh+rKADPc0w0IVW/ESH1Gl/xooIW/RSyHRcbO/DGRrk7xWGdclOKAtgDelmD\r\nP4hfnmU3Pf/yPOc3tajRfLfytr59nbRswUnTa8KhON39Wvf1O6hN6OLS7TUu\r\n82wglZbKBViTmpsXDfMfrImI2smRh/zj0nqr7GPuAhE7gWPARk8TA8iZSA7g\r\nfmA7c2oQ0FlTQhSxhEuCoZMNaPFZPHsEd9twixSYOzoQMduwJaQuz2KZtwne\r\nJm6EKMQymkuCKKUNGK41tjsihjhHeL6GNhp/9TOsDsMS3RWoSBxDFHfo40zM\r\nnyV8b9stLRotHtxtjCser5SMC6dYqISlsJ418hR7MOtJZZXbAAjoF9Z9PGh/\r\nvucZtnt3FFTE1IP/JPnaagolojDBAcWtcFN2oSwj8JK+XvE37DeuGGZJjuzH\r\nizzZCfLxq/cX1TeHEla/9OTqgDlJ+hzFMc8=\r\n=emsV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a49c88610e49a3242576160740a32a2fe11161e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/1.13.0/node@v18.14.0+arm64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"jest-get-type": "^29.4.3", "pretty-format": "^29.4.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_29.4.3_1676462242190_0.9324309911274566", "host": "s3://npm-registry-packages"}}, "29.5.0": {"name": "jest-leak-detector", "version": "29.5.0", "license": "MIT", "_id": "jest-leak-detector@29.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "cf4bdea9615c72bac4a3a7ba7e7930f9c0610c8c", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-29.5.0.tgz", "fileCount": 5, "integrity": "sha512-u9YdeeVnghBUtpN5mVxjID7KbkKE1QU4f6uUwuxiY0vYRi9BUCLKlPEZfDGR67ofdFmDz9oPAy2G92Ujrntmow==", "signatures": [{"sig": "MEYCIQDnB71rsPARHGbaHmnNat78z5n8XRzqcegXOItVRekcjAIhAPuYizPOzZniD6Err0hRjcVSD5aZ+VGjWP70Dh66Dnbo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5591, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBeuqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo6Fw/6Avi6okdgsy9671TWT6vzuoTGGKuBVy4x7dRZ7XLkid1JCAX0\r\nR2ZopitL1HFj+eXccoobsRRNFr8XpVNkaTmGzsbNOumpRRQmN+jnW6Tr3jyq\r\nnUZDYwaCbLYCymkKoJyoYysLV9kF92y7O+V2dDfQyE2XpJnERzXiwTw5oQqz\r\nOG9kMPOTsuWK9l1wX4Zvfu+JhQD1pe+1BlER9jTGS5gxAEVu/0c3ysivOBfV\r\nj6r7To2UTzU0dZ2+QMa5YKGWaE0ZlzF/Z4MRJyU8goV7PQdflkaKjZzIkg/3\r\neqUNOkXc9CtWAzWZadsiIPyqHYegqrPqcaVzV91Kii+5kiE6Aeo0QVCQ9Thd\r\nF7ZiQz2+qalmblHPIqOGPPWHjnnRlwmSBmfqspK1BllHeMYg6PwmfHvI3AwB\r\nUgCIhpRA5kH1ITpXueiN8J6jYz/79c96JrjAK+h3Cza2nCWQuBKJJ6dZGhYK\r\nwtpc93tM8JZVWzWJ6RlHZX/88ugFPLxA7BKVs4to9Mi4tS9Vpf4ueoMzcix+\r\nlW/pULaz3EojPJbjgqjZ+1ddXxQLIWB7ATe6t8f+9kCvsag6H6zm/1rRhtZt\r\ny2HH4TzyZKCvwkUe+LPfmoHGdHHO4RlZa0GTBagYCpXw3j4r77dLNBhlhc2O\r\n07MUaOXZd6XEM8xXAmZ1q5XJYQE18BBWZpw=\r\n=cmw5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "39f3beda6b396665bebffab94e8d7c45be30454c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/1.13.0/node@v18.14.2+arm64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "18.14.2", "dependencies": {"jest-get-type": "^29.4.3", "pretty-format": "^29.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_29.5.0_1678109610429_0.002404314538319463", "host": "s3://npm-registry-packages"}}, "29.6.0": {"name": "jest-leak-detector", "version": "29.6.0", "license": "MIT", "_id": "jest-leak-detector@29.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9b96d275622739b4436ee7e91b3f3d386471105c", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-29.6.0.tgz", "fileCount": 5, "integrity": "sha512-JdV6EZOPxHR1gd6ccxjNowuROkT2jtGU5G/g58RcJX1xe5mrtLj0g6/ZkyMoXF4cs+tTkHMFX6pcIrB1QPQwCw==", "signatures": [{"sig": "MEYCIQC6tUa/mNDzUr5pYPgqVSmq4rhTUrDCy8rsX1U8G1KIQQIhANa8uK5RLP+gCr/py4677D6NW4xe7YSOAd/QDuNrSn4g", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5591}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c1e5b8a38ef54bb138409f89831942ebf6a7a67e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"jest-get-type": "^29.4.3", "pretty-format": "^29.6.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_29.6.0_1688484346673_0.8678929682968215", "host": "s3://npm-registry-packages"}}, "29.6.1": {"name": "jest-leak-detector", "version": "29.6.1", "license": "MIT", "_id": "jest-leak-detector@29.6.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "66a902c81318e66e694df7d096a95466cb962f8e", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-29.6.1.tgz", "fileCount": 5, "integrity": "sha512-OrxMNyZirpOEwkF3UHnIkAiZbtkBWiye+hhBweCHkVbCgyEy71Mwbb5zgeTNYWJBi1qgDVfPC1IwO9dVEeTLwQ==", "signatures": [{"sig": "MEUCIGV/wBJXNROukt9m3KQCOwXTIKKINoupbbOY4R+jZmJUAiEAqRc84Hl1SA+DMQloAV78oeugoKHfEikRLVhaRM0/0+w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5591}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "1f019afdcdfc54a6664908bb45f343db4e3d0848", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"jest-get-type": "^29.4.3", "pretty-format": "^29.6.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_29.6.1_1688653099986_0.16746846955184336", "host": "s3://npm-registry-packages"}}, "29.6.2": {"name": "jest-leak-detector", "version": "29.6.2", "license": "MIT", "_id": "jest-leak-detector@29.6.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e2b307fee78cab091c37858a98c7e1d73cdf5b38", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-29.6.2.tgz", "fileCount": 5, "integrity": "sha512-aNqYhfp5uYEO3tdWMb2bfWv6f0b4I0LOxVRpnRLAeque2uqOVVMLh6khnTcE2qJ5wAKop0HcreM1btoysD6bPQ==", "signatures": [{"sig": "MEYCIQDovOfefodywmozEUgdnvMQCNwgGY4N/hfEVWRYKUMLNQIhAP2phaieJsQFHujQX5seKkTC23N9Gvw+88ggRc8L9kBq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5591}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0fd5b1c37555f485c56a6ad2d6b010a72204f9f6", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"jest-get-type": "^29.4.3", "pretty-format": "^29.6.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_29.6.2_1690449691332_0.3605218492716842", "host": "s3://npm-registry-packages"}}, "29.6.3": {"name": "jest-leak-detector", "version": "29.6.3", "license": "MIT", "_id": "jest-leak-detector@29.6.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "b9661bc3aec8874e59aff361fa0c6d7cd507ea01", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-29.6.3.tgz", "fileCount": 5, "integrity": "sha512-0kfbESIHXYdhAdpLsW7xdwmYhLf1BRu4AA118/OxFm0Ho1b2RcTmO4oF6aAMaxpxdxnJ3zve2rgwzNBD4Zbm7Q==", "signatures": [{"sig": "MEUCICT3511DwE0WIvGIPQyj9UrXuwMxUrk32hq0xwkxwJX7AiEAmZB4yFzQVZNtPd76zYJ6ARn9cPQeI/31dWoTMxZX1+8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5589}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fb7d95c8af6e0d65a8b65348433d8a0ea0725b5b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"jest-get-type": "^29.6.3", "pretty-format": "^29.6.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_29.6.3_1692621555989_0.2843978511030971", "host": "s3://npm-registry-packages"}}, "29.7.0": {"name": "jest-leak-detector", "version": "29.7.0", "license": "MIT", "_id": "jest-leak-detector@29.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "5b7ec0dadfdfec0ca383dc9aa016d36b5ea4c728", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-29.7.0.tgz", "fileCount": 5, "integrity": "sha512-kYA8IJcSYtST2BY9I+SMC32nDpBT3J2NvWJx8+JCuCdl/CR1I4EKUJROiP8XtCcxqgTTBGJNdbB1A8XRKbTetw==", "signatures": [{"sig": "MEUCIBcSJ+ZRGPzFnevmitgUGFxJIuR/zQ7qfvFSdTotHfDZAiEAnKkd3E/46WuCMUiN0fQtkmlOLnHjfaoRgprlxn3IMwI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5589}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4e56991693da7cd4c3730dc3579a1dd1403ee630", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"jest-get-type": "^29.6.3", "pretty-format": "^29.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_29.7.0_1694501023593_0.1908251006937869", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.1": {"name": "jest-leak-detector", "version": "30.0.0-alpha.1", "license": "MIT", "_id": "jest-leak-detector@30.0.0-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "a9b2914d5339f624e054f8554fa72faa301f96df", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-30.0.0-alpha.1.tgz", "fileCount": 6, "integrity": "sha512-l1RaMjSnvSFw7doSHAWUr/DTtJhVIx4p1Nz6ZlNvQ9/ab6NG4cr3ONu+DZRMpyOp0JYyP8/tZ34p1+mxuFDikw==", "signatures": [{"sig": "MEUCIHNA0vCd0X8/Ox6L+tvWcyDUsX7IQ69pjmBRqwb3dvB8AiEAu5MKiqVNX8I2i8vEardX40vfBZa4EsotWeSViwvAPXs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6529}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d005cb2505c041583e0c5636d006e08666a54b63", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/1.13.0/node@v20.9.0+arm64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"jest-get-type": "30.0.0-alpha.1", "pretty-format": "30.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_30.0.0-alpha.1_1698672790881_0.6519052887533769", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.2": {"name": "jest-leak-detector", "version": "30.0.0-alpha.2", "license": "MIT", "_id": "jest-leak-detector@30.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "58d770e9a3e2a914316b840ba03d906dc0bf6545", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-30.0.0-alpha.2.tgz", "fileCount": 6, "integrity": "sha512-wvG6Cntan7malgaVZiCcEd9dslPjDsHc8g3JdJWCCpEnZYSMEnEJXzmBHQpOVJnXxP2NsFdatzhOrWHQKhK4KA==", "signatures": [{"sig": "MEYCIQCc/QC3KRYibVZSCJ5OF5oIKOrA6GVEo9+tTngmPgOH8gIhAP1KXdJ7dQoH4+bIo3uZH+M31f18VH3NoBqOuEiSuVhf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6530}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c04d13d7abd22e47b0997f6027886aed225c9ce4", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/2.7.0/node@v20.9.0+arm64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"jest-get-type": "30.0.0-alpha.2", "pretty-format": "30.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_30.0.0-alpha.2_1700126900817_0.08242080987209022", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.3": {"name": "jest-leak-detector", "version": "30.0.0-alpha.3", "license": "MIT", "_id": "jest-leak-detector@30.0.0-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "a4109b17f248f5f5d8241962b1eca0c75573d9a8", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-30.0.0-alpha.3.tgz", "fileCount": 6, "integrity": "sha512-g9ATCuifGCIKwlyct2GogQZQA+QEK0CESkM9MfZ4+6heJDtHijJ9XBvmAonVFUA7wPAVp3hx6oM6YUxfEEHZgQ==", "signatures": [{"sig": "MEQCIC/77gpFRZZgi5srbKYtiJZ3XI4kGv5TSuArfVykLvxfAiApMKV1ioXr53nVLp0SyNCdctOfUpt0R+XJqKR90N8Q/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6530}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "e267aff33d105399f2134bad7c8f82285104f3da", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.2.1/node@v20.11.1+arm64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"jest-get-type": "30.0.0-alpha.3", "pretty-format": "30.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_30.0.0-alpha.3_1708427346580_0.8541885828434792", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.4": {"name": "jest-leak-detector", "version": "30.0.0-alpha.4", "license": "MIT", "_id": "jest-leak-detector@30.0.0-alpha.4", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "b152574ad5829af1c6f416f31fc2f423db11ee6b", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-30.0.0-alpha.4.tgz", "fileCount": 6, "integrity": "sha512-vTdkvX49K5R0Dybliz2acVmI1hbfYfgvWHUULs63FCT08Mw6Q8GvGRQiaEg3DZf01MYO6Ta7/HlVedHwpK7lxw==", "signatures": [{"sig": "MEYCIQCAGSRiCQlKPQwPLG4qhRWFhBVasi3/Z/ukUDWepYrGbgIhAKsEUj4OOm4IikcgJYnnLbJOZnlZaqbiZ3aO6TsKep+P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6574}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "32b966f988d47a7673d2ef4b92e834dab7d66f07", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"jest-get-type": "30.0.0-alpha.4", "pretty-format": "30.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_30.0.0-alpha.4_1715550203869_0.8518125700406023", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.5": {"name": "jest-leak-detector", "version": "30.0.0-alpha.5", "license": "MIT", "_id": "jest-leak-detector@30.0.0-alpha.5", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "aece5c6ba1dff2cabd32e8e14f00b18595761c88", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-30.0.0-alpha.5.tgz", "fileCount": 6, "integrity": "sha512-GTvYhmVSWajONNYHy6XsEjXbz/d97kmyn9JLzT5Cdlzxftwx1j/1c6HrTIYTQPC1JnYoVty5GQRMZilXgSi5kA==", "signatures": [{"sig": "MEUCIQCRCYuIink13Z5nvWfpy/2klx/IsKz8YUYzl2fSCbQCrAIgeTCxnaeS1tOhXfGRJki1O/Uza4Nv/VOA4lfUqLTHrgg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6574}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fa24a3bdd6682978d76799265016fb9d5bff135e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "description": "Module for verifying whether an object has been garbage collected or not.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"jest-get-type": "30.0.0-alpha.5", "pretty-format": "30.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_30.0.0-alpha.5_1717073044783_0.6791180462616886", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.6": {"name": "jest-leak-detector", "version": "30.0.0-alpha.6", "license": "MIT", "_id": "jest-leak-detector@30.0.0-alpha.6", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "dist": {"shasum": "23109e249b53c3dd9a72cab031ffb9678c5a0f00", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-30.0.0-alpha.6.tgz", "fileCount": 6, "integrity": "sha512-a6fh/6h6dCDyj+aplGqkajVqzmi+qYHs5X8orMZv+u56++gUezJZJf8GCiQqw2vtxcsWVPUuQXa3kF33tAYzNQ==", "signatures": [{"sig": "MEUCIFtgZ/RrHn/n9okkpTmZWLIhP1MYwU4J19EdbLBCwK+XAiEA91gbmwyoMqc4UTSoHYWdFfjdxERNp4hy4NeaMDbH2d8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6495}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ba74b7de1b9cca88daf33f9d1b46bfe2b7f485a5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.7.1/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"jest-get-type": "30.0.0-alpha.6", "pretty-format": "30.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_30.0.0-alpha.6_1723102985416_0.05078839954110825", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.7": {"name": "jest-leak-detector", "version": "30.0.0-alpha.7", "license": "MIT", "_id": "jest-leak-detector@30.0.0-alpha.7", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "dist": {"shasum": "86bdeedb6721c61d0bd9043b6e33f86b8839ad85", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-30.0.0-alpha.7.tgz", "fileCount": 6, "integrity": "sha512-MdS+e4GnipvUsh7WBYz+hFLTiLhn28asqAFugUKL8IDTHJRhwKi0QRaKRFUXDyA/RX3HWuobeQisR/YMjdsgcw==", "signatures": [{"sig": "MEUCIQDtju8ngB7v62pHYFPVqr/kyrngeP3n7jotqn9lyyGbIQIgPNE6JaHWPifM7TznQnPZ+ywJMufAfUWXN6ckE7XKhoI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6496}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "bacb7de30d053cd87181294b0c8a8576632a8b02", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.11.0/node@v20.18.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"jest-get-type": "30.0.0-alpha.7", "pretty-format": "30.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_30.0.0-alpha.7_1738225712180_0.9881813525585179", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.2": {"name": "jest-leak-detector", "version": "30.0.0-beta.2", "license": "MIT", "_id": "jest-leak-detector@30.0.0-beta.2", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "dist": {"shasum": "f7a50b5c5d8c13f0626153efd06962dd79074d9d", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-30.0.0-beta.2.tgz", "fileCount": 6, "integrity": "sha512-3BNPJMhfh7AVTSsaG0yPgUS9UbRgyN+cXixokgyR1Z3MAPCGcxBo2ckq5Bbe4pMqzsusoc7m4+Ekco/vKU1hRA==", "signatures": [{"sig": "MEUCIAFqfHObm1f28KpPTzeG/I1TmpSFfMUr57XAdjLzXST4AiEAvLCkpnUdpUTsb3OEUmQT/r2uEym4RGtY2XB8Of1KJoM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6482}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "53a5635ac9a43099033f6103e179b13a5465e017", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"pretty-format": "30.0.0-beta.1", "@jest/get-type": "30.0.0-beta.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_30.0.0-beta.2_1748308996582_0.37128766783682554", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.3": {"name": "jest-leak-detector", "version": "30.0.0-beta.3", "license": "MIT", "_id": "jest-leak-detector@30.0.0-beta.3", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "dist": {"shasum": "0205eced17a49151fce07a967a6272b2efab2f19", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-30.0.0-beta.3.tgz", "fileCount": 6, "integrity": "sha512-5Z5pca/M/OMVnTH+9UBkPOWKrdwYaq7qx2IAKfoO9suTM8x8/Zv2XmQ43e5JuVhbMzmEK2JXotj7xGGkMPC6pg==", "signatures": [{"sig": "MEYCIQDMj5zpjbDzt5L2GDZM+iE8dJADpSPDcf4LQ1d/lGko+AIhAL+4IvibnBqcXE4p6ibw8SeWObOhehARSocPI2kSxR5A", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6482}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a123a3b667a178fb988662aaa1bc6308af759017", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"pretty-format": "30.0.0-beta.3", "@jest/get-type": "30.0.0-beta.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_30.0.0-beta.3_1748309266046_0.8774305496354922", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.6": {"name": "jest-leak-detector", "version": "30.0.0-beta.6", "license": "MIT", "_id": "jest-leak-detector@30.0.0-beta.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "0d6af93df11808dd029a6fbeb362424f0df01fc6", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-30.0.0-beta.6.tgz", "fileCount": 6, "integrity": "sha512-AcByHGXSltZQL+2wK89+GMPbln52JJkg/n6JZ8h7pY7UbbpUyyyK3zzSRKR8STEgMH1PX9+eyqAQNYF7BTzmug==", "signatures": [{"sig": "MEUCIQD/3lscku89doaV0EyGEOZUl/cLPUN1i+4zkKL3HnUcPwIgHurFm9Es9aRcAaPI9q0VMVvx5y41FZ9k7bPeelKSC2M=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6493}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4f964497dc21c06ce4d54f1349e299a9f6773d52", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/3.12.3/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"pretty-format": "30.0.0-beta.6", "@jest/get-type": "30.0.0-beta.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_30.0.0-beta.6_1748994647539_0.9828163059404156", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.7": {"name": "jest-leak-detector", "version": "30.0.0-beta.7", "license": "MIT", "_id": "jest-leak-detector@30.0.0-beta.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "43e2e95651b7f32d84c578c2fc05db2fd79bc81a", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-30.0.0-beta.7.tgz", "fileCount": 6, "integrity": "sha512-xLiR2mzSh5C3OEyWJUss7WLHlKaZ20hgUwqILVF57IolSxjM5WFD/gqtHZtGU5xVlTRjN6RsRrXHowfYwHoY2w==", "signatures": [{"sig": "MEQCIGe7Nwn8o+7uKGribQo+ek6n4FnMxouoTt5JyodkWj9DAiA9rmUu61a9erJUWpM9NpQiR25MddQ0dmYzkSdb58HQxg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6493}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "48de6a91368727d853d491df16e7d00c1f323676", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"pretty-format": "30.0.0-beta.7", "@jest/get-type": "30.0.0-beta.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_30.0.0-beta.7_1749008140876_0.4622129828931132", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.8": {"name": "jest-leak-detector", "version": "30.0.0-beta.8", "license": "MIT", "_id": "jest-leak-detector@30.0.0-beta.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "5f8c706d4c973c21c13edc382ff005e9216ec905", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-30.0.0-beta.8.tgz", "fileCount": 6, "integrity": "sha512-8kyhNFH24mVhjp3c/S8XLbWbPwnAbHtCaHx6j78NW8v9mg/SF7cNFi5WZuamK7hDmsDe3ZeEJ7QHmiEP1yOrPQ==", "signatures": [{"sig": "MEUCIQC+wfnq7Ri7WJo2k6lcq4Jrsp1/hnfDiJDhKmpfNdgmGgIgfR6U0ECvLjtxiDmp6uzn7in3nHJB5uBbmwxbQYK5/Hw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6493}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ac334c0cdf04ead9999f0964567d81672d116d42", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"pretty-format": "30.0.0-beta.8", "@jest/get-type": "30.0.0-beta.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_30.0.0-beta.8_1749023589901_0.7431927327406556", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-rc.1": {"name": "jest-leak-detector", "version": "30.0.0-rc.1", "license": "MIT", "_id": "jest-leak-detector@30.0.0-rc.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "c07a1c741bb8358aa7c025caed5a3c1a60548d49", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-30.0.0-rc.1.tgz", "fileCount": 6, "integrity": "sha512-0QJ4jniYIbt7zAbewlkiSNd9OVV4xyLXuNn9IN52f//G+vBG8JL8EOQzQwghTg/RJ/QGNo/puEyXyGSiDV9Ptw==", "signatures": [{"sig": "MEUCIDNwlOnHw1gsduHYLtMupWzWPkGo7RJlwn4+FzBnG1+iAiEAjKcDmFeHiJwnnugYACfu/YRU9r/6ul7RRJm5DSfVq5g=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6489}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ce14203d9156f830a8e24a6e3e8205f670a72a40", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"pretty-format": "30.0.0-rc.1", "@jest/get-type": "30.0.0-beta.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_30.0.0-rc.1_1749430963888_0.999371449167842", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0": {"name": "jest-leak-detector", "version": "30.0.0", "license": "MIT", "_id": "jest-leak-detector@30.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "056d168e6f308262b40ad05843723a52cdb58b91", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-30.0.0.tgz", "fileCount": 6, "integrity": "sha512-E/ly1azdVVbZrS0T6FIpyYHvsdek4FNaThJTtggjV/8IpKxh3p9NLndeUZy2+sjAI3ncS+aM0uLLon/dBg8htA==", "signatures": [{"sig": "MEYCIQCi4t3Dy93nRoGg3UdyVSsJIoqRrFhGLvZ5gu1GXbgxPgIhAMwkGj96TlF5deQpc7ghBEu8PVjltQuQ/QfhqCFz9Rye", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6472}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a383155cd5af4539b3c447cfa7184462ee32f418", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"pretty-format": "30.0.0", "@jest/get-type": "30.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_30.0.0_1749521750396_0.8611619495599314", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.1": {"name": "jest-leak-detector", "version": "30.0.1", "license": "MIT", "_id": "jest-leak-detector@30.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "0c0adef6a7475d90fb3bb1f279e1d27568eb1500", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-30.0.1.tgz", "fileCount": 6, "integrity": "sha512-67NTiVwvaI5K35oEy2Z3Xo6z4WIzSgcw08AEUXTcgNxhu8D8A7jOol/9YqA6ZJMVXC0QttsU7fxMOJYee08n0A==", "signatures": [{"sig": "MEYCIQCwfKEyyjgLrC3ZKH0GCfQG3UbW8uCWuAd1Evj6TqP5vgIhAJXR0ViAQtfgcupO3IdvL7KE3y3L1CNHnPGCf7FibuTl", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 6472}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "5ce865b4060189fe74cd486544816c079194a0f7", "_npmUser": {"name": "cpojer", "actor": {"name": "cpojer", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-leak-detector"}, "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.2.0", "dependencies": {"pretty-format": "30.0.1", "@jest/get-type": "30.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/jest-leak-detector_30.0.1_1750285888395_0.6615932730791569", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.2": {"name": "jest-leak-detector", "version": "30.0.2", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-leak-detector"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@jest/get-type": "30.0.1", "pretty-format": "30.0.2"}, "devDependencies": {"@types/node": "*"}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "393acbfac31f64bb38dff23c89224797caded83c", "_nodeVersion": "24.2.0", "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "_id": "jest-leak-detector@30.0.2", "dist": {"integrity": "sha512-U66sRrAYdALq+2qtKffBLDWsQ/XoNNs2Lcr83sc9lvE/hEpNafJlq2lXCPUBMNqamMECNxSIekLfe69qg4KMIQ==", "shasum": "da4df660615d170136d2b468af3bf1c9bff0137e", "tarball": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-30.0.2.tgz", "fileCount": 6, "unpackedSize": 6472, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCcfZM6azIW6mbIk710VhNhORkv23wJRa8RoiWArgJvlQIhAKJRqnM9ZmfbKThucXeBoeP6ST8VQLzA0pY5W5LMPbuZ"}]}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>", "actor": {"name": "cpojer", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/jest-leak-detector_30.0.2_1750329978289_0.5194065502867911"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-11-11T20:14:52.846Z", "modified": "2025-06-19T10:46:18.629Z", "0.0.0": "2017-11-11T20:14:52.846Z", "21.3.0-beta.9": "2017-11-22T13:17:40.205Z", "21.3.0-beta.10": "2017-11-25T12:39:30.032Z", "21.3.0-beta.11": "2017-11-29T14:31:24.986Z", "21.3.0-beta.12": "2017-12-05T18:48:41.006Z", "21.3.0-beta.13": "2017-12-06T14:37:28.266Z", "21.3.0-beta.14": "2017-12-12T10:52:41.136Z", "21.3.0-beta.15": "2017-12-15T13:27:44.712Z", "22.0.0": "2017-12-18T11:03:29.516Z", "22.0.1": "2017-12-18T20:29:29.605Z", "22.0.2": "2017-12-19T13:53:08.679Z", "22.0.3": "2017-12-19T14:59:00.251Z", "22.0.5": "2018-01-09T15:09:57.477Z", "22.0.6": "2018-01-11T09:46:50.603Z", "22.1.0": "2018-01-15T11:57:22.970Z", "22.4.0": "2018-02-20T12:03:35.867Z", "22.4.3": "2018-03-21T16:09:07.618Z", "23.0.0-alpha.2": "2018-03-26T10:40:51.038Z", "23.0.0-alpha.4": "2018-03-26T12:31:43.786Z", "23.0.0-alpha.5": "2018-04-10T19:18:22.777Z", "23.0.0-alpha.5r": "2018-04-11T05:52:52.028Z", "23.0.0-alpha.6r": "2018-04-12T07:01:37.718Z", "23.0.0-alpha.7": "2018-04-17T18:55:29.400Z", "23.0.0-beta.0": "2018-04-20T10:10:58.321Z", "23.0.0-beta.1": "2018-04-21T15:44:31.866Z", "23.0.0-beta.2": "2018-04-26T21:17:44.524Z", "23.0.0-alpha.3r": "2018-04-30T13:10:24.772Z", "23.0.0-beta.3r": "2018-04-30T13:15:01.518Z", "23.0.0-charlie.0": "2018-05-02T10:56:34.214Z", "23.0.0-charlie.1": "2018-05-03T12:10:23.929Z", "23.0.0-charlie.2": "2018-05-15T09:51:38.117Z", "23.0.0-charlie.3": "2018-05-22T14:59:10.399Z", "23.0.0-charlie.4": "2018-05-23T10:42:34.636Z", "23.0.0": "2018-05-24T17:26:35.213Z", "23.0.1": "2018-05-27T15:31:15.225Z", "23.2.0": "2018-06-25T14:05:21.563Z", "23.5.0": "2018-08-10T13:51:50.254Z", "23.6.0": "2018-09-10T12:42:59.947Z", "24.0.0-alpha.0": "2018-10-19T12:12:51.759Z", "24.0.0-alpha.1": "2018-10-22T15:36:07.521Z", "24.0.0-alpha.2": "2018-10-25T10:50:56.853Z", "24.0.0-alpha.4": "2018-10-26T16:33:20.973Z", "24.0.0-alpha.5": "2018-11-09T13:12:51.141Z", "24.0.0-alpha.6": "2018-11-09T17:49:47.472Z", "24.0.0-alpha.7": "2018-12-11T16:17:56.649Z", "24.0.0-alpha.8": "2018-12-13T19:47:59.099Z", "24.0.0-alpha.9": "2018-12-19T14:23:57.795Z", "24.0.0-alpha.10": "2019-01-09T17:02:44.994Z", "24.0.0-alpha.11": "2019-01-10T18:33:06.779Z", "24.0.0-alpha.12": "2019-01-11T14:59:20.972Z", "24.0.0-alpha.13": "2019-01-23T15:15:34.195Z", "24.0.0-alpha.15": "2019-01-24T17:52:39.361Z", "24.0.0-alpha.16": "2019-01-25T13:42:08.967Z", "24.0.0": "2019-01-25T15:05:04.081Z", "24.2.0-alpha.0": "2019-03-05T14:47:02.757Z", "24.3.0": "2019-03-07T12:59:47.029Z", "24.3.1": "2019-03-07T23:12:26.727Z", "24.4.0": "2019-03-11T14:57:55.847Z", "24.5.0": "2019-03-12T16:36:37.718Z", "24.6.0": "2019-04-01T22:26:32.411Z", "24.7.0": "2019-04-03T03:55:26.512Z", "24.8.0": "2019-05-05T02:02:28.176Z", "24.9.0": "2019-08-16T05:56:00.638Z", "25.0.0": "2019-08-22T03:24:08.816Z", "25.1.0": "2020-01-22T01:00:01.430Z", "25.2.0-alpha.86": "2020-03-25T17:16:33.338Z", "25.2.0": "2020-03-25T17:58:07.328Z", "25.2.1-alpha.1": "2020-03-26T07:54:25.873Z", "25.2.1-alpha.2": "2020-03-26T08:10:37.888Z", "25.2.1": "2020-03-26T09:01:19.032Z", "25.2.3": "2020-03-26T20:24:49.920Z", "25.2.6": "2020-04-02T10:29:23.610Z", "25.3.0": "2020-04-08T13:21:23.637Z", "25.4.0": "2020-04-19T21:50:31.638Z", "25.5.0": "2020-04-28T19:45:27.255Z", "26.0.0-alpha.0": "2020-05-02T12:13:05.478Z", "26.0.0-alpha.1": "2020-05-03T18:48:07.003Z", "26.0.0-alpha.2": "2020-05-04T16:05:34.741Z", "26.0.0": "2020-05-04T17:53:11.385Z", "26.0.1-alpha.0": "2020-05-04T22:16:04.558Z", "26.0.1": "2020-05-05T10:40:53.545Z", "26.1.0": "2020-06-23T15:15:17.405Z", "26.2.0": "2020-07-30T10:11:49.932Z", "26.3.0": "2020-08-10T11:31:56.326Z", "26.4.0": "2020-08-12T21:00:22.033Z", "26.4.2": "2020-08-22T12:10:00.767Z", "26.5.0": "2020-10-05T09:28:21.471Z", "26.5.2": "2020-10-06T10:52:51.148Z", "26.6.0": "2020-10-19T11:58:49.574Z", "26.6.1": "2020-10-23T09:06:30.010Z", "26.6.2": "2020-11-02T12:51:37.546Z", "27.0.0-next.0": "2020-12-05T17:25:21.301Z", "27.0.0-next.1": "2020-12-07T12:43:28.627Z", "27.0.0-next.3": "2021-02-18T22:09:55.480Z", "27.0.0-next.5": "2021-03-15T13:03:22.812Z", "27.0.0-next.6": "2021-03-25T19:40:03.112Z", "27.0.0-next.7": "2021-04-02T13:47:59.830Z", "27.0.0-next.8": "2021-04-12T22:42:32.942Z", "27.0.0-next.9": "2021-05-04T06:25:08.682Z", "27.0.0-next.10": "2021-05-20T14:11:20.653Z", "27.0.0-next.11": "2021-05-20T22:28:46.282Z", "27.0.0": "2021-05-25T08:15:13.740Z", "27.0.1": "2021-05-25T10:06:35.134Z", "27.0.2": "2021-05-29T12:07:18.624Z", "27.0.6": "2021-06-28T17:05:42.516Z", "27.1.0": "2021-08-27T09:59:40.686Z", "27.1.1": "2021-09-08T10:12:15.587Z", "27.2.0": "2021-09-13T08:06:42.178Z", "27.2.2": "2021-09-25T13:35:08.481Z", "27.2.3": "2021-09-28T10:11:23.099Z", "27.2.4": "2021-09-29T14:04:50.028Z", "27.2.5": "2021-10-08T13:39:22.303Z", "27.3.0": "2021-10-17T18:34:47.209Z", "27.3.1": "2021-10-19T06:57:33.721Z", "27.4.0": "2021-11-29T13:37:06.428Z", "27.4.1": "2021-11-30T08:37:10.605Z", "27.4.2": "2021-11-30T11:53:42.706Z", "27.4.6": "2022-01-04T23:03:35.740Z", "27.5.0": "2022-02-05T09:59:22.133Z", "27.5.1": "2022-02-08T10:52:18.453Z", "28.0.0-alpha.0": "2022-02-10T18:17:34.015Z", "28.0.0-alpha.1": "2022-02-15T21:26:58.164Z", "28.0.0-alpha.2": "2022-02-16T18:12:03.607Z", "28.0.0-alpha.3": "2022-02-17T15:42:22.603Z", "28.0.0-alpha.4": "2022-02-22T12:13:55.957Z", "28.0.0-alpha.5": "2022-02-24T20:57:19.412Z", "28.0.0-alpha.6": "2022-03-01T08:32:24.610Z", "28.0.0-alpha.7": "2022-03-06T10:02:41.136Z", "28.0.0-alpha.8": "2022-04-05T14:59:43.023Z", "28.0.0-alpha.9": "2022-04-19T10:59:15.577Z", "28.0.0": "2022-04-25T12:08:09.500Z", "28.0.1": "2022-04-26T10:02:40.502Z", "28.0.2": "2022-04-27T07:44:03.736Z", "28.1.0": "2022-05-06T10:48:55.317Z", "28.1.1": "2022-06-07T06:09:36.982Z", "28.1.3": "2022-07-13T14:12:28.718Z", "29.0.0-alpha.0": "2022-07-17T22:07:08.135Z", "29.0.0-alpha.1": "2022-08-04T08:23:30.305Z", "29.0.0-alpha.3": "2022-08-07T13:41:37.966Z", "29.0.0-alpha.4": "2022-08-08T13:05:36.441Z", "29.0.0-alpha.6": "2022-08-19T13:57:51.157Z", "29.0.0": "2022-08-25T12:33:29.959Z", "29.0.1": "2022-08-26T13:34:43.786Z", "29.0.2": "2022-09-03T10:48:21.307Z", "29.0.3": "2022-09-10T14:41:41.109Z", "29.1.0": "2022-09-28T07:37:40.124Z", "29.1.2": "2022-09-30T07:22:49.041Z", "29.2.0": "2022-10-14T09:13:49.021Z", "29.2.1": "2022-10-18T16:00:13.544Z", "29.3.1": "2022-11-08T22:56:23.432Z", "29.4.0": "2023-01-24T10:55:52.252Z", "29.4.1": "2023-01-26T15:08:37.103Z", "29.4.2": "2023-02-07T13:45:28.999Z", "29.4.3": "2023-02-15T11:57:22.391Z", "29.5.0": "2023-03-06T13:33:30.609Z", "29.6.0": "2023-07-04T15:25:46.851Z", "29.6.1": "2023-07-06T14:18:20.199Z", "29.6.2": "2023-07-27T09:21:31.474Z", "29.6.3": "2023-08-21T12:39:16.163Z", "29.7.0": "2023-09-12T06:43:43.801Z", "30.0.0-alpha.1": "2023-10-30T13:33:11.042Z", "30.0.0-alpha.2": "2023-11-16T09:28:20.989Z", "30.0.0-alpha.3": "2024-02-20T11:09:06.733Z", "30.0.0-alpha.4": "2024-05-12T21:43:24.015Z", "30.0.0-alpha.5": "2024-05-30T12:44:04.976Z", "30.0.0-alpha.6": "2024-08-08T07:43:05.584Z", "30.0.0-alpha.7": "2025-01-30T08:28:32.332Z", "30.0.0-beta.2": "2025-05-27T01:23:16.751Z", "30.0.0-beta.3": "2025-05-27T01:27:46.230Z", "30.0.0-beta.6": "2025-06-03T23:50:47.710Z", "30.0.0-beta.7": "2025-06-04T03:35:41.084Z", "30.0.0-beta.8": "2025-06-04T07:53:10.063Z", "30.0.0-rc.1": "2025-06-09T01:02:44.075Z", "30.0.0": "2025-06-10T02:15:50.567Z", "30.0.1": "2025-06-18T22:31:28.598Z", "30.0.2": "2025-06-19T10:46:18.437Z"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-leak-detector"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}