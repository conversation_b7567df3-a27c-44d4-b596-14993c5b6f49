0 verbose cli /usr/local/bin/node /usr/local/bin/npm
1 info using npm@11.4.2
2 info using node@v23.11.1
3 silly config load:file:/usr/local/lib/node_modules/npm/npmrc
4 silly config load:file:/home/<USER>/.npmrc
5 silly config load:file:/usr/local/etc/npmrc
6 verbose title npm install
7 verbose argv "install"
8 verbose logfile logs-max:10 dir:/home/<USER>/.npm/_logs/2025-06-23T21_47_18_464Z-
9 verbose logfile /home/<USER>/.npm/_logs/2025-06-23T21_47_18_464Z-debug-0.log
10 silly logfile done cleaning log files
11 silly packumentCache heap:1228144640 maxSize:307036160 maxEntrySize:153518080
12 silly idealTree buildDeps
13 silly reify moves {}
14 silly audit bulk request {
14 silly audit   '@ampproject/remapping': [ '2.3.0' ],
14 silly audit   '@babel/code-frame': [ '7.27.1' ],
14 silly audit   '@babel/compat-data': [ '7.27.5' ],
14 silly audit   '@babel/core': [ '7.27.4' ],
14 silly audit   '@babel/generator': [ '7.27.5' ],
14 silly audit   '@babel/helper-compilation-targets': [ '7.27.2' ],
14 silly audit   '@babel/helper-module-imports': [ '7.27.1' ],
14 silly audit   '@babel/helper-module-transforms': [ '7.27.3' ],
14 silly audit   '@babel/helper-plugin-utils': [ '7.27.1' ],
14 silly audit   '@babel/helper-string-parser': [ '7.27.1' ],
14 silly audit   '@babel/helper-validator-identifier': [ '7.27.1' ],
14 silly audit   '@babel/helper-validator-option': [ '7.27.1' ],
14 silly audit   '@babel/helpers': [ '7.27.6' ],
14 silly audit   '@babel/parser': [ '7.27.5' ],
14 silly audit   '@babel/plugin-syntax-async-generators': [ '7.8.4' ],
14 silly audit   '@babel/plugin-syntax-bigint': [ '7.8.3' ],
14 silly audit   '@babel/plugin-syntax-class-properties': [ '7.12.13' ],
14 silly audit   '@babel/plugin-syntax-class-static-block': [ '7.14.5' ],
14 silly audit   '@babel/plugin-syntax-import-attributes': [ '7.27.1' ],
14 silly audit   '@babel/plugin-syntax-import-meta': [ '7.10.4' ],
14 silly audit   '@babel/plugin-syntax-json-strings': [ '7.8.3' ],
14 silly audit   '@babel/plugin-syntax-jsx': [ '7.27.1' ],
14 silly audit   '@babel/plugin-syntax-logical-assignment-operators': [ '7.10.4' ],
14 silly audit   '@babel/plugin-syntax-nullish-coalescing-operator': [ '7.8.3' ],
14 silly audit   '@babel/plugin-syntax-numeric-separator': [ '7.10.4' ],
14 silly audit   '@babel/plugin-syntax-object-rest-spread': [ '7.8.3' ],
14 silly audit   '@babel/plugin-syntax-optional-catch-binding': [ '7.8.3' ],
14 silly audit   '@babel/plugin-syntax-optional-chaining': [ '7.8.3' ],
14 silly audit   '@babel/plugin-syntax-private-property-in-object': [ '7.14.5' ],
14 silly audit   '@babel/plugin-syntax-top-level-await': [ '7.14.5' ],
14 silly audit   '@babel/plugin-syntax-typescript': [ '7.27.1' ],
14 silly audit   '@babel/template': [ '7.27.2' ],
14 silly audit   '@babel/traverse': [ '7.27.4' ],
14 silly audit   '@babel/types': [ '7.27.6' ],
14 silly audit   '@bcoe/v8-coverage': [ '0.2.3' ],
14 silly audit   '@discordjs/builders': [ '1.11.2' ],
14 silly audit   '@discordjs/collection': [ '2.1.1', '1.5.3' ],
14 silly audit   '@discordjs/formatters': [ '0.6.1' ],
14 silly audit   '@discordjs/rest': [ '2.5.1' ],
14 silly audit   '@discordjs/util': [ '1.1.1' ],
14 silly audit   '@discordjs/voice': [ '0.18.0' ],
14 silly audit   'discord-api-types': [ '0.37.120', '0.38.12' ],
14 silly audit   '@discordjs/ws': [ '1.2.3' ],
14 silly audit   '@istanbuljs/load-nyc-config': [ '1.1.0' ],
14 silly audit   '@istanbuljs/schema': [ '0.1.3' ],
14 silly audit   '@jest/console': [ '29.7.0' ],
14 silly audit   '@jest/core': [ '29.7.0' ],
14 silly audit   '@jest/environment': [ '29.7.0' ],
14 silly audit   '@jest/expect': [ '29.7.0' ],
14 silly audit   '@jest/expect-utils': [ '29.7.0' ],
14 silly audit   '@jest/fake-timers': [ '29.7.0' ],
14 silly audit   '@jest/globals': [ '29.7.0' ],
14 silly audit   '@jest/reporters': [ '29.7.0' ],
14 silly audit   '@jest/schemas': [ '29.6.3' ],
14 silly audit   '@jest/source-map': [ '29.6.3' ],
14 silly audit   '@jest/test-result': [ '29.7.0' ],
14 silly audit   '@jest/test-sequencer': [ '29.7.0' ],
14 silly audit   '@jest/transform': [ '29.7.0' ],
14 silly audit   '@jest/types': [ '29.6.3' ],
14 silly audit   '@jridgewell/gen-mapping': [ '0.3.8' ],
14 silly audit   '@jridgewell/resolve-uri': [ '3.1.2' ],
14 silly audit   '@jridgewell/set-array': [ '1.2.1' ],
14 silly audit   '@jridgewell/sourcemap-codec': [ '1.5.0' ],
14 silly audit   '@jridgewell/trace-mapping': [ '0.3.25' ],
14 silly audit   '@sapphire/async-queue': [ '1.5.5' ],
14 silly audit   '@sapphire/shapeshift': [ '4.0.0' ],
14 silly audit   '@sapphire/snowflake': [ '3.5.5', '3.5.3' ],
14 silly audit   '@sinclair/typebox': [ '0.27.8' ],
14 silly audit   '@sinonjs/commons': [ '3.0.1' ],
14 silly audit   '@sinonjs/fake-timers': [ '10.3.0' ],
14 silly audit   '@types/babel__core': [ '7.20.5' ],
14 silly audit   '@types/babel__generator': [ '7.27.0' ],
14 silly audit   '@types/babel__template': [ '7.4.4' ],
14 silly audit   '@types/babel__traverse': [ '7.20.7' ],
14 silly audit   '@types/body-parser': [ '1.19.6' ],
14 silly audit   '@types/connect': [ '3.4.38' ],
14 silly audit   '@types/cors': [ '2.8.17' ],
14 silly audit   '@types/express': [ '5.0.0' ],
14 silly audit   '@types/express-serve-static-core': [ '5.0.6' ],
14 silly audit   '@types/graceful-fs': [ '4.1.9' ],
14 silly audit   '@types/http-errors': [ '2.0.5' ],
14 silly audit   '@types/istanbul-lib-coverage': [ '2.0.6' ],
14 silly audit   '@types/istanbul-lib-report': [ '3.0.3' ],
14 silly audit   '@types/istanbul-reports': [ '3.0.4' ],
14 silly audit   '@types/mime': [ '1.3.5' ],
14 silly audit   '@types/node': [ '24.0.3' ],
14 silly audit   '@types/qs': [ '6.14.0' ],
14 silly audit   '@types/range-parser': [ '1.2.7' ],
14 silly audit   '@types/send': [ '0.17.5' ],
14 silly audit   '@types/serve-static': [ '1.15.8' ],
14 silly audit   '@types/stack-utils': [ '2.0.3' ],
14 silly audit   '@types/ws': [ '8.18.1' ],
14 silly audit   '@types/yargs': [ '17.0.33' ],
14 silly audit   '@types/yargs-parser': [ '21.0.3' ],
14 silly audit   '@vladfrangu/async_event_emitter': [ '2.4.6' ],
14 silly audit   'ansi-escapes': [ '4.3.2' ],
14 silly audit   'ansi-regex': [ '5.0.1' ],
14 silly audit   'ansi-styles': [ '4.3.0', '5.2.0' ],
14 silly audit   anymatch: [ '3.1.3' ],
14 silly audit   argparse: [ '1.0.10' ],
14 silly audit   'babel-jest': [ '29.7.0' ],
14 silly audit   'babel-plugin-istanbul': [ '6.1.1' ],
14 silly audit   'istanbul-lib-instrument': [ '5.2.1', '6.0.3' ],
14 silly audit   'babel-plugin-jest-hoist': [ '29.6.3' ],
14 silly audit   'babel-preset-current-node-syntax': [ '1.1.0' ],
14 silly audit   'babel-preset-jest': [ '29.6.3' ],
14 silly audit   'balanced-match': [ '1.0.2' ],
14 silly audit   'binary-extensions': [ '2.3.0' ],
14 silly audit   'brace-expansion': [ '1.1.12' ],
14 silly audit   braces: [ '3.0.3' ],
14 silly audit   browserslist: [ '4.25.0' ],
14 silly audit   bser: [ '2.1.1' ],
14 silly audit   'buffer-from': [ '1.1.2' ],
14 silly audit   callsites: [ '3.1.0' ],
14 silly audit   camelcase: [ '5.3.1', '6.3.0' ],
14 silly audit   'caniuse-lite': [ '1.0.30001724' ],
14 silly audit   chalk: [ '4.1.2' ],
14 silly audit   'char-regex': [ '1.0.2' ],
14 silly audit   chokidar: [ '3.6.0' ],
14 silly audit   'ci-info': [ '3.9.0' ],
14 silly audit   'cjs-module-lexer': [ '1.4.3' ],
14 silly audit   cliui: [ '8.0.1' ],
14 silly audit   co: [ '4.6.0' ],
14 silly audit   'collect-v8-coverage': [ '1.0.2' ],
14 silly audit   'color-convert': [ '2.0.1' ],
14 silly audit   'color-name': [ '1.1.4' ],
14 silly audit   'concat-map': [ '0.0.1' ],
14 silly audit   'convert-source-map': [ '2.0.0' ],
14 silly audit   'create-jest': [ '29.7.0' ],
14 silly audit   'cross-env': [ '7.0.3' ],
14 silly audit   'cross-spawn': [ '7.0.6' ],
14 silly audit   debug: [ '4.4.1' ],
14 silly audit   dedent: [ '1.6.0' ],
14 silly audit   deepmerge: [ '4.3.1' ],
14 silly audit   'detect-newline': [ '3.1.0' ],
14 silly audit   'diff-sequences': [ '29.6.3' ],
14 silly audit   'discord.js': [ '14.20.0' ],
14 silly audit   dotenv: [ '16.5.0' ],
14 silly audit   'electron-to-chromium': [ '1.5.171' ],
14 silly audit   emittery: [ '0.13.1' ],
14 silly audit   'emoji-regex': [ '8.0.0' ],
14 silly audit   'error-ex': [ '1.3.2' ],
14 silly audit   escalade: [ '3.2.0' ],
14 silly audit   'escape-string-regexp': [ '2.0.0' ],
14 silly audit   esprima: [ '4.0.1' ],
14 silly audit   execa: [ '5.1.1' ],
14 silly audit   exit: [ '0.1.2' ],
14 silly audit   expect: [ '29.7.0' ],
14 silly audit   'fast-deep-equal': [ '3.1.3' ],
14 silly audit   'fast-json-stable-stringify': [ '2.1.0' ],
14 silly audit   'fb-watchman': [ '2.0.2' ],
14 silly audit   'fill-range': [ '7.1.1' ],
14 silly audit   'find-up': [ '4.1.0' ],
14 silly audit   fs: [ '0.0.1-security' ],
14 silly audit   'fs.realpath': [ '1.0.0' ],
14 silly audit   fsevents: [ '2.3.3' ],
14 silly audit   'function-bind': [ '1.1.2' ],
14 silly audit   gensync: [ '1.0.0-beta.2' ],
14 silly audit   'get-caller-file': [ '2.0.5' ],
14 silly audit   'get-package-type': [ '0.1.0' ],
14 silly audit   'get-stream': [ '6.0.1' ],
14 silly audit   glob: [ '7.2.3' ],
14 silly audit   'glob-parent': [ '5.1.2' ],
14 silly audit   globals: [ '11.12.0' ],
14 silly audit   'graceful-fs': [ '4.2.11' ],
14 silly audit   'has-flag': [ '4.0.0', '3.0.0' ],
14 silly audit   hasown: [ '2.0.2' ],
14 silly audit   'html-escaper': [ '2.0.2' ],
14 silly audit   'human-signals': [ '2.1.0' ],
14 silly audit   'ignore-by-default': [ '1.0.1' ],
14 silly audit   'import-local': [ '3.2.0' ],
14 silly audit   imurmurhash: [ '0.1.4' ],
14 silly audit   inflight: [ '1.0.6' ],
14 silly audit   inherits: [ '2.0.4', '2.0.3' ],
14 silly audit   'is-arrayish': [ '0.2.1' ],
14 silly audit   'is-binary-path': [ '2.1.0' ],
14 silly audit   'is-core-module': [ '2.16.1' ],
14 silly audit   'is-extglob': [ '2.1.1' ],
14 silly audit   'is-fullwidth-code-point': [ '3.0.0' ],
14 silly audit   'is-generator-fn': [ '2.1.0' ],
14 silly audit   'is-glob': [ '4.0.3' ],
14 silly audit   'is-number': [ '7.0.0' ],
14 silly audit   'is-stream': [ '2.0.1' ],
14 silly audit   isexe: [ '2.0.0' ],
14 silly audit   'istanbul-lib-coverage': [ '3.2.2' ],
14 silly audit   semver: [ '7.7.2', '6.3.1' ],
14 silly audit   'istanbul-lib-report': [ '3.0.1' ],
14 silly audit   'istanbul-lib-source-maps': [ '4.0.1' ],
14 silly audit   'istanbul-reports': [ '3.1.7' ],
14 silly audit   jest: [ '29.7.0' ],
14 silly audit   'jest-changed-files': [ '29.7.0' ],
14 silly audit   'jest-circus': [ '29.7.0' ],
14 silly audit   'jest-cli': [ '29.7.0' ],
14 silly audit   'jest-config': [ '29.7.0' ],
14 silly audit   'jest-diff': [ '29.7.0' ],
14 silly audit   'jest-docblock': [ '29.7.0' ],
14 silly audit   'jest-each': [ '29.7.0' ],
14 silly audit   'jest-environment-node': [ '29.7.0' ],
14 silly audit   'jest-get-type': [ '29.6.3' ],
14 silly audit   'jest-haste-map': [ '29.7.0' ],
14 silly audit   'jest-leak-detector': [ '29.7.0' ],
14 silly audit   'jest-matcher-utils': [ '29.7.0' ],
14 silly audit   'jest-message-util': [ '29.7.0' ],
14 silly audit   'jest-mock': [ '29.7.0' ],
14 silly audit   'jest-pnp-resolver': [ '1.2.3' ],
14 silly audit   'jest-regex-util': [ '29.6.3' ],
14 silly audit   'jest-resolve': [ '29.7.0' ],
14 silly audit   'jest-resolve-dependencies': [ '29.7.0' ],
14 silly audit   'jest-runner': [ '29.7.0' ],
14 silly audit   'jest-runtime': [ '29.7.0' ],
14 silly audit   'jest-snapshot': [ '29.7.0' ],
14 silly audit   'jest-util': [ '29.7.0' ],
14 silly audit   'jest-validate': [ '29.7.0' ],
14 silly audit   'jest-watcher': [ '29.7.0' ],
14 silly audit   'jest-worker': [ '29.7.0' ],
14 silly audit   'supports-color': [ '8.1.1', '5.5.0', '7.2.0' ],
14 silly audit   'js-tokens': [ '4.0.0' ],
14 silly audit   'js-yaml': [ '3.14.1' ],
14 silly audit   jsesc: [ '3.1.0' ],
14 silly audit   'json-parse-even-better-errors': [ '2.3.1' ],
14 silly audit   json5: [ '2.2.3' ],
14 silly audit   kleur: [ '3.0.3' ],
14 silly audit   leven: [ '3.1.0' ],
14 silly audit   'lines-and-columns': [ '1.2.4' ],
14 silly audit   'locate-path': [ '5.0.0' ],
14 silly audit   lodash: [ '4.17.21' ],
14 silly audit   'lodash.snakecase': [ '4.1.1' ],
14 silly audit   'lru-cache': [ '5.1.1' ],
14 silly audit   'magic-bytes.js': [ '1.12.1' ],
14 silly audit   'make-dir': [ '4.0.0' ],
14 silly audit   makeerror: [ '1.0.12' ],
14 silly audit   'merge-stream': [ '2.0.0' ],
14 silly audit   micromatch: [ '4.0.8' ],
14 silly audit   'mimic-fn': [ '2.1.0' ],
14 silly audit   minimatch: [ '3.1.2' ],
14 silly audit   ms: [ '2.1.3' ],
14 silly audit   'natural-compare': [ '1.4.0' ],
14 silly audit   'node-int64': [ '0.4.0' ],
14 silly audit   'node-releases': [ '2.0.19' ],
14 silly audit   nodemon: [ '3.1.7' ],
14 silly audit   'normalize-path': [ '3.0.0' ],
14 silly audit   'npm-run-path': [ '4.0.1' ],
14 silly audit   once: [ '1.4.0' ],
14 silly audit   onetime: [ '5.1.2' ],
14 silly audit   'p-limit': [ '3.1.0', '2.3.0' ],
14 silly audit   'p-locate': [ '4.1.0' ],
14 silly audit   'p-try': [ '2.2.0' ],
14 silly audit   'parse-json': [ '5.2.0' ],
14 silly audit   path: [ '0.12.7' ],
14 silly audit   'path-exists': [ '4.0.0' ],
14 silly audit   'path-is-absolute': [ '1.0.1' ],
14 silly audit   'path-key': [ '3.1.1' ],
14 silly audit   'path-parse': [ '1.0.7' ],
14 silly audit   picocolors: [ '1.1.1' ],
14 silly audit   picomatch: [ '2.3.1' ],
14 silly audit   pirates: [ '4.0.7' ],
14 silly audit   'pkg-dir': [ '4.2.0' ],
14 silly audit   'pretty-format': [ '29.7.0' ],
14 silly audit   'prism-media': [ '1.3.5' ],
14 silly audit   process: [ '0.11.10' ],
14 silly audit   prompts: [ '2.4.2' ],
14 silly audit   'pstree.remy': [ '1.1.8' ],
14 silly audit   'pure-rand': [ '6.1.0' ],
14 silly audit   'react-is': [ '18.3.1' ],
14 silly audit   readdirp: [ '3.6.0' ],
14 silly audit   'require-directory': [ '2.1.1' ],
14 silly audit   resolve: [ '1.22.10' ],
14 silly audit   'resolve-cwd': [ '3.0.0' ],
14 silly audit   'resolve-from': [ '5.0.0' ],
14 silly audit   'resolve.exports': [ '2.0.3' ],
14 silly audit   'shebang-command': [ '2.0.0' ],
14 silly audit   'shebang-regex': [ '3.0.0' ],
14 silly audit   'signal-exit': [ '3.0.7' ],
14 silly audit   'simple-update-notifier': [ '2.0.0' ],
14 silly audit   sisteransi: [ '1.0.5' ],
14 silly audit   slash: [ '3.0.0' ],
14 silly audit   'source-map': [ '0.6.1' ],
14 silly audit   'source-map-support': [ '0.5.13' ],
14 silly audit   'sprintf-js': [ '1.0.3' ],
14 silly audit   'stack-utils': [ '2.0.6' ],
14 silly audit   'string-length': [ '4.0.2' ],
14 silly audit   'string-width': [ '4.2.3' ],
14 silly audit   'strip-ansi': [ '6.0.1' ],
14 silly audit   'strip-bom': [ '4.0.0' ],
14 silly audit   'strip-final-newline': [ '2.0.0' ],
14 silly audit   'strip-json-comments': [ '3.1.1' ],
14 silly audit   'supports-preserve-symlinks-flag': [ '1.0.0' ],
14 silly audit   'test-exclude': [ '6.0.0' ],
14 silly audit   tmpl: [ '1.0.5' ],
14 silly audit   'to-regex-range': [ '5.0.1' ],
14 silly audit   touch: [ '3.1.1' ],
14 silly audit   'ts-mixer': [ '6.0.4' ],
14 silly audit   tslib: [ '2.8.1' ],
14 silly audit   'type-detect': [ '4.0.8' ],
14 silly audit   'type-fest': [ '0.21.3' ],
14 silly audit   typescript: [ '5.7.3' ],
14 silly audit   undefsafe: [ '2.0.5' ],
14 silly audit   undici: [ '6.21.3' ],
14 silly audit   'undici-types': [ '7.8.0' ],
14 silly audit   'update-browserslist-db': [ '1.1.3' ],
14 silly audit   util: [ '0.10.4' ],
14 silly audit   'v8-to-istanbul': [ '9.3.0' ],
14 silly audit   walker: [ '1.0.8' ],
14 silly audit   which: [ '2.0.2' ],
14 silly audit   'wrap-ansi': [ '7.0.0' ],
14 silly audit   wrappy: [ '1.0.2' ],
14 silly audit   'write-file-atomic': [ '4.0.2' ],
14 silly audit   ws: [ '8.18.2' ],
14 silly audit   y18n: [ '5.0.8' ],
14 silly audit   yallist: [ '3.1.1' ],
14 silly audit   yargs: [ '17.7.2' ],
14 silly audit   'yargs-parser': [ '21.1.1' ],
14 silly audit   'yocto-queue': [ '0.1.0' ]
14 silly audit }
15 http fetch POST 200 https://registry.npmjs.org/-/npm/v1/security/advisories/bulk 321ms
16 silly audit report {}
17 verbose cwd /home/<USER>
18 verbose os Linux 5.15.0-119-generic
19 verbose node v23.11.1
20 verbose npm  v11.4.2
21 verbose exit 0
22 info ok
