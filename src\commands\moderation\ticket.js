const { <PERSON><PERSON><PERSON>ommandBuilder, PermissionFlagsBits, EmbedBuilder, ActionRowBuilder, StringSelectMenuBuilder } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('ticket')
        .setDescription('Create a ticket system')
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageGuild)
        .addStringOption(option =>
            option.setName('roles')
                .setDescription('The roles that can see tickets (mention multiple roles separated by spaces)')
                .setRequired(true)),

    async execute(interaction, client) {
        const rolesString = interaction.options.getString('roles');
        
        const roleIds = rolesString.match(/<@&(\d+)>/g)?.map(mention => mention.match(/\d+/)[0]) || [];
        
        if (roleIds.length === 0) {
            return interaction.reply({ 
                content: 'Please mention at least one valid role!', 
                ephemeral: true 
            });
        }

        const validRoles = [];
        for (const roleId of roleIds) {
            const role = interaction.guild.roles.cache.get(roleId);
            if (role) validRoles.push(role);
        }

        if (validRoles.length === 0) {
            return interaction.reply({ 
                content: 'None of the mentioned roles were found!', 
                ephemeral: true 
            });
        }

        const rolesData = validRoles.map(role => role.id).join(',');

        const ticketEmbed = new EmbedBuilder()
            .setColor('#61607e')
            .setTitle('**🎫 نظام التذاكر | Ticket System**')
            .setDescription(`
<:1294025486296551496:1352763127426580540> مرحباً بك في قسم التيكت في سيرفر **SAHM Community** <:188d3ae0aeda472198d020d24fdf4db5:1352764164019453972>
إذا كنت بحاجة لدعم، استفسار، أو لديك مشكلة تحتاج حل، هذا هو المكان المناسب <:1294025486296551496:1352763127426580540>

<:1294025486296551496:1352763127426580540> كيفية استخدام التيكت :  
   <:1294025486296551496:1352763127426580540> 1 انقر على الزر الموجود في أي قسم تريد فتح تذكرة  
   <:1294025486296551496:1352763127426580540> 2 اكتب مشكلتك أو استفسارك بوضوح 
   <:1294025486296551496:1352763127426580540> 3 انتظر رد فريق الإدارة وسنساعدك في أقرب وقت 

<:1294025486296551496:1352763127426580540> شكراً لتفهمك وكونك جزءمن مجتمع **SAHM** <:188d3ae0aeda472198d020d24fdf4db5:1352764164019453972>

<:1294025486296551496:1352763127426580540> Welcome to the Ticket section in **SAHM Community** 
If you need a request, inquiry, or have a problem that needs to be solved, this is the right place. 

<:1294025486296551496:1352763127426580540> **How to use the ticket:**
<:1294025486296551496:1352763127426580540> **1** Click on the button on any section you want to open a ticket to 
<:1294025486296551496:1352763127426580540> **2** Write your problem or inquiry clearly 
<:1294025486296551496:1352763127426580540> **3** Please wait for a response — we’ll assist you soon.

<:1294025486296551496:1352763127426580540> **Thank you for your understanding and being part of the SAHM community ** <:188d3ae0aeda472198d020d24fdf4db5:1352764164019453972>`)
            .setFooter({ text: 'Ticket System' })
            .setTimestamp();

        const selectMenu = new ActionRowBuilder()
            .addComponents(
                new StringSelectMenuBuilder()
                    .setCustomId(`create_ticket_${rolesData}`)
                    .setPlaceholder('Select Ticket Type | اختر نوع التذكرة')
                    .addOptions([
                        {
                            label: 'Kick Support | الدعم الفني للكيك',
                            value: 'kick_support',
                            emoji: '<:customersupport:1352794989821820948>'
                        },
                        {
                            label: 'Staff Ticket | تكت ادارة',
                            value: 'staff_ticket',
                            emoji: '<:teamwork:1352793934920941638>'
                        },
                        {
                            label: 'Girls Support | الدعم الفني للبنات',
                            value: 'girls_support',
                            emoji: '<:customerservice:1352793132592271472>'
                        },
                        {
                            label: 'Roles Support | تقديم على رتبه',
                            value: 'roles_support',
                            emoji: '<:user:1352804063422447684>'
                        },
                        {
                            label: 'Complaint | رفع شكوى',
                            value: 'complaint',
                            emoji: '<:complain:1352794654931681320>'
                        },
                        {
                            label: 'Server Support | دعم الدس',
                            value: 'server_support',
                            emoji: '<:technicalteam:1352804962047885332>'
                        }
                    ])
            );

        await interaction.channel.send({
            embeds: [ticketEmbed],
            components: [selectMenu]
        });

        await interaction.reply({
            content: '✅ Ticket system has been set up successfully!',
            ephemeral: true
        });
    }
};