{"_id": "@babel/types", "_rev": "220-57f8759b22424983f0a6e19db68059da", "name": "@babel/types", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.6", "next": "8.0.0-beta.0"}, "versions": {"7.0.0-beta.4": {"name": "@babel/types", "version": "7.0.0-beta.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "988cc7683c448d7710e7d80bd88558183a102349", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.0.0-beta.4.tgz", "integrity": "sha512-yLvBW5TTAgJwURAUAdZa1vrFTkwXXvk0Kw48LYvgxpyT/IaV8W4OIhxdVztAt5ruDQ/OFUwHpzWqk6TN3EfmWA==", "signatures": [{"sig": "MEUCICYQV7Yko78K+udKLMRdclw3xgXaAGf0y3FVf4Mo131aAiEAnuP7hYO6eebFeljlmERtK31d4Eyx4XGe8PFfjXPHakU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"lodash": "^4.2.0", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "devDependencies": {"babylon": "^7.0.0-beta.30", "@babel/generator": "7.0.0-beta.4"}, "_npmOperationalInternal": {"tmp": "tmp/types-7.0.0-beta.4.tgz_1509388495763_0.6357525852508843", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/types", "version": "7.0.0-beta.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "56d6b5bee7d999ba198dc245447f11ffaa6c3683", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.0.0-beta.5.tgz", "integrity": "sha512-1mUWQwJMG/KxBVCgLISQIxhtN+bnZs6qxjnzyHdXIEa9YE4z+XMQHxZsCIL8cJvP7H4NZOva+fg9YNDVcy99xA==", "signatures": [{"sig": "MEQCIFdRrl+vvxC0it27JLLpya/VaFs+0XsIyeS9QVCC+T0nAiA6sfnyU436p3fXKCKdaiY3y0WrkBB34S/gqZsr9v/Sbw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"lodash": "^4.2.0", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "devDependencies": {"babylon": "^7.0.0-beta.30", "@babel/generator": "7.0.0-beta.5"}, "_npmOperationalInternal": {"tmp": "tmp/types-7.0.0-beta.5.tgz_1509396995638_0.6233448232524097", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/types", "version": "7.0.0-beta.31", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "42c9c86784f674c173fb21882ca9643334029de4", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.0.0-beta.31.tgz", "integrity": "sha512-exAHB+NeFGxkfQ5dSUD03xl3zYGneeSk2Mw2ldTt/nTvYxuDiuSp3DlxgUBgzbdTFG4fbwPk0WtKWOoTXCmNGg==", "signatures": [{"sig": "MEUCIEMayD7+kSSl6b845nhV7WX9jv+TGiIs7EMwBeOvr4cgAiEA2BId0KQggofdDH1uFruAUUT2bptYWdcErGTERT03Cuo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"lodash": "^4.2.0", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "devDependencies": {"babylon": "7.0.0-beta.31", "@babel/generator": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/types-7.0.0-beta.31.tgz_1509739415515_0.19840487372130156", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/types", "version": "7.0.0-beta.32", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "c317d0ecc89297b80bbcb2f50608e31f6452a5ff", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.0.0-beta.32.tgz", "integrity": "sha512-w8+wzVcYCMb9OfaBfay2Vg5hyj7UfBX6qQtA+kB0qsW1h1NH/7xHMwvTZNqkuFBwjz5wxGS2QmaIcC3HH+UoxA==", "signatures": [{"sig": "MEUCIQCkdleCTvptJHPYL0vGEyHl7H1rukRlYmQwhII4sp2f6wIgfCCrC+z2BvhaWwLl7PVN4I66FGLzhfK0N8d+7+vPkHE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "devDependencies": {"babylon": "7.0.0-beta.32", "@babel/generator": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/types-7.0.0-beta.32.tgz_1510493607326_0.45274083921685815", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/types", "version": "7.0.0-beta.33", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "d74217bf84524b244f60ba475b4a611c2630564f", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.0.0-beta.33.tgz", "integrity": "sha512-Re3LmA/LeS0MoNr2G9wuNzPlSmLFSfUiFWsYpfZG5vATbWalLoXPZja8GQt28pMRAqNuy15FLEZnAoF3YnkfyQ==", "signatures": [{"sig": "MEQCIEl0S6Izo92CGqaQjm6URpS6jUNdShVK0gKC8EHI/LoKAiBxkw6A4JizoiPAa9N+LIDYaQ39TQ8sXOpVNIGIzqjZMw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "devDependencies": {"babylon": "7.0.0-beta.33", "@babel/generator": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/types-7.0.0-beta.33.tgz_1512138512372_0.6821447301190346", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/types", "version": "7.0.0-beta.34", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "ce8da730b834c782ec64a2baf3ac0200dd328816", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.0.0-beta.34.tgz", "integrity": "sha512-KD5CF1dmZE8i9ggW9L+YSak7j+b3YTGQZUs9WMF6tgG/aw2x/VhTxfooqXR0Bbfzkb5M6WGiTHbzCmx8ll4tQQ==", "signatures": [{"sig": "MEYCIQCTUncp5+d2eHp1CuihsrGPNK75wUW1DMJeig0G3DuK0QIhAIZcYOcFMwd5ubERFR8jJ0ZUV2q0Or/E5riDJDmwKpBd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "devDependencies": {"babylon": "7.0.0-beta.34", "@babel/generator": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/types-7.0.0-beta.34.tgz_1512225572135_0.38195543619804084", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/types", "version": "7.0.0-beta.35", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "cf933a9a9a38484ca724b335b88d83726d5ab960", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.0.0-beta.35.tgz", "integrity": "sha512-y9XT11CozHDgjWcTdxmhSj13rJVXpa5ZXwjjOiTedjaM0ba5ItqdS02t31EhPl7HtOWxsZkYCCUNrSfrOisA6w==", "signatures": [{"sig": "MEUCIQDFXjbpSdme4VziYNnWkOKdAzqPrEW11i9B3katJLbEhQIgTDcWrmlLZ38D2PuyoGykiJ4toh6yIjWT9MzLBNnhCHc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "devDependencies": {"babylon": "7.0.0-beta.35", "@babel/generator": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/types-7.0.0-beta.35.tgz_1513288077675_0.9689391618594527", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/types", "version": "7.0.0-beta.36", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "64f2004353de42adb72f9ebb4665fc35b5499d23", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.0.0-beta.36.tgz", "integrity": "sha512-PyAORDO9um9tfnrddXgmWN9e6Sq9qxraQIt5ynqBOSXKA5qvK1kUr+Q3nSzKFdzorsiK+oqcUnAFvEoKxv9D+Q==", "signatures": [{"sig": "MEQCIH7OK0B29n2X2pLn7w81/VCzxy7YE/NgMjOFwJz7FPEUAiBP9V8mgGrNSSh9ZxsWQlHO3JJUgY29xv/AgJH45KSNnA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "devDependencies": {"babylon": "7.0.0-beta.36", "@babel/generator": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/types-7.0.0-beta.36.tgz_1514228691995_0.5335837327875197", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/types", "version": "7.0.0-beta.37", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "fa93af9aa9d85c331729bb923495af04d9b0617d", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.0.0-beta.37.tgz", "integrity": "sha512-RLbDz5fUFULue678wqBu2MR3V9BBvu+ugAQitrJPFCjDhgwy/CKCbUg+yonpvKqOcYGvz54GdBpCoQoSMYK9UA==", "signatures": [{"sig": "MEUCIEYTASLobwZsYetvylbw7yUT2IO/syqMaKKgI9Nh5CbpAiEA6IS7JwduNsz+4au4TU3Juyz3NBGvuZZdZU4ZqyyoWYY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "devDependencies": {"babylon": "7.0.0-beta.37", "@babel/generator": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/types-7.0.0-beta.37.tgz_1515427359223_0.24704321892932057", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/types", "version": "7.0.0-beta.38", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "2ce2443f7dc6ad535a67db4940cbd34e64035a6f", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.0.0-beta.38.tgz", "integrity": "sha512-SAtyEjmA7KiEoL2eAOAUM6M9arQJGWxJKK0S9x0WyPOosHS420RXoxPhn57u/8orRnK8Kxm0nHQQNTX203cP1Q==", "signatures": [{"sig": "MEUCIQDh2AsgsLxPNsGrGAELZ928UipoR5Slm51eqtTHB6h+HAIgJwSU0YiQtz1ZK0aB4SgoO+QUT1DxcAWnXrfxT7RVvg8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "5.5.1", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "devDependencies": {"babylon": "7.0.0-beta.38", "@babel/generator": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/types-7.0.0-beta.38.tgz_1516206725516_0.5681764360051602", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/types", "version": "7.0.0-beta.39", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "2ea0d97efe4781688751edc68cde640d6559938c", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.0.0-beta.39.tgz", "integrity": "sha512-wrEe0z4kFP0KbFz8aHbPOGQal0gn+J2Iv9ZJGYbD77JN4BpavbF5l/BvLNZ0Omn665VENncoLVmQpclMbh64sQ==", "signatures": [{"sig": "MEUCICFBzO6RTosbRtdZ2gEWcX7sNkaBPfRGrxXyKmf2Z0r0AiEAv5jsdZ6m5EFlYTH0Q6bTkomKfxNLPczhUt8zKSbtdrE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "devDependencies": {"babylon": "7.0.0-beta.39", "@babel/generator": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/types-7.0.0-beta.39.tgz_1517344060355_0.6547915895935148", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/types", "version": "7.0.0-beta.40", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "25c3d7aae14126abe05fcb098c65a66b6d6b8c14", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.0.0-beta.40.tgz", "fileCount": 86, "integrity": "sha512-uXCGCzTgMZxcSUzutCPtZmXbVC+cvENgS2e0tRuhn+Y1hZnMb8IHP0Trq7Q2MB/eFmG5pKrAeTIUfQIe5kA4Tg==", "signatures": [{"sig": "MEUCIB+kFrrMFpZR2tBvLRZI0BXUnE7Bb7kzGM5kbP8YkeXQAiEA634+UiJFreJFlIupupufNl+MgTWKk/YpFMqgpGgwfNE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 581244}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"lodash": "^4.2.0", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"babylon": "7.0.0-beta.40", "@babel/generator": "7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.0.0-beta.40_1518453710418_0.9606338540935242", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/types", "version": "7.0.0-beta.41", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "776e6ec154fb8ec11da697be35b705c6eeb00e75", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.0.0-beta.41.tgz", "fileCount": 86, "integrity": "sha512-q+Jf57E089a98CjAN5x0C5PAn7fqhVrEHTVAeVWHviyauASWgqff3F+t8migYEagnn5PcJ9lmtr5Mgmi3sV8PA==", "signatures": [{"sig": "MEYCIQDfaWAy1aWDN7btznbWT/9lbAemLCJew3MY6up6eN0hggIhAJ5lF7nGp4AYbc+nPq/HQ8lfyuXAqnC+EeDOpCAUaSEW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 585979}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"lodash": "^4.2.0", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"babylon": "7.0.0-beta.41", "@babel/generator": "7.0.0-beta.41"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.0.0-beta.41_1521044738037_0.1574821565136304", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/types", "version": "7.0.0-beta.42", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "1e2118767684880f6963801b272fd2b3348efacc", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.0.0-beta.42.tgz", "fileCount": 86, "integrity": "sha512-+pmpISmTHQqMMpHHtDLxcvtRhmn53bAxy8goJfHipS/uy/r3PLcuSdPizLW7DhtBWbtgIKZufLObfnIMoyMNsw==", "signatures": [{"sig": "MEQCIAFwPTiSYUCfGSgB5DKkAHbbLj3W1XKaloMCfIt6rPovAiASSlHlTf71b0bqJ154HbHa3SQtAfz060Jl2WdJO+z8Rg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 588759}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"lodash": "^4.2.0", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"babylon": "7.0.0-beta.42", "@babel/generator": "7.0.0-beta.42"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.0.0-beta.42_1521147012822_0.1642209609936891", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/types", "version": "7.0.0-beta.43", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "9d82c2d773b6baec0474ddb774eafd7fb4511f8b", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.0.0-beta.43.tgz", "fileCount": 86, "integrity": "sha512-URKibaAMFKPb0Rm3xCOqLkiSCMNvm+7HWB73xY2wtv+Uwu5al8knrcRUzvPIXgIVZab1Ewt4vz9n8P7fDSiDRQ==", "signatures": [{"sig": "MEYCIQCJU/HURQpCrSzxcNrzXtd58qr4Y65AcODf2szFtvbFXwIhALio7dBlNzHpBXKJN6uGJIHm9cU8Sv+pJD/nBY0O64TW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 548348}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"lodash": "^4.2.0", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"babylon": "7.0.0-beta.43", "@babel/generator": "7.0.0-beta.43"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.0.0-beta.43_1522687683594_0.3295129193465849", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/types", "version": "7.0.0-beta.44", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "6b1b164591f77dec0a0342aca995f2d046b3a757", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.0.0-beta.44.tgz", "fileCount": 86, "integrity": "sha512-5eTV4WRmqbaFM3v9gHAIljEQJU4Ssc6fxL61JN+Oe2ga/BwyjzjamwkCVVAQjHGuAX8i0BWo42dshL8eO5KfLQ==", "signatures": [{"sig": "MEYCIQDQLVYsOC4N6JMNowXtaQ1G7FWih2sctfLJP4jXRyDd2gIhAP7des6BIb4KuY2O3+4xw8hPPv8xjaM0vUTy+N7IOJMZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 596455}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"lodash": "^4.2.0", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"babylon": "7.0.0-beta.44", "@babel/generator": "7.0.0-beta.44"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.0.0-beta.44_1522707587063_0.8920671545327745", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/types", "version": "7.0.0-beta.45", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "27dae79ac324be3a82b686ec6e302b7ab6a2bb76", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.0.0-beta.45.tgz", "fileCount": 86, "integrity": "sha512-I4uHHpgZQZ91BtTqMP5CqMd0nqMHX45m3fEcyLst5kPGjgojZ3TyJXifANwHk+I0+5B9bcJTwkTXhgE/cXSqIA==", "signatures": [{"sig": "MEUCIHIUvdW57E3YXNKK79XKu816FXXXKm1LqrkokI4IxQMNAiEA0jv7CEoB+RnOIFitgZgCnOv//OO9d0meXYfXJ5RseOE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 664399, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T0XCRA9TVsSAnZWagAADFcP/A28Rtx0icvYSCIq4eV1\njJnW2WiCi+UQHSFyL0ko6Y9SB6LcyF+J5VZSSV5gCJL6Bn3gwO0ieSjwUCb2\n43nf0iG1lO6NG+2oVPI+waXWrtPHPoS2zzdObnMJHjYNnctGOp6a7bXYRrOs\ne+PJiXc7FkC9h5WQUB1yP+nKCN6sv2uNuqLZzuufWvSLQHMq71GVFY6Omw8n\n+BJrgCTUFIKcUHu08jFCbhVv/y+tbMkrXEuQR3MfTlXC1J6erreY/cWVQuEM\nG6TrP20gM1jk/jK1Zk/XSR/oDTk85u5Be0WHqu351JW03jERDDjw/7d2xFSo\n36JT0o4/GczGZKDZY3ZnDt2fgI3cwvRYVALCJ4Puhz0GvrGqGR0/lc9xPM1q\noc8b83WSIj/6t8aNsBjE5SQvXcT64wYe8I9DsByu0gtLYwnOe5Pl2V1ZJBIM\nofE5Jy/iwT/jgbkY1AejcQWIpRF2zGq9i+qojNJYBhj4zG8JY9QwosKIQDOR\nyQkKQX6MrpEUofKG7txR9ygyd0lEvjZaXHOgy4N/OBMQNH5OuRAj8yERpT7b\nQS3rifHhTeLClaxma2RWkdtAUhh1fP+imfm2MjPdvPMlG7ePQfabKkTDhhG6\n0GBEnaQ/c4Nq2VcQEd/wErvRAehlhCqxwQ0a4MsslM5bbRKJI7E82/bFspsU\nKga4\r\n=KL0w\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"lodash": "^4.2.0", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"babylon": "7.0.0-beta.45", "@babel/generator": "7.0.0-beta.45"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.0.0-beta.45_1524448534048_0.15902637175123213", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/types", "version": "7.0.0-beta.46", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "eb84399a699af9fcb244440cce78e1acbeb40e0c", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.0.0-beta.46.tgz", "fileCount": 86, "integrity": "sha512-uA5aruF2KKsJxToWdDpftsrPOIQtoGrGno2hiaeO9JRvfT9xZdK11nPoC+/RF9emNzmNbWn4HCRdCY+McT5Nbw==", "signatures": [{"sig": "MEQCIA/RgkRfMO1fb/w6ncZ84UJOH93p9JuM699nKSS4FxbSAiBqMywY888FGs4cuD2Jav5HOvQoUsYdqHqhDhHzhOLKUA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 664399, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WFHCRA9TVsSAnZWagAAU2sP/RMK3I0BUxajFzBB8GN8\nj+zsMdlVZYebVarCObKL/pb5lv1sb/iPDjGEV5FYVnDYckex4gtbznk0DS+/\nCJxzox/D4vOno6jIzvauXVI7gnOg01WjwXQUHIHIHSJ1k5O2YmXzYdVbLB0S\nYorSNEQKuUOMzFNfmb1beJL6u+PzG4zcCc9VdPdYYAugVZN3XsEFWEFeJTDd\nSLRpEBm+u/v8sCk77l9PgUMxLAx+hjGS/WB6yQ373tr4EkLal9eQJpeZXRYN\nwAOupChzvEqA59NdSupAs3EkVzfv8nz7QwawNr30nODGrLyaqFvwbN3LDQXH\nyj7hlSyrQZwn1+ShWIgiomxshNPLpudFdYyHZxBmb0nuyFGfbgia/jNtx1ve\n4aV/r615s1770iRBSu5MAXW1Pjnn1PNskSkuJkos93Cuy0weko9IisLDN9u8\nE2aHU2AgZNMkMPsol1eXR3OINeW3lSR0fajicHNJUwGkXVOyRFR8RFsEq/eb\n8cLRH99NPVnDtbTQRI/Npk+7Xmvwf1S8C2yYj1xAaKB3/A+92ZK1+0Lneb2U\n+X2MQUdm5hApGetvezNeYqwW6u5LgHKF4qFdZ5Gmk8QdEflY9qdMyO87+2SZ\nQvIQqBF2JQMTO+usCsSG2ucA6BYw8FlnBQXde4x26af3Pe1XqtmxcYePFmN+\nO9ez\r\n=uZEU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"lodash": "^4.2.0", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"babylon": "7.0.0-beta.46", "@babel/generator": "7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.0.0-beta.46_1524457798598_0.912013201889122", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/types", "version": "7.0.0-beta.47", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "e6fcc1a691459002c2671d558a586706dddaeef8", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.0.0-beta.47.tgz", "fileCount": 86, "integrity": "sha512-MOP5pOosg7JETrVGg8OQyzmUmbyoSopT5j2HlblHsto89mPz3cmxzn1IA4UNUmnWKgeticSwfhS+Gdy25IIlBQ==", "signatures": [{"sig": "MEQCIBYWhIcQ6IZiYFrSUX+U390X/2DMNpN+aTIYhUBDSgcSAiAslzc4lne+zi+HqZcB2kC5qVf5W5rVIORf8uq3nKAGtg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 617020, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iS8CRA9TVsSAnZWagAAZX8P/2y0EiVRovBEeNJsPdRx\nbGp15w+kUZm3gzikp+Bs4PTWew262HCLWaB0a9eLGPQ5OL4H2vW4PJ1JdnP5\ngpQxQSRBblpxw4QFNLTk5nUPCA6LldP4ZB6Z7hmF8LXGj6HBOQHvp4C6Ke3r\nD2IC1vo4RBargUqaEijACKpwLrjMsIQ240AEdK/s4SKtpG8UrviO/JHH6c83\nVqioMt8by5vH7oV5OMA6NJJpa0On6d4Cv4gTfmjuVC0glMStql/zb7vwNY22\ndWefNpE+Z79ahFuVPbDpoTqTwh70daIAARyLB9in6OoUu43ow7bvZuBpFkl/\nEWtEsgLvIbELipt1SHhD0WBQq3hgkkZRL9hFP/zqw7POacTUbkudcDVv8xrL\ne9ShcJp2FPhHJV6Dk2GFcLnDQ1Euto1yQ7RtzFW1JIJOk5e3FaMlH6sD7FpT\nO9ctsA+Fqa31Jno/JUCk8Lh1B48FMi5BYgSWbGVhR+Fg/oDDXZNMWcDQ9FPJ\nF21vTCsp65L9gOqvO/JZcVe9q5jfLkj4CkYLaz7B1OExkd1Tb4VdQ6+SRgS4\nUtRZXV3V/tVoiOtUaNc/rlWIv4yQlpCqRWhMac4CtcDHOlIT1JuiSx7ejCLV\nxJQvrbKnktsr9JBp2KudeopFataHy3287zd5SsXDkuEa9BN8PlnviD13SM0Z\nj9jR\r\n=4xDE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"lodash": "^4.17.5", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"babylon": "7.0.0-beta.47", "@babel/generator": "7.0.0-beta.47"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.0.0-beta.47_1526342842598_0.8659735200859908", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/types", "version": "7.0.0-beta.48", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "095872fa6f8a87846f5872a39a938f34d5dc55a3", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.0.0-beta.48.tgz", "fileCount": 86, "integrity": "sha512-h37aY8aUmTMEDEaPrNwmYmwSTjf7SvJ4h8jCljaHOSq3785TZSZnxdCsNDVs+fKK21lOPh9X29rvOLTOwxgVmw==", "signatures": [{"sig": "MEUCIQDDv4uVBBn3uO9/6akdzsxMAotQTsd92s5ria5oAPbmewIgKFdAYpEVkdX92CYYkYhF4jhP92XBUjJMIkplj1AWLrk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 624544, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxCKCRA9TVsSAnZWagAApqMP/RUynNriz/dUCNvG/ijM\no3tNTD79pGGOcK6fIDwalTMU/3hk/WWcQs3uj6YwkPFzJEeRz6TmwOBzfSir\ns1fl/2kpR4m+S+OiyVDiRRAY84OdnYERS8dkjLOblACM5GzHKUZ7G3z2568j\n6KN7O/vGsjisKDf55fswIKx+C8nuISgtO1bS8Emc4XfGvs8yAXEkTCzpKmvD\nyUDqreBfG0rZg/huQYpg1qPL4lN/mS7aq68nllliPOR1kP8DClDMjv9+fcbn\nVnZDBxWpanbudrdr2Dcr+Wb2XqPg1O4FumvSVVPYD02qDWuN/BLDgYtaWeXJ\nQTiM+qMh5ca8wq3MakLfcWXQN8Zcs+0/vvXoqEHpNFDvCzhdGTPB1EVrS9hI\nQ6akUDA1DiAFeCa+1Jod1bHtO8LZRF2J4nmMb4iVV4cuI/lEE+hZ1rqmvn9C\ndeNa6PRei11VOUjDaMJJEQ8ZVlunK4FYZECP5J+/iwReCTIPJVjChDfXgrnQ\nI7SDxBEvCcP71o5Xy8vnHtp0QdUZbDURzmHHAofvc5ZghZ0YnKob6w/gulY/\nIHL3qJVIb1nux5EqHGvNbMfhddynGZksjnSaZe1VGKaHZ/fk/OQkGsQHTNmb\nM9Qn9Wo4PJCa7trvjRMmvdi60KdSsYYEnGlRAEUV4LseuBxjOfigWX9jVNiD\nvzwz\r\n=L9B4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "5.6.0", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"lodash": "^4.17.5", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.0.0-beta.48", "@babel/generator": "7.0.0-beta.48"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.0.0-beta.48_1527189641637_0.7050412770369203", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/types", "version": "7.0.0-beta.49", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "b7e3b1c3f4d4cfe11bdf8c89f1efd5e1617b87a6", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.0.0-beta.49.tgz", "fileCount": 87, "integrity": "sha512-NauS4Z6gKNVkeOOF/Qk/6eKMk9IscYB2MzjoIMTfVmFavIerYxzyCiTz7/FgS+g+GnPQtzlmADh9KNLT0GsCvg==", "signatures": [{"sig": "MEQCIBHdzSqLW+cZOJmjvfwxhLxiHWRBi1q2dSbfa/ZffJwJAiBftPxz7f7aP8A9LNjtBEwpYd1im32vcTkSQqyB/aVrvw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 624559, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDMaCRA9TVsSAnZWagAAWGsP/3Ew62M6Tfp+wugBniEt\nAfl8cBm7YOf6+/xl5mWu0Mx9PCVcdRbFNT7OOSUQ1nLZ6BjFVInIBsr7xyDc\nPY3B8NPn2EKZbCJiNMkoBubLgrshay/s6MVUtsCtvU+unw2yuk8m5g/psZ9q\nwD56FAASokjINo8cviWDQw80oHZ4mjh0op03F5hvav38pbsKLqV0dH0c0YpD\n2lcIxNs8i+/2dwndf1DofvXsSk+NlUK9ot44Sxt4ujBTAbon9q6YJ6UrKTh3\n7/bVB/8rDwV2BMdomC7SgZZmV7EYb8INyFGffEGR6hlD7htSnrLBJa1fI8r+\nXmwUGamjKGDU+ATjaPQAGeXGOv73R1ONX8qMZbEMcL9THXvUCb/NG7BfKC62\neTeukibqyoFi7l8hkYjUKsXO0SQkbZdAIIcuDO7ZrSVXesFTbeWPf0UkXyw4\nuelgIdYXs5otXrO04Y66Gl5Mq7U29o3Vrj40v83IjWY08fD4aAyHLFRRn8yB\neGIwHsCamBWE76nROipxMqyTmDOYtAXVtNKdCw5SelOJ8Vz+9LWEer52PkSO\nahHkUeWodBcf9i80HIxCEfsMOlAFwP5Gc7YwPta87E5n0CdIHiIam9oD8Xrn\n5ajBrQUQ23G6iY76MwNfjmDLSxsmm8bJJQxGwFoY5jE8Xm+uuq/2NKyF2PQ3\nzWCk\r\n=yH+2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "types": "lib/index.d.ts", "_shasum": "b7e3b1c3f4d4cfe11bdf8c89f1efd5e1617b87a6", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "3.10.10", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"lodash": "^4.17.5", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.0.0-beta.49", "@babel/generator": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.0.0-beta.49_1527264025672_0.20200191926653255", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/types", "version": "7.0.0-beta.50", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "00ddbf150fe551911e9ca01085b87a16f0cbc5d1", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.0.0-beta.50.tgz", "fileCount": 112, "integrity": "sha512-xErIqY6zvh2+TbAr7BzuvVIWrmFWEMLVN8+c1ucQm0M/AW4I0GJGnMlMvjgyTGCTxKvY5D2+AoQGHfiskk+zMQ==", "signatures": [{"sig": "MEQCIEcOqywDoJ9ZMf9piTQjzitWoqXD6QennsQmAUY0JuSZAiBDgATJshpHrGUlj2+UA17OT92NDp5DsSPwsQy9k+Xpkw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 556399}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"lodash": "^4.17.5", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.0.0-beta.50", "@babel/generator": "7.0.0-beta.50"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.0.0-beta.50_1528832806479_0.6789837686790985", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/types", "version": "7.0.0-beta.51", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "d802b7b543b5836c778aa691797abf00f3d97ea9", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.0.0-beta.51.tgz", "fileCount": 112, "integrity": "sha512-q+MkuRwtjG6hos2yUkBy4KR4RRPAh0nk8H5m0RSQm+0bz1T3cNZWFxxNnp7AMrK3c9+B+FRWPss+eQNNRJy7Lw==", "signatures": [{"sig": "MEQCIGEpNhNxewCAfgvH99iNAoWi1KuOEnkx1I86fm+hvEoBAiBU0brhQXda2os8mroPwcrEHRfBQ85StyP+XALDjem0rA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 556399}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"lodash": "^4.17.5", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.0.0-beta.51", "@babel/generator": "7.0.0-beta.51"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.0.0-beta.51_1528838352871_0.9998622879149617", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/types", "version": "7.0.0-beta.52", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "a3e5620b1534b253a50abcf2222b520e23b16da2", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.0.0-beta.52.tgz", "fileCount": 112, "integrity": "sha512-QVvCZDLhzG9geHP9fcK9rX6bbQtTyN7wmd6gO7rj6MRcmOVaQXV8uoZ8KTgZ/z5SocIoCkTj/rmMR91evu/wVg==", "signatures": [{"sig": "MEUCIQDXfDckCeW9UkJ62aovP8YNk7z9n03wsFuRjyC8RoPzMQIgKW9LFf1OuXEqkE/EFWk6l/cI5oS9R40CqVpQceH23JI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 556432}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"lodash": "^4.17.5", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.0.0-beta.52", "@babel/generator": "7.0.0-beta.52"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.0.0-beta.52_1530838749240_0.7719748969506965", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/types", "version": "7.0.0-beta.53", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "19a461c0da515595dfb6740b4b45dc7bb0e6b375", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.0.0-beta.53.tgz", "fileCount": 112, "integrity": "sha512-iL3DSWjQ890rA97uR5F1PhGtYniVGjqaRoRZtLz76bZhNNqmALftafrUnuJNzWC9z0eoaNcAtk7ZT/26mW/6Tg==", "signatures": [{"sig": "MEQCIFBiG1yD2UpgmecOiq+ImjMSwq4oAJNeJPHXp00Ik4ubAiAS/5VLN3HAt9NOZ8w5kHU6CVjuHe2cVwCtNXdDcrPVAA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 557241}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"lodash": "^4.17.5", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.0.0-beta.53", "@babel/generator": "7.0.0-beta.53"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.0.0-beta.53_1531316399179_0.5703298140579149", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/types", "version": "7.0.0-beta.54", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "025ad68492fed542c13f14c579a44c848e531063", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.0.0-beta.54.tgz", "fileCount": 112, "integrity": "sha512-m/MY/sWjJlo1f0BQDg4FqQxsA5pDM997tBvMrp0TlrQrdGzoTuaWwf6pWpTkJUy0JaQA7Z/0YAMFTG/4HzcKAg==", "signatures": [{"sig": "MEUCIHPY2OKfarhc8UhISnKPqw8ig2TFHJ97lJtFabIrQrOBAiEA8mBvCoUQI6MWCuhU8dTYwJri2Crvau5n4wXRNRXcA00=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 557241}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"lodash": "^4.17.5", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.0.0-beta.54", "@babel/generator": "7.0.0-beta.54"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.0.0-beta.54_1531763989413_0.8757414425435319", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/types", "version": "7.0.0-beta.55", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "7755c9d2e58315a64f05d8cf3322379be16d9199", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.0.0-beta.55.tgz", "fileCount": 112, "integrity": "sha512-HnrWD7OrJRyNneDk0D1+hXAB+i+uN98dgkK0rPARIQjZFWUm+0XsIWSB4yu7kl9KSyao9lN7lR+119e+5dyU6A==", "signatures": [{"sig": "MEYCIQCWvWIuGkSSpGeOxbIaFd1QCiM2CQM2JD7xVR5MY419fAIhAM/T7sO5PvBOAX7X5Zjefd7X6AUoaUtwul9gZ26fxfhK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 557755}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"lodash": "^4.17.10", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.0.0-beta.55", "@babel/generator": "7.0.0-beta.55"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.0.0-beta.55_1532815609196_0.11008800596351942", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/types", "version": "7.0.0-beta.56", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "df456947a82510ec30361971e566110d89489056", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.0.0-beta.56.tgz", "fileCount": 112, "integrity": "sha512-fRIBeHtKxAD3D1E7hYSpG4MnLt0AfzHHs5gfVclOB0NlfLu3qiWU/IqdbK2ixTK61424iEkV1P/VAzndx6ungA==", "signatures": [{"sig": "MEUCIC7CpjLueRdxRrnnyNPAZnpUnMDM1aUe5BBnXihYESUgAiEAkdRvjGUX/ouj5XzWAeaIlIGeEy7zxITS8MBPEUdrooI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 557755, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPtcCRA9TVsSAnZWagAAcGAQAJBEy+YFbDBtgQpC/IZg\nzX1RajFfHHGlALYX+GjsvxH58VvJKZDuFQQmTmgtRJurJgk6uFyYE0T6STvN\n7istwLnEpyvrq6nEamBBVax0SDPJigHcL5roqkzSNEAsizN5yN/53Ip86ceL\nmDZPLeRHffu6Uj8IzoLDkeV8SWAiAYjUTseg1jl/N3j+1aOvHQIijzYS+m4E\nzgVKVCyLX3if4nK4raU3xLVLgKeF3yrpf9O895CtfcfSgPi2nRR9I5LDA89d\n8F68l6l9cK2NUWHgFbqTyqjLX4wQ6g1IHjpcrnE0Ra/zc5int0kRZpCNgK6W\nY9+3hh7zTLLZ3VpplPE+RxV+gNWk9VTMLBcjaoWOyeHAP3jLsTnF9vkQOgVZ\nTPBqLaqcA27PuUK1E/9NTkDbtbJ4dqcL8IETzCPw5n//+sVJaEtSEqCvLF9H\nGYTdxeKjbGm2SYrWQv4WuK8YGVE1id+LVnP4RFxWxORTTiVzmgUHl89fw4W8\nEAPhGsFylmDVYg+rXr2kmGxOYOqaswWqopgOXut56D9F3S1hyGgnhIrFUjcx\nDuGNaVu7EAqARmdNm1nunHpeNDaZ3olkZprtUysyi75EtP9E9aR3/cU516LZ\nQirDM6b0XGftDI2SMXwWi+1QDMUkwZDjVNazPwHIjVhE24Ze1WfAzllCbD7v\nXChw\r\n=afjs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"lodash": "^4.17.10", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.0.0-beta.56", "@babel/generator": "7.0.0-beta.56"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.0.0-beta.56_1533344603626_0.21199898303306552", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/types", "version": "7.0.0-rc.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "a36aec79e316eec15ee1a49d4096ec9bb2db9c6a", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.0.0-rc.0.tgz", "fileCount": 112, "integrity": "sha512-St2paxNOc2XHnvlEuyKLhwYO3HcmHD09rFnfxgHwab+AqW6BK5bqESZ4HsrE6ZTIpkpJ4F4tGbKT0wXlCMP0tw==", "signatures": [{"sig": "MEUCIEEdu23T8l6dCR2XAz7gpL/CYXjY9pPCtyRmAoLKel55AiEAm2RF1Zczl3lj7w3vWc7ZkhAv0vk8aOLti3j8cJXWnDE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 557746, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGQ2CRA9TVsSAnZWagAAIZcP/js1t+oJCBv/ui4iJP1A\nrXYtKz3TPOZvhvaRsxUqLaJqHwjX44S0dzlgpQM/vB767cXDxmlel8fQC5JV\nnQVEctIjoOQwIb0a8M8aPMBg1/bHcdP05i/mssLlpHv7FK/0yrjQBHLr7lgq\nOujxg44lJ6YTJmOdliRWiD4HU6NYqgZ7fvsI1Oko2oCwvq8lAjOdnVNnt+og\ntf7d5jVYst0i0GZhy2VrMCOHILxWSRdgLQhBIMPop+/E011u3toRhYpC0R3s\nZgnHhTskErbYPRFGzttSaIAQJn4NBq4aucQhoCN4kKalOJan0FoU2B81ZBR4\nWViYZTwYq2y9/VPenJeiBWd5JPvtoWJL6CYOlwJhBww8AUCF0x+argJzFdNG\nGnZN2V/gtvfQ4GMOophQ3qt1Vt9PTTDdeAoiqIZOlnEIFYoQQel0jleRkJ7f\nRMHqoTrpO1DNGDkJyNSYYaOfR3iZOrUUQK+sb7cY+Gbfsv4zX41ioa2/jFP2\nB7N0gdz1w4zUcRAZ7Q3Hob6GIkWu5YWQHrBPbyzEqL1VL/XFq2smOm3MD7qt\nmJcirj3AGy9eNOlkco93xtZWUwrAEvaMR3BYBIEoI9oXh/bY/jmNLWsXOtmc\nNC8lCnB7JkpSpdYqqBDACeXZurIGwAKm8O8UVCxy2kM+s8kH+094g/ZJ1wbz\nKPj8\r\n=Kcjp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"lodash": "^4.17.10", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.0.0-rc.0", "@babel/generator": "7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.0.0-rc.0_1533830198133_0.8914326284616634", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/types", "version": "7.0.0-rc.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "6abf6d14ddd9fc022617e5b62e6b32f4fa6526ad", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.0.0-rc.1.tgz", "fileCount": 112, "integrity": "sha512-MBwO1JQKin9BwKTGydrYe4VDJbStCUy35IhJzeZt3FByOdx/q3CYaqMRrH70qVD2RA7+Xk8e3RN0mzKZkYBYuQ==", "signatures": [{"sig": "MEQCICvTsDwMcyNaGFwIPCXYG7TPwoN2vrua4H3bAvbJIlPXAiBtJ3XYUBi+Lf4ABn9IllQLWSeLz8bKRGa0o5z3HBpoHA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 557746, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ7KCRA9TVsSAnZWagAAMGIP/RR62s9f3Uku7Q+GAyXc\nsgMkVaxFkcHFMfuBkMdipCSajZDAv4kGYDI0KJbVeImC2ufs/fQptj78IEhx\nHxRpfDOeABe0QrecFQ4q94s8JTcirhAerxaxYUToYyqYkwm/4eXGAGybd6sq\n76/+9KrDmEQAHIpuj/6buipkzSTnlSkAy6rc52zZ7PFvgvrncBEJetx6wrnd\nicCcZYDcRi6CQhsCStdV4YCohGPuKuoYECgOqHOLlQCcl7oHLlzWLYXpJaci\ntS4hXXaHvYJ5f/BK2Q+kMq01fX/VnuNQ4l/dO/d3zDFyZJdk//dqNnnKv3AO\nF69n+DNVJNoXdXKCxKkELG1r267nzUAV4CdvKjnhDGEVC77cmzTq34XpvBhb\nVLeejoJ43trkeu17bXfDrU7hZObJBa8LTB96/jQONAxEd3V0aY5NGz1Argqf\nYB/ffbBUtlwC+JJKzMFeUa9NDlx+XcOt67+FKzF+i5aCambsnyJe08CShJm/\n4S+myow1J0LPUm6qaLBmo1L2YC1/ejB52X11wxZ45+358D64i7r3rWcGRtby\nf3A8ADjODlOkNefzMZSQPMr8mLwLjqbIHogi6YicgbGOEQ3ixq0+jG3oxxJU\nik4jcQX1fjsznYSXmUBRW9sJeL/d1HMaIeupmrTIkLRySJtpCJl66G8OkzmV\nEEQn\r\n=wCi3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"lodash": "^4.17.10", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.0.0-rc.1", "@babel/generator": "7.0.0-rc.1"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.0.0-rc.1_1533845191781_0.22688219130203469", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/types", "version": "7.0.0-rc.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "8e025b78764cee8751823e308558a3ca144ebd9d", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.0.0-rc.2.tgz", "fileCount": 112, "integrity": "sha512-I2SMGD8bUX7sysOwGM8TcwCoaHiOx2YWZmT9h5oAncsPQ9Wy068yJneCF4vkOGTCzPFIETPDR5i3EIEm5QgMFg==", "signatures": [{"sig": "MEUCIHW9b0MxKsTmJ57khgypMEDySjfVkwfjP61KfFwAG9EnAiEAlfYgwtVwlGREa+3hBjIX8VEGpsWK2mhJq4q1K0ctpR4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 557746, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGZ9CRA9TVsSAnZWagAAy8gP/A/QBpfXAj4wY7Cm06+H\n88ssoYPOxGu5hphVnXPREQpZkINsMBUNMpw3Z6Jq+BmfMjYuaR8XuZNqktOm\nA6CGUwRjSnWJUWJPtd/pCO2b2f0g6Wcm7GjXQuE5TnqE8Tvz0UucPWDseeLY\ng1/AXspO3XyuAMzZUNYyXv++p1JUxQDZ6DbC4maUqEUU6ZdCfUU0QIUI8k/N\nAzxqMYN7nbSzFfk8aB3+ASjZGskhDqxb0uvt4IZbDf3cWfZjoBMpwJKK1Zel\n73xibBnT0v4aLtIlFshRY5QWESgZSaFnnD5DA7Yl58fWO/BjUsYadv5EYYSe\nrTKEhqNGMsa2VhlONFKGbsFWI+C7EN8aVQkDlRykI+bn1rHpVkEk8sMWSRht\n4KGz4z+39wHt1zg2s16oJT8CI8CNM5jV1EiMjHiK/xCkRsqMHxjErkm9HHPY\nY6B8wFowIJ3w+wha1tTmVPe3ZtDdsYfR6BOt0CBHBvEulFIFQPZPTipb3pta\nOEB/y5yt8IJZWbWA7AJVwgfFqXExGeCWmAUHDCg7BbElq8mnCIYmwu36H/lw\nW/eDTHV0dtmv4/RRuGWkaWwrsYUA9gMlkOuv6ses+xwNQSFbUZFu7K/b99ct\nByjYA9BBmml87XxVog/TSQK7t9AdhFNPT94WQF3mWj9m49GGKzWER49dmTnj\nmQD9\r\n=49rt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"lodash": "^4.17.10", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.0.0-rc.2", "@babel/generator": "7.0.0-rc.2"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.0.0-rc.2_1534879357133_0.07464291755648755", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/types", "version": "7.0.0-rc.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "877ebc543b139f4a1e4c9bd8849c25ab9aea8f41", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.0.0-rc.3.tgz", "fileCount": 113, "integrity": "sha512-9sN1GWRztypQ2lViYR9HcClQILkObaX8vNFwrdgtMGB697xQePxSZqyhe8R2VR0afnJ1Jdg7y4X+IRE2CDRhDQ==", "signatures": [{"sig": "MEUCICP4yexJ3WiCWgAxADKsuOvLpCLErVaTNykoT9YVwhgIAiEA8CbgIFs1Xz/MbcsCAyejTvhsVJMHHLYKSKGEzRQyxng=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 558923, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEkpCRA9TVsSAnZWagAA7xAP/RwkRooCxS6FCFV75k6W\ngRR7P1KxwyWPseJIJyBZAn6xFJjDh1KvCEZRfgnSUrRwRb/nVHzCh0v8HDYo\n8e76KOoGerZUq9oARlN/FQFYcMJaqkJShnCNxKDp9iW2oV9S5s1kEbZWaT4b\nY3542D1WM1TbDxRwuv3k+8n6xI00ZL9FIfEY5Hf4iDao3Fgiq4rTL/vJTyht\nb2DT2smKovR6dRXXkwJR+1o96NazTfFl6IhwJz+Ha8oq7FOL3wOwp+Nvxqfx\np6c9EYAe6uAqJscq6A2bxZur40RX6W0GHXfwzkDf7E6W8oCRPAyAkNxGxnwR\nhUJ/dcTHyESPyN5Yy5iK4KGeReQxgIU8fPvc0tG0hyqR/pgOhmJi/gTE1+7T\nPkTv6NnrHiKIfqWXTIvw0Glhs2K8XD3MADrvq0/d8oAWmavYLz13jN+WDefQ\niCA3v/LRVRV7zSU3CbSjUmrjYZJ1WqndabXgEA18xfsfroE3bRilzhWosgCD\nrz/lx/WWWR+zi3bmIQcdAoiLivxlk/po2oPY731CGCRuyW3UjTN4dV6VCCaj\n1b8SQfAyDVr4q8RC6imPKp1y74nRTzYkOpoXcWm7AQRljXinYp9Mkl1QU7tI\nkcD8hSAJ9dkU36auGya+hKriVUoqkcWrTplptA/h29nUOLPh6UwyZ5qL/QRX\ncRaC\r\n=KvMB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"lodash": "^4.17.10", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.0.0-rc.3", "@babel/generator": "7.0.0-rc.3"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.0.0-rc.3_1535133992424_0.40886985503780804", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/types", "version": "7.0.0-rc.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "304dbb9097fadcd35a59a53859a0b4c519186b38", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.0.0-rc.4.tgz", "fileCount": 113, "integrity": "sha512-9BOpIf/b3C1r9r4wuEFcjnSntYi+ta+iA5aqRO795xM3Qyl6H9GqgvH88jaWJZ9yqY0e75u6z0ZBbAkudvIjHw==", "signatures": [{"sig": "MEYCIQDzTWsAuRNXD3pMzTyjWkgUNpkC2xxYbOuvLagxbdYbaQIhAIV0hQqaRezZnp79PSZFFOsYeMUwkBCAfnhxZIbO+87p", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 558925, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCoDCRA9TVsSAnZWagAA92EP/Aokurrcfod5dt5qoO8P\nt9MEveNra1eSF/PigYn/cl8zht79X4LgDVqmFQs6KO/R90DgmzmGp9PmR7xg\n4ov2zG1tsmB/SnFLQIth82n0v3qakH1GYNm7PMwovrRHKj2FHOs7kvEeayDd\njbnIE88nbWgO2b/TsPWZapO8wAR1fs4bTsXBaBOSjkIb3m69pFdSeRBljNNI\nrUf9oIprvTvK3Yvw+7I4Y+67qq0FLyJNrEjG9/b0HKAYjwJHQewuJsCPb89w\n3MH6OexgPdaUVNePMmqOowRqTbCiyGUFh10jxVzUTLFA5ETY3/AUQJo4e8H8\n7QPpCNBx34d74zQzi0sVNGZit0yVmMIpI61yo0rYCkzAFKlaCkS2PkauMjVB\nyU0xrDlSxch5PrrOh+ljYrFyxPDGT70rAKLedSm8BhawIexQhxxJikOs4rpw\n7VIY3k7pcrLXNLvhQYWUVi9l8UVJDozy5azFK1lYpyJMVMEn7j0MpIhGhwt+\nmZSro1rGh/JuyKVN5XHlR3sjzr1X9lJI4WI1tdXLE8W4rTq42N7P5RZBjvbh\nORc9DUiKXxk4WqKC0J6gJ25RzaWUVI9i4OYJO8KE37Izzmt+oQ6SDcvMJy8R\nMCgTlPcX7UQqmS2RoGpBJ6JypNfJHNWAkk7GFEgeTCfFBLhFYgOJaKjhLWrd\nwEf2\r\n=NRn/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"lodash": "^4.17.10", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.0.0-rc.4", "@babel/generator": "^7.0.0-rc.4"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.0.0-rc.4_1535388162759_0.19802452991654862", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/types", "version": "7.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "6e191793d3c854d19c6749989e3bc55f0e962118", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.0.0.tgz", "fileCount": 113, "integrity": "sha512-5tPDap4bGKTLPtci2SUl/B7Gv8RnuJFuQoWx26RJobS0fFrz4reUA3JnwIM+HVHEmWE0C1mzKhDtTp8NsWY02Q==", "signatures": [{"sig": "MEUCIQD4dUXqDYu18H7N9CgU5oJeIkmdSDNNgJwJhKa071ardQIgfIsS2s5K6n/+eVDP9Qx4njH/U4Vr0V9Afd8WCj1wIHY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 558910, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHAiCRA9TVsSAnZWagAAUaQP/2eYAYPysS7Apu5xu20r\nPsWlyeL839BuGi7qehzma5qPjO1/LxQRq38F8/j6rINRxragivJ2eANO9745\n9bvgkA+DjMw8J3weXOUZdGUv8zcNaHHrn49IOFxfn4sHRN4Jq8cl7OVL3J+Z\nyVFYMotN6RxyBy71+fJwXq45yMOLci5d1Vy6Azff59veHmOuBYhH0/gCgAeo\nPT3O3oq0kxuJqlIGlgo9hCN5J8wTfi9XzOrRatbR+gwYZWeFQeEl4V4BMRjw\nkohepW5rmWgTHfK+wJZFLSjFXtR+c2EYq/R4cpmAHH5PT9/F0QATxaCJ/+jC\nByHHb1mi4AU1S0Pwb/kTNzgXYJ+DYlPwQ4qTsOhuWXXIW3uZQu2eNdeJXMjO\nvrlAgroKwmQzXVF1o4t7c3PRk5IFrrI2Q7TbvTwPXrTVfnhR3EPh55r9NNl2\nGsX/4HRUIZK4VNyPfp1VQikdoFGfSSVuSlsp16PD5HVmfZh1yotlfR92CmEU\n+ojJ10xaIutkTlcH5pkvBHpo6iRi96OwZ0oxGBa6WENejKt9559JeAxrbdJn\nOb7ufsViJLSku7CJepjnp/+tXOMZUXEF042hXsIcsY0eKsBctYFNa+UyQCOS\n1T17kotT2PGUErqPLLFQ10mNRNaN8Xk98uTN9m63bwoeZxiYFzsvVZjHOIY4\nqBfd\r\n=bh9L\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"lodash": "^4.17.10", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/parser": "^7.0.0", "@babel/generator": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.0.0_1535406114168_0.023198178482132903", "host": "s3://npm-registry-packages"}}, "7.1.1": {"name": "@babel/types", "version": "7.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.1.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "d4cd23395df3e2c24d398a55285a4432e8e97a46", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.1.1.tgz", "fileCount": 113, "integrity": "sha512-jH+k//ZjhdYRum3LROaNwJKHcpjoA9ioMclz07XlZW73U/djzJ5l6U/dZCHO0uNH3t490/vFAdfh6y6BfmwSbA==", "signatures": [{"sig": "MEQCIAPGZaAF8vpo/qVNhdd6FClsHtO3gUcd/FUfu+l4l5trAiA/svU8Z7vnwT8NdYdpFB21PKgNGlL4EGveoIx83zDDWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 561070, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbrojvCRA9TVsSAnZWagAAl/YP/jqOgnc5Rps96Fcm2gtF\ngfJyK63aPw2fEd2CIES12CTLP5Q4MoBHZtmZzSqd5plDPtkBdChhNMdUv6ky\nIPzZxWa4gE4PXjWmDvp5oGAcA7fNWq/T9s5YsnuO/0NRwonnjk7EiewGPn91\nw5DYoneglHdDQp6YtWPNrcttZj72glVwy1kPaAhPIlOZAjqel9mAov8E6g6X\n7V7OpPEnOywH3v4NFckLUq8+EBji/0BsxQ985EcXHoMu+GokXylObaBeLPYi\nbOWXpYjLdo18AxlsxbdnSdFlvsbPTcPi/3280/sZwEmNDxpd7mic6o/XouFX\nNn3OLbdvLQqK5rbVdvRRu8ftjVVI/3b2NrSOR6H+di31IqJevQC8z4Q0el/B\n/RnF/dOsiYLFbU1P3AJr1e9kP/PtdfxFtqVZcO8fzeFLhPemaUttCIoQvpaC\nGFV2NWnUpaHJcLra+mTGxdgxa1h6UNRI5r4opeB8sahPRUjI5OlzSwLiZuHh\nplj9fr2tbIVk/SI+6HQQpGFrsoXhGLoa+1RR3DUx92yElr5ZESMlczMELBOP\n1FqaChLqztyw5MwxQPBuPmyimxOzsnO79HrMEFDinnOqP7pWcP3GnuUzwC5+\nxOelEq08tWZ1rJop01gKMCC20bS89mjfcgnDF3mB7X63CLHczso3g8LdWL7b\nHlGr\r\n=bY69\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"lodash": "^4.17.10", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.0.0", "@babel/generator": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.1.1_1538164974908_0.2481724748955294", "host": "s3://npm-registry-packages"}}, "7.1.2": {"name": "@babel/types", "version": "7.1.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.1.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "183e7952cf6691628afdc2e2b90d03240bac80c0", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.1.2.tgz", "fileCount": 113, "integrity": "sha512-pb1I05sZEKiSlMUV9UReaqsCPUpgbHHHu2n1piRm7JkuBkm6QxcaIzKu6FMnMtCbih/cEYTR+RGYYC96Yk9HAg==", "signatures": [{"sig": "MEUCIQCaxenYZATpAcWODm20+QjVpcPHUSV7mTAmHBBTJ5Xw7gIgVLbinOrD2sWwlNWXctOMI5g0584IzHMM5XNlCteGjW4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 561070, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbrqkGCRA9TVsSAnZWagAAlysP/0FJqmvapwIeW18ae6ZV\nMivLwuNIH4/9H2w2R5lqw6fBJFaOIwLfZjXi0R8WHygEYj1wnjnSrcG2b9CG\n78ZKMsqu1u7s3cBxrY/rEN8BU3tJhKN2ofiFqF8uF5ycRilYw5iHsq9lO7jf\nMcoUGRxnZ0CbVi4J41Rl7GTgWNeKIWW/qe16aGMA1N+n4wB8wq79M9/x4kgR\nKAASwaiS+jb2aAtzC9t99hVsZFQuNWtyBJT2CpTkhWixwqTT7x8gA7QHVEZN\nWLIVbr3o1Ry8zLsHzHFlG8iZOoqJlhhvVrEDxXrfhxrgzXjqZlkWt12FemVh\nQe51zJ/QxuLBNXhh4BCzhDfJJxbIVjYLRap7vFNNf9o78sUmPfEpW4s9X2rW\nLPL/w9uNCeRR0Lmp9VZA8XnDDCFsDMpflujD6Z9JfvTt6ra0Q7Ko0hDQwg5s\nT3mSs+ljVXEOAQR29uIO7+4bpc+rj2YvDlncj7fiPvxPd9E2on+1B4UinMml\n32SORzXzFLZzF8/gMiJnIBrHRI6h66SRj311g+PHXBJI5en8aU3cPRb8Q9uV\ndDQVCeDYSYTaOwuULS5Xjgbg0qHJOy27351oPJwGbYxTOT57ZicmsJhblwoh\n3OGI1JZO0dI10cjjt6+6n9rDZ8teiRa02wuBxmc/YdGRV6hPJE5hdOnNcWKp\n3HdV\r\n=bkX2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"lodash": "^4.17.10", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.0.0", "@babel/generator": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.1.2_1538173189655_0.8686717765793117", "host": "s3://npm-registry-packages"}}, "7.1.3": {"name": "@babel/types", "version": "7.1.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.1.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "3a767004567060c2f40fca49a304712c525ee37d", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.1.3.tgz", "fileCount": 113, "integrity": "sha512-RpPOVfK+yatXyn8n4PB1NW6k9qjinrXrRR8ugBN8fD6hCy5RXI6PSbVqpOJBO9oSaY7Nom4ohj35feb0UR9hSA==", "signatures": [{"sig": "MEUCIQDgA98YzHC0JJLSfulUMxgNWMaTC7rnHl7tfWAoefYtOAIgBpMaWuEAIPlKTVEt7CtHYh0DqkfNdFaOYduxD3qkEu0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 564156, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbv3G2CRA9TVsSAnZWagAAw2sP/2cI05CQSsUekxgBQQdu\nUDXIbP25+7+XUsMQo+vHGoJ2V3nXR85yTci28Kz31s6jG9c56qAuY7+GDXta\nXxXnR41j3kAKDhwToW/XgVPvA+TPGwDVj71DZc07McPqAj+lQGRmdwd07RKa\nUPfsdWTjea41FXAi0jbNGnFxPkvbybQq+mbdlRg4sF4iNrQafX+ULpx+Ox9D\nWlhbQraEe80b2DNCzqncFh8GNePIax/UlpNwlONjrZ51CD/NoTp6t4iS/lcm\nMu4maEDQieVz62JVb0b6Uzu5+rNIimOaAONPfIO3Eqs1Nx3Y8eqXltt7II5j\ntxskcmMYaljHmepUdLGh5YEbkG7ByI+p0/ezt/yRoMaoBDxwlmLTybleZLCE\n7xF6EQAZVZtmybg50ZhiM/R5DVruq2X3Dzdg9fmbfve5JCb31qUTZoclF1Sy\neRerX1nR3mtW50MmflQtZ9aruC2jgSwTkiQEWfcia3D8irFEJPw3XO6bUgvN\n7jvnbHtkYPvg1Tm6cOqm7XGkBgHzo3IevBekJWC0OeMJM1bJVu6blOJHzKPW\nMVztwWBYn1qdt93zutZ7CKSlxJ6PCNziVEe503KPoFXnoosL6zvbyQrklfCs\nngGj0C5vbXJtgeR0X4hRHE4ORbL40XGouKYRvZd0yZ9YU7mZ1FlNSpmZiW7L\nLC+Y\r\n=C408\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"lodash": "^4.17.10", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.0.0", "@babel/generator": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.1.3_1539273142013_0.759721901686224", "host": "s3://npm-registry-packages"}}, "7.1.5": {"name": "@babel/types", "version": "7.1.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.1.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "12fe64e91a431234b7017b4227a78cc0eec4e081", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.1.5.tgz", "fileCount": 113, "integrity": "sha512-sJeqa/d9eM/bax8Ivg+fXF7FpN3E/ZmTrWbkk6r+g7biVYfALMnLin4dKijsaqEhpd2xvOGfQTkQkD31YCVV4A==", "signatures": [{"sig": "MEQCIDx8OaxR4SJuWXr9AwnrHYA3qNhmBQB/rfAsRStEXAGQAiAgmdtNMsy8xRRFMvv54SEPkbJGfCuwD6f7gw2rptRPKQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 564792, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb4hPtCRA9TVsSAnZWagAAZnEQAIkBnKNiedkusokg0R2t\n7UD5u0jkdy+Q2ANyO0rbMvofXg3qlNP/6h8MOGLtxxlavxqqA4yzXZRoF0im\nvaQZ/G6V+83LKKdEzJMRRBOlzg6iNEUqU3C7mfcEug7X6AAZCLPL3zPrXOGf\n/rYPqdcUsec92uUyk5lLzAJNf2rGWxZdgHadR3SNlXWimLZbvJlKqf4SaQgn\noPfqt+9NlOUJDfl/FV2XNEpaoMF387q7oL4+39vVjb4ns5pSrr7wPRO43z+F\nKJPNdmWH0hL5Xma3jPAqSgCbAYyLApSGfI/+NnXeFtHrVqpJlHicaFKclXRP\niEO+DB6T06AAN0hqsdhJSV35oyZkkqxkkHDxDsffYEs1gt7GZZt/EkGtQGlG\nlWpESC3A3ET9q9SNpwJUlgDfAImzjazO4pky149N5t/NP3sBQEXR2zFNApjF\nl0DdhlZ/TKiEr2fblaHC63zRgY7tjrJZz5qMHWGy6kqPPOvIzxF6RIUPAifq\nCeBGp1DOrqf/kTy+WihMBM3lVvpQT2Ls+faYV+neT3oF9unbfeihMdfvBzbK\nis54otHJo9L6l7AIanRfbrItgWSGheblM83qqiOqEJwA08WRTvfpIwPpauWD\nJwsAcNjLKF++Os7b4/+2sgM95JWp6Hr+Cp/H22RQabIvw10YaSl9vJiK/FXm\nCnwn\r\n=YivP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"lodash": "^4.17.10", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.1.5", "@babel/generator": "^7.1.5"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.1.5_1541542893037_0.5858388278507363", "host": "s3://npm-registry-packages"}}, "7.1.6": {"name": "@babel/types", "version": "7.1.6", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.1.6", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "0adb330c3a281348a190263aceb540e10f04bcce", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.1.6.tgz", "fileCount": 113, "integrity": "sha512-DMiUzlY9DSjVsOylJssxLHSgj6tWM9PRFJOGW/RaOglVOK9nzTxoOMfTfRQXGUCUQ/HmlG2efwC+XqUEJ5ay4w==", "signatures": [{"sig": "MEQCIH7dMAO5nbQMwtolhcTipxYUVhTZXycKyYptHryvvLRYAiAXkWVn/gqGaujKupDwwYIjJZul2DzCDMD/OWh7VUZPaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 564945, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb6z3MCRA9TVsSAnZWagAAN9UP/juulPCe6h/6fvWrKReF\ngjILc/5DEKeNLMvNimOog0dwkHl1cMKz4Xaek5T/wabYGBD/8hqmzJ/EoUJX\nmkeQ4dk+qyrOZfMNYJmaBceTaKyvBLO+MyowdPortoPx8qTCwKOqCoRLL8UR\npTPkkTRlxveyL/JfXhuWxgNZS+IlEIVq7i6dDOG9luIQhDqqvJBI00/Sk6k6\n7ZwzbLuYa+j6a+Tuh9DBPQKGGR48M/QWnlK/Ou10MMDHg2Dqk//6x9fMU4im\n1uGsz7mLLifAnNE6MBNxu2zNYy2PZ5basUzW0zH9nxtedZvDgERAHP/7h+lf\nLHEG3P+3DW5Z6U6bqm8ZujR/LOXttUlJmW8Rf1W6kd4to7XkqrwUss8Zcy9l\nqLy+dF+z/c2rMPHBj8gEWSCWI0bKbttlpyy40gi3TIFSQedYn747wbkPv8Y3\nJhmHUmgKNV6K3Z8q5nKfvFWT4Q/IaAH1/6BuSbroPYTzyrkGasKkWKy35Kfk\nHvfD43uAtcKZlfIGee7TPnhbHs5gS9eDeT6VWFOrXOIznYvIIeMncb+Y1GzW\nWLwGTuTWduu6WFPSXnPhKIDskvxMRhRkbWNBDpoj6zs8tbI4nttIrril+ODd\n4pBLuiEN0CErN5hig1Y/MQBXSbX17NX/Odz65auig3bJ8MKhuY+uAh1LKS8Z\nhGQY\r\n=GDMq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"lodash": "^4.17.10", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.1.6", "@babel/generator": "^7.1.6"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.1.6_1542143435865_0.5826150212192625", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "@babel/types", "version": "7.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.2.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "7941c5b2d8060e06f9601d6be7c223eef906d5d8", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.2.0.tgz", "fileCount": 113, "integrity": "sha512-b4v7dyfApuKDvmPb+O488UlGuR1WbwMXFsO/cyqMrnfvRAChZKJAYeeglWTjUO1b9UghKKgepAQM5tsvBJca6A==", "signatures": [{"sig": "MEYCIQD19PCRPCDeKoDMqplGPnRz6tEJriEh9WN48OlnbgcHKQIhAIjUgnjjtW2RJwvtn4aWqNqvOXeTNK7sO7o4MY6nt54y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 569234, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX2rCRA9TVsSAnZWagAAXn4P/2kJXZSQVK/FljvZkcsM\nAd/H/9gb885xS5JPTjFMage++gi0bQAqcraZ9QRvR9IWN/lIhFsLKs5nJI0t\nSSyl78D25QnIO/yibg2ol2rg3E3yaNAka/AtITygISEi280Sup5pAk6PWJsW\nWr3F3rdhNOWjnKZVV4RjKziruLGWNdt1syt528HX1nY2fFVOO3n5qenMU5k6\n0EJHWX2xY3uRo55OR/6zcw+7uxThRl82Vyq33dXwQb3pqkO1ZEhVSmjZS4xd\n4omwQAmU0wYiykL7lWmzPrAC7pd6OrJ/ghfx5bE2SQGhGgSXcv5Vv9P+VwNL\n9EKQ18levQ87oajgJWv23zAByLqBBz7fFmk0jH4hdzIIDttxbze/ST74GtWy\n+j6nXLuJnoR/oxx7oP1UybzwBVQbjIQA/zWQd3hDFW9IJcEYEapZTbkgUbGz\n98lQG7U0NBABfcAHMTRB63rQc6eVSxFuYae0+sl8VCrHHsLFV+d8nLOoNqJ+\ns2aKJODQ4F5Lq/fHY5kEJ/CPrOg5VZLJzcpb4FfGN40PigZC9bwW3ipGxTUg\nGAdYFErTkwF4ovEk+IQNRnPhFtiXyY9lVEdnX3afXY+sCGI/ndzPeSc58ZNW\nkogeHVecrsENaH4aia5je6RcXgBEseKo5E9BDOigA9Me+dFLZMyGa/SGYusJ\nXIg6\r\n=SSHf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"lodash": "^4.17.10", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.2.0", "@babel/generator": "^7.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.2.0_1543863722501_0.6906388443214184", "host": "s3://npm-registry-packages"}}, "7.2.2": {"name": "@babel/types", "version": "7.2.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.2.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "44e10fc24e33af524488b716cdaee5360ea8ed1e", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.2.2.tgz", "fileCount": 113, "integrity": "sha512-fKCuD6UFUMkR541eDWL+2ih/xFZBXPOg/7EQFeTluMDebfqR4jrpaCjLhkWlQS4hT6nRa2PMEgXKbRB5/H2fpg==", "signatures": [{"sig": "MEUCIQD4s+fG0j3GEOskSyL4smRVYA7YwpBS9Nit91fwQuMhhwIgdwIRNhuYevBHmvpAPp60yVReMDs2n2EKj4ijZjORYjg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 574302, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcFNHjCRA9TVsSAnZWagAAIRsP/iScO05k7Svtat0/aeXK\niRVWh8bJWRHwldszsrat5WGdd4f6ln9lU/xiqTG+OlM6z72cDiwmJNwP5WZz\nLHAm4WoRuo0dcruMRo5aoxKoSdkzbaqRlZZLCdwjT3jucxUQCP5XJEux96ta\nxRaZofMdilsHn8rhGGTZIsOfl4E9T07UfNunp7aopNoxgw4j+koZdZdlrxfF\nMBstYK/PgbtD+BFk4gehMVK7agx+GsEh8tnDvmKTmvn8GXdNt4sQEI6VQj5Z\npt0DYeipAicGBIdDDGXVCVsCgea5sCTurQ4zOFd5UMozlH0wvserx2c2g2Z6\n41+ySpx4Mup6H2zyPojYObX6cNItkAtGDCuObbSjka4b3F606pQsG5pOUJqW\nPbvmcx84w9Jj7KLTrKuwSy2d/xxXf9oemg7tRusX/mWyOfLzMNop5LpPuegY\nMVvDCFR1XZDpMM48kD9QLl3iTkXRC9RGFQ0zEu1t4hkr7Rdy7cTaWZ2TPpYy\nuFE209iwRMwDHy+3m9y29TFQ8KDmih9eNsbA7PK87wCehqINR+XZu9z/3O+A\n4qQvnNPbGRFoDQMEaUNHelspS49QWTJcU8nhCamwR2DDiKu6J67vWZqnNtfF\nJKBwbEW8lIOfwuptYR0p/0AxGBzOix2Ei1KiALOtxrNRXYJwTdWnrmsf4iLv\n7LL3\r\n=7kbZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"lodash": "^4.17.10", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.2.2", "@babel/generator": "^7.2.2"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.2.2_1544868322909_0.8975348331884552", "host": "s3://npm-registry-packages"}}, "7.3.0": {"name": "@babel/types", "version": "7.3.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.3.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "61dc0b336a93badc02bf5f69c4cd8e1353f2ffc0", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.3.0.tgz", "fileCount": 92, "integrity": "sha512-QkFPw68QqWU1/RVPyBe8SO7lXbPfjtqAxRYQKpFpaB8yMq7X2qAqfwK5LKoQufEkSmO5NQ70O6Kc3Afk03RwXw==", "signatures": [{"sig": "MEUCICkDvJjXjvDRevNktmlq2lFGe/7TWepLSXgJQG+SDuXWAiEA2v4Jq68+tYqThNuG2ucQ285sCzv2wpqbTMCU3c73l3Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 591200, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcRjzNCRA9TVsSAnZWagAAi9YP/ixf2slIMMayLPZHxuKH\nuVzB8kRl2BGtDDyV9vm5ZVrPTLXG7CXZA82SIfKIqSSB6gh+FBcIxV2F9UQZ\nX6/iBx7BlevlDRnK2Gk+BFwO9R65xUWrR9NiUA7IbSRR+KW5Ye6lV4lgTCMr\n8kQ1Qz7iUW2NRIdduz5Yw6ADDLCKovawx2TMItx/+iKk80o9KCzixqSc87T2\nkIo2GSy93z7LrQXRcb6VHYI1UynL2CDvZrADPJ8ItDALFsVFDN712NmpL+dJ\nPQkMojAnVFh7Ap8K9daO6QWt3lUxC9LnKNbtyu5cIiBTQ55R2LcLszZM7HXk\n5FqpEKQkDVMlm11rAT36zvBzTw37g0Nc25qRBqhiZIjHzDuqA4aik7UVrgMr\nCKlqvS0gwJxQun60sZytCzj1/y/3832LPpmD+Qcql/tGBRDfyhYzjYyMwfbZ\ni2LznUknaFPPdzfq4WvdvpSyCOFHhuL5OscXSyOhmyCFIeM61jr9dQjGqMya\njRD6CjpE/XT5SDdkyfEMxHqqImgNWQsOklAQHkl9wYcGef9UNmdD2NSAWIvf\nVDVuxbSbafX2SkZHnikFuN8b2qBB1or+g6pj2U6KBK0M+775NGkOcWPELs7M\ntkNsqOz0XlJY+c9M/g4bi5qrPv/m/4bnlN3Ah+Lv3ha2pBYdKNsA00bSWydz\nY9e+\r\n=Jstw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "6.4.1", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "10.14.2", "dependencies": {"lodash": "^4.17.10", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.3.0", "@babel/generator": "^7.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.3.0_1548106956621_0.5915004352910034", "host": "s3://npm-registry-packages"}}, "7.3.2": {"name": "@babel/types", "version": "7.3.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.3.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "424f5be4be633fff33fb83ab8d67e4a8290f5a2f", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.3.2.tgz", "fileCount": 92, "integrity": "sha512-3Y6H8xlUlpbGR+XvawiH0UXehqydTmNmEpozWcXymqwcrwYAl5KMvKtQ+TF6f6E08V6Jur7v/ykdDSF+WDEIXQ==", "signatures": [{"sig": "MEYCIQD6VKBkFL44hgjOMZzTfQwtCWGF/fPAYSWFYfIL8qf9UQIhAOJoIUx94Ecs5xh4k5wK6bq8OkvUQ0AnDDGkCkNxGl/A", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 591910, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcWLtQCRA9TVsSAnZWagAArswP/i2uJNqdJlEcqbYoZDHv\nUL8fVsD2dDTPEKvtpV9okjJjNilYgR6WARo1w0j/6fYXuxFWY/pnztiR/jX7\ngfrsuGfPiTtsj+hRv5NCdinnM2BlVZhwytQniNcZ4l+efJTMZ95nIXNNdcZc\njhhMRPgrkrvtqg8AE9BbPjwe1rIHAz1TOhh4c3JjjEIPQcBeq+1Pbf7YtXFJ\nWliyRNA0FBNUCvPzP4ezfjF0eOxjXGsDtDPgEvQGrArmuh5UnMI4WT0IT+hF\nGbfL4618uNxmi3j3Mj2UN9f9n3i3Y/VgqVUWtV5EMIR4Y/d2ERkyEWR4hyRt\nWzM85IofObPdHp4//OvpPNg0jMPuOXgFIJ4sFzNdPor1oD4jwNVPKSIP9rlp\nTJD0Jb2LVStUHfvX7KcdJt9ZavTt7IpOKu95cdSWASdvt5ARkXogd4mlIv0B\n/L33zDqBkXC/UKSlwpWxgrVDnisoPjopJP/2SI8320782EYVF+ZZi+b67Qqw\nOkZBFAA7l11bRDhxHQS2xt/B4oUYcFODsY4u/SVhMIUnH41yIyS4tyqJQTct\n54phpZbQ+blx1KzQaC9VpgiR6+CKfbuczZL960fHeoAmKbE+6uTEGkZEkTK4\nL7Pdxhfbn3+pefmrKvY9bRkn4I0FvI07P1nGY8UbD4tuTOIDbyhdb87HPCib\nI1r3\r\n=E3Rc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "d896ce2b53f64742feeea27dd33ee45934cd041a", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"lodash": "^4.17.10", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.3.2", "@babel/generator": "^7.3.2"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.3.2_1549318992367_0.5749419173253438", "host": "s3://npm-registry-packages"}}, "7.3.3": {"name": "@babel/types", "version": "7.3.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.3.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "6c44d1cdac2a7625b624216657d5bc6c107ab436", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.3.3.tgz", "fileCount": 92, "integrity": "sha512-2tACZ80Wg09UnPg5uGAOUvvInaqLk3l/IAhQzlxLQOIXacr6bMsra5SH6AWw/hIDRCSbCdHP2KzSOD+cT7TzMQ==", "signatures": [{"sig": "MEQCIHLtSw22kOdxIcceGKm7u+8orzt9NQZnF61SVejzqzs/AiBBvTAkAe/MyfS1s6lzgaWi/xT3GalGEu53rfZF8anuEw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 594310, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZyu+CRA9TVsSAnZWagAASWcP/iymH1/t7JjLIlTOc9a2\nRca+Zea28+QMkeXCoqFD86fJk5AvlNaEZNusM0jtGf9hltzClcLEICdquprG\nXV9LelyXs0zQIB8FD44UB1W5uXKgF1fHKGWUHMcYum8d8qa/eOk2SnLlhoey\ng+1MxPjhn+YDtwfTj09q7iLC6BTuNai6LgRjJomhM3sQ0pkxdJSy4M3ixNph\nHGDVZbG8JMR4ztrRmzAZeFkhRPFpxYJ1mbJBrOx+pKkhga55zpGhKmMld0u5\n5mRa/kS8UzSauqQTgqyS7onKK00HewcfQW9UEII62nAjQEc5/Hf30TYAmw6B\njUASOuT9e6GSL4OY1RpKLTVP7noE0V+Y3AWPnlccQc5Giy3ZPrwxhtOmRm1k\n16NKn862CydMg4dPcpmmKHDsq2LSxunwUHx+l5DeK6o6LOUfxx1qOBrtj2Rl\ntV1LgTX/DQ39b62sYYVvzsybMLw1hAJYIGVBN1sjMXqDOfK8xtLsb6e3dkit\nEK/QDOsdcr6n496Zt0l63avb6VGGz5Kap/CLAMfi4OP12KpZ6LXNSdXK6/RH\nogwLIoiiFqilbb9U4pxUu5PfjAM2A39mYqMa+QbKDz3+Bx4VUFrSmecflcoo\nejXPFR0d9c2x7P3daVXlNBYmWU0Q4xIlwy1WwbKzrqrqFc91dftszFgcRxRQ\nu3c7\r\n=e9zj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "d1fe2d05f4c468640facf40565e30f7110757f2d", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"lodash": "^4.17.11", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.3.3", "@babel/generator": "^7.3.3"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.3.3_1550265278083_0.7066531602629298", "host": "s3://npm-registry-packages"}}, "7.3.4": {"name": "@babel/types", "version": "7.3.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.3.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "bf482eaeaffb367a28abbf9357a94963235d90ed", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.3.4.tgz", "fileCount": 92, "integrity": "sha512-WEkp8MsLftM7O/ty580wAmZzN1nDmCACc5+jFzUt+GUFNNIi3LdRlueYz0YIlmJhlZx1QYDMZL5vdWCL0fNjFQ==", "signatures": [{"sig": "MEUCIQDLZwtJV3SbVyCcE2w2qooMoS46CHMJrDM+vzBzlZbHAAIgPCLAMWTFskpBolWRItpkv9MqpllYyEMT4NHwB1yBRbU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 599671, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcdDV5CRA9TVsSAnZWagAAlN0P/AhATzjoNsgz0+W+bvte\nmu0WWLiMy0o4TQO50xbm2zx1IZlRuT9ecQunTCfreiZ17/RVyl7aEr8c1bcX\nIucD6LpwahHukvef6Imj2lcYtxsVSGXMXj0UKzUoXj7HUW5xOffI64m0eTZB\njY+jADx4fi3JNgnnWW0qYT5kOXmDaj69FW0J2Dd/G3fCQ++56lAZ3zrTrVJB\nhulmpxOrmNZIauZyFFkSxuFIxzMVBczThPket990daBwjVDalNodN0S5KS11\nsNB6kqPjsG4m0Hs/156mgluDco4CAbegs01AqKvR5eJ/6ztBqbPHEQJXKCQH\nqcCWb44E0ScMEZ/uN0SQ4JxzHIQUgbrzkqDv0IH9nQ871bPPaKS0raVBNixg\nX7lyZqHIppEioUbgWzbG3bmINwG+h93rXBsXFD65VzevhRGvlCYTkD7m+Duc\nj761w1vVzLM9wDICfFpFnSX8GILSRHyc5rggSVzzL4AW+u9yU88zmhGefIaR\nkwiwc3nRCGWPK3pQaU5DeuNBIvKIyYSLhN2BL5fjGN4KoV9tYhYLa/tB/U5d\nCouUrrUgxo4TnQZgHlR5VzC8p2SoHL6PulRkSG9w5HsT3TiQGt+mBVNUcA+f\nv9M2wuxijHDx2olgZdq7f76mKGoFvWy9wVs0qPRMWCkRxhI9on+pHYiWMBKp\nNxDy\r\n=mTRo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "1f6454cc90fe33e0a32260871212e2f719f35741", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"lodash": "^4.17.11", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.3.4", "@babel/generator": "^7.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.3.4_1551119736384_0.5159646196981387", "host": "s3://npm-registry-packages"}}, "7.4.0": {"name": "@babel/types", "version": "7.4.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.4.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "670724f77d24cce6cc7d8cf64599d511d164894c", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.4.0.tgz", "fileCount": 93, "integrity": "sha512-aPvkXyU2SPOnztlgo8n9cEiXW755mgyvueUPcpStqdzoSPm0fjO0vQBjLkt3JKJW7ufikfcnMTTPsN1xaTsBPA==", "signatures": [{"sig": "MEYCIQDAc+kB9Z1bajRYbE7dSzsZUTPNGtDjaiuM2td/9V7QvgIhAOtDTf3zt0UCUtoHlsmbw8zQuLvWXoX3nBA071uAvjXo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 608394, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJckVTGCRA9TVsSAnZWagAAWe0QAKDo2JG1JTvC7h1TXDbW\nnvyF4nLZtbd2BIVBNthk/kJeNP8QE0GH6n5iSzzkG0nq9v9i2A8yNWhxOkxZ\n8qF9HsqTxIQfkTu9MLzO7E4vFQu5bClkJPPuOlHcq3CQWr5Rx/A+TcWu5i14\nFSx6+i3B3bKaazwBSI7SeuX2NITKwX0kNQ5bXtPMD16yZAg9c5w260zMaUzX\nGXoJou9tkvzk5KoIrJ2MbnI5i5j+9mcwOi8UArJ6//mhxn0j5ngqb4gFoj19\n3ftrQJMuYknc5f6iyYXwOeps/xZZJ5E/I9+kUfB/1pZ5cahL4/AhHotMUmre\nK3EbEgngAjkZcrkzH8tcj2QeO2ZZSvzurVMoYzJ72Z6VdUPPn9amapceElda\nQF8uYOYffkYX6o8xSGgbkJYZIFZVsHT8/+Yl/B8fJw7/cF3k4PKgLctdvDCP\n3naJNiwCdj/yR9jHYh24BScz7JCkltOchsQy0dylxb2rLyJ8rpoHtvfA8qEG\nmAbY3C+ofzVSjdyXWkpy6ItYVaTSw76BFPXu7w57oMst2FsyWREXtsZWHn1o\n3g50lgN0Atw6/OQPnUPzQdeinj5NNcERGR6i7CbV1hHtNKogypmtuMNp1xOo\nmYfFSa10izfxoJ3KEy5jcwKq+L7oNiFuasr665mgmk6UKqbrPpgE60jD9hFJ\nKLvL\r\n=nbIQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "f1328fb913b5a93d54dfc6e3728b1f56c8f4a804", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"lodash": "^4.17.11", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.4.0", "@babel/generator": "^7.4.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.4.0_1553028294458_0.5680571275592368", "host": "s3://npm-registry-packages"}}, "7.4.4": {"name": "@babel/types", "version": "7.4.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.4.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "8db9e9a629bb7c29370009b4b779ed93fe57d5f0", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.4.4.tgz", "fileCount": 93, "integrity": "sha512-dOllgYdnEFOebhkKCjzSVFqw/PmmB8pH6RGOWkY4GsboQNd47b1fBThBSwlHAq9alF9vc1M3+6oqR47R50L0tQ==", "signatures": [{"sig": "MEUCIFlSF92wp9KN4d7jqLyese/AdVaQyGh0yYYMnsxcLri+AiEA8VMWpYEmlFCai3OvP4x/jRH49v8nX848PpB74dqmNtM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 608440, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcw3JYCRA9TVsSAnZWagAAOIsQAJB3mCt/nOtZ5vbA92To\n7KM7N0y/13p0cHRFd39EacdBdH+lpr+BgqOo56r+NRsQoBqXH8VTRiLiNTv+\n2uSDTz1DRPubAIHvjHmatJaJcwRWcu2Wm5y1VFTor9pkMjQrd0oeNdqTOQEZ\nHflvgDWFLA8iJbk6O8tI/t6AGb1l1uHgSiLBzUwIU/hH4jPNmQtXv3IJ3PYM\n6dqtoLxTLBgPWkofxf14Mo7nDaeu5B0GlC6GqDzzPOiKL7z2r4Rg4VQgm3/Y\n2U8viqpvDthmkhBQF0vXGr2UQd14kNVlnlQHzwDmRmzJI8jS+7IIVidMaXm9\n8uB/+EHKZIKCAUQ8vUFhYdPDndBoOR3XN9MrZY5eAVDBRggqd9uRar/h66az\nkpoToQ7IX6WsQtEaidoCGfj/6adESukZqxzxLo/MEsRs+ZL4jhBT1GIPGLFU\nEwhqQE+CPvlnWc/9t1NHGCpeKkS9RCQLHiZ9Uf+lmOAaZA/swdtiKVHoUMPv\nRxL11WZrlAE9KQlQz0RjaWQAqHZRCWfMntiHo4rsJb6QV7oMjjG2gOk8hM/Y\nZJkqM/JjhJO+H5o4VsOChpKeAXkt+yLWtj9tUFhC+PK34ZuBgUONmpqdxx3p\n4MLk1su18u2utb5zDA/UzywR/6ZH7G3xVcmTO/ld6AsMBPwK2NyKnx5QVIIp\nvhho\r\n=QV5e\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "2c88694388831b1e5b88e4bbed6781eb2be1edba", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"lodash": "^4.17.11", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.4.4", "@babel/generator": "^7.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.4.4_1556312664011_0.8218100352346172", "host": "s3://npm-registry-packages"}}, "7.5.0": {"name": "@babel/types", "version": "7.5.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.5.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "e47d43840c2e7f9105bc4d3a2c371b4d0c7832ab", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.5.0.tgz", "fileCount": 93, "integrity": "sha512-UFpDVqRABKsW01bvw7/wSUe56uy6RXM5+VJibVVAybDGxEW25jdwiFJEf7ASvSaC7sN7rbE/l3cLp2izav+CtQ==", "signatures": [{"sig": "MEUCIDB3R4n8BXP0ZvIF5ISxmqrEvg73Jfo8y0cR/F6NlqvKAiEAhOX6WpdbVKKNc7en3nv/Yumj459HhJWsv0Dv6vSJIac=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 608935, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdHffiCRA9TVsSAnZWagAAwD0P/RE0smA3/8XhAxWJb/pL\nAnHqnz1Q+vA0gQdlaU7VI/RZa5uiL3AOkMfuD0qHMQNkVrIqadlBw7z0dEI3\nXg3p4QiWMZg2EoStIRF/MVD5b7/QqUCnRzgXndttYv6ZdmYCLCWrtlJSLwRI\nS1CAC/OVVO3iJbLgkLBu1JciBQ4zjr4jUtfNuJxgaqhrpUqUIhru4atp6bb/\nAwV0Q5Xbl6QqZdlcaxQvsw2MMA4SwvTsMWFc/Rdj0itpoUIk6CD8AsOcbb3E\n3Wa1Dh12BKCBV7wlRpHjRKwq+9gVfJBDQzJbGgDSAIlTe5vZrQM/1us17aCS\n+XvsAt7MlwRaxYm09JrxdPMACdijjbowuIkARmPp1tihV7GMzJmYhJ/6S4FY\nXM3+j879bzfSNR8mXAZgQIDQE2mWs9cGAQV3a/xsQDCjxo6QYjel/NPk/om0\n67UXo3+REq/SH7jiH7lkyqX+iZdoWogSZ3a57nJwcfsDTKgfqJxwaDoLAGGN\nr0OcA6FgakDSPBL3WPUuOw0CplPWVKInEhO/zceQIVonawP8TiwhUnSqqStl\ni9oTWWEHO+muWIUpXqNt8w7dQfXvH8Mmdy4GXoAMTIilG4gQIY8OxwAuWOBb\nnBO5YeXtPSc6QM61GbPYbyhjj+QXfhprxjzpe/VBX7/d55Zg4Svg0Vld/gMI\nIofB\r\n=tcHI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "49da9a07c81156e997e60146eb001ea77b7044c4", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "lerna/3.14.0/node@v12.6.0+x64 (linux)", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "12.6.0", "dependencies": {"lodash": "^4.17.11", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.5.0", "@babel/generator": "^7.5.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.5.0_1562245090053_0.5340719923650319", "host": "s3://npm-registry-packages"}}, "7.5.5": {"name": "@babel/types", "version": "7.5.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.5.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "97b9f728e182785909aa4ab56264f090a028d18a", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.5.5.tgz", "fileCount": 93, "integrity": "sha512-s63F9nJioLqOlW3UkyMd+BYhXt44YuaFm/VV0VwuteqjYwRrObkU7ra9pY4wAJR3oXi8hJrMcrcJdO/HH33vtw==", "signatures": [{"sig": "MEQCIC6yZGIzWIUP3tpub2+A0lMGlkyxiVdfzNrE3cWqo5wMAiBhm/5wpr5XEgBvT8p8Mr2+UzbSmd4Di9FxBMHokOdW1A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 610869, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdL5FgCRA9TVsSAnZWagAA4tAP/1oAOhhiewExzvDLfgSg\nELwL8VGtcj1zstRzMKpXTfjQ5+4mCPuuMjzPX1P705JbzoDdQOf+PGRS1Ytf\nHQkCjYyKIfJfWQCbg1zNuK2eN/c1bDy2Iwa6O8l/hdPzFw9TNJnG0tn54nqH\nIbioSFoiqBwQLCZgqVTGG84P8KWePPDLEMLUozih6cJV1koYM+nY8Lgq+Gw/\ndm534/pWVtrGF3k8D8CKSQhXUFVwcI5SJ/50ax4LwY7To4a+rp8B4R3F/NFA\nasWFadz9GxuUVczttbvknpeDay9kcIIq2i5D8pT+PMe1Z1L4JjJ1969G69Ri\n9mOhtK6mE7rfP0cKJ2ZtesQQH0Xxd4QJ0WFVq0ufB5b72hiXng553mpPaH/g\njcDvJtjDChXBv98s/Ag72wbyFP0vhLeUafkg2y8MwrIVdcALLhX+/78vI/3I\nCpzEew6p7TPpdVk2ZRHsM0FqZx79EOP96Cz+KE6p0LuYQrZimGRXN4sgP+LO\nAyYdpS0gIam/gu2gSijH8zQahplLlLMfYhW7pqMlJCSgTCQVArLoNzd47kVV\nNqYG+uOyHl6KuXFc3wOnZNe0/dOL2N66fue8hwb3gAV4l5STy4JL+wdnKoAx\njUOefVuOul1gp2VPOpTkQ1gmP8muNsno6cVwY7WVEdIKpl1n/wSC0WPlAWL/\nvtGy\r\n=t/Fm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "0407f034f09381b95e9cabefbf6b176c76485a43", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "lerna/3.15.0/node@v11.14.0+x64 (linux)", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"lodash": "^4.17.13", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.5.5", "@babel/generator": "^7.5.5"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.5.5_1563398496298_0.650127759775541", "host": "s3://npm-registry-packages"}}, "7.6.0": {"name": "@babel/types", "version": "7.6.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.6.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "1b5eaad0736e963bd2c6cf7911a53c84a7b35c08", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.6.0.tgz", "fileCount": 93, "integrity": "sha512-+wLIp3XW60cvkZP/pvKMM85qoJbx7Hn3tNUpkGBLsGaSEYRz8Ut389/UsSa+wSBwSchtsLJm5IsqlA5sXawqew==", "signatures": [{"sig": "MEYCIQC43Sdo4gEpTLAP4J5eZZftmU5Wit0tLjh60pYV6ehndQIhAJu5QyANFhvp9AySaJvbvHYHUVeiG/510ErPYskHvCC7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 620087, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdcph8CRA9TVsSAnZWagAAAI8P/0VvnLZky2dJ9bTOBqk0\nrXIgf8zGrNvMuCFFl0N70mSBvziCieGm2f4/ss2MHppaAnmtEtW0MV75DRWG\nV/mUnmxNV6y2p0gGnCvBW1wI7rvuEcMtuurz1NrCAgFrJd1067Zoy9lOv1s7\n3+DvpWlI/hvQpWSrGPl5D//HHfS5Bv97vGyDnGsb/Gv6kNfEeozJ03N88B/h\nRsMsFDrt6/7f4NR8JjaGGds95UyfEosbKw+I8zA6uQMysQIoP+DYFbhFzaWy\nMHuiY5e/pg/M+dj8lXx0DjWozqzC8EA5wjSkl9eCN/fu0RA4M4PSzt8tkWvH\nD+aEnu8mlAMF1Uy9peyqrk3Qh0BYYRsqmwZseAWfvr0NOsaY/3HSnTIfqMes\nS/Litf/4sf/zV6CaxnY/jdgJLxdwwHp7NIrGd9t91v2RuK4f5Ud08pzBgYT0\n2uULwtSAUThrDw8quaAfQ9K0PWMNTQ4A0pg5my+LKDDpdbomZsrkqxdu+nkB\nHCNH/JvMFwBQEVojJ2CdPoEWPnb9VEk0WySf/wbEqHbKI+yNmZx8pj0NSk3D\nS5wLH/gRYmpXWrrnYaS2G+4BHjpXHiRNREpa8O6lhVlMDiQwvFpdKi0yS5Dh\n032jNIhBLRbg+tM2ebm+jQBm6caRYt8t/vyKGoA7USVJLa24I8OIP9v70Sk5\nnuWQ\r\n=82WI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "cbd5a26e57758e3f748174ff84aa570e8780e85d", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v11.14.0+x64 (linux)", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"lodash": "^4.17.13", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.6.0", "@babel/generator": "^7.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.6.0_1567791227075_0.8163771467778957", "host": "s3://npm-registry-packages"}}, "7.6.1": {"name": "@babel/types", "version": "7.6.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.6.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "53abf3308add3ac2a2884d539151c57c4b3ac648", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.6.1.tgz", "fileCount": 93, "integrity": "sha512-X7gdiuaCmA0uRjCmRtYJNAVCc/q+5xSgsfKJHqMN4iNLILX39677fJE1O40arPMh0TTtS9ItH67yre6c7k6t0g==", "signatures": [{"sig": "MEUCIEW5NxyQPWDQaLfvasOKlcAIg9xU0+oeVjtuZ6/hqyPPAiEA8hcfCvGPOBjy3Jd9iJpnZRU/YxqtogS8OFb2VvOBwJc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 620155, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdcsdMCRA9TVsSAnZWagAAqGcQAIpT56ZqqHAbUOELKbEg\nazAIaJFhs0/hTHDZ1LrCHZC7dvdccqendnVgONO/DR8kS7LnSjqB6MC7/Gzl\nZOxa0TzakcRrrPMhMnDrgnx2aNtA8BZh7D8YZztwg3EiTA8eG6Wp8lIfoVHN\nWRDhqm4eZPXGvZDEV1iSFDg8Z/KUAFBBrhcTxG1voaLBUTukmm9UQ7K58vme\n2u97PhMjkGyPwUvRqd5A+fvav0UbWzbiL5cVwjCZO8ZX+1riDp0g2EaqwSM5\nN1xF6fEO+wdRSIkrPuYzyAbQHV8eWfwF2XVpa2ahDKw6Y4X4/bszX0on79rH\n0n/93zpnoqocAh5DSFl3yfhM1WpPUE14IHLRMtqFNmR96I+RNDuFy2uqFg3j\n3OSZR9EFwBC74G4bSulZNcmni/egJEgIWQk8jvYAZeqpRRBBogtg1nWrsOj4\nPcYqfuNgjo98Q35MFwvEZdbqTdXxQLWcyfyeZNjx8AVh4f2oDqJh9AKgg6Tw\n27qkC9ARHh58gG9M7xGs5IgvNVuZ892cfAcpwRiPYPawNqR3/aHKiWoS+ncp\nT6lB8izJnDkv/gJNl3+FmfeVDlDmMx70gmiuDTOINSnEyCxWGuqsy7+woFrd\n6YJWRdz9Bc/sq+g1mqlyC1iKOG9cJbctRrHe0laFp6SGCpLttXaS0jcEinmF\n6/lB\r\n=NeF2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "381b066bd0260e4ac3a20b315403267c8f8f96d0", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v11.14.0+x64 (linux)", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"lodash": "^4.17.13", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.6.0", "@babel/generator": "^7.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.6.1_1567803212449_0.2568574557163741", "host": "s3://npm-registry-packages"}}, "7.6.3": {"name": "@babel/types", "version": "7.6.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.6.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "3f07d96f854f98e2fbd45c64b0cb942d11e8ba09", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.6.3.tgz", "fileCount": 93, "integrity": "sha512-CqbcpTxMcpuQTMhjI37ZHVgjBkysg5icREQIEZ0eG1yCNwg3oy+5AaLiOKmjsCj6nqOsa6Hf0ObjRVwokb7srA==", "signatures": [{"sig": "MEUCIQDPs6quxBvk6mufQnenfZji8ovy7s8oJtBFwR0CR8/iCQIgBnScv6Q5UR70odkyvMNh9TFeqVlOIl8nBB1E/fZh/j4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 683057, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdnOhVCRA9TVsSAnZWagAAhKMP/j0C2InT7ZIhilK2h948\nrqjft0FxoPqFDtFwlzgQIvmiiMGipw7W+V3InUy79dfhTOSeAzdFVidKlGIS\n60Ntxb224bdR79d1RjQATQeNTlrt+zawn3fCF5T3X9+KBJXtS1IxUmasNcO+\nsgNvhHUlGPZRDpGbhWh2qhlSDBCLCqntxuAzaEcOwU5GrOL86Nd+LoeFUCcv\n9n1vkDJeEn27zB4H5O17Ex9WFtWBOAKCtuInCSGia4y8qJ//1fAKQwUJmbA4\nI3qk+R/6OtDK372mc7Rg90f29TYBhlkkafdtGych6BU9+hA78irnpn8WJ8L2\nu9rTevY52kQ4z/aZuF5CnVgbhtFK4x050gq/bOn+aLiX4Ycsum9RDHewTE9G\nndC61AY7zlQkFg0tGrSSMP/3mzah6opI0twi3f9eUTwOJGNC8D5zw0gcJObB\nLeCG25rfWUjuawAGp5AjxA4xN2dzc5qHmTs7tjBzMfLSiydfbMiu2qUeMxkJ\nEHnNjLklm325miRWJQncwIVr9cHwhpWVxchoB4+XthZHErYYU6E9bVUsHbfK\nNzDFYkkZQoi5RtXeeoupJyWsj8eBrzz+b9OyzbyJgAONnOcqXtSKOj6ag+c6\n3NCy3Aj7o/CfdQtrS7nF/9qNavQU6culHSM5kCTt8SX0obLHdSWLalms6hOA\nX3zQ\r\n=po1x\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "d329156ebc17da01382acb83e212cb4328534ebc", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v11.14.0+x64 (linux)", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"lodash": "^4.17.13", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.6.3", "@babel/generator": "^7.6.3"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.6.3_1570564181117_0.962444740489216", "host": "s3://npm-registry-packages"}}, "7.7.0": {"name": "@babel/types", "version": "7.7.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.7.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "bdf5967277ced7590da452c31d60853d0beb5d4f", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.7.0.tgz", "fileCount": 94, "integrity": "sha512-ptM1jeXPYi3BfRBelzbTgL/7aP99oYJCbLtGha8phqRY3EeXch+i6LuxRcNQAIgL++yyNPR/rO6cGLDc0cvUMQ==", "signatures": [{"sig": "MEQCIGAzOOjTZQRCJLaK1Y99FGfRJWXvTYV6Z2p3k1acmI4hAiBci4oa3r1u+rdtrKHCpiHutv51t0r37PVjaWF4368hGQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 703015, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwVSgCRA9TVsSAnZWagAAne0P/iW2FsBiJYdQI9JTplDZ\nGwh2eq+qjY631bFRBcepeBx21tR7K0703IyAZiT0Yibkjcptta38iBLXy7/7\nq5Ru1EEq8MRd9nRq2AOO/6fsvRXvq8pHzYt5JW8RA2hHrgQhjPGrlywv1oaJ\n3WoruSLXRWUPEGoYiorU6BPYwISTfWNCiD2jM6gcmoiHKca3Q3Tp0HvNHwg4\nUb5j1O19NsP1if8soN8Oung6IzOvmRHtPnCVz53Q130gFQda892N45dFAfGM\n48OrjU7J5/+crH5nxMv1Eiuji26N5bIh4BUZDDXYIZX1eke5UwDuH0YbT6qv\noHa1+h0eQ8pbxpAPKuLUXEtTZBzfu49GSvFPIzsgHc2yFcxqYVayd5pMGSlD\nyqii86cRS53Bs3YwS0QPF+a9BSfZ7BjBMSfZs6SiEWS6iMWGM/cxpW0J2PzQ\nUgx69ErcffVHe9Y17KsW2wrKn0pCLAHzh1EaT+Mm0Ufv0kGA/6Rf/VRHZgkc\nOcdNUwoPWCm+GyqfM4g3ikwNAX3nCidFCUba0KduTqx8B2KomjzxpBOCEOqr\n3bZoOtPKwlZxc6pijgcp5uAh66MNu4/APn79hruzk9ReH+QjDh7Bo9njKkdl\ntK/k0AwogYXYUYROw8sy22ajecKuGR+tmBEkCDo+i/s4CRqKrJZ8oAkoHYL/\niN2r\r\n=Db/N\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "97faa83953cb87e332554fa559a4956d202343ea", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"lodash": "^4.17.13", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.7.0", "@babel/generator": "^7.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.7.0_1572951200321_0.9508435757694489", "host": "s3://npm-registry-packages"}}, "7.7.1": {"name": "@babel/types", "version": "7.7.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.7.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "8b08ea368f2baff236613512cf67109e76285827", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.7.1.tgz", "fileCount": 93, "integrity": "sha512-kN/XdANDab9x1z5gcjDc9ePpxexkt+1EQ2MQUiM4XnMvQfvp87/+6kY4Ko2maLXH+tei/DgJ/ybFITeqqRwDiA==", "signatures": [{"sig": "MEUCIQD8AbX6G4GvtQ33NgI3P2xZw2V3g8ovy9t6rNOB04AIyQIgU/azBU8z6GWT2JGSPTQQtfVzzSZoLHVYgUCg9l0m39o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 702309, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwX12CRA9TVsSAnZWagAAqKoP/1SmtnymDO2Qy+ND0wJJ\nv2iLk4Xw4ybjycJ//DzIdZlc+0u1tB4vE7sNRg3db1k2FwoljKGDCTt7j+4J\n3h2o2rEhSCGwQrLqwN7U/rrtwgH2RaN1c59MCFkj+nqvqPiP1RvuIt2Y813V\nYoqNAD3SGuX3hswwbVlr2kgsTjoEII2CmFQlS/NfGW0LEujVr+V1+KRUV4gS\nWnwf6CrR00m+h14B3gkIbDX+NieLWQVAgR/cERwjSXdU2yxvzDTay0aH3tJY\n6+fvPErMYWF/T/KQjMIbHBDuFjOsCP5J98wQGnyQfCaM24P2DBWmz0XkXDlB\nTdxWYbsFBAhyR2pNZBzhnEWPtaNj0rqua7kHCpXu9coJV6PngjEvjNb8u66t\nVSBOQbUDWz+5YuBAdFC6b8OLw728jn38Qe8Bi0hLyMd0FJJk1o4F6ksjKaZZ\nMgxLG8+K8Q/uSIUqb1o3K5SBjWPfec7q/qPeu6T3ZcZtntT4Jt7RJd7gDdKM\nStHJJybcsN2DU2A3/caJuZUzV995TMRS21jfDL/AWW++IqdmYrq5QCDq/9n/\nvg4vvRm5iwg5/n/uZCBQBOUQqcjaoLWxGWyaP204qbsLSnyvwWXOUsDSLB7W\ns8E/Pg32mL0Lzh6GTsFZQjUEgnaZd8CsP05QvtCOjM4qrDuO28COPFUv+4By\n4nL+\r\n=icXB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "42c8e0fb2f44734f175941859952b3c909e20304", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"lodash": "^4.17.13", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.7.0", "@babel/generator": "^7.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.7.1_1572961653541_0.46353396455603013", "host": "s3://npm-registry-packages"}}, "7.7.2": {"name": "@babel/types", "version": "7.7.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.7.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "550b82e5571dcd174af576e23f0adba7ffc683f7", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.7.2.tgz", "fileCount": 93, "integrity": "sha512-YTf6PXoh3+eZgRCBzzP25Bugd2ngmpQVrk7kXX0i5N9BO7TFBtIgZYs7WtxtOGs8e6A4ZI7ECkbBCEHeXocvOA==", "signatures": [{"sig": "MEUCIAGIW2yDqX5AvBIlkB1gYnEjmYEjPzoqWVTo0uakqPGpAiEA1WJ9WK/GEwCLy1q7aYGiE9DIrjNvTpxiM6wvF/wqvY8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 702309, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdw1bcCRA9TVsSAnZWagAAm30P/A632zhiXINmnE8f9zMA\n/eO87DhIQWJmkTfQMyKuUla/Rdy4tSwDDa7ldnNSlrv61VxxP4ieZwU3RK8u\n20QedN/VPANOMt6df97Y9mgL99lg2F6XcMwMRHWq2WhvXTAmFDOyyWPoo/FF\nmrvxezG+sWCxN3Nq0ASnQO7kF63oFL6gbc4e+jx/JArSi9yRzk5FraLWZ3Ca\n6I26jDDvS8tmXRAwugWAggjJE79QcYTDiDHtgEolVBDHuwNC83KM1gFVwWwt\nkLRT+TyQHXjQ8Az4VU9IE1g/kFGs1JX1LsHKUpaNthBfkqbJzQY4buCiq0e0\nsQNMblR+dYBCkyjHi3DCzHjSypI4JIGHmSY9F8c9uWevj1pO+JIUi5wNkbSk\nJkA93u9h5i/pdt0rFWW3/c4vw/9s9KxLHROmta6Oh3oVFZqQpeKIvUYPohUK\nsGvJYojT2LS/MfJlbtg6P+2kycs8VIzVHxxz3JLZFMllxVCkMTnJX2aib6cu\nU5quTsBfYRwGSMzKJ2n4vDNBAOFFkta409aQbxV6LneHN+z508kIch0HfbhL\nIVZrHjpnHhTQ0ZOClaE/Jmu0TL4FJY631iGn3myTjxtRbqwdyjXJJKtw/dyF\npQBC+YjXIAjz3WIdPnHlwfWh8LfiY3p3H+5JBq+8v9FJNf0IpyL2MxXVG9V3\n1qiv\r\n=JnYF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "35f4d1276310bac6fede4a6f86a5c76f951e179e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"lodash": "^4.17.13", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.7.2", "@babel/generator": "^7.7.2"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.7.2_1573082844494_0.635326310290409", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/types", "version": "7.7.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "516570d539e44ddf308c07569c258ff94fde9193", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.7.4.tgz", "fileCount": 93, "integrity": "sha512-cz5Ji23KCi4T+YIE/BolWosrJuSmoZeN1EFnRtBwF+KKLi8GG/Z2c2hOJJeCXPk4mwk4QFvTmwIodJowXgttRA==", "signatures": [{"sig": "MEUCIQDnuzdDyxvX+nqszoI0hOGc/CkwPOMkU9Rz8SIuS3NJjgIgKUKL3I0BGBOZh6EI/kPBOfevfhCSYYs1+NvErYtDCqs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 702945, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HALCRA9TVsSAnZWagAAoeIP/jSL3CyooUMOxIvN7xrf\n73zjRnNNpOydu7u1ohb56GWHNmKswH1eq03l/7X4opuyZAe2JPMnedKLXkDV\nioyjhZ25lisIzf6HjTflmFsczn8n7z94CnnXwzV5SB9J8u7Grvii5YcilIMI\nIrfYECv+wKJ2C04CzuKmYj1Zl2OIeRLyqM+FtifDDBKCmp6U+U1jE3L2KfKr\nioBxBz6mj2HYDnEJFy8cz4J9ftsmA8pGUHwgNgGiedPTYQi+wXbKsH7JGhMR\nDSO6XbPyYVaKndvauFCh5b1NSPVjaQpkbYtIZ4c3meP+42BneDAUETrKoTuz\nI8bxKCnkUbl3RGLpzD0M0uIDp6glQkJPOnXZ5GI2Ae1rE1AjdRH+X41X/KGm\nSdyEJp6I+Y7AJmV7841vBBN6g2lLCaneH+3rPtbZJpT/Mr+r1UzhEiAXskVr\nc+hY8BeD9LHcTc/vt9F5ONt71HiWwYs/An+ZXOzlvOuXQTO8gYYqilz/xiA8\nzXYMGO/PvisRkXSNcITkV4qmStqn4yh0qbQZuj78xQ/OqEx8aZLDxu5EhbPW\ny9nm1ElziBjjpMqMt3qjrtMXdvpuYflheuIF8Ae3K9683UFoWxsz70RWSv2d\nzV9RtDpDLoozSZ/WppmRUa1fXFHtLW23YUBCrtFaqsL6VYXxI/33R1v3qVS6\n44bY\r\n=TtMb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"lodash": "^4.17.13", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.7.4", "@babel/generator": "^7.7.4"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.7.4_1574465546653_0.6706054637304335", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/types", "version": "7.8.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "1a2039a028057a2c888b668d94c98e61ea906e7f", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.8.0.tgz", "fileCount": 93, "integrity": "sha512-1RF84ehyx9HH09dMMwGWl3UTWlVoCPtqqJPjGuC4JzMe1ZIVDJ2DT8mv3cPv/A7veLD6sgR7vi95lJqm+ZayIg==", "signatures": [{"sig": "MEUCIQCAFhnwCguyLhYHBi/SayJIl+q3+DmKHSvKFp2n5obYEwIgPi2S5rQUOv6ZkJ77jO/eZ2PsI0z8D/Yc4+5Xt+RvjRQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 711138, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVECRA9TVsSAnZWagAAcB8P/26u10rWRW2v1gE7Sh0M\n8okjKeo6XfVPk0xYcdnuRns0+Wob0fgH0vDS4pNACgOy3kQG/tEAhZak1WaI\n9u3odeVdmzFd26AG3vMuQ+ULi+SmByB5DDsVQ0j0wwmwPirvaJBPSlNajFXp\nnI2KRB7LHXT8VMInM9lI0LUs53eFNNIoEkauOPVZBgl9I8ZacIuaZYsjqjS1\nlEthlbSFC5AbBPY/g1PJD+/MC7IDEnz4WeumFKZmoyVoWYC47YcSifSyb4sh\nbSbsw0xfv0ZP8oTYe2tmtmUXeQwQGLNaYoeXaSN7QNh0tBVxsJs8rhxrxVW0\nNhCcLddxoR9XhAgfWQsSA/qZiLtnnPu7SDi4Pt09HmhEiNeBaJu7G1g8knl/\n8m6KPQqK3tlUlHiNWpUcluG2LF6mKPIaAsf8Ejf9Huqb2OSeei3FWZ+fBB49\nNSXtG6tsG0zV6Y708VSrV5NUa1jQRBqS/b21ecE5mebYLczzxLTFiivDFvmK\nBlINmxTFhE9SRZtbRnc6oDkEZs2DipDvurgft7/CbnqbI18O+uLKDrgsBwox\nZxpIOKmJPJ2YBt9doN8qjlBIanVG5auNXUabSAe9VshqN+45goUs7V7+2eb1\nh/tlE8xm2rvKllEm+8SqnlK42dIXbr5de+QB4Dy0/4uKAjwrs1DiwNBrFhKX\nknnR\r\n=roiW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "types": "lib/index.d.ts", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"lodash": "^4.17.13", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.8.0", "@babel/generator": "^7.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.8.0_1578788164635_0.404293252011914", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/types", "version": "7.8.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "5a383dffa5416db1b73dedffd311ffd0788fb31c", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.8.3.tgz", "fileCount": 93, "integrity": "sha512-jBD+G8+LWpMBBWvVcdr4QysjUE4mU/syrhN17o1u3gx0/WzJB1kwiVZAXRtWbsIPOwW8pF/YJV5+nmetPzepXg==", "signatures": [{"sig": "MEUCIGbncigCrFTNAZomrzoInT6CE2gCpTFzov+jc4c1I9i3AiEAjQmDivezYdhiQ5slkzS4HhwsMUkY4MrJ1tOIfL+cias=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 701562, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOPzCRA9TVsSAnZWagAAk6kP/i4c6+vWKmkhV9UvNE6O\nvMOFno6r1+O5YEC9kdzI+S3vd0N0ngoALnvKChBCDTz2W1tA2rVVKLtTNH6Z\nEkllz3tFC6J9ufHPPNfOM1Kc/T276tXVrKlZTMp3Xfsz+6WvQsTNGrUtw39k\nDJ+CkEJIjPaA9paabTOaisZtxrOiDfZnPkJNGBfkO8AiWXlXDEeUHPo1WFej\ntggkJhbi0mUgRx1zY3+iTOVszY3njUvTsSs21t1V5Bcu+maF1fAAy2x00Qym\n7jhQge4RWSCXZFeJW3KMzaVjl8ThB8YSWgaBttx69WdkSodKHAUKtLILEaYx\nFIIdakh6LN/vqk8nlAfXlQGQ0b/4dKEMSMTD614p9xIYx/pfBAbewYnKGgkX\nvVzWeVX5ZpYModLBKFjPtOh4LAMdyDUZBEPsygYX4oo34vq6dv/bTNpA/Hp9\niVw6S+D7lbxBJkF09gvB184BKRkpPaVjzcB0cz/m2GxHaXQKhX7ce6vkVLjh\nkX7CdzgRNxc7ntA9Y8i/KGgtIrmMn5rBjKyH+TQxK7JnTDw0rsERao9wrE7P\n/NMjABS0nuoHi5YydUSwj1vfYaH+xMwX8H0J+CQVvd0rviqZT72bDK9xVd4n\nbwSLDoPLUmwFtVCcKrnSSPjzEN26EDNTtiXUMmtfOV/zxIPNUQTKODRqGDR1\nYRdv\r\n=+zEi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"lodash": "^4.17.13", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.8.3", "@babel/generator": "^7.8.3"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.8.3_1578951666980_0.7213230665156058", "host": "s3://npm-registry-packages"}}, "7.8.6": {"name": "@babel/types", "version": "7.8.6", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.8.6", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "629ecc33c2557fcde7126e58053127afdb3e6d01", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.8.6.tgz", "fileCount": 93, "integrity": "sha512-wqz7pgWMIrht3gquyEFPVXeXCti72Rm8ep9b5tQKz9Yg9LzJA3HxosF1SB3Kc81KD1A3XBkkVYtJvCKS2Z/QrA==", "signatures": [{"sig": "MEYCIQC02G9drXlZHZXjRhpVvt4y0DfHnlbkTgSGAmCt4LUK3QIhALpzc0bDS5IfPuMber0CgmaPjzTXEjgjQqOvNehUs5Vn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 701635, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeV7RCCRA9TVsSAnZWagAA1woP/A3D7hUTNd8UhPI2KWqI\nOvKJu+blBgGOU3v6Yhu4QYGVOVeSxxClaKuq6CAnQfB9aMSSeb93cV8vxC5V\nAP57ABnLJFGmzKptfGUc0YWZRsjeKWC3PiuTlsO3Rl4Iv+ow3HgYQhY/KBXf\nXPIz6HXdFvkBwkbze17uXqvyaXIALEZhR1YwELnXruH4Lfstg1gINVcQzv1P\nu/WrkWNEaOgfGgWUIMTocalJTc44A36CKBMEOSL810IMZ5FynRG0FYmuDE95\nf2+m1GmVZG1OIn9LSo0GMRv7VLOerYKo9v4FA7wrKG7TJqT7SGHu9vlBGIl1\ny/hPYfq/n4alHnbBWYpirXgfnqbkGVBan96kowlAh2/zw5Jcw9fV4GG5ywDi\nEgFBtlUvJQ1RLpBDbR0ze/ey1at5Rk7zALaGxVJMv03uncyTW7/tzb22nYzK\nOfWdnyTEKe+IPR7C3ZE523NAl/bk6nlT97G9DVPDnooS3Vce31mS+qm2j0UU\nO2f2n7xSaql0JoT+NX9/Gol+K+UU6U5CJ9pSrR/7iiM3R4f04wp1/GbsedsU\nAtwquZOLfR2scyd3T3NC0JQohFb5SIF7FPbxf+yGcgO/s6jx2c+kgrzS8R7A\ncFZciTjJrjNtkDJCu9Q3PjyDwMcU/HO96jTKRQqTKwMuGGxP+L+1VyBnCcW2\nQovD\r\n=wUKt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "750d3dde3bd2d390819820fd22c05441da78751b", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.7.0+x64 (linux)", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"lodash": "^4.17.13", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.8.6", "@babel/generator": "^7.8.6"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.8.6_1582806082259_0.9644671026378187", "host": "s3://npm-registry-packages"}}, "7.8.7": {"name": "@babel/types", "version": "7.8.7", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.8.7", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "1fc9729e1acbb2337d5b6977a63979b4819f5d1d", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.8.7.tgz", "fileCount": 93, "integrity": "sha512-k2TreEHxFA4CjGkL+GYjRyx35W0Mr7DP5+9q6WMkyKXB+904bYmG40syjMFV0oLlhhFCwWl0vA0DyzTDkwAiJw==", "signatures": [{"sig": "MEUCIQCezB8F7xQPBSzI7GDjAXwAyRiWLuOwo/+lOwAr+9fH7gIgXt1/Gw5fHYM6jt2JrWZvRVC8ydFs1tFRyQAw+syc+8M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 701727, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeYFw0CRA9TVsSAnZWagAAgzwP/iMy8kI1QIJcKyhDLcGA\nJJ/Q+CFB0QAyq4kBLgVZ5RVyD0STA9vRlI5Y63zucNzL7qHHLvPWPKVvFUSY\nKN/ATXfOhfyhmsnT1c6x1Vno3DRoqPlmCaEhNEH+Hvxa9FYaGGe6+xCAwets\nt6HGt755pmNNnI/ETB0ePWo/Zq1YBbs5dSlbtdE+bWnAZfr6ZIZ69HmXE66l\nOmzhJqpuobo18P1n5X/iraOs5fng37pTy3Vaqr5+Bm8uElBI8p5pIxLhu/UR\nI+SG+kssr+Lta2acF9avYNWZmb3zDFSQrd24NWhaEYJ2yT3DUZ7n3D0W8kBH\nKi/G8hqDRB9LqaDzjJzH9QTnrEH+9JqZEs/Vwbbsp/eQgytzWFzPRCRqD62L\n4papbwhDGiQEJO2Rzb04O7DZWAqwHzrNr9STYU46BJTJ1gfQpQmR09OrcnI6\nZYCNQhsEBwPx4EgXHCR0PkgSbrv/q5MUC90G66//6M6fr/zcSMi8YBrESWNH\nSIYxAh0dCQxVQokYw90Kkjih7mIo6sMeFnRe4Q1j/LjuLhzDd07Uf5Xd0B4z\nX9QO3+qUL1CqGBuC+n2juPD0CA8o4UO5UegHTmNvvQOV/UCApg2k0PtIvJWu\nKq3JUfloVdRozShFJPJYtLjVZfjc7aY1qZFoZ413p89sf3Yo6DZeh2DIAYLS\nRKU6\r\n=lyNx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "595f65f33b8e948e34d12be83f700cf8d070c790", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.7.0+x64 (linux)", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"lodash": "^4.17.13", "esutils": "^2.0.2", "to-fast-properties": "^2.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.8.7", "@babel/generator": "^7.8.7"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.8.7_1583373364155_0.28225035925123", "host": "s3://npm-registry-packages"}}, "7.9.0": {"name": "@babel/types", "version": "7.9.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.9.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "00b064c3df83ad32b2dbf5ff07312b15c7f1efb5", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.9.0.tgz", "fileCount": 94, "integrity": "sha512-BS9JKfXkzzJl8RluW4JGknzpiUV7ZrvTayM6yfqLTVBEnFtyowVIOu6rqxRd5cVO6yGoWf4T8u8dgK9oB+GCng==", "signatures": [{"sig": "MEUCIBmem65xqEgnpN8CWuWhcamOUaNnrSgrmuw+Ax3jAs23AiEAkUsMRn7Hb75KxrVFFr49YzNmF/f5Np2PVzdTKUyGYns=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 710402, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJedOO1CRA9TVsSAnZWagAAaWkP/2K96qP8nHBlPOKbAtgP\nFVHZvKeWKdXiVKToksWUQPVnxFKRXsQkJdV/c3Nrgl8uGIl9R7tOG9gbuaiL\n7/8iurWhwiPP5blnOJmmMUZklBAv3mVVmN9KvRxOLBN3rrIBsVlf+OyL3rq/\njw0Y/VpUyzqXOFn5t4OIckundMYksjX4YWQcGAEk14r9+XhoIfaHf5TqBdYl\nb8pM0WTjvURmSw9HSxhkCCWrQM0idbN4M44Y8a4P5YLevb5njmyosLP9KGZ1\ng99TfV3AXnnXSVBtahQ3ZZHvn2/ZP94xPnuxxcIcCTmkXxkw2koyFOIRUgeg\nTUT2R35laYWUj81hwtpoVjGBWv4xqcZt3cws8HycNKmPXTw7FqzPk2/tsq8t\nde4ts0ds12cgStnwBn7EWSbd06E3VIfSBisz7xnTXrboKTSJK9ubv4pwEmlt\nMusd9PtFqnabsLsdaIF+DliC4f6Bs9M3ZpeyM9+1uN35VtVwm5VJP4vUsltK\nuU10xRTRV4zGryLQ1NAEwCLkMs88IoxZa4fGDdBTBz0q+HZ9X9K00PmHwMnM\n7fS//42/ideEDgAnmDsQ5FMGVRlEM7SgztzeGhcpomhUfKx6c7NON0WUtbWc\nQjDmsLnycqu/aBs+RQQ/LVpXMDhxCTyLlv5lc1UPDwPcNlQoA+63Jz3ggerw\n8E6e\r\n=vjD0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "8d5e422be27251cfaadf8dd2536b31b4a5024b02", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.11.0+x64 (linux)", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "13.11.0", "dependencies": {"lodash": "^4.17.13", "to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.9.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.9.0", "@babel/generator": "^7.9.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.9.0_1584718772641_0.5992065235993034", "host": "s3://npm-registry-packages"}}, "7.9.5": {"name": "@babel/types", "version": "7.9.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.9.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "89231f82915a8a566a703b3b20133f73da6b9444", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.9.5.tgz", "fileCount": 94, "integrity": "sha512-XjnvNqenk818r5zMaba+sLQjnbda31UfUURv3ei0qPQw4u+j2jMyJ5b11y8ZHYTRSI3NnInQkkkRT4fLqqPdHg==", "signatures": [{"sig": "MEQCIA4HCMi9HLiFFe/fC6Zh7O63uRn1M016PJOX6clyxmofAiAxUwKSwr+iV+erJDGzVlaP5fRjYeHM7UEM+RZD8Un1Rg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 710444, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJejNOhCRA9TVsSAnZWagAAtlYP/1kt/o/p8hE1mFvoRdqh\n4of8Wme3fLTm+wNmFntklO7sObZFyTwZsNtYotTp3Gn+n7G5de88f3ZfwY2e\nj7muRvPHSznw2HBL78CYZ/zyvB3O/LYrIQD2HyhWIQldhlUzywyiVGt3jXla\nrasSMAb83ayXd8wl5godCPaqSbo2h0va5z2iFgtbTSkPaSKshas70VvUI5ik\nxSLCut/12Vu8/Rbzc3s7yeNSRpHlmR8zkxXZ+6JqPJMUXUPBOQww41aXI++4\ncTWNQzwZQbpLelV+LAXBengoa2zPELcdorkj4nMRfMF1QWbUTJfUIupQfHji\ndOPMFxX8RgqMXBrI8olHZ0nogJ8ni+4mDMMk0HJLc7+etJfEYbPlFkUQ20Fv\nE8s5V/BUK/zIWo3O0cfQB82NLuJbyHJkCxX34ivqkOGD4t7QkNUPPjgJObun\nJ5x9cvHEMrZsIkTXwZGWimhoMwIc4As4RjJ5fBlbg23KuWLaiLmSFEzYp1sm\nvMflEoTlzxQyTw0FS4+WxMZTfAkUNws8MGBIVrNf3pf0kb8lfqwam74vT08a\nyBSJ1LOuuoqenTkIkvsNMZ+/nccIoMODiyJX4H2N+2DOu/s4K53GH25z5Q6a\nCk+gQv7lbVHZIpFMSIuH2pHWWEsrqPs6ChZL7BLU3d3q5KEKd08voCm3MQQn\nl1p6\r\n=C1f6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "5b97e77e030cf3853a147fdff81844ea4026219d", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v10.19.0+x64 (linux)", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "10.19.0", "dependencies": {"lodash": "^4.17.13", "to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.9.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.9.0", "@babel/generator": "^7.9.5"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.9.5_1586287520737_0.39064718255798137", "host": "s3://npm-registry-packages"}}, "7.9.6": {"name": "@babel/types", "version": "7.9.6", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.9.6", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "2c5502b427251e9de1bd2dff95add646d95cc9f7", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.9.6.tgz", "fileCount": 96, "integrity": "sha512-qxXzvBO//jO9ZnoasKF1uJzHd2+M6Q2ZPIVfnFps8JJvXy0ZBbwbNOmE6SGIY5XOY6d1Bo5lb9d9RJ8nv3WSeA==", "signatures": [{"sig": "MEYCIQDk7XohtbnzytfDpo/dVbHK/byE2ax6Py7qW6kGmglHPAIhANU7Ky2/rUBb5FRqVFgMtGW7gAA51zsowrgbGszM5iVV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 715270, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqcmNCRA9TVsSAnZWagAAvW4QAJujWrKrx1WSVnESj0hv\nasOl/bCjZXAqB2VIFC8nbSHVfu9G5rH385/mIP1+eA+ewzf1tX01jUav932C\nRI/RRkt/vZc95a0R7yrxwCA7N29V86PSYW+s5xkc/eKX9tqZe2GDj2uNn8K3\n6HWsURMROL5QfX7ikTW0R2Ie9KTAIHkecjo7XhRAjl8uDxLgd6qQTSyHK4pb\nBcVvz+xHGXK3NPi+UL2q4Ke7vRhifoNd0tDVnurLcpqqHfysHHH5xSoK1bx3\nbmrlcT9js3v8no2ckmDUq7+LuvBjJKW60sdLNt3F95TVxZoqRINQeUyJYu0D\npxBOVr/Rr29Q4uXPt0/x+CbtM0HsqFhgz7zkZgXd2Vsj8qcjKHZ6BVEKEaLZ\nYuSzMQ7rfiYlO9qHNolprR/6K94FnXVY1HfbOyccyuJgt4Rkmg0aF2Ljff+I\npp10msDJGAQ/wzJQC+TkVjuszfotFfFNw+ROTP8okpJBs+NIRG/tEu26A8VO\nl1z9vhkUaYm0jVbrFuwebPIG3KJB1rhoIWmV3KyffD9dhDVBrR+9r5KAp5a2\nVFGGCE4DPavj8vrOymthE4EPNnOzT2rBWixSAE47UeOTd7z/9S6oBZL/9w8U\nTuFsv/RBiOURgmMKzR0MZsPeH2P/BsuswXjE0YWzIIWwmwfnZT0s0IyrhJTN\nyuJs\r\n=JXlH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "9c2846bcacc75aa931ea9d556950c2113765d43d", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v14.0.0+x64 (linux)", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "14.0.0", "dependencies": {"lodash": "^4.17.13", "to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.9.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.9.6", "@babel/generator": "^7.9.6"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.9.6_1588185484633_0.5084400127251483", "host": "s3://npm-registry-packages"}}, "7.10.0": {"name": "@babel/types", "version": "7.10.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.10.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "d47d92249e42393a5723aad5319035ae411e3e38", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.10.0.tgz", "fileCount": 96, "integrity": "sha512-t41W8yWFyQFPOAAvPvjyRhejcLGnJTA3iRpFcDbEKwVJ3UnHQePFzLk8GagTsucJlImyNwrGikGsYURrWbQG8w==", "signatures": [{"sig": "MEQCIGDv/bM7TgxC2phT8/bRb9/gpRJRnVZtgINAG22s+XGiAiA69is576BLy4/V8x7jNMN6u5cUXpN8anJKF7n9QIovbA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 716191, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezY17CRA9TVsSAnZWagAAOAEP/RTlelASu97+UJeBTAO1\nKBvQWndfcU7jSsbX79sU5uI9Wjn5LocN+lBlmpnHR9e6164Rqcgf8j+BZVG6\n7ygF4T5hWDzAnAAH4t/9TeA5Mq3M7zZo5A/E5gMbe+omm6MC1QMPsFaKqEDp\n0cjnZktVbXG4VqPOZssO4CjftviE2npV18BYbgRus0gsxuvvmAjbHpA9j+iY\nhkt/zRwuENB99qdTdarxkdINt3Z2Bh5PztVFv1J7fsYutbiY9BtM8ZOo4uak\nprM68XlrziVcdYrpmI1gELUKozTa70S+iwwF6YQ1sTwy2rOEeitScBJyCbZ7\nj/b6dx8Kkt/Oj0vNrv+OM2fW76LALGy7SJ50/2O2c5mJOpSMqlj/4ANRT/cP\n68vcCMCuyPZgQYEhSY4F7J1j8mxzrPJ0LfH/HWN15YfpMTsHRZvxt+bAGnNj\nO8vq2J/fwqJ41bekQGJl7GcDUCiuk1arhI5aiN4cAhlwrcXAeq2kum0WxvxX\n558RAwg3UAu+nmO33CqxG2RbSsBBUHV9Cv2X5PigY+EpEaF0JDxFklZGVF1C\ni0uu6/KYxlO5wzmlFJF4nYNrjnQhMQNKzo/lSzWmY2XQmCdHnW37nGpjTpTG\nO4+xZFXmfwO5kaltuJ4CNQ3T+dzJ+dhLpNPlXbJHebIW3R5dyFc9QuEJMflu\nSewQ\r\n=C1K8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "5da2440adff6f25579fb6e9a018062291c89416f", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-types", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v14.3.0+x64 (linux)", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "14.3.0", "dependencies": {"lodash": "^4.17.13", "to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.9.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.10.0", "@babel/generator": "^7.10.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.10.0_1590529403387_0.2115317202546234", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/types", "version": "7.10.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "6886724d31c8022160a7db895e6731ca33483921", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.10.1.tgz", "fileCount": 96, "integrity": "sha512-L2yqUOpf3tzlW9GVuipgLEcZxnO+96SzR6fjXMuxxNkIgFJ5+07mHCZ+HkHqaeZu8+3LKnNJJ1bKbjBETQAsrA==", "signatures": [{"sig": "MEYCIQCRv2pkpTLTEGkziJipwPfmtBTI1LwnDDPW4omxY57YGAIhALaRmBiYs/5m503O+s8hkrISIgm0fgrWiyyavY8j3tC+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 716240, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuSrCRA9TVsSAnZWagAAnZYP/A7N7uOB+7Pj9q3JU9lM\njnl9j/5XgZPlRgH37UZtxCNrRXus6MDzUqWWkF7P8gTY1cybskBpd4U6/Uct\nd3dmrH6EZhEkVtCNKgePThWDy2K+hC81KUZzidrlzuat9Elf09UiLea3weuO\nyLo5EmviPR5j/4Pkjobpdyee3NjUdI/+/UOlKVa4YtGMBGv4o+Qr6xFAQnDo\nZ5WJz+rryhHdyfxBQAlwLY2d6vLm9P/JiuPdWw8L/D2x1PMMv9eTuxUUsMKM\nyYLPZ8qpOZkAOqzLH0qR9KhEiaC6Qf5YDNNz+ObeMSnbecJA1b8c/OmSwCCm\neav2+NDmG6dgvM2E29aRTe4kPr01tISzeJ8NMEwM97IHd9l9ivRIbD5nRPjo\nn4gvQswgS4+ZYiZoZnh/n7HjVgOEIVsQr8zomce8rAAuiJaWZutbzHXxYul7\nYyuVZdgmuPzvtCtXxNTGXg/1OQW+npcNo9xnZQM5Nv7wTReXWkpiEIDqLyY0\n8dsgKshMb6bHsLSB4p36nluZ49yOhnmy1GYE6yLKGtPaKHTetq+bVpXDXQMG\ndoQdEdgrtapT9GSk+WBU9cRABCv4CgZIzfWeYnCQIEg11b++XhMpza2xqbhh\n+Wv6vQVRqSztSBSQ3B+mmjwF1kL0wThh6LVDSm+NUyHlyze9vckGSufOKXOU\n5U2A\r\n=+KJv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"lodash": "^4.17.13", "to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.10.1", "@babel/generator": "^7.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.10.1_1590617252301_0.33483842212576165", "host": "s3://npm-registry-packages"}}, "7.10.2": {"name": "@babel/types", "version": "7.10.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.10.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "30283be31cad0dbf6fb00bd40641ca0ea675172d", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.10.2.tgz", "fileCount": 96, "integrity": "sha512-AD3AwWBSz0AWF0AkCN9VPiWrvldXq+/e3cHa4J89vo4ymjz1XwrBFFVZmkJTsQIPNk+ZVomPSXUJqq8yyjZsng==", "signatures": [{"sig": "MEYCIQDpEEIjMToO2vpmaPhPxcPM+Fv4a7uh5gMjsFWRajii3gIhAMjY8qgGDj+eHoDwPsFmyvOqwMgUC+5MFzB8wHYbfCfe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 717740, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe0rMWCRA9TVsSAnZWagAAv1oQAI1/u5HWiYdbrak3DNwc\n3au4NOb0cI3SAvLYPD+ZFv7MqMbZmAckNiaibqy/e6ZVZ2oHCp605p+crYKS\nnl5yBUvx+c+f2zKEgBKqiwDoq/xu2S2B/b98ztBRRIkNn87HpY78ADssIj5t\nood9Bh5JoNZKfIYvTPVL+AyM7ZEyPHJTKlO4SUAXGyVnk4dpMCCdw4f5ZiaW\nesf06mj5Q0hMG0IUhQuarY9xbgEboeQpxEUGrPAPQgf0C6EJa/uRa/+n2n8t\nN1aABk/m9xfeCtlDY+ZcYI/CZe54ERvn2nn7aUjwtTGABDo3LsNYUu6ywx5C\np7qsSy9AQOCxsw6yyaI0B3N1ENrfpb+jNqOzpau3fsE8EQ4TFNGIflEJOaX2\nYW56s+szuP7/IMRPmMtNfRYhNtZ+jWyx9ZBmLtQ0IxGjDmczTwBe7aVu3D8b\nbcvBmiZVNOavKtSsR94nNOedHebA0WRC6EvNr7g7LrS0J6VQDc0FKfVNpfi5\n6rghibCFtaB+klvMBqrbY78UgKC+MMkhG/QhnOqx9b/uINOJCeIDVoKTdIhc\nPq6nvTFq1v1OsE1JUXuh3ShbODz8yz25wVpvhvOLx+NcJzyn12QlAPuJhCQX\nGomDGlPabZVLC7BYF9cJgAM02HuYewWXuYjJqTQBXU15WCtrn7/9arLSi/lv\nsgWF\r\n=fXCB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "b0350e5b1e86bd2d53b4a25705e39eb380ec65a2", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "_npmVersion": "lerna/3.19.0/node@v12.17.0+x64 (linux)", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "12.17.0", "dependencies": {"lodash": "^4.17.13", "to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.10.2", "@babel/generator": "^7.10.2"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.10.2_1590866710352_0.2895587189141977", "host": "s3://npm-registry-packages"}}, "7.10.3": {"name": "@babel/types", "version": "7.10.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.10.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "6535e3b79fea86a6b09e012ea8528f935099de8e", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.10.3.tgz", "fileCount": 96, "integrity": "sha512-nZxaJhBXBQ8HVoIcGsf9qWep3Oh3jCENK54V4mRF7qaJabVsAYdbTtmSD8WmAp1R6ytPiu5apMwSXyxB1WlaBA==", "signatures": [{"sig": "MEUCICiziKkeHp/ectdYimgoNo58HtBH0kV9DdYHAKu8r312AiEAmjPBwB48vKW9tqLbFbfwdT0rxo48AFV/dHF5i/1JERk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 722809, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7SX5CRA9TVsSAnZWagAA8PYP/3OtMDF11H1LbEAWtoK/\ny0w0hjbIQQKrQGkerlkfZQY/EO3D8oDYqCvfsvAQOCHuCKtO3NEYIClyvy7d\nwv0kx6z76EPpzUTD5ZD2/wXWFiJdm46y0PdzBbFdnaoEaRdbUejWbEfOg9BG\nAnRbwwjbxiDhFKp0VPqpN48s19iIrLehKXTCByWXw3PD/hFWcO4fyW4sGiPu\n6gsEx+BNqW3NgSHxOjEmib6as3IrAHuTObHarLl2D0ZiO0hGsF6+INnp+8UL\n8lt8Q7/Fzr+/ZO5QxpaoEdF8+wYDGtjb3T9ChwKYcomuDlAwKz46avREGiSq\nH1Pf7ZZwDPqVT52L90e5bw/ykwbepJUx7MMzhGGZZkji7Q44xZWGSmTD9BF2\n3T6W04FxyUS/Ri4XJ9AVFdrMHTWsy/e6DmEasJ+tMKACWeEe306V6hXkyvy7\nGOTKXsguN05Kfn4zs+oD6H6kgwugOuD7Gh5eqgU2NGIKNcAElFOSMkEc1GlF\nxxNgI9/BOjkcPS914b8i+ZsAEqkCl2nJy9z9/Qa4XadAeIWPJs2faZ/t+Ckv\nlipeCzvn2GC587EN6G87rKm6jfjDlwlJ2xOorRYuqGNJFKbDHnOekQchwEtJ\nq0Z4lCw/4t/d4+8jyyIk4uvSpjU/UdRoAJf2752PqqWRG0RJV01scv6QKONa\n8ZVr\r\n=IM9c\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "2787ee2f967b6d8e1121fca00a8d578d75449a53", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"lodash": "^4.17.13", "to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.10.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.10.3", "@babel/generator": "^7.10.3"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.10.3_1592600056655_0.267123289686128", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/types", "version": "7.10.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "****************************************", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.10.4.tgz", "fileCount": 96, "integrity": "sha512-UTCFOxC3FsFHb7lkRMVvgLzaRVamXuAs2Tz4wajva4WxtVY82eZeaUBtC2Zt95FU9TiznuC0Zk35tsim8jeVpg==", "signatures": [{"sig": "MEQCICsLEkvWnHgfWzaTr8yBIq2B5T62OhgjrM0pd6s+ExgGAiBfebVigUV3aF5ujgUBW1/6+u8UOv8xH1rCzmk3ynW9sQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 723053, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zoFCRA9TVsSAnZWagAAJAEP/ivOoJ8Y8GfJtL7RtpMt\nnafKpgQl6rW37nzQjhJ79jzyry8Bf2+OKZgRRRVT540hvLIO1VItIhmeKmol\nHPVco3+LmGE2ZNF5jw2m7V/La3qbCl/lc3uhaiDLnQmbo8GnED3GYZq5ALsz\nDZLsyFZkmDq4KcE+1Y+FlD3/mKVpl1vZhA0atjPD9gVkr7izTt4Q3Ry+zx5/\nokRnJM3PV8NahmHyBqSa3OnujvAGrnhDQpz12f0iCqa8vk977gcXl9eCGynT\n6ptS1W23e1p4QGs4oQKcIsGLhBXvNj1aG7JaKRHGOpXwwSA3tArnmKM74/kq\nrOLdeQfTi/Hq4enRwd/6rN1RWqssro1BqBjk2859oZeRgBpHGESuaq+KFojX\n6ruatiFGQ53nrssqWlBuTHoyZtk+Xa1vfh8kSJALMX7QbrbRCQWasY2FRpP0\nBWZ0oq4hXPsnXIuweBmvESBJ8BronMq57w7ThvaLLjtZMXbFlN6OO3EQ7ntB\nd97GIjIj/G+eBAwVj7ZmBFOUnjJ+ox4ghOBfVd7P9wX3PCzI3m/IPDR6dGpp\ni87z9LvFzM9+m+WTkubCKtP36vDybeI62eKgxRlGpA1+4a/4pmGsC+3ACnDU\neGIcYZo9FDxZYNMYGpnpfH4162PaTKvIdSFRfcQF/JzzxI/Pt1IVTvpUPQoT\nbRWu\r\n=Zj9v\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"lodash": "^4.17.13", "to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.10.4", "@babel/generator": "^7.10.4"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.10.4_1593522693295_0.09286984534248877", "host": "s3://npm-registry-packages"}}, "7.10.5": {"name": "@babel/types", "version": "7.10.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.10.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "d88ae7e2fde86bfbfe851d4d81afa70a997b5d15", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.10.5.tgz", "fileCount": 96, "integrity": "sha512-ixV66KWfCI6GKoA/2H9v6bQdbfXEwwpOdQ8cRvb4F+eyvhlaHxWFMQB4+3d9QFJXZsiiiqVrewNV0DFEQpyT4Q==", "signatures": [{"sig": "MEQCICRXzBi+CgO2iEvF0L0hYP81ovs2ngd8nUI6vMxg3z/fAiBZRuzbJjWlMK38ZNvtz5pDf+LIwsDcAgwg/DD7527vYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 723578, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDfbUCRA9TVsSAnZWagAAlBcP/A6K3SdfNa9fQgPNOB1P\ndj8vCBYEHqO2rscr+6LNTBWBWV7InbKLR4XB795BO540Og1q+ovsdxNwQJh5\nCB2hw8ClOuANgd8K7K8/U0SV1ujBFf9uKYrt2anB6eu/K3DY+f0UkUKQZ2Y1\nc8g8xgzDV/Q5ckizeXe9rkYqoAHmW/e+ntkRdwowc4NNHOAaGrHGHSr5Vnhf\nOd8Z8zqyoYBQX3gNcx59F2mVu+7+wwgth0hVXIMyvbCQXLyAmY8C85FVsPBv\ntEixcSJ6Pu37E02BntxfOacb/OBCDc7GO0ntKwDF9TzUH7ED4qKVm/U3DS11\nm1lrrN5vBsvI1B22jMSxdICTNhxT6hviPDe7NYrM2ACy0o6RrTOzauNOWzRZ\nGLOowCvqXgMlIbdYR6uqmF9bB8Ojl3htAj60zAHppM+6WWGiAIQyH+/7ZkEE\nUOLdWj7kzjdLpV1dVF+Yxh5kcQ1lfyM1QpwUhYu/nGPGQM3YtKjr9OoLi0v2\nX2yhCgWHg/Jp4tsW6+mGuuOtj9AUAidbF+nIcnLxC5DPMb/yC35IJzk2e7r+\nwwIxnnUK2LGbvxFXQz/lJsOM/sFe1zVI8A/IQ9uDCrgC9kcIyZvsVmQHpXL4\n1ipo9EsuwPU5nd1wowL5ajhCtK1UKDfg3TOxoPDGjp3lNLqU50S3C/IP6KL4\nq8Uy\r\n=mmD6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "f7964a9ac51356f7df6404a25b27ba1cffba1ba7", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "_npmVersion": "lerna/3.19.0/node@v14.5.0+x64 (darwin)", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "14.5.0", "dependencies": {"lodash": "^4.17.19", "to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.10.5", "@babel/generator": "^7.10.5"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.10.5_1594750676290_0.5005699125572154", "host": "s3://npm-registry-packages"}}, "7.11.0": {"name": "@babel/types", "version": "7.11.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.11.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "2ae6bf1ba9ae8c3c43824e5861269871b206e90d", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.11.0.tgz", "fileCount": 95, "integrity": "sha512-O53yME4ZZI0jO1EVGtF1ePGl0LHirG4P1ibcD80XyzZcKhcMFeCXmh4Xb1ifGBIV233Qg12x4rBfQgA+tmOukA==", "signatures": [{"sig": "MEUCIGdzkeaA89gKZTrlJ1AsRerRlnHygI3+Ys9S4noeXORqAiEA7We1OJNVL7oreC4BZtNKYlBrvDjPWNJe1x/P6SLwBZQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 726190, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIzuSCRA9TVsSAnZWagAAVhEP/RTBAzH18Qx7VjeuIQeC\n/Xxe96UXk2ZKRxo81aY+ZULORbVAuiC4gL/O/SWB7hkcyYIsD5Rpw1gPyAaJ\nXEGofCihQYeiAPu15JimM+1S7Hr1zFuWC9z75881KGHDOPd89eAEnRAKXJmG\nwUzHsrJx8Pa0USp5uwkjLKGS/PoX6bjDXU2lJzwhOVxnLahNIuZtpbTPzMNh\nhAnpQ7tbNnJZlBQgL4qlvuyzu5q7vCsSXWgCQr1MZtNjuTK8YyZryXPZ31zu\nTSG9rtGG/HToXBE9oi9l3lpVZ1EgH4bPRpfH5XovMwq+OQAREoIx7wiuTl8J\n/f1G2HnVgw0psZy6TSRGlDH9ojcaomLd1EqUe/BHG01HvNcA6HxeQ+R3fVNK\nKHeX9CruWCgvRVv62mfvAMbUFmcDud3jZsoERYcb5N/5Ssntg2uskq1pmpEr\nEbLG0JeH/PtqTiEkfkdGnwaCoehms3Ei/gMwcfH8XmKh+yA/m1Q8qb7RuoCm\nEQFFcqlsj0e7Xyg3UWCczk9oyWuFYTfQ4IGt0tUW/kbyaqBAtnc5ImKjcBA7\nT23UwaI0ZxcSS2lgzoVa337NH+30fGeO5GmAQX/UaOt1ick2zD4zlmcU/xmK\nOrmXDqIUY91Y/DpTqTbFRGf6nKfAp083cp/80x2DZMxzeQW2MPSeDqVizTjd\nGDLM\r\n=depS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "_npmVersion": "6.14.7", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "14.6.0", "dependencies": {"lodash": "^4.17.19", "to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.11.0", "@babel/generator": "^7.11.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.11.0_1596144529669_0.01763518289709376", "host": "s3://npm-registry-packages"}}, "7.11.5": {"name": "@babel/types", "version": "7.11.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/types@7.11.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "d9de577d01252d77c6800cee039ee64faf75662d", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.11.5.tgz", "fileCount": 95, "integrity": "sha512-bvM7Qz6eKnJVFIn+1LPtjlBFPVN5jNDc1XmN15vWe7Q3DPBufWWsLiIvUu7xW87uTG6QoggpIDnUgLQvPheU+Q==", "signatures": [{"sig": "MEUCIQCjmLN/EL9X5DLLNJTURBbZgiI4ym1AouBb4QEVZXmC/AIgVVNsh0xjkZiCKpeGcpNgz456NpHp8Y0h6n9ucoMnD8U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 726269, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfTVdICRA9TVsSAnZWagAABW0P/AmnnhNyUkA9FcNBWAdg\nwWFQ8WqhP8AKsIqrUpIKw0lw0ugTmLu1xu6KSJqD+i/qM3bnLiTsF2JWa2Oq\nDufmuhP/ktMDh8ANn1Rbst+bMd9dsixY7MwnQlqu1CgyR3hu0TIGofZwCBeJ\nEC8CdUqCjFZg8JmZ4zchdJUdN05Xb5cqcisqRIbHkadzCIeNpk8nwcQ780wF\n4yFvls21EISJ4YOmxZ74WyrQk5GjbGgffAFFusOuh0rNCYnFfLyt+N+VQ6IG\ntJBkf2rHz3UqKO0gUd8DMBl4w7JWJ/EOP5LHIJoGERN1gxk7dGBHdiYyo4RP\nVHA+Ua8SHQdkL/gAfUKq3forxItTEC5Lbb4Ig2kGL7rsZ/20dBZeyX2x2ggu\nPux22XZM8W9xxLPa05ogvyJleHeppm4PM9zp68/6xhYAC+nsI1o0rPwjFJJ4\nSfaOEvAV0248RUD4vE1eetCovXibD7gOZn/XeufDFMqCiwctLpYq9OlDwmp5\narM95vzT/XUIRrxpiPGpQniXI8iEyAWL0x01t0gEwj50BpBC5zUutFnZC5j/\nWb9Q8XL/+hQiReRs6HvukPk1RyaOOwbHqmuBkpTtEuVrezeIsXkGh3cYcke3\nyeCL89Vc89JxDKPck801mc/iQ0TtQv5Ur5swjp3otdWS6epWjvXPvmXIQjcz\nCcc3\r\n=hdIE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "gitHead": "af64ccb2b00bc7574943674996c2f0507cdbfb6f", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "_npmVersion": "lerna/3.19.0/node@v14.9.0+x64 (darwin)", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "_nodeVersion": "14.9.0", "dependencies": {"lodash": "^4.17.19", "to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^4.1.0", "@babel/parser": "^7.11.5", "@babel/generator": "^7.11.5"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.11.5_1598904136354_0.2535421373995328", "host": "s3://npm-registry-packages"}}, "7.12.0": {"name": "@babel/types", "version": "7.12.0", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/types@7.12.0", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "b6b49f425ee59043fbc89c61b11a13d5eae7b5c6", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.12.0.tgz", "fileCount": 95, "integrity": "sha512-ggIyFmT2zMaYRheOfPDQ4gz7QqV3B+t2rjqjbttDJxMcb7/LukvWCmlIl1sWcOxrvwpTDd+z0OytzqsbGeb3/g==", "signatures": [{"sig": "MEYCIQCmWA7f1sFWl0e+QsF+UKc7yzKMbRH1h1DO0r9ueKtFIgIhAIrZrG84MEkLwq7McdKxe+SK0FRS09AvJv4UUHw9u8Vw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 730281, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfh1l8CRA9TVsSAnZWagAAPZ8P+gK9HFY2ibvyipd7UIcD\noznB/zE7j5CEyPxzMNpFS7W2cm72WdJOI/V7C/Sm/qWjkmkw+Sh+w7PThYsW\nMCTo1MlHYHSciiud/jQdwIwmCbQSN3QCZhCjNJrKJATCZA/SIFb8UHfvBgZ9\nbUTY/qRRMS7FRVun02JjIAcGNOV808ns6ahKfanwI+bHtMh6dem4zpVnFbCS\nu2j12DCLIzgAEQWPsAJFQ77KMOg6o4zscerfk+skF5kLwlSd8dGp9XwV2qGk\nR4xeUEECUUYoZya7+EhTpUA/rAGwv4O3rz9rQQ99mTmni0IUk8IGmn4HalfF\nswIhIeb6/iItZSmWOLA5hmH58qOHNBkrSi6dK2IDSTG1nfilrasWvf66WQWd\nhFPddva8hQLVK0mWCslhnDsITdUVMhiEkAtIcL7RWmqzmnKihsj/gAh7r/pc\nsd9b2sSOWRrSLUY8qtjSTktjIAOPafG5ipL3/OnNnWDtDhV18pIINq6meI8N\n9YDgBYkRqRmoaCQqhTlEL/uMyKyKotjpcJQnov2aLu+yEhArmrFVM3cpG5r4\nE9emlwYt53Jif8yQbpcy7Q8FQ/tHBUfZ5DI4y/yN02IIN4gkRorwvBQ1OIM4\n4EDufd+fssWpJaPROM3R2b60tQyHFPDlTOnLcZL3ZZQdFdfhTNBryBIBT9Ab\ngW6C\r\n=y2ff\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"lodash": "^4.17.19", "to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^4.1.0", "@babel/parser": "^7.12.0", "@babel/generator": "^7.12.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.12.0_1602705787614_0.280527988139202", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/types", "version": "7.12.1", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/types@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "e109d9ab99a8de735be287ee3d6a9947a190c4ae", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.12.1.tgz", "fileCount": 95, "integrity": "sha512-BzSY3NJBKM4kyatSOWh3D/JJ2O3CVzBybHWxtgxnggaxEuaSTTDqeiSb/xk9lrkw2Tbqyivw5ZU4rT+EfznQsA==", "signatures": [{"sig": "MEYCIQDNbZwzO20sPi7LtaXfhOGFZRoAdgT1KFYWbxR9CiDMvAIhAJHcJpSSjgvlaHvGk97hr8SZnytRvdJOnfoOKtq+JI3B", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 730682, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM/rCRA9TVsSAnZWagAAMEsQAJb0y0X4USt4BUFATzJn\n4juzi/6QHIx0Kc2vd3Kg6wRfskWkrGtPH0E+79iB4NXG7xwZPy4mV4F4Cf98\nX/km5/bkTZa6LR+iVOi8xg2O/98i4SA7+3DQ+0ygygr1g2kREEXdeFrGevCF\nQoyCEecHt/yrYXP4LGxKmR6sF6yLT8J74id1cEqs1IFx5RWIuzv8Tc15kB6K\nAC3y+0q66SMRGd4RK0AuxkFyA5Rg4KzJ8IZkrtAmC3OHQcFrquHdQW4hsWK4\nLMPc/79f9uTNrY0fFNcLdFDBYwuiHGk1yLPJ2ltVirmoUV8eBwBtzV7yYPyd\nkS0V1zvKJuqjyHDfOtchLvEaG/LxVG4KXejsXydl0UGuaj9YwYzOGoYxfMII\nDPTP+6hW81SVqzrYdPRjAsxiI5M21/1bYiZn200O34hxFci7lYiXUN5BGqP2\nADyGF8p7Y4PQB0BTsC8SbPJZ950W2X7CXuU+03rXLHG68pagyIbMdLwUvRYt\nJFfezup6Asr75RLYMdabRycT21evl6DQidbDGFcFV7huxdcRdX/YbGM7e8Da\nqztJifwMaOtPNO5l/wiV9FVMpKIER+NbEtGML3pH+CP3+wgmcxeLc0MWxMg9\n1zE1vPSyiPesbG548lKWANf0JSEBDO/wkXCUL6C07OdnPib2Z19jaEfTnzHC\ntrwM\r\n=5icn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"lodash": "^4.17.19", "to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^4.1.0", "@babel/parser": "^7.12.1", "@babel/generator": "^7.12.1"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.12.1_1602801643237_0.3034789551972328", "host": "s3://npm-registry-packages"}}, "7.12.5": {"name": "@babel/types", "version": "7.12.5", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/types@7.12.5", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "5d6b4590cfe90c0c8d7396c83ffd9fc28b5a6450", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.12.5.tgz", "fileCount": 95, "integrity": "sha512-gyTcvz7JFa4V45C0Zklv//GmFOAal5fL23OWpBLqc4nZ4Yrz67s4kCNwSK1Gu0MXGTU8mRY3zJYtacLdKXlzig==", "signatures": [{"sig": "MEUCIBioqfyGRjQwJST7pjcM0vCklj1flJmhmQu/qq8tBPgsAiEAlRqK48542ruKqkzT3oLjXAxrcRUg6+O4iLt3OF7xhbY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 731552, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfodrvCRA9TVsSAnZWagAAuBYP/j+nhFIt2ds9cg130Zcz\n0v1QE6H8eGCiiHk+Dd0PlcRMDANSBLLnVSQeYKIyZxMypZq+3hkprRzs8RIT\nrYfSMikpFkhwv60RlTreOTGkqTCynr9tGPgb08Y8K7PgTXVL7kfBCVBbivG6\nbKxWH4cgLQc+3z35dbNVnC++KVKdl9qhvloSrtOUwST6R1z+7Z97BYH9PZBF\naORas/iacqxjTnABU5qV5bSDjK80eZtpKfrIDP0KFacTYoQZV0aESugbxrU/\nzw/pCxm4bJvqxWpZ8vYaODwsb9Gb6pQLzDJNLAEwDuTr60oz/5oycVlu2PtN\n4feuYnzHj9M1h1A55Kry2ThCMC8qbwRr/Kdjt/m6757L3vr4Yfc3lsvL9C5B\nV38wuW+jsYXvs8boCblOHaJ5EBxgynTUZLfrCYUt2lmPY+MBeATPdHIIbpbX\n8M8cReIDjuqZJbP24QriHUSDY9YVuY60yl/HA3d6TAzO1ngls6v/8ElyH5Dr\nFltomS2jzYr2DeT8jdanPg/4gOYrT2c6fdPo1bKpThbIuQIQiDFcZ3h4kjFp\nEIR8FF9XHJBemjSzrFz25q/xbV7eBHx6hvLmIrDQbvvmHIpI9zdrR+MY8wHK\nV68XMbO6F65iOwPtIhCRNJ+f/R1MOLJVKCajQML8jDmZPKB/4i4Qjd91GFUD\n9DvX\r\n=65kI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"lodash": "^4.17.19", "to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^4.1.0", "@babel/parser": "7.12.5", "@babel/generator": "7.12.5"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.12.5_1604442862468_0.577564776223245", "host": "s3://npm-registry-packages"}}, "7.12.6": {"name": "@babel/types", "version": "7.12.6", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/types@7.12.6", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "ae0e55ef1cce1fbc881cd26f8234eb3e657edc96", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.12.6.tgz", "fileCount": 95, "integrity": "sha512-hwyjw6GvjBLiyy3W0YQf0Z5Zf4NpYejUnKFcfcUhZCSffoBBp30w6wP2Wn6pk31jMYZvcOrB/1b7cGXvEoKogA==", "signatures": [{"sig": "MEUCIQDD6158lvoI09frWEDgeJZ1ghEFVSA9mQclB21OL90O2AIgATX3tKj0goOt9VQFHSPgCX2DjmlcFxZoki4AnLoa25s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 731405, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfoovkCRA9TVsSAnZWagAAI7IP/iueLMKjQK9OLIbjEIG4\nhEbkzPZll/xC7/z34I3a+JgA0FsZqKsBXsLzplO1HS0xFe5b/K2dHkmApcGi\nVVY52lmmUdhd7Sg4H7hvRxNHvwTQY137TBIay14b84XoslvqlFneHqjx2Yne\nE9/EFPhp8SPUfGKa5mzMBaGaTKaDZ40DmEEJkxndK8LviaXJA5XFXleZmrq7\nr8LmQNnwzRyBMIzBLBQt6FP6JNlqS6OtNSt5uaOZl6GZHtlb7bcDeWge/dCw\nzGo++BcKRCoRtwKS+MJPB/OrA29MqYMGNyplH3KRm0CdOXWh3XL/ETFZJWrH\n0INrI1mXgGO4aIvyKX5QWkkqHWoe7dCjyQlLPJ2CN1QxsCoKrRnq7C7M90sb\neZ4p44nXWjRXtn5kpoPpB+9rcgEZ762Sj2fKkbwe4+GMPYj2sxY34D8s74WG\nPKO1FXVY4pQBZXPfLXck+uzeQlSrc9wiDKlr1r9FJMXwiErw3dfZ7uUGcxVx\n+f9Rt7ciwJpoGEkCLegBm3FCy+MNe//rpdgVH7PX2xqBsRh0wQ6M6vT5P/sE\nJiQ857uTzkidltCCOs63bh88piamLW7DzXoRQIsoEXCkL+6LzmyB8UbpXn+a\nu67/LUTn1pJZXmYHlGZye3VQxVNjJ1sgnnLkbqDBCf7I6JcSn5sznNXbz4fx\n4lCD\r\n=N9rB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"lodash": "^4.17.19", "to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^4.1.0", "@babel/parser": "7.12.5", "@babel/generator": "7.12.5"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.12.6_1604488163854_0.41441204900792683", "host": "s3://npm-registry-packages"}}, "7.12.7": {"name": "@babel/types", "version": "7.12.7", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/types@7.12.7", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "6039ff1e242640a29452c9ae572162ec9a8f5d13", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.12.7.tgz", "fileCount": 96, "integrity": "sha512-MNyI92qZq6jrQkXvtIiykvl4WtoRrVV9MPn+ZfsoEENjiWcBQ3ZSHrkxnJWgWtLX3XXqX5hrSQ+X69wkmesXuQ==", "signatures": [{"sig": "MEUCIB1MBH5IVhMmghYyg9fZO0mllb6SE9oErEqWGfpuAyb8AiEA80pTuCfLzFO8PoMKRVNiP8iW2R+u2+n96UcWRBhM05Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 912333, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfuC+oCRA9TVsSAnZWagAA8+cP/RBQ/bCz1lKVXLupI9Hh\nRG/RCBGRCOHOVqy/EhmuvwD9YtFhyQBnq3BoISO4s0Zjc7KTNHEDZjMTj67C\ndhAlVP5RsUiFHBtmFBEO3VJnbfChlf+MnZ3mIjNSbguVIbgS+v4dqWZGP4Vj\nDJoNj7yt7kdDsvgqhTRnH0BqKoehuo5wE6+IyUxjdFdYuTZYbe4aY5ZMtkB1\nNnX9xeOvuuHlGh1R+uu7g8X8esCpPG4tPyLVN9d6nvdF4X2I2Xp5xHzEEJR3\nCzXQTFcdk/9ZQcNo5NFOB8MboiZByEYjj3y+TpzxjiW/iGgu/nZ+b8sQil+v\n29cw5GSLBcLzeuyI+QJy6pETYNqoTKAZPSR43j1evB1hV04kxfMqcYFqicLO\nsKB8AAcW8E1kMUWK9iQYYNhSRqNRtk5yoW/7NFpJ9yPUzlpr3xT5dcC5A9jG\noCb6PWCbCHOPQMrawubVSReJz+9jmqOwwUEFRFarIyuB1OI0tgvZq63DjR1P\nSdGjwel956Zhbh7FuSIpmogcniwZMFwJdWtbE3GvMdrA1RU7OVuDYViHo7cB\nIxiF5EZxSykXuKWzT5b67kPI1gXBiYs1fssoQ+khbTtnvQwajXNuH/aFJ5+H\nNcQbadbq7y6VFD3H4cMp4UjJXsd343ZG/c04Zp75Hpi1iUMyqmBTRO7+/6w4\nMkKE\r\n=kp2J\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"lodash": "^4.17.19", "to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.10.4"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index.d.ts": ["lib/index-ts3.7.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^4.1.0", "@babel/parser": "7.12.7", "@babel/generator": "7.12.5"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.12.7_1605906344072_0.933010602063455", "host": "s3://npm-registry-packages"}}, "7.12.10": {"name": "@babel/types", "version": "7.12.10", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/types@7.12.10", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "7965e4a7260b26f09c56bcfcb0498af1f6d9b260", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.12.10.tgz", "fileCount": 96, "integrity": "sha512-sf6wboJV5mGyip2hIpDSKsr80RszPinEFjsHTalMxZAZkoQ2/2yQzxlcFN52SJqsyPfLtPmenL4g2KB3KJXPDw==", "signatures": [{"sig": "MEQCIFLtIC3M9VD22YojwgtTfgGZS+5MCUvZenJbNraNadA9AiAcSVaZd7AlJ7PPwXi8zjvmbWYzWsI/OgOteU0MyWIOJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 912545, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0VQfCRA9TVsSAnZWagAAFpgP/jBQ88YnL/lqBVZ5Bcs7\nVgsrf0NTpWDguM59NG2o0AsmyLc+hBOnS9IZ2HmFEzzJiZIgocpIiDmhNAOj\nqrNfffdfRj1c/UV1IsUQ7Tg+ILWVpRKcu9LHSEK5ZBK/m0zKIoxUsMTVS/qN\n5ZfR6ZA5+rwzjmmVbJPp86SKsBacM3GAVUTBZ/85UWD/yREg4e8GgbazEn8n\nff0zdHTOTke/4A++xWKSoMwq41C6jUv/C+cV2LR2axG3PkAcs9mzu9Um0xX+\nMZmMjMoi4I7s7Rc7bb5VfU8Y6fnioVyhp79/AZ1xSq+j+AKkUKzukl0HPoK+\nWxfD7aDNr53w0HcMrZBJyH7VhN2UECYdHbQ/35cJIXTKD52BKdxF7dVcQWeu\nHS96L82Ra3TkL1CgHCrviRc3HFgMxHqVjuy33AgEQZcOyRbI6wITJQ+ZQH2c\n51fnRyy3PuW4RDfPMnAUxkso5q5WIGCc/KoRuDZH89mEXmQONNRikMrZ4B9F\nqvBwk9yGWRjp5bGb5enyudTN4yx7W3cDDfskHzUsn7qZSjf/v5imr76Ut/+S\n/WSfCL1myMTco9xphgdKEuAzEH94WHQndtXiXweuw39M0jRd8gEjITzbfb3w\nKAhgXKtVFvR3DE2SwhTz1jtup9ssKdrlZAlcAm91e77sq7w4XwYO8EKyVHD7\nHsFo\r\n=Vzik\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index.d.ts", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"lodash": "^4.17.19", "to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.10.4"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index.d.ts": ["lib/index-ts3.7.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^4.1.0", "@babel/parser": "7.12.10", "@babel/generator": "7.12.10"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.12.10_1607554078794_0.36525642085195376", "host": "s3://npm-registry-packages"}}, "7.12.11": {"name": "@babel/types", "version": "7.12.11", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/types@7.12.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "a86e4d71e30a9b6ee102590446c98662589283ce", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.12.11.tgz", "fileCount": 99, "integrity": "sha512-ukA9SQtKThINm++CX1CwmliMrE54J6nIYB5XTwL5f/CLFW9owfls+YSU8tVW15RQ2w+a3fSbPjC6HdQNtWZkiA==", "signatures": [{"sig": "MEUCIQC5DC1fnu7A/pYNzPY6Z0AhCMCiqa9hNhdWo1xa4czyIgIgOxry0NJQXu5HkJEj76bmNYnnAbbN9dqc78lR56E0NdI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 996392, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2U3gCRA9TVsSAnZWagAAddQP/jTlyJLBQRLbV+XOUVbY\nNU8KWlip5/ZofbJ8emXYHs45/tBJE5IC6uhDqlY410bi6TETCXyG2I5Xpn9J\nQGLZusw+yKn1idggsVQBia42CzR3/ANNf4Ev4UNHoT9Jnr0OLRWRzmANNNAt\nuaEj5bxG0bqpORmbmvhLbVb2luaMqlFhpg5msh3eTy6vBapSxthuyNwExNHQ\n4RNkf+N5IvbzFdqOI3e+OXmxaAYe1xJNbRVD5sxka4XBQycrT/KpTVs5apAx\nI/IAAhayW0ex427F4DUR6KDp9HyeXhRXfYadR0dTvkA2kODi/LyFZN6TWPQd\nqZ9itPx6l4YxdY+RtbQ09cj0zN+TpMb0waxNUbvfgjrAr8LrjJNbNT06Uqdq\n3sPFgTkho2F7XwwVDNc5Z8EvHq3cSr2x/u84CYyPkELLlgJ94yvy7KBAbXYy\n0rfKOWv1b4ln3ou+Ay/9oqWPEBGgKByA1RUEU6GMHVe2dc2FHHIIF8+j20IV\nwFfZZFLQoIlXU6iYr513pt9MPDY/mDBoMStnYNvqIvFeu7JCF4b/GtP7oMgT\n64NuwU/3QgPjkT3+r5b2aqU8qQtzdux9YtHpsoTNHaekCxi70DgWgruQ9wB2\nNggZxY3cNRrXy/4eNMqevLcOkxZdUJsXHo++NltYJUzQUZfNxrtu7TA7jngT\n480y\r\n=9hwr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"lodash": "^4.17.19", "to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.12.11"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.7": {"lib/index.d.ts": ["lib/index-ts3.7.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^4.1.0", "@babel/parser": "7.12.11", "@types/lodash": "^4.14.162", "@babel/generator": "7.12.11"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.12.11_1608076767694_0.6160133786598596", "host": "s3://npm-registry-packages"}}, "7.12.12": {"name": "@babel/types", "version": "7.12.12", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/types@7.12.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "4608a6ec313abbd87afa55004d373ad04a96c299", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.12.12.tgz", "fileCount": 99, "integrity": "sha512-lnIX7piTxOH22xE7fDXDbSHg9MM1/6ORnafpJmov5rs0kX5g4BZxeXNJLXsMRiO0U5Rb8/FvMS6xlTnTHvxonQ==", "signatures": [{"sig": "MEQCIDamg2qML4Zx9auzoXP2Il+4aPsZklWbK3dO9CBIl71dAiA4E/hYXlNQyRecOuCjs5dH1sAVjp8W2N1PBO+4vdNmbQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 996436, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf406lCRA9TVsSAnZWagAAnDcQAJFkU5P2yZoETDGLB01i\naNXgpHh0jWAidAO7eMYAFYdIGpH4yoXMXQuEV+XlfMYmDlMseKf1L354O0Lv\nsX3Yhoavle3pp+Hd8YPxUhPHDIQzbaR4ujnFmmLLXnZlZIMGVEJ/tYUYpVNF\ngZQVRIjvOYTw3EG9T0ZIeVuv8ZJN0C2mVzD/H9DAKIbgoqeNI2nvqxpSu+Mb\nLGP1Z2on4RY4UxNgJf/4ZHm+VQmlNSEdqS1zfC2QFupiO5+54PGoGjJpdwTi\nGp9RMqN3DWLoFyxEunRfFukGKXMfEM46j42jBSOneqMI74FxAWGOBxBxUnKY\n16Ib6Bi86MlUv7C3PTDwEeRUYyor7KWWAJxZXntoyI+bt+zy6OcUutY6xh+U\nXMFVKVsB0WWkagnoXg0c7TYkp9EqxYcdz5YQu0/EEL1M1YJuZztuN8vK2/dM\nYJhDTm8wjrrHiA2GbZviIf2GZXjrbejVmETX5BQ2bulSVjAkqSaoumGaOfx7\nBOJwEalMkLU4aWhGiGs9CGdNyekl9OcYCFuysSVi4Ow+qycA/63/xJIg1KBS\nPNDQVf9WbrnvE/2c9/hD/NnEJFK9XUD2G+LYPDUDQ6KNPb4dR+wekQgIevvR\n2mYZC/3JplUPRXT1q0WLt/yCjTT0tplfrZqfjhLr7WQKpUjBNEgdTKOzPEKQ\nzioQ\r\n=oRra\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index-legacy.d.ts", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"lodash": "^4.17.19", "to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.12.11"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^4.1.0", "@babel/parser": "7.12.11", "@types/lodash": "^4.14.162", "@babel/generator": "7.12.11"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.12.12_1608732324740_0.5073990748729049", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/types", "version": "7.12.13", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/types@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "8be1aa8f2c876da11a9cf650c0ecf656913ad611", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.12.13.tgz", "fileCount": 100, "integrity": "sha512-oKrdZTld2im1z8bDwTOQvUbxKwE+854zc16qWZQlcTqMN00pWxHQ4ZeOq0yDMnisOpRykH2/5Qqcrk/OlbAjiQ==", "signatures": [{"sig": "MEUCIExOS2hHoTdrsb/UEPpT+Yyotx3Mf4ohxHRnJRFGHz5xAiEAiUdPYrN2zOo8x1EVZVgf2jr2ryJ7npsMP/9J5P13WfI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 996554, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGffoCRA9TVsSAnZWagAAQU4QAKPPa+B7g4vksMt2To22\nVL6v0Kj7SwRJHWAY3TREpu4r8YMLJq9qVjO/cY47mt2CCRzqiQlopmI48Zt0\nekm/L3jSgPbCm8P15geqfPSaoGc7Ue4tQO7WAZFXxQGBEj4ryw1OYpzeVS1Q\nAMa/9EbDRqp5iC66JKwiFYRxs5XmQ/ZlzE37r0lsfWwaCBQrJOMy3bV0MoZo\n85GwehG2RjLypmqQcP+qXRVpTpQ8/RBipRERD+YVNhNWa60DFQvO3Sv/BlAl\nY0t2kCq/A0ONywx9vechMb0+yMQdjL/a8ds10Y0ePHj0ljkLCC0p3fWovquf\nG/TUnwgZLyMoOjPRytXH1FcF1OhPVEHo0P9JNmP+O7gwLja4mbxDGECvn2PL\nOF81Ie2fFN9yKn19izUUMVOruTarIrnXxoge+RCUOAyFeIGiiE2XEAYZpsy0\nhzmSAqxRBk1BwVjHK7hj/L/Jv21Cnv6YQUNLBx4GExiMm006kqKhOihV1Au3\nJnVaKno9GiGl70GyqEH7YMNz/PQAmYd0IKPalyyxD3YdY1VmfdOgobktjLuW\n7IWVr9o9mlsibI9BNulu3RO2knGx157nks5A0L7iYsDuahnUEU0W3pXCt1zQ\nZUO2q5sih7BM5m+RUWLWExpGI39T4tIEyy6kxuerSiH6XC9ZuZpqAHxXVHeg\nYE1c\r\n=YcGW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index-legacy.d.ts", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"lodash": "^4.17.19", "to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.12.11"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^4.1.0", "@babel/parser": "7.12.13", "@types/lodash": "^4.14.162", "@babel/generator": "7.12.13"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.12.13_1612314599584_0.671969951559195", "host": "s3://npm-registry-packages"}}, "7.12.17": {"name": "@babel/types", "version": "7.12.17", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/types@7.12.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "9d711eb807e0934c90b8b1ca0eb1f7230d150963", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.12.17.tgz", "fileCount": 100, "integrity": "sha512-tNMDjcv/4DIcHxErTgwB9q2ZcYyN0sUfgGKUK/mm1FJK7Wz+KstoEekxrl/tBiNDgLK1HGi+sppj1An/1DR4fQ==", "signatures": [{"sig": "MEQCIEMzZw6cGRdJaMxRs5X1dFQntSm9x6CjWC1KTAsxuf1hAiBBWXMnfUZwIo1Zxa/EIGunUjanoV0KnlZBu9Ex0W6ZkA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 996896, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLoPHCRA9TVsSAnZWagAAe5gP/iEBUSzjvw4dh8pypMvp\nwO2brnp/GGmA/6J3/IZ4QEm2XBMUycR0h2H6AJcAY1oYBHYeQxbuiu72K+EW\nOc4xtVhqWo6Vvt9SHyAWhcIeXRwk9rxWheTZyZbXbRgXjJLHyi4GMO+m+rPh\nYoG2Enzjq7llFMnsIJ4GQfdKLvjCDgjEu67ZciTGlkC0yTzGB1vEJqAKtNVD\nPSOyACcaN4RSvHqqqUlTXqFsg100TWYRZAtLRaYdZRZvCIRpRvEGRiR7hcDV\nONwWd7Qk3AbSwDNYoNTIe4RQJeJPNnRO1fkNPf5PnPa9dbZ+fXC+vfyCu0f3\nrvtYtlcYw+UNkkm0rlNb/A5IojAFW4Zn9Oxfo52T0XtJKUxsR5BXuzak9NQT\nsO6GDwzH9IEKdcrG9+9qk2qm0X+Imx1PtXQpmucqBEVPC9D06iSXkhbNp0Kl\nTMAG6g2uMlrNXBT+DjKr6JGJfurAROZu3S9v+fnHApPhrnpbOTm43QJ7aavg\ns/2RjCxIQq67osbmbdeg45ru9H/6XWGGh+nPoqMo+VcoCfC8IFAF94x1/qnc\nHm4wTIfQHY0z63sq7HilgWo9sGPpOw+W3HKFeAnO2iOiNxMtXXY8mnR2FoIA\nfDk9iO7RbXUA0NXuhZKvcVNvxW6M8SPSafW4rFtg2SK8YwsJw4NwMlgmt5uQ\nB+0R\r\n=M/pF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index-legacy.d.ts", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"lodash": "^4.17.19", "to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.12.11"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^4.1.0", "@babel/parser": "7.12.17", "@types/lodash": "^4.14.162", "@babel/generator": "7.12.17"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.12.17_1613661127093_0.03007920160923616", "host": "s3://npm-registry-packages"}}, "7.13.0": {"name": "@babel/types", "version": "7.13.0", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/types@7.13.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "74424d2816f0171b4100f0ab34e9a374efdf7f80", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.13.0.tgz", "fileCount": 100, "integrity": "sha512-hE+HE8rnG1Z6Wzo+MhaKE5lM5eMx71T4EHJgku2E3xIfaULhDcxiiRxUYgwX8qwP1BBSlag+TdGOt6JAidIZTA==", "signatures": [{"sig": "MEUCIQDFYkY+ziQE+EBNfJ+vwOT1Fl7uNuUY+WslroZRecTPxQIgLTh/gfXrnYHik5O971SMH8sKM8gNDBXolIZz+I5DnYk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1000412, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNDUFCRA9TVsSAnZWagAA4SsQAJ3W7iRX8mL5aAuH30wp\n6Z1YxcPkILpjdpQEKZo/Laac4StWbpS5ovDGLoh3GsUEFkbyIGPk2rYfW02t\nCD/Zj/bJbHOlUIdsZc9keSZILnYlSSdGNF0TOalujRHPJ+T27yZL6UHHWGft\n2UjVTHFLChRVHOEGTUylgzwAF2m4Ywbm2S1j6Ctmh086LWrsrN8LkmbJWd6G\nf5Ka/QPhZNWp5kvl90gANn4g8eFa5p0ghQqwpXYEJJQBwkKqRV6j31dSCyTA\nHxNm0Ws9x0IuT04wypSMcsobueu9T5QtVVznWxS2EfIEb37ETBUmBVCjquQA\n9bxpIw2pXJp7rO6OfGysPZTLVWtqkC0ZfU5CZVBzJPE/XJVgC+uuwyw03ZKd\nFxSGWkQrmt71TyPnT0peGdeI6oTT7UhSBmRD28v1vZ+MUlN+/GEOSWeG3DhQ\nEssVjr590hZYWfMpLmLsrx+7ZU/8bGMGO2xfU+EA0mXVIX0C9y8og+fNSlQx\nXNgNylPreGAb5eKgLOWa4iN1xwZGon4YpD4isvdIdSs++PBe9sxG8oZt/UTZ\nV0BWk+0B/WbwfnVyEEu+aZxJ4aidO8nFLGlLkg11Qvjk5MCHTyUYwSlNc7KP\nYg8k2OJMSu7+0EzqUVrKdmxmHvRE7/rrbxWCfI48uto7TV9uB2H+MhMP8ubr\nhmWd\r\n=KCmP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index-legacy.d.ts", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"lodash": "^4.17.19", "to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.12.11"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^4.1.0", "@babel/parser": "7.13.0", "@types/lodash": "^4.14.162", "@babel/generator": "7.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.13.0_1614034181194_0.20089208368823952", "host": "s3://npm-registry-packages"}}, "7.13.12": {"name": "@babel/types", "version": "7.13.12", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/types@7.13.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "edbf99208ef48852acdff1c8a681a1e4ade580cd", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.13.12.tgz", "fileCount": 100, "integrity": "sha512-K4nY2xFN4QMvQwkQ+zmBDp6ANMbVNw6BbxWmYA4qNjhR9W+Lj/8ky5MEY2Me5r+B2c6/v6F53oMndG+f9s3IiA==", "signatures": [{"sig": "MEYCIQCkA33fWmzVGNkrKedoj8K623W88ZMhesftXKv0nkK5TQIhAOJln9pq3HdzBJFIXsNEvPw57xEjB1crl1b3qg8y88mi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1000146, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWLv8CRA9TVsSAnZWagAAJ1sP/i5MJRRgI5c5+tgyhMZS\n03ofbA1OAK6z4G/dhNPCAAvqVZ4n36+J6nFW1LnVXivb/Ld373D0y7d3n4SP\n/y3k91dLHMt4Toakwevjz3Hjh5WQMtDSEuFEinMpkuJLwroongMRO13PQ7+U\nTI7yvoV7K+jE0lyC2BQrOz3WB8+KcwIIVIi1gSjKOW2oGNv1qzZ6ya7RtQqU\nXbtb7R41CymkH9mWg/6OUlYZv4prh7/VHFSrt8mWVBSfJF0788JzV7P41cYT\nXN0rZAnWojYqFs553OcmDb02oZ3RSbBj8+ra7WvuvyV36SpNw3ickl9+Ck0J\nQbXtnWTSwTNHkeXSOeuNxJBPhtKPxqz/6KLtEPMDlLkWt3+mCyLnFXsjEgn+\nUO3LkJ2qsYANuZMPWbnYMax0C9U27Ow3Dj047v8A1eK520UWvkBgaO0LPt9L\npTv+vGpLDgNISMo/20c7ayub1ApY7t5AShpkpdsn+MuCpTz/aiKeP7qtOaL0\njkp3JUgL8er4KZoRTvySX/29r7MClRWHxrgI+1K3OS2YIXhCvqeJWlRN5MY9\nHOg4tpiyDJvDyuC/08aF71GGNl7cGV3weB9p1foDVgHQVIm2LXLHN7FG685Z\nh4UvSOSZA0IEMhV3wuD//Ejj7xhOMk4FrkUaUqyKWoBba+WHFHLaXfvfJg/v\ny28m\r\n=AnqY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index-legacy.d.ts", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"lodash": "^4.17.19", "to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.12.11"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^4.1.0", "@babel/parser": "7.13.12", "@types/lodash": "^4.14.162", "@babel/generator": "7.13.9"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.13.12_1616428027681_0.11974462007771702", "host": "s3://npm-registry-packages"}}, "7.13.13": {"name": "@babel/types", "version": "7.13.13", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/types@7.13.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "dcd8b815b38f537a3697ce84c8e3cc62197df96f", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.13.13.tgz", "fileCount": 100, "integrity": "sha512-kt+EpC6qDfIaqlP+DIbIJOclYy/A1YXs9dAf/ljbi+39Bcbc073H6jKVpXEr/EoIh5anGn5xq/yRVzKl+uIc9w==", "signatures": [{"sig": "MEYCIQD3qXvfd5bluqhv9EDIQ2Z08GDL+lgMuiv7cQ511DFm5QIhAJC1Pj3XYUTHG/usTGoMpXGEk0SsjDwlBSo3DHBDn6Z1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1000204, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXlAbCRA9TVsSAnZWagAALloP/1BgOUeXxIgXvsCzxECJ\n+pBB/qrcVte+965/NoE9+Q1mNMPGY1LaxBZE84RvpPoky3WfLYHwkueQJblp\n/aOXH7CpDeK30i2+iyD/1RSUDYwCtAK9Y9nhzRrvJqncpZcg/E+nfC5RirKW\nuy4n+/MseeMEdBHHcBVPJKlx926ObwITo1951MYXYNIeeZiH7AfMuTCUzIn4\nXmJJk7fSW0wk48eER3R7l2V54cz+LlXsQpdlSvvxnKEaYjk4y5W+wkO5PVZW\nHQs0pdoTPvbBfdAg5O0o9Y25nARkdUaBVSfvOn8UuooPxz/2WxyszAfI7/Fl\nEgODZSj8Q9075HVmrIfhPhFx6zkLjZpOVlpiP4bS3RzLUk1hyzypUxvUKNy7\nRSPbCWiRDfOs9ciNdzjWTYo4vL9PYcq8d9B6N3hKIdC5A4FcidPH2Dau5eGC\nrml3Tdjppu5WGp9EBkMyjbifrYejORWWgwZsEErXpJVolviFLTG2D4SNZ0uz\nxwValRjeZXkR37TvtwMPh8eajtPTZeW34V2K8swy908fc771szoNyHG1NCaK\nfoi7i5JQiTTQBGMDz/V3uqz2meKqQ5D6RZyfmM44mvlMeeRXbIaVmRmQWbbp\n6/xQ8oFkY2H0hExrsGkmUCmmfakOJPIx5L3upY1Zb651dddQKUzFfSx8NEQZ\nFf71\r\n=Nagd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index-legacy.d.ts", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"lodash": "^4.17.19", "to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.12.11"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^4.1.0", "@babel/parser": "7.13.13", "@types/lodash": "^4.14.162", "@babel/generator": "7.13.9"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.13.13_1616793627363_0.6829013699457687", "host": "s3://npm-registry-packages"}}, "7.13.14": {"name": "@babel/types", "version": "7.13.14", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/types@7.13.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "c35a4abb15c7cd45a2746d78ab328e362cbace0d", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.13.14.tgz", "fileCount": 100, "integrity": "sha512-A2aa3QTkWoyqsZZFl56MLUsfmh7O0gN41IPvXAE/++8ojpbz12SszD7JEGYVdn4f9Kt4amIei07swF1h4AqmmQ==", "signatures": [{"sig": "MEQCIHH7Whxg9x5B5WxadKWp3V0NzZ+/sGtpaUHHjE1fsAXiAiABpxgRDaNX3nmjCbBgfealnSmymcNvxOfXMjmuXcfEhg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1000500, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgYeEICRA9TVsSAnZWagAAULQP/3IZQ1mF6tqYg8iTwSNK\nwv60sBD5zK2XWtH/bMfda21D6kBrOp7IK7kYAKR/N2sgDpIMnLzYsnv/dg4/\n89gROQ+ryBA0NsOzIXY0HfhhAAXaNxzcYeObI4WMF5pWRY2JEGcBMcGfNiNs\nEeQlFxDZ6ZUlGkIIjxVm2VIq7z3Y4aDvTfltPzbMsGAlhGmCmOyiqhLBzRme\nIqUopQ7733zifcqlCV+FoxtC2ZB6OoQqSspNHMlpEP8H7mIV77YjFCo7VS86\nTkC6dfDpFIzrxDHLAvvyDjLUXA+ObqY26z0kLcUdTTd4YlFYRw1ybtI3UNPn\nmbTIwwOHt+IkOgzL6PW8CP26LR30IzN+BCxJ/3QqSpjSMv2ZIeMymOyF/N0l\ninJKNiR4lUowW+nyp2FeB9ia8DReia0cttOOnNvMBjngKcVi9OyRG+uVWd++\nsBD5s+lSYt85tS2R7kUSAPztcDcBDrb70wBNLnmvM3rbAzKlf75zWrB7bZua\np9O8GzgEBXFVtIHaC/X/jyoa6Prm+uVLkvJWxIT1LUadr8iSgk+Axb4Ud6yB\nRTEBBdnvy41qaoidzXr82sfZroYUdSLRQFxG/0yejnZNjx4mAt0R7M3hqKo5\nHMDSLqyPzufUwdoZSLa+fRGd76OS4B0D8FCpJ6/c/lJm4m5JLN02pBPsVCQ8\nfxAY\r\n=Kzag\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index-legacy.d.ts", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"lodash": "^4.17.19", "to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.12.11"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^4.1.0", "@babel/parser": "7.13.13", "@types/lodash": "^4.14.162", "@babel/generator": "7.13.9"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.13.14_1617027335570_0.9163705519398664", "host": "s3://npm-registry-packages"}}, "7.13.16": {"name": "@babel/types", "version": "7.13.16", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/types@7.13.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "916120b858aa5655cfba84bd0f6021ff5bdb4e65", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.13.16.tgz", "fileCount": 100, "integrity": "sha512-7enM8Wxhrl1hB1+k6+xO6RmxpNkaveRWkdpyii8DkrLWRgr0l3x29/SEuhTIkP+ynHsU/Hpjn8Evd/axv/ll6Q==", "signatures": [{"sig": "MEQCIEgFctO2dWoZYm9K9m+T9xrCOwK0MTqOak8x16RnzYdRAiB+Ak1ijtCvRoOSjuZowE6clWLegq/XOIpV1ropX65Rvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1001699, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgfrkrCRA9TVsSAnZWagAA5iAQAJgUhJr9fbnvzOj2RvC5\nsSGd7T/5pNYMQaJ7p1pzy2rIk1AIQzRBEpX9r8pj8i1J9xcK7xzztVdIZuv1\nr1F4yQGs+hH3WFR/gde3l66aKWemnpQ7ZtPUMiMi89lezuK/7+N6wT99rqy3\nVIGRysuSvPqvsMrb/GQhxNVJ0zo4Hinp0Ctb5me06+T1a8l8ppZpSD9DkpCw\niiLyk89IH6aOTI6YC2XAeR5HuhMkQ80JHSWnzKpydc/UXJC5njYA6b/riYKj\nobPlcTLx0unuOoj1Qav4Cu1sRKG5xJzHYTn6SIUOp7j5tWkp1m5Qj5YttwYK\nvuAPDrjIFn6Jr8auXmH+jiMNtSypIfCzW1yU8vKZLc8SenpZNptfs0ApgF/V\n0VuZrLV3L/youscQ80/GcNHYyvHYnY2AK5rgPHNWhuRGVhEYWh/DDK3ja4j0\n94R2/+LnwOFMUkPEGG2NBrLyTi26Z7aUldHdz/kibtsE3RHE2lYd33WGSERE\nm1ZKEIZP5XT+Qz1WJWhO0Z3jaRkXW6x4xG2AL/jTl/6U6kKK+lr+5dGUQHql\nnzN/V6CiL5XpcDgGJMe/bTkid+zEXbI1lKJEfkydIRsKR188B1jak0RAXogK\nYkwVHUyrEoQpSvRdrMGpuKVIlZvZkLQ8yLKWesDctXErzgAndU6um9mF82hl\nUuR7\r\n=7P/P\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index-legacy.d.ts", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.12.11"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^4.1.0", "@babel/parser": "7.13.16", "@babel/generator": "7.13.16"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.13.16_1618917674958_0.8793193485798643", "host": "s3://npm-registry-packages"}}, "7.13.17": {"name": "@babel/types", "version": "7.13.17", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/types@7.13.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "48010a115c9fba7588b4437dd68c9469012b38b4", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.13.17.tgz", "fileCount": 100, "integrity": "sha512-RawydLgxbOPDlTLJNtoIypwdmAy//uQIzlKt2+iBiJaRlVuI6QLUxVAyWGNfOzp8Yu4L4lLIacoCyTNtpb4wiA==", "signatures": [{"sig": "MEQCIHj563fDpUrLEZ3QJUyBE/eLUSo+/yiKtU8WyVUjnaAYAiBSzYQbZygYbJYwlGdaxB8E65Hw0esegDHAyEGsJ323Fg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1001744, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgf2FwCRA9TVsSAnZWagAAyhMP/0QrVXkOku/wR2sxrG2a\nzvHrGcIRai8eFDmHsjlBNl7JuYISU8et+JPuSoa2uwpX+NxLb0DY5pMdOc9w\nIytA0/0FEBybalu4k/vrTPEYg60smmw1hNOm5Tnj87/ZHVN1+VUh54wwdY6R\nkaLMoMmDFbaSuHoT91AzmDYgqdepm2b2oH2FhvtFB2nytaq+PFBt6Xx5hUIO\nMohqiMHJIC4F3xhOAD3E8VOZFxHEImFztmtYZC4aRdBKC6F8k0xNb+Et4QSt\n+XghPFD5tpoMG9w0CWTar88upsUPyxM/xJvjzdjCgSgDhGJyXuugBxhbyzY0\nm3r2sr6GrBJj8LskYKeLBu8S986y7h4tBa6oND267VSzoLTv0GgOeu+Q3y17\nQvoIS+YSaAKh0XoKCfINqtYU4AW5GRavge3yVzXsyJEaffy9ajZ5Hn346GC3\nmwMAc1uve8vQHET1W07rpujFWc7MrGcUgETflDayuxIkz6dK1Ese7wZ7boXR\nvwhWdVU6XIQHbSqxOMtZqCh4Y1Liwj7GGliRepLqKVGMuochKU9INTx+RoMW\nk5adl21GyRt5CfkSyVXYkERp16t7E1lSuOIZE4NQRi96L5AhvrWfVob4uYLq\nUPW0SVU6dVwTx5AttOBl7NUVRLCS3EVzlST/+rWg92Pkgmli6+AYjyxDUdkq\nnEX1\r\n=Bw6r\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index-legacy.d.ts", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.12.11"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^4.1.0", "@babel/parser": "7.13.16", "@babel/generator": "7.13.16"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.13.17_1618960752431_0.8937014460036614", "host": "s3://npm-registry-packages"}}, "7.14.0": {"name": "@babel/types", "version": "7.14.0", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/types@7.14.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "3fc3fc74e0cdad878182e5f66cc6bcab1915a802", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.14.0.tgz", "fileCount": 100, "integrity": "sha512-O2LVLdcnWplaGxiPBz12d0HcdN8QdxdsWYhz5LSeuukV/5mn2xUUc3gBeU4QBYPJ18g/UToe8F532XJ608prmg==", "signatures": [{"sig": "MEUCIDNQ2Alm34HTi3rWI/YH/vyPIcz/GiKBpUFPwt1U48GIAiEAzHd3HMRxUNO0fpxkofpM0y1ejYaSzR9qR5EqZqAdNh0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1008803, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgixKdCRA9TVsSAnZWagAAw/8P/0ASVlMVx+3rN8S6Z1vp\n5WWf3l7DcRYnrReJYRm56jfyChejLDQg4r4Sbgx9eMdp0IOFjUXfTy/Cf502\n8a7iIRyV4c9+kVjwyUhvjFRbQy4JsRus5YCZLnZoeh6jejQtd/eFtfYjZI2U\nE+EJUvuAz3YEnMwN7R9GucOFavHxzBkxDPoDI6ESGObyI/BJDyNwrGW4BQFL\nkSlZ4U7I8mug9qaRBk/4t605a2ILUybXtv/WqMn5SLbsJ2qCvvEtGcSj4Htc\n1vz5i4Y04i4ZfNMUoHSrRCVtzdbK6Ov3wMZ+/cJ494yhtIOXJn/8NO/ztre0\nBoaI5PwYFUwIQEciQTXc/roqCd4s15TAIiaetnd9HMENQJsgs1AXZw2Rm9+4\nioQqG4I5hUyv3aOKRR53ptgqW454cREUe0JXKq6YbpJSlrq8319oUbnnzVJT\nUWS8kKOnn1OMqfcGlE5QjTq1/5IzSgg26i/p6lTTsNIPCr/vFPxBmi/1kDvB\nFSVS6ITbO0tAKUjcQYu2PtjkOQM1QroCjKaIz91oj406twR43OAystEO9Nen\ni5amhYa3ueI2yXfMwlrVxQQctY1RZsZyvIKew/WbEvwdKZpSg9chXaMDM5dH\nRt9kUQAEa68mdvPJDEvlwOMsbf2LkOe5zoAoyoRPn6HSAJ+etc6FaSWO7Qpp\n7zDc\r\n=dum7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index-legacy.d.ts", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.14.0"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^4.1.0", "@babel/parser": "7.14.0", "@babel/generator": "7.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.14.0_1619727005505_0.1866717766644952", "host": "s3://npm-registry-packages"}}, "7.14.1": {"name": "@babel/types", "version": "7.14.1", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/types@7.14.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "095bd12f1c08ab63eff6e8f7745fa7c9cc15a9db", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.14.1.tgz", "fileCount": 100, "integrity": "sha512-S13Qe85fzLs3gYRUnrpyeIrBJIMYv33qSTg1qoBwiG6nPKwUWAD9odSzWhEedpwOIzSEI6gbdQIWEMiCI42iBA==", "signatures": [{"sig": "MEQCIHK2l6AtuPdEZ0kYS2fS4t8UNoH9rjXVLu5oL+LWjNvZAiBbFGL3+h5IwE6BsydNV/pCxtbdPDABcRk1EGF1pCv4rg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1002327, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkKmzCRA9TVsSAnZWagAANWIP/3w11/pir9MkNbNQZLqt\nlri/rk/MYmHZ+7eWedyf4VJAH0ekv85ObERuKYjsIRVMwckKkg8ar2yweJio\nqH0IV87SVhJRknku7apm5/cVLWVM9ORR1qSaDC+umuNC7Wof7QIrjx7Jw+6v\nq8eG+OH0QJ0dnK7kZWkdfFfVcX23RePLXD+kkJH6bCwKfd6xoivGXSXOYnZ8\nXiFvJmaVXflJOTluRePAHmRjlEhV4++6pIn8fYMt/134+ZsWCYzWnMLZRv50\nC3xjth7u+7rl7cn+e0VVwo5soaJDW7ASfVhl29mhk4RJ13wL3q5qr5yJQPhF\nIhaP2Nfm0moh9GTFfrJhkV3V0yKqco58qFLiIx73ybfqiNsonPFffhrR7AJo\nSTUcelcjdJjzJXWySHC/goPCl1Kv+CW+vXTgqDFewQKTKklW1hw6Wj0qsRZ4\nymluBa27EdlFbXgki2ztBPSC9jokN9be5BF96T+zEuty+A6AymLCr0ceXEsY\n3YP8vmeGsmh7f9BTO+eKtXPmhjhrEj2pGE2tCBMa8hJslK86I9oPSXQVxbD1\nMF/rGNQY3nDzczXqRgXZp1DW/8P9LljHc4oJEdckABMotDfBw9h4Ec7argtI\nWon7pJcKXqfTNMgg1G19DuBYHR56BXEptL4klnstNWeQnGsBR6B3y6QWHFkm\n7Lav\r\n=VP0+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index-legacy.d.ts", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.14.0"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^4.1.0", "@babel/parser": "7.14.1", "@babel/generator": "7.14.1"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.14.1_1620093363447_0.70920795424312", "host": "s3://npm-registry-packages"}}, "7.14.2": {"name": "@babel/types", "version": "7.14.2", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/types@7.14.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "4208ae003107ef8a057ea8333e56eb64d2f6a2c3", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.14.2.tgz", "fileCount": 100, "integrity": "sha512-SdjAG/3DikRHpUOjxZgnkbR11xUlyDMUFJdvnIgZEE16mqmY0BINMmc4//JMJglEmn6i7sq6p+mGrFWyZ98EEw==", "signatures": [{"sig": "MEUCIQCu58K3ntRzoua0zmrbIZ982DXNfITuQDjIjIAnt8WfFwIgX/Z1IfglyNqPXfCtLNSBfjW45LfogBx2ugESZ6zQzeU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1002230, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgnAvJCRA9TVsSAnZWagAA7QMP/i8b+k/hX0yPJkZkGkZH\ncbYkq5kKL7qx04Fch877Jx3AdCLeCD9rcVyndRt1VlPVqgQHl4zNvsVexZJN\n2BaDlQAxGA8wAreH3xJsP/dnortHn8bncpSZf3jcUtT4WojKQ+Tq+IcFaK3T\ntLuEuWt0opr7NsBBqfu4cXtrGGGN6q81iS5YHiwGvC3hVUk8dYEa+6PTXlJe\nnXoPag0HuM0d6uH7FyOdkq21N0r2unPix4fpDXlF2X1w2IFgoCBgrsbxD7k5\nQ5V9HkUROa6hxutZWGg1QYv++iotjtw07V8ycKAX0aO9mkl4wUumULZC7jE7\nZr1kHQs55Aa4WLlXiqarJKeveziJxmPE+hBwMvR/WA4QvadEh4PF7ePcOGXs\nMGYmAkc3Yj9CksVuJrK67eH9OOvB+7Q+CN5KGExz7OjfBHToo1yNd39xVzKz\nkyjaVRp0XEow4kP1OSZ9vDG7sZC9Bm1XHxbfcugFzK7rRiG2nGtTnarrPvNP\nw+iUmyo6sCkF59Pphxaw6WBSdmiF+OoDx+s2HloJHxgJGRoJ6b2PFx92ahyS\nEw0jAmRmpxKtwVWBaG2+HgFc/jaYwYtBoAM4c5vM/62/ps6FcLkU8B/ZJNmY\nS40mMNRckRz5watPLwwL4Hls+1bZ5cAFt5ly5bvsUQMmVo7AcTZjKg8oc2Iq\nFfkS\r\n=MLnc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index-legacy.d.ts", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.14.0"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^4.1.0", "@babel/parser": "7.14.2", "@babel/generator": "7.14.2"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.14.2_1620839368712_0.527362727378907", "host": "s3://npm-registry-packages"}}, "7.14.4": {"name": "@babel/types", "version": "7.14.4", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/types@7.14.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "bfd6980108168593b38b3eb48a24aa026b919bc0", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.14.4.tgz", "fileCount": 100, "integrity": "sha512-lCj4aIs0xUefJFQnwwQv2Bxg7Omd6bgquZ6LGC+gGMh6/s5qDVfjuCMlDmYQ15SLsWHd9n+X3E75lKIhl5Lkiw==", "signatures": [{"sig": "MEYCIQD3H+LhMlK2Gi0lcg2NZllMYosffdwxusiVm2woMAZRMwIhAIV0lmVyf3TwafiBXkkINVrhPrDa23QoBQmgNZTM1+2R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1001659, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsSGKCRA9TVsSAnZWagAA17wP+wdQQrLXe2AVIPDLpyTK\nqTn/hQUasHEwkDpHoXXZ2wOHjRTlV+Qs85rT901Kidvr138dcKismBGehMDv\nLU7Os9k8WvDdEwY2CYbfht5tSFiH86Iro3qXxC/Kzi6dNMJTr5/a0bU+v6MV\nQqosucOibRLc3xafZywfkOhpRJirFtSfytzXuXb8EINTqJwQCvzlVL7sGamV\nAuyJen5C+H0M32Yk/XuLuslPCWyTfE2jMIuwXYBgkiub0DeIxGn8VWEUWsYl\n98tBDnIPEiDKqIrBYuoq+UWW0CBLVGF3Z5YcMxZMLA0NrlLbB0wmiNC8mRdY\nu3r54iejBXvntge5DU4JVT8kX1vVKXElx0wu2TKHVfZEe2evFwPbG+yR9eMs\nMtDOzBnXVCaR3YWzaWlIk06TtjT/J+RMj8hZRu/iN7Zb/eKatU0pTFhr6gMb\n1FkC8Q/MmY39yKTjSMSAFugPG+trtnCJ+Ec5X0lLP91ya7vQig4MyhsQHmtQ\nN5be8rL9KdcbfvaERI8i2X/o/LUxlJ0CIRgl4P9vNzboGJsAMu7lY6FGkEv9\nMpOgacl07jMPy4QswBgt5kSfN03nCYYujLuNubcu27XrS/46juEvVzY1Ukxy\nv7ABi8ajl6TcCyHgwowqBL2/17/lxRg8eQSzzNZvDeiwzQWyluBEcLQk7bdy\nsl8A\r\n=sRYF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "types": "lib/index-legacy.d.ts", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.14.0"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^4.1.0", "@babel/parser": "7.14.4", "@babel/generator": "7.14.3"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.14.4_1622221193623_0.5941962551103595", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/types", "version": "7.14.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "3bb997ba829a2104cedb20689c4a5b8121d383ff", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.14.5.tgz", "fileCount": 100, "integrity": "sha512-M/NzBpEL95I5Hh4dwhin5JlE7EzO5PHMAuzjxss3tiOBD46KfQvVedN/3jEPZvdRvtsK2222XfdHogNIttFgcg==", "signatures": [{"sig": "MEUCIQCSVl2o5+kWdTxmPPFyRt3R04m9GNMgKxpF7JXm/iC8XgIgW59f9lmAFCjmt3KD7tUV700wasvBcbg9Roj2zqGIak0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1001757, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUrSCRA9TVsSAnZWagAAcQEP/i3wyKmqhGIrV337iIr/\nf8fwIuRnnZ+CwnBfPoDcHFIgAVBYlhNhcZn5INY7+PDsseYh9+awGqUTITTY\ncQLEU63p9r3xCNJW9GnqSyinM4dDKl1Do+ZAw19bOh+A/2DAONG7o1/97KNx\nHKgpcREgc1pdmhM0bE/1mSTm7ZACXGJ4abaipWCjZ81nJI3Gs0q3cdhDCa3v\nogP5qk51HNbsqhX1xNLEgOVKE2wwaxRZUw8QAc3TmYRqzKuaRmmCzIlyuYIR\nQvL/P54M7UIeCh/lOHIVIYwYreBhtrBQ4rKnk02hcBqUYrOpW6sRhun4JC1e\ntlYGuXpdVxaUHMwP2YhCVGPArvC3GZSr1mK/p7ysBK6DMHSjuT9Dvm7Zp3Lf\nCb2Fc+8GsPOlwyH5EfdeXj7j/FNkcEQnxfSQzPpEpplul+D404BLkdYH/u7Y\njCFjJdXstuxoR4CGo7ue0raD5XXvg/7QxaWy9Zy9oXyDRMTMEf3YY+rnaxIJ\nXofipTuo11jv13pjuLfjMmQC/7lYSB9ta9HM85C+VRyiS1tXPmSHlS6DBjLT\nuhp+W0WAJ9jIC1lGpGcqJre3JH7KfoBraX0ctdjvlGQWws0d8Tk34D5grgI4\nKG+kgfR27lQ6zf+es1SPwUkbDpThdJ8o5lIF66DH4vS3XMxra1x5tBIMx3Nc\niw8T\r\n=g5L8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.14.5"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^4.1.0", "@babel/parser": "7.14.5", "@babel/generator": "7.14.5"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.14.5_1623280338781_0.8038018574101666", "host": "s3://npm-registry-packages"}}, "7.14.8": {"name": "@babel/types", "version": "7.14.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.14.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "38109de8fcadc06415fbd9b74df0065d4d41c728", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.14.8.tgz", "fileCount": 100, "integrity": "sha512-iob4soQa7dZw8nodR/KlOQkPh9S4I8RwCxwRIFuiMRYjOzH/KJzdUfDgz6cGi5dDaclXF4P2PAhCdrBJNIg68Q==", "signatures": [{"sig": "MEUCIF2GcxejXRIsU3WkZjTb0nWpaAyJbxMiYUrQJ37jXU8qAiEAz3EgrGlQ8/O0bQrDUG/3TtZVkCVaEnLdfILrzd0HZdY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1002630, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg9w/DCRA9TVsSAnZWagAA4vIP/RdT/SHiGcAMXDKbi6ZB\n3zCLevwn/h9Bgz9JXaJic06auBLgC56AcerDv5WukzAnWkGAaBNGZ8l8RCBm\nW3MwRel5vqnHle7kFKceb9it0CaArdEUMLuV4EJuUJtScPCE0EYvpedoMvp3\nQz6elL/GC2wE4PC0islWduvGi5gPY1hhLTBnqsC/15arB9u9JBUoxRzgf3lh\nAxe5CdA8Ow4/pOGPxeX3yaFaahaG57mvX4jWwDTJkFotCdLdIgAAN69AGiqQ\nhQpGlUzs2KB2+1SxBZM+xBqeaK0yfOEMMztqIgbr8ARaK9OLEcapM6NUvB97\nIzYlGI7X9xasY/YGP/X4VNBBnfA9BoV10ejKIpGNR2oDld6z19pFiZsPKRe/\n1VMvJn5rrC4mJn7X0Fhx00/KDIs5w4hJu1dewC+GlPFLaYXYOf18el0TkdoO\n5BKB2KJ0rZ1oGSFw0VUhdJQzCkV2BDG8eRtUfIKxyM/5n4n6Q5XETf7jXo3x\nblzYYTnfIG8PMZT4rA6jLdLS2b03suovQpGUsfu0fxZFAVZzU3BQtfwwQJYW\n75YxSEqYJ3z4PerLeSfCrrIxB3mYI3D45md56220Myp3W4KMFTBbypqYQW2x\nYWL5OnNJAVCGWKGHCuDyBXpABA1Au+kfEh+gRBYdhWugKeIznRXlb/vbWBlE\nyjoR\r\n=LnoO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.14.8"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"chalk": "^4.1.0", "@babel/parser": "7.14.8", "@babel/generator": "7.14.8"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.14.8_1626804163185_0.00030692555056610615", "host": "s3://npm-registry-packages"}}, "7.14.9": {"name": "@babel/types", "version": "7.14.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.14.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "f2b19c3f2f77c5708d67fe8f6046e9cea2b5036d", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.14.9.tgz", "fileCount": 100, "integrity": "sha512-u0bLTnv3DFHeaQLYzb7oRJ1JHr1sv/SYDM7JSqHFFLwXG1wTZRughxFI5NCP8qBEo1rVVsn7Yg2Lvw49nne/Ow==", "signatures": [{"sig": "MEYCIQCq7EKVsEieVp0FV0zDb71smm2oweE/otbk9J+7kcyS2wIhAMRFzcY10MTELSnEvFuVp1XPNj6fFaKg/Dv5X+aplgSS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1005276, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhBlLwCRA9TVsSAnZWagAA3cAP/2pPs1bk68GaCEKaYJ87\nsdLhqzvXsJR4KGLExG8dsI8e/a6MlbpULKmdvx8E7+gbBnC4VHcUrkdwwcau\nPgxfNyxBdg6b1Kutn62+lsuVe88FBw3DJTPE1evGFsq4WdBnfQVwmX5mGVut\nX/NZMRP2CFid+wUQu4f72BOHVRpmdLjGKi+ZPOtUqzD7fwTeyPRLg24PQoUc\nQevyKyNW5ZdvXQ4ZM8VYH9QHth5VBqEfDolajLzTcNwNMZOQ1k9uWcUj0MrW\nv1Sy5DTEThyuNiZPJeW7FNPGC/AOXSrW+QUqhmWkBZhcnlR6LQMrOtG7sVoO\nOB4GqWaB3zqOrp5HoosHa9HgTwLXHU3ecJxjka+3BKPKIuBzjwXvPKgShgcH\niVjJj0H9LzUXdduO0YOE+raXg9uiTJlmMEsORxT3kGsQLMYRwUrqM2PvxnBt\nRqpw8UzVka0djnhVAA5VP1GBdH1Fnkzyd7Qiw+Aczi5rNfqHlUQy0YR2fxE6\nqotkPxBQ5R7YESrH1oxqt4sreIV2BvkGsZ5OUKwNkvjJq2inVNCObUgBWKeO\nS4q4S/ZaE0ZsmGrSZWfJCMIrQzxOCODNfJz4tXe8AHJTxd78zXhsnzecQ0/X\nFvMJlHIkS9hiZHH6TwTarLIRaJx+sjM/kKGYITUni5YP2MzoAgy75UOHDMaM\n1o0D\r\n=RMz4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.14.9"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.7", "chalk": "^4.1.0", "@babel/parser": "7.14.9", "@babel/generator": "7.14.9"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.14.9_1627804400513_0.38193064140515887", "host": "s3://npm-registry-packages"}}, "7.15.0": {"name": "@babel/types", "version": "7.15.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.15.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "61af11f2286c4e9c69ca8deb5f4375a73c72dcbd", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.15.0.tgz", "fileCount": 100, "integrity": "sha512-OBvfqnllOIdX4ojTHpwZbpvz4j3EWyjkZEdmjH0/cgsd6QOdSgU8rLSk6ard/pcW7rlmjdVSX/AWOaORR1uNOQ==", "signatures": [{"sig": "MEYCIQCVFfA2eNvuVOiFS2zWuY+3ZAqt1A99LMIM3OwDllVxxwIhAL7haTZTmeS2jXwUko5DDD3QYeI5afJQLOgUWPFBFDux", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1007671, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCwLaCRA9TVsSAnZWagAA7DEQAIyhsZhPDq4gekhtZ7h8\ns+Ue6x1F1St1CdgKitcQBxbuHc7g2AZJkPCWpTUX7PGl6iGcz7dDak1Wdz4x\nNJe+S6How9Lqc1Z72ZuaAX3egG9vzpYKtlAgSXVnw1AWj98R0dNiOE6P+I6l\nV/eeu97mfBhzyWrgi47Y46c5c7l9IfIYllIED+U05bJg4YxB2EeFeMve4UxC\nIxP8VX6K8T/vTUMM0Mm7XWl3d897p7SijMGemaNGwThzCAFloi7clVbJ6zr6\nXUvVt4zmXrv9RwoOBVnedL9lJG4FlzA0ZiPqOjcBtFYvWFT+tkd2zr4OKBcg\nSACcw11hsijV06HrBBJAdDBbXbNgprOEym0BmD6h7+1C5UwVWMY5A9ql8/7/\n1pQ+yf2xlRrQFfQx6hYRIW2VEIPFJ+ckBExnlSbwXPefkYcuXUrl5TyhH48D\n8rIpIe51D9/59iBcx12Dy3rzT8N8jYpWy00qkq1cQeHmoUtmjeKxGDv6oAlG\n9T/lkTc0doQw3a2xcROkDCGYdHMzj6FbnnRhN/1b3xRR+mFtFNnfsUnqRZt2\nk2UFBIdbCSw0dKLNWaDNQMJx2vxjNctGh5MlvzqEoTek1kEPvUAEOUNAEBWl\nC8LOTRVAYubFzbaUTQwB+wxrWAfb0fPIVfI6UOo4d5So+lUVop8RLUFxWGU2\nLQg+\r\n=g2iH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.14.9"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.7", "chalk": "^4.1.0", "@babel/parser": "7.15.0", "@babel/generator": "7.15.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.15.0_1628111578117_0.21158191971275642", "host": "s3://npm-registry-packages"}}, "7.15.4": {"name": "@babel/types", "version": "7.15.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.15.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "74eeb86dbd6748d2741396557b9860e57fce0a0d", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.15.4.tgz", "fileCount": 100, "integrity": "sha512-0f1HJFuGmmbrKTCZtbm3cU+b/AqdEYk5toj5iQur58xkVMlS0JWaKxTBSmCXd47uiN7vbcozAupm6Mvs80GNhw==", "signatures": [{"sig": "MEUCIQDcIrZpnDmX5gflWtS3G6t0MQ7pbS+595HkrIb3/uPSAwIgC6+RAt5oW7y4Csfd8d3X5SVUOuBzsV//hvoN/sldLlc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1007821, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhMUSQCRA9TVsSAnZWagAAIyUP/iLgLRtHhyVw9Isn4K3p\nzq38ZeLVoQEeDeLrqEQ8lRr5BpmWbAvGMAl5UNdfD5ZhRj/2nY2vrSNE9Y6T\nAk8IZVLJlsNvIpekdttwE1zBy9oR6Xvw0iqwS5v2+Q3sCJYZOX9kNlDTL3/s\nbHgVd+PUjSCuqLSBu5XH0mNe/b4nxRsMfI7SZDt5d5tN7yD9H8XrUrr9aEwZ\nfuNg7yv4CKH5j7mlvnzmtqQUBfLZPCjIjGVOBVKmT01QeD1z9oJx2CZA7Bmi\n9vkqaGlKQZyUDuLMnRIQCRmSIH1ni17IUZKic/ED/Svq0yyOfKoCJCj/xjAz\nIcjbWo3YCOgr8JdHOW7mFN6QcX0LhyKOHrG49rWOXrRlPe4HLAjQsnZmKwyL\n4niEbOVlTDih0GJOTS/FNbFYES5RJc3qm7MDvevL9Wfw1izhOKLEmEolOCP2\nm/r+Jywrd1pVKTCTjJymq7Gs/EIE8gbNOcxp/BiIbMJijFqvXDB/zhTz2zeK\n9v4+CmLkNwkZIgNMGe/oWplmsjvRAkLV01a8grxSifp96ROmkujRfWzp9Q16\neNoWLv97nIefcTJq8iC5BBi3aVT9HHzjDYGmvXl0IWbLdft1nADe/4ZneCPr\nrA8Rh5qpwncfN/dB8NwukqoXgugmUmOdEvWmkkOwYC7LKQi03T60BWX5bu0Y\nKe1W\r\n=jpFt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.14.9"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.7", "chalk": "^4.1.0", "@babel/parser": "7.15.4", "@babel/generator": "7.15.4"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.15.4_1630618768424_0.18060343596609352", "host": "s3://npm-registry-packages"}}, "7.15.6": {"name": "@babel/types", "version": "7.15.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.15.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "99abdc48218b2881c058dd0a7ab05b99c9be758f", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.15.6.tgz", "fileCount": 100, "integrity": "sha512-BPU+7QhqNjmWyDO0/vitH/CuhpV8ZmK1wpKva8nuyNF5MJfuRNWMc+hc14+u9xT93kvykMdncrJT19h74uB1Ig==", "signatures": [{"sig": "MEQCIFbWbs3m9ZfR+RKYRGiaqZInjm5F0re9fOeYeg4Jr23LAiAaTGJ+q6LjCNZkX/wfyGpSKS4WPyIGaSCxiohlFJ7qcA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1007998, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhOmH1CRA9TVsSAnZWagAALoAP/13qKnzJZfW9nGXqyLB4\nTg4WKKgXXKpMba4GStZD5cDoLmX7Q0t3Bs3IoA+5pDsDRBZW8CqMQitU9MaN\nU7wkKEBB6+ca396VJo+OsFooeGbK5kVvi9tGpX0SnlxbEAQzfF2y3dVD84E3\n0ih6kyMAXv/jjwkXZJwtlSnfYjJKc1guF6MVnbicuU7fdl/jSQc4pDI6tHFe\nlfDtDFgheRuL0hqTwpWly/RAdqK7UvZIB/4YK6BvuttOEOFRo62fz5xDyeT6\nHrC4n5SRUzPkqAeQMwvg+yCFW+UwxzzwhsMIBuM5iplGPbBjlll0syRWw2oh\n7etl3ZEdnaDvdRXDolMW3/Kx1PNq/v7pz8MfmKngCqQVJa7Wzr8CaiaGH7Bf\nX9vp3NvHWzX1xbFBwtiS2R3Gn0W5qTFWi0Hf2DRiiS3Jycy8oEK67neIIYuI\nDGzEHwWPp1PiE3GWe5uZYiwy8KuOSyuOsiI5hv8+akdJ/ue2NuVjwCEZYvAd\ncAGVps7oVxSZTRn4Wd/Zh9eCf8bVBnb6BzYTsSeJbi5SFd9WHmOf+B8bVVzb\n30vkWkjQioF15yHtT0ZPxV3ko0lkPjXW2KLOUut3eIDpLFKw26jXAuZYj8/X\nAJTePExrt4eGIOXBnvrkaM/kWnbPqYk9xfWjsIcG7MUQQ/gpy8BoebaVKP1z\nigbU\r\n=gP9n\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.14.9"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.7", "chalk": "^4.1.0", "@babel/parser": "7.15.6", "@babel/generator": "7.15.4"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.15.6_1631216117516_0.1655560611919651", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/types", "version": "7.16.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "db3b313804f96aadd0b776c4823e127ad67289ba", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.16.0.tgz", "fileCount": 100, "integrity": "sha512-PJgg/k3SdLsGb3hhisFvtLOw5ts113klrpLuIPtCJIU+BB24fqq6lf8RWqKJEjzqXR9AEH1rIb5XTqwBHB+kQg==", "signatures": [{"sig": "MEQCIAtpoXNVf0Px8cFRpKxanZ1f5ta2sVlFy3edRrcOIn+IAiB5SqqMcEK8NXuolwJM6MF7P7h9vheo3pB3ky6Dqg4GjA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1026850, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzHD4CRA9TVsSAnZWagAAqOYQAJqoclpciv5D7ja9hvpj\nO89+KZ894swAe7j9XzVmY5ecN/uj3bC96kRU4WZ3bWSzksMGjqR5hmdMsjnt\nFQDZXjTcYA/ncsBvdziRCgHeNjb0GWpfE+Cse9PPlK1ZqodymBNqpIuFHc9d\nKPCXPPFK6xALCNm5pEUzMxZPW7kSEbfK6/cEkipnrpO3M7n7psYns+Bj3H3u\nBWL+vpuwxFQ/px6w52hi2qdLCiRYrj4Hi3MGuS2JTsFi69Dd+aQ0oE1Fdsg5\nyHKGcdp6Iyt6XIWmzGlFV0aNTufg4uivV9Vqov4ViBy+Qs1pZlYqUZT2a2qI\n6SDO2JNXuMVGb6UKFvpSffwspchxq3zTYfh5w+lssEDsoNIkeRu6uo5+qsjH\nNqsT38Vgp3i8aSUIBHtMnQiD2XDQh6jgBb+EFdZyh+Sz46apwCgknF9u9cLg\norwNEKUUmZCCxKJDNXYPtzbrv6Z5Er9rY+sAuQyVczYGyVZLtTFcg2/MJJlm\nk2abrQmHidRocY6Hy1bA+Qhr77nc55bDOJfBcxEX39vGrTq2SVESFJo5Vw6w\njo/p6afKBL9lox7BV7nvjQZalZx6kzqi8vJzgATd24AldY2d/466caBxprzR\n5Biue/o8khI0Y5cB4g9tjB2+9K7pWrTdWpzA2NstqkYd1qT9zabcEN19RV5P\n70Ql\r\n=luEF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.15.7"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.7", "chalk": "^4.1.0", "@babel/parser": "^7.16.0", "@babel/generator": "^7.16.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.16.0_1635551258155_0.7323606105536422", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/types", "version": "7.16.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "4ed19d51f840ed4bd5645be6ce40775fecf03159", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.16.7.tgz", "fileCount": 100, "integrity": "sha512-E8HuV7FO9qLpx6OtoGfUQ2cjIYnbFwvZWYBS+87EwtdMvmUPJSwykpovFB+8insbpF0uJcpr8KMUi64XZntZcg==", "signatures": [{"sig": "MEYCIQCmx30RWiz1QjmTdQ2fkPdQtjA8ug+RkCgbp6e1kd02owIhAPn3idagXfVqzWEGIcWXkPhwc3u19KD3550cv9ZuTGma", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1023908, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk02CRA9TVsSAnZWagAATGoP/3melvLdFPrh+uaooliN\nGf1nHMbF7ByLFN5bm+0QlDTXLpmJ+W/Np6ZLWkcyW1lGfmqW4WKyozSgg9tQ\n+ta+W2z31otfFv5ODq9pQ55jKUKwUaCRVG9E1XOTNp7E7sFo03BoCS8ScC0X\n0JSWCDDv62+sZAZYB3zKXXQOfZ0LtHgRDvXcwH27EK06Y9IwHrbhrwOuwwJ0\nO/ZgaNs7byQHuSaEYydwFOwzejk20e4khqWYsHT3+Fx778ighvDaNpRXOyxL\n4MRv9//uyQbuRk3XmRi/coffmRNHrvdD8xpywF8MYcIIqEbjLmJpdNFRwgaC\nlarP6QA+jepZT64ulNV1jL8Fhk4Z+g8Ns3RXpORX+tUx1PV65PNZqLGgoNbF\ny41W+1NsP8ztXeVuzywwbZnd1YPOGWgVMcnn9YYWSrij91DXwhBjHkxlDUB+\nJy/g2yR2H6aMQxvIEi49E569Oxgvx2Tcwz4flgNt/zYYTP4XTmIbpZFGnqqh\nJ++10YrT1TtFub5YErIm+couDIgr95RxXk+yv/v0C5wv+mt/F3Xkk1GBDoD2\nujxzA4mbZlkpntPKHHVLkfMSJ99j01W6ctMDo9gkCPvKOcheDkQIK2BSbBDe\ns+kGIy0z1mQdEiJ3UiVtuTtGbcE3tukPPLm6kOsLQZA+b4mhoKoDx4uNAz8I\nT8ko\r\n=cLJV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.16.7"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.7", "chalk": "^4.1.0", "@babel/parser": "^7.16.7", "@babel/generator": "^7.16.7"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.16.7_1640910134108_0.3643230764149188", "host": "s3://npm-registry-packages"}}, "7.16.8": {"name": "@babel/types", "version": "7.16.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.16.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "0ba5da91dd71e0a4e7781a30f22770831062e3c1", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.16.8.tgz", "fileCount": 100, "integrity": "sha512-smN2DQc5s4M7fntyjGtyIPbRJv6wW4rU/94fmYJ7PKQuZkC0qGMHXJbg6sNGt12JmVr4k5YaptI/XtiLJBnmIg==", "signatures": [{"sig": "MEUCIQDQR4S8JlVG2xr8k1br4zV4SM8akjnSmxTGwnptFGtxCgIgKgyETCDMHkHpoQIveQ71SjGB9AI+r5z6AmE4/+FRDWw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1023916, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3KKlCRA9TVsSAnZWagAAKRkP/28SGsqHnTzBuwcwl8rp\nFZO8suDRKj7R5UmoVghIfUjpbjI1ASt6LpkjUObQ8hc/E0uMwt/snEsIFTHo\nP6wxy0qrxoirXEA+xV9kgtOniSyR5EPfZidhAJE1Qg+Je0NQIDyq+9zE5qJB\nTYdQWozLvuaOh70ybJudLzBSL+ss45fYzM0bBCiICj2S8Zl9LxTZhLdjzlVk\nW7P4U+CTxSIBkrmUIhG2xyzaobCCAU8CqVs8/2AsxofuANdBi79wOwTgKb0z\npj8ZgS1T1i1YdqzLJ5C/Sp92wM1LeK4u+LqIBGlkAGyD81/5ZZFA0ht95AaK\nT467wVkGWfYnWJnGX+zLl8Vih3CEVCTBIkfgPf91zHBp8wzYKH+GwOAptV2b\nYbxoZ5tB1Cy/G+DsH0muI7zQk1psCgBcL1YN+6puTQfAgMtRIj1cspjwGAt9\nNMUPMvdcc4LmnOfVADYII9yQk+kG0Ja9MmrP0pskttMmfFIBusH1L+rgHp9Y\nbt37+27lU1l09BmI67PnPXP1KbSHxn8zX3/v9to6nh1N2a2hCTbnA+aSKlSI\nnBwEATc/+4dr2OQIQGoIjN+J+V/a18z9sopPZjSEcs26uEJe7ys0OSNReTM5\n9z6GROCZ6u/958H9jIGz4s3Edp2oHTU5kKW9wdCNHWTYQe1eeyKpfrsNA8Bs\nuwQZ\r\n=zPdi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.16.7"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.7", "chalk": "^4.1.0", "@babel/parser": "^7.16.8", "@babel/generator": "^7.16.8"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.16.8_1641849509052_0.11272295119960196", "host": "s3://npm-registry-packages"}}, "7.17.0": {"name": "@babel/types", "version": "7.17.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.17.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "a826e368bccb6b3d84acd76acad5c0d87342390b", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.17.0.tgz", "fileCount": 100, "integrity": "sha512-TmKSNO4D5rzhL5bjWFcVHHLETzfQ/AmbKpKPOSjlP0WoHZ6L911fgoOKY4Alp/emzG4cHJdyN49zpgkbXFEHHw==", "signatures": [{"sig": "MEQCIEkOIoWqfN7+hEcBjJm5lgUsGq4AxaXo0DayVueXFh0QAiBhdK7xkoSOhWqXpUCw01on8w7YXRv5/Lv/8RZgkqzbmQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1032242, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+w4VCRA9TVsSAnZWagAAto4P/1ii949BRUUsd8woMhlP\nGgV/PtXEHAeYru0lAHq3XjIDaEmwAIjoNI8Hxv8qHywtVTn+2aANAisxGwqU\nVWr3FwvR5aJBb9QPX8VombiPlBmxk0VBmFYcyyc2wbZ6wmUm2L8HQ30OwAEJ\njyFWTEXrMHRyYReuFmNhzLnri3q6c6zkVhliUu4SrvfaBbkWhGWxPuz1jdkC\nPyvQlAxCvMTkLedqduhPShGYY3XaJUeoFuBfjrR3P32gwNC0Q4Nfyhhwr+5Z\n1kS8JLZZ3nrQbns0US1wveL7V/D/WU0ycqrIeqrJz7AGTdTziuTRg2KUIwyL\nK2ZJn7E1OdmceaysMblkcCcM8sAz181ku9StP/SXPGl4smgGt+7whn8lkDYe\noDKiF93saDG/uxZj1Efc9CjeJ8s3xlp0OyGcboRDszekNIY2ljEJLZ1Jajgg\nXmEhDW4SRoVw9Lf0Am61w88gA7J5snkl6WQ6jhbaB5bZkSzktjdlyw8dvnf1\nGB8D+9EIlSfDUJnHGoPtw2P0RW6XUiXWlljWox3bcXQrkSWcKbk4rfnjOA17\nnHMSZAMEAWXxT4HQ+WWATdJ1/ZDn+plemmxUDjfYB9AsHuUM0hFcGtQLEQ57\nk5eS4KL2cd0JTM//+8FZewt/+8PZ9u14xPmqIT/H5Jt6/q1gadMAc1rBBjfX\ny/bi\r\n=qZz8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.16.7"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.7", "chalk": "^4.1.0", "@babel/parser": "^7.17.0", "@babel/generator": "^7.17.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.17.0_1643843092861_0.8580581440155179", "host": "s3://npm-registry-packages"}}, "7.17.10": {"name": "@babel/types", "version": "7.17.10", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.17.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "d35d7b4467e439fcf06d195f8100e0fea7fc82c4", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.17.10.tgz", "fileCount": 100, "integrity": "sha512-9O26jG0mBYfGkUYCYZRnBwbVLd1UZOICEr2Em6InB6jVfsAv1GKgwXHmrSg+WFWDmeKTA6vyTZiN8tCSM5Oo3A==", "signatures": [{"sig": "MEUCIQCdNb027MVvuxRR+e2sPckUCLbFAJ628EWp3uaG7orNdwIgOVZ4mLoHPdSsJQiAycLsWeAHeOPbvUDtSjerWKkII2Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1032392, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJibBRTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpMJw//ZVAsc7yi7/sB6NbUiPp//1ov2Ki8T2oEQWvQgixYrqqHVe8N\r\ngAVGNcYcOUswcrFrezzOSK5+yUP8IDE8Q2bPQMEnBRGQ0svbXMPFHiFIKvNH\r\nmqLXcYZWaCGEe5D0sVHONUSU/vvIF1H3Zr4yoZcZq3vhsCUplEY3n48Qmctv\r\nxCWqHegrPkM+Oh90LYPXdToDo7aBseWzNgFi39KKFhJCsJTXJRq/nZP1mpKz\r\nJB1cL2Wd2rEqZPlToA7Zmek9UZSUqRZX6HaNbh53FljhuZqGsEpVRVidqV3m\r\nlpalS0L2QniyvK0VVaL3FAcXK9L+i+XP3jLQ9kpoM5yrBgFETDlrcCnR0Bag\r\nIjd/UZxnUUlSDDhZ21uKCvYdZLo+vpkqoscfPCxKI4RlAvX6bcRXvjNwJ7m4\r\ngrN3ddki8nahfOOsQ28HfP65mpNhWY2I4x0nqeClstr8O7fDHfIEDAAgZNIo\r\n1Wx2oRrYPn+tDKRr28ykbw5egR3W5oRER8cX1SgdSKjHRgVz44s3YTbMPVRU\r\nh5Bix8aBEkHmZQIHE+O6//Kc36kyRBCqimv21HLI/BqMeqpxuJgXe1BR9P6g\r\nvX9XX+cdyNqqnfNaEZJXz2gUeGbqeB6ISZ/CFZuefhNdDPU++fl1ko7zWV5u\r\nlw0Gzno6HE6ga1M3fqF+DaiblKPYy4iXebI=\r\n=b70V\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.16.7"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.7", "chalk": "^4.1.0", "@babel/parser": "^7.17.10", "@babel/generator": "^7.17.10"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.17.10_1651250259469_0.956090284622451", "host": "s3://npm-registry-packages"}}, "7.17.12": {"name": "@babel/types", "version": "7.17.12", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.17.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "1210690a516489c0200f355d87619157fbbd69a0", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.17.12.tgz", "fileCount": 100, "integrity": "sha512-rH8i29wcZ6x9xjzI5ILHL/yZkbQnCERdHlogKuIb4PUr7do4iT8DPekrTbBLWTnRQm6U0GYABbTMSzijmEqlAg==", "signatures": [{"sig": "MEQCICLjCU1KTBHvMjJXsALXUB/KrgAins3UJeOqstrkWHyLAiBabMQpArkRhDBFiFUaIcj3n7Xw2W5kWp/ySbpj0nRk3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1043827, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqa+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpLxA/6AsLOLn8cR48/HctwDyixkgjQ1dhULRgmMtEEUesmA0PMzR2l\r\noJI1sOmzILLN15gcEAI35xztFyyUmJl1r5rL14dLldDdsEGqTANOWq2Gx4XF\r\nTVwaKK2aw7sOFqMkJg06XX69Qb9/aykMEGp3Pq/DotTH70WeEBMMPhNop773\r\ng66jfipb6QUatLMIaKgLOGIm6LuacMeTNujUPAHhVd8GFqoQ3Or45FtZK3P7\r\nwCr2d9SjqXT0cHwE7wTiisAXj86HBF/qpRLuEOjdxVUXJA8FiACmkOLyMltI\r\ng3J3NcWzTI9g8yWQ+7og7oziNsjz5JnWGgT4JncdDs+0i6TREbAws8okqn3M\r\nYQc6prcjqhbwU4Nqzx9xQ+rt3uiwPLPTTcRw6b7pWmE7cJnJ/ETvHmB81Yrr\r\nQmx1j0zrEywullhcNyHd3RiP7Im6BJTHKhSb5e8UdQe/tay0125U+/GynLMJ\r\nsF6CU7Qz4WgaNjBb3k7t5uzbZvFWIAKPysUKtmUAnmIM2dw6XyBHbONYceE3\r\nvrYQl4S1Y7rA/vID+DAtgtgbv9xJ4j1LtG8vsPZJq2NGiZ/pbmGqvjGTvOTe\r\nR5bMBMITkMhLmJQKtzo3avV9rIbvhN57wnL7rcpr1BuX90LFTDv578W371iR\r\nvB1Q2aIIHfVk0pp9K10rg59SPtQEWF+jpmo=\r\n=WMxL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.16.7"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.7", "chalk": "^4.1.0", "@babel/parser": "^7.17.12", "@babel/generator": "^7.17.12"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.17.12_1652729533793_0.270928791814258", "host": "s3://npm-registry-packages"}}, "7.18.0": {"name": "@babel/types", "version": "7.18.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.18.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "ef523ea349722849cb4bf806e9342ede4d071553", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.18.0.tgz", "fileCount": 100, "integrity": "sha512-vhAmLPAiC8j9K2GnsnLPCIH5wCrPpYIVBCWRBFDCB7Y/BXLqi/O+1RSTTM2bsmg6U/551+FCf9PNPxjABmxHTw==", "signatures": [{"sig": "MEUCIQDeLndSwmO9UYDobfXLPO+gBQpRtfKKheG9WcGv/JjEywIgTjwO3wmenM+StWLjhhhf35DvxVEhQxXhJf5c5GyeZEQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1049140, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihomEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpAhRAAl9BS4HL/pXkWXjKi8V1CKihEq15/rUtRy0llALYdge6o0eqj\r\nBPe0NULcfd0OLOmH6pbIepCwLyfk80AZdh0bnW7c4bgmCZl1CwWclqyEnZPq\r\n8BZMx5GcRxzVsUK8XPtstnF1grac4wSfw8nO+kVzFtC2I6ojamSiceQef+Vn\r\n1G2wLnValQgprcZWQq9gsZ7ffSGJAFKeKesKwYD6EAhjJuY3orlqBs2O60eu\r\naWKauNNmPgNMwRzB0on6k0fQI8VI5h6lzxndErb7oKljcv7PQTLnNrrPkWaJ\r\nSccljUcSIaFLTKW1fNFANwuwv+Ausl+YToMpVY8S5Xb3KikrvMugQd0AYWWT\r\nMswxR9eoyIo5k4+Ubyik3EaTJQcpMoMzDGzEtYzXzqwgKNQ0Y4jppf9zyAwp\r\nTUbR3F5E7fY/TcV02sKy737A3kpE52mVouspMhhf0F+lOi85mH6ZNN3B3RgJ\r\neB6o0eIUpAimd5brZrLOXQxQSIud9yn54qIZ9ffRAqeXyEmLYWy9GFA0AhZr\r\nRA8pDK5nvdl99gAGn4owOVwfoqnPSc8Hf5aZNAMnuVguWOTgO/9BywMRz7ii\r\niPgfRYXIuDMvg9VbERcKjCgDPyqntQ/O/nq+4G8u5pYbAHg18lQJJ6bcqqBb\r\nospE5Egl18OdNeHNjmhRFWi4V/JNisVFd7U=\r\n=nWVi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.16.7"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.7", "chalk": "^4.1.0", "@babel/parser": "^7.18.0", "@babel/generator": "^7.18.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.18.0_1652984195890_0.3377662841666733", "host": "s3://npm-registry-packages"}}, "7.18.2": {"name": "@babel/types", "version": "7.18.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.18.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "191abfed79ebe6f4242f643a9a5cbaa36b10b091", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.18.2.tgz", "fileCount": 100, "integrity": "sha512-0On6B8A4/+mFUto5WERt3EEuG1NznDirvwca1O8UwXQHVY8g3R7OzYgxXdOfMwLO08UrpUD/2+3Bclyq+/C94Q==", "signatures": [{"sig": "MEYCIQD9t7QKr8keMc0LhLA+opEPatakIKsfGees0kdXpyDoPgIhAO8oN6z3fLKJ5eBy3RzOWce7r5TVOzjI59ePxFFY+nii", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1049195, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijfPuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpsDA//Q0ThyWDr838MiKyUYAzHB+juZXm6Ki2jPmvGSG7DACn9xiTx\r\nDhCU79Vi9pYm2eYzdl+R/BngFFPz6vu2xISudKARi9dKU/SDt8R1JvFWc4lZ\r\n9h34r1yDyvrGuavd6ykgGbsCamZM0KKTK4H/dPank3BNPxlQoOsDD8ogTO3b\r\nRGemygW+mpQcAICRsAA1A0HjzKYGzTrH9KggQosPFAm7pQ0xQp8l43cmdHzK\r\no687MYU3yX/m/NFwY0EhWr+mDNl4797UEwrlucu7XmHz0X0kTOGMAt1T93Tb\r\n8kp1UYaJhOVhrmG8eI0rCNgNXbdyEw9tPeBBKLP8yUtCVCNtAA6R8z0rtzH4\r\nVYOEVE9TkPNnBCP4N9iCiag3Og7ZwllAAypHiHb0Doeu0cQFTjxq9uuVdhKz\r\nKQJBG3JPTX8HeJPF4Ekc+iJPvPu+SfVwJwMzIuTAKZMbp0cdHFm1j+19Fpsb\r\nEN1rP+lvvJrnz4GY2qPmPN9VDJDr70SryIyBek9hLfQ3TiguvN/sSyhIrsyG\r\ncYlOjv0rFf1UkV6dx68JX1OAzFbF/eH4MVXELiMmmmWMieTVKAabS/oevQkG\r\nW3OB8nemcQBkX4J9I5a3/PRJXWTptBd5B5LLm6052Ge59r86Eid3oCT/HmHO\r\nDNiid9/MFFL/PCoaylctQo5xgs6jKy2mzZs=\r\n=o1yK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.16.7"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.7", "chalk": "^4.1.0", "@babel/parser": "^7.18.0", "@babel/generator": "^7.18.2"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.18.2_1653470190053_0.43884553771557067", "host": "s3://npm-registry-packages"}}, "7.18.4": {"name": "@babel/types", "version": "7.18.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.18.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "27eae9b9fd18e9dccc3f9d6ad051336f307be354", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.18.4.tgz", "fileCount": 100, "integrity": "sha512-ThN1mBcMq5pG/Vm2IcBmPPfyPXbd8S02rS+OBIDENdufvqC7Z/jHPCv9IcP01277aKtDI8g/2XysBN4hA8niiw==", "signatures": [{"sig": "MEUCIQCdRLdhQu7TysCjc5cmSgMUN9+xvSMtfjzFzJeCuM95IAIgfB7QPZ87G/KfFY9lOaS+uzEViTqohO5lRrUapMW1CYk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1049036, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJik+qUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpuJA//aF7Rw+jhztcd+eO4et1Dq84t3Oq4RbqlXbRZrOAwNzhdHG3K\r\nlYE3XBq4zYgONwMG5XEuCHhFkLRZDFn/scWNClj/ZpgZyvdhY4WQ3/flcjOS\r\nNQv8DCOOZoMDqDV9TyTATLHhTdZlh5Z0jA/Djt2fy4aZ3Diw45ZLoZ109OZU\r\nJ4iwB0+qZtqLNNA6HLaJFnJ/OeeRtzkAhLWmlXv8iMUQ2hQ08T1bmbm+zccV\r\n25pGvpW4gFjh1n+/qtM/EU5XRLRMmyfojDbXvRC+ifD4L1qJClYXSiie4OP6\r\nEPy1rzVh9Pn1o09RsahQTHUf9wyOy7bajkppp9tMhfkvkHH49fAES9UQuhG5\r\n8638Xlb68bUf3AvOiICPMzdKiCSRa1mSv7b/XImltikytUUtS6ZX9pQ2Uwj6\r\nzidrfyCiaqr56sBuBz5GQrOFh+9RcihUv4qH463XjHtSDDO4S9awhJ0OFlF9\r\nrYAuHgLE0L/fbE1IQ8b2vY7PevWAkqMk/orjUTp6HMGGqC1vzUcYnohNoiKp\r\nnNioPUSDzEoe0G5z2/OJ3BDjOOvTpoaPtcfRoKr4/xU2QKuxDpfyeqEAdIr6\r\nljoRAX5DSqcwyIjHjsiJlRUuMcDQBeSb3zReLh1/EbwU/zfstuvaJQua3jTy\r\nF20PzTyB1r8IdQtiqlx5ugxTKm9Q9lCIPoY=\r\n=OKCK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.16.7"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.7", "chalk": "^4.1.0", "@babel/parser": "^7.18.4", "@babel/generator": "^7.18.2"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.18.4_1653861012381_0.4251810240672882", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/types", "version": "7.18.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "5d781dd10a3f0c9f1f931bd19de5eb26ec31acf0", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.18.6.tgz", "fileCount": 99, "integrity": "sha512-NdBNzPDwed30fZdDQtVR7ZgaO4UKjuaQFH9VArS+HMnurlOY0JWN+4ROlu/iapMFwjRQU4pOG4StZfDmulEwGA==", "signatures": [{"sig": "MEUCIDyfd2o8T0QX41Yzd+HfaoG7ynHc4c1FqXxubmhOIJozAiEA9LhWGkI9CezhtB2O6cyjYOoA0HwG5jAFD/liPCCMZN0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1050405, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugnuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrCWg/9H0qE6hHmkPIZA0LYz+idjZNdjAwKC9nm+cMM0uIjV13cC2rf\r\nqIwWOUcZm9ZLzB0T0lCpaH++hHFo8YgGbU2cJZZdybuvFctyx+Gqy8WornOq\r\nn3oPd2OYTXlOkxvdjUNH8kRSBqFUsanaqvHEqTD1eF2ves0h9sV2YjRSJevL\r\neR83HX8d1kS+JLLXUbJfEiXH/TGfst9wGmNvQytgtB+XEsFRiztq+AnMlCvf\r\nrG3vDwO1lfpED+C1ZZdsVqom2imslh8/ZBiCINZJMVhNpeTPUrDprwdZr4LW\r\n41hGXuLW8a+tZdCwVHwYaPp4R9fLJEETGhtiyPPJC4njhvtZ2xSVQM4haZfP\r\n83imh8V91FvXt6pvQ68nSt0AnYsItJyaVM6oPusPrH1CekLq9v0Dq2Nihn92\r\nChauIPbwvWJBxWYtZ878SnFsIqWVR9nS8T2rZZumGHz5/510ibxOngKOdorE\r\nz4fLdqWLYCfVQYTk3W2QmzsJZSLzmNHIWP5OYWs7rQ3srPHip75do6bWD59c\r\nb513wTUktASQRVaE7bqp6D5kMG6fo1lfSNeYcCL8OM2mD+Ez0kHWnsQ0m6AX\r\nQqMFB0MbfPKhvborNzFEghfe/UUrWN9hbB/Zs2xq1OdAh/8oJLQgY0vDx6cQ\r\nA6E9OdJCuy1HthPWMqd2Na3pCfdg9Bpdl7c=\r\n=UTYk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.18.6"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.7", "chalk": "^4.1.0", "@babel/parser": "^7.18.6", "@babel/generator": "^7.18.6"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.18.6_1656359405797_0.3943448047332141", "host": "s3://npm-registry-packages"}}, "7.18.7": {"name": "@babel/types", "version": "7.18.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.18.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "a4a2c910c15040ea52cdd1ddb1614a65c8041726", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.18.7.tgz", "fileCount": 99, "integrity": "sha512-QG3yxTcTIBoAcQmkCs+wAPYZhu7Dk9rXKacINfNbdJDNERTbLQbHGyVG8q/YGMPeCJRIhSY0+fTc5+xuh6WPSQ==", "signatures": [{"sig": "MEUCIQDhbZd4OifwwLyLfEKhPZ5kT+Cp/i2Z8dhXov0KCl8WEwIgFZ3zuO/73al3OlTcMaC+LU9VsisEVt8PPntmJreMm+Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1050361, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiu2ToACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrtSw/9EptAiSa7mvQ/b/dclcer/4prq9ncUyi0RCZTVAPt6pM+pVBa\r\nU9VS1TtCdwv3Vw350gWgF6eGDyq51TlI8HLy7A0TXD0AZ4lXhjJuV7D/vUrN\r\nv8nex9M0AXroVe2LizM74AlcS1NrYEQ6CW9K3f3qgT4c2ESQvVV+PmF/o9C9\r\nTXo6WWKJVB3/q1H0IYZwtrDzO2dFT/sm/prJZlgLhBmVWoQnGwOMWrDi8RRY\r\n8TFKR+EwGAc9cu3/5I58YREnoZ2iLsJnDRwfhuxmuF+8mAMffKjmsyJFkOR/\r\nhxjt1GcfvQPnwC5LycIL9OMdmnfv3BvgEs58iLGOl6VAkTjtChXKPQ6NmQfZ\r\nJld+eyJfaJMphSg6JxFQv2D/Qr4RbNoLHsdp8yKYw7WXL6xEoRh1g856z8Mf\r\nEU+IbeW2+JTaG4RyTC/pyIWugJ5ZeBXlpzx/MIRKyv0aE/1aAZfoBHu4GOt7\r\n4foyhabQfDcrgLlM4mVpjS/82msb9B9hnfBvNuHTK2Z1uJFIcsU8KOZQmunu\r\nbhIdd/Assr+gRGxzInH1bCP3CWvqlinOvEI+FEA9T02P19HLplZrUYJY26ew\r\n1aT2dnAx5pSTZBT4vl031Iz4iQgWHdbKidq4VIrAGSL7SpvUY6eNZP+Hj0vB\r\nnAAxVioTporqnkMu5MreOJDStY5o6h5voAc=\r\n=DQiX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.18.6"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.7", "chalk": "^4.1.0", "@babel/parser": "^7.18.6", "@babel/generator": "^7.18.7"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.18.7_1656448232134_0.1671223053385662", "host": "s3://npm-registry-packages"}}, "7.18.8": {"name": "@babel/types", "version": "7.18.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.18.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "c5af199951bf41ba4a6a9a6d0d8ad722b30cd42f", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.18.8.tgz", "fileCount": 99, "integrity": "sha512-qwpdsmraq0aJ3osLJRApsc2ouSJCdnMeZwB0DhbtHAtRpZNZCdlbRnHIgcRKzdE1g0iOGg644fzjOBcdOz9cPw==", "signatures": [{"sig": "MEYCIQClcdkEB5riz7YO0bK1i3VyElLbffSTTobSE1EaYx9eegIhAPdZQCNbEpYKRABnD47YVoVHeW8udeGa8gLlAyvS+l6E", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1052119, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJix/mzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqbWg//XQx5mFucPoZWuKl3+T92CHpauxiTEV3DYOeN1tSmFql7QfBT\r\nTl/KzBW6OQBWjZZFMs4vmYO1rZgEQmTv/+9VZ2pYcirKxlhBOVB3OE94bZvH\r\n2H/AlrVXXdv5fuSTy3qbOdCoKnzc/mU/sPowQBGliv3mM/DTYr+swBdyy1up\r\nTai4kxQZDpvR+psyi6YmVmS2hZVnS4XXTZpm3HxXafTX4OlmSq8goEL7F8ve\r\nBXWtq+NdYxUWq2LV7siwljvNd2sVvl7Z9x9XKrBfPZcbv7nZQpSJLIORLwWL\r\nDU807ptETSjbtUUUVozzrJXRkO3dDrp36cL7fOX7Gj+wjuAhkzePxa8B7SuL\r\n5lqGDpkF+PI7XQcV0QeuAklm5ej9/o6D8r7UX0hfQBGTFO/lgznarY3CymLm\r\nqh+YTja87pdvq6eQDX+h/4PtKk7QId/oVBRI2wp8glmAayODjiBdzZ61KK+r\r\n++/eqG+Y6bJRTqe2q8WQeGZ92eXNbon4vG2h1/PSInqmZh+dM3SNkeOo18l1\r\nzBmaINV6quX8VSaCA/ZN5Ve4SOiKwaKUHOk6iWOh+HFHQ9x5WCGdFfN+IuM9\r\nZi8baKfejDZqen88Zo+w954hjLfLkvK/iPQuU5X1biRZlTwn7p+GMcNVdygT\r\nUBcn5Vro2wU1Rb+oQ9DXNSI8zsgZkVVp+1A=\r\n=Wi9p\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.18.6"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.7", "chalk": "^4.1.0", "@babel/parser": "^7.18.8", "@babel/generator": "^7.18.7"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.18.8_1657272755699_0.2387063113364658", "host": "s3://npm-registry-packages"}}, "7.18.9": {"name": "@babel/types", "version": "7.18.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.18.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "7148d64ba133d8d73a41b3172ac4b83a1452205f", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.18.9.tgz", "fileCount": 99, "integrity": "sha512-WwMLAg2MvJmt/rKEVQBBhIVffMmnilX4oe0sRe7iPOHIGsqpruFHHdrfj4O1CMMtgMtCU4oPafZjDPCRgO57Wg==", "signatures": [{"sig": "MEUCIQCAXVRku6VjfDYvC/K6PkY3bcpxniyhfwoXjxsW9ujDOgIgMHfUUb679tVdOrsYvUeVsxS6vEccNiRU3G1NuVy8o2U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1168050, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1SUlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqRLw/6ApCxQFArw9Ef1pA8tNAjPUYrkG/RZRcoQ10zf7GgESnd8mki\r\nmNJ91qKJPMqfXbD7d850SaSNEHjx8aRIwMpC6BJT03T7vmGp4h5lhM5odCc/\r\nOL9zMoMjPM2Ejc4Yrh0lfiU7Gq6UrDMPjNzPmxlt49Yebkmq3UdJJUVajnEo\r\nK6OrOPdeiE0KfWCfGSZJzHkonbr2301cuu1zSR/mzaOXOcSDM4W6NFdoGQa9\r\nJbVbZv/8Tkj3d4cC0g0HHTF+DwUio4d89n6Tm6cZFc6esF359nsjQ0nuEvcj\r\nlTr/xMLl6t38cv623u5hnCpfRK8/n1kB+mleiNjayKt7u0/qnM9BboDOO8X/\r\nZ/CN5q6sQHvF9segmXIlM8ziG710UHYKCYgHMlrzZTBu5WbDTmHhoxV+itIl\r\nhWmSLnmg7hMpyF5sqWDVSf6zQEAfvOJEqU1qrO6iXhcZoBsFkJ3h+aa1jMdC\r\ntx+Yr4hFJ2B/wk3M8ibPXsN+WVasPq2BXp2BYYBwery+YDr0bs1iIY4amdZq\r\nhQtrV6npi7HB3S6FbgxBtxw7TAKeJrtMwjXd/5O59yZLfJ7defU8QImr5G46\r\nVTEGZ1RfuQeXLsx3411MqOUgMBTA5ycSdreeLUsr9NVjJrYT3R1pPrnWXVov\r\nvdHUk1epqYtpUw778kdQAhWTVS8ZC3R15TA=\r\n=HiIG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-validator-identifier": "^7.18.6"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.1.7", "chalk": "^4.1.0", "@babel/parser": "^7.18.9", "@babel/generator": "^7.18.9"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.18.9_1658135845249_0.9097347886890421", "host": "s3://npm-registry-packages"}}, "7.18.10": {"name": "@babel/types", "version": "7.18.10", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.18.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "4908e81b6b339ca7c6b7a555a5fc29446f26dde6", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.18.10.tgz", "fileCount": 99, "integrity": "sha512-MJvnbEiiNkpjo+LknnmRrqbY1GPUUggjv+wQVjetM/AONoupqRALB7I6jGqNUAZsKcRIEu2J6FRFvsczljjsaQ==", "signatures": [{"sig": "MEYCIQC0cur+TyxOpuZ8TCqAOXsgboGCfmH3JOYg4c7KnIUELwIhAOExZuwTrEUqrmDi4QG182T+tIFCj+MIKABCumrMvB7o", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1169362, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi6B+UACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpBfg/+Mj33p5BUnvJ27I3Ids1Jzi4qqg7MjDLXKn9LC4fKFLK+/7cL\r\nahR7echZE2W5bziU8zf8ESwJWuUkXftpqnzm8VXSKd1uRQQXpuapWJwlJgLj\r\nzH2qLt4QXGdKhl7rPJJ268KX5PrfXfFG1nr4lGuFfRgge0qlVekjo9vZyaIi\r\nY+P94/xoon8Mdbpk72tHTNtbZHzxhCYWcPoULM2LHcAcUaUiPI1VgzhvJyIN\r\nNRRS6W2rbFiTeNuIVf886eCAuGkzIAki6UCITkLzj6W7cq4SMBFja/IT2HX6\r\nhOTRZDpdOYijCPLq8e5mbHkWcnqArjc/KGZLHWhq5qaw7Vfkp3QaXzfc28wf\r\n7yX5qeZDA/hv15/NQxIKYFBuDrqPXqruKZnLmFH8++bIDZU8c4iGfQioGSh5\r\nAIux6h9uV2MOzmKcxIZPe0dnB+zx4NjRayYcMgxZrtTgH6j7wEyq/R4UE2XN\r\nIVsGfihqb8CG0tfy1sxqWJfIygBPPVylF/h13EXwbOh3eSQ4R7ON3XYcxH4v\r\n/jiI/7Jtf1ukYEXdZa8VV0Gummzm7d6Lse5JR+bdmbxMtkLu2PZqF15hkg/2\r\ned0vGDSnWXbLw4BwR5cALysyrNE9OTmJw5IjefsippgzV5QbI6hJ8Au66Q9y\r\nMmyX84GfMmb4X7rYRIfksBPWSH+/C7Js9eM=\r\n=FmPA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.18.10", "@babel/helper-validator-identifier": "^7.18.6"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "chalk": "^4.1.0", "@babel/parser": "^7.18.10", "@babel/generator": "^7.18.10"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.18.10_1659379604079_0.5134128643339655", "host": "s3://npm-registry-packages"}}, "7.18.13": {"name": "@babel/types", "version": "7.18.13", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.18.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "30aeb9e514f4100f7c1cb6e5ba472b30e48f519a", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.18.13.tgz", "fileCount": 99, "integrity": "sha512-ePqfTihzW0W6XAU+aMw2ykilisStJfDnsejDCXRchCcMJ4O0+8DhPXf2YUbZ6wjBlsEmZwLK/sPweWtu8hcJYQ==", "signatures": [{"sig": "MEUCID7dyXfLrlDXJQ4MnbTArOQQ1ELPgysqNIM9DWtMhR/FAiEAifeID/yXQEoYufg9DhK1fr5yCVFiwUp5eZzfcXjfviU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1436652, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjA6k3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqNMg/9HkY+6+/nvqmB2sO7YVxDNLx7glPENOKpPf/yUB3ftMDj5kGr\r\nkqFOlnHKwRaFxPIlXGippvjAYTzQbVr4eR5IaA4dYTVSp888dF/WdYPQ6ejX\r\n/heVhAWOp6Yg5I3uwgZkg/a9mN5Wm2bCrv4HX2H9QstJ4SSkEmInZmXOV7pM\r\nj3GPQJoMVYExudMW+LnyQX0IDvjo84Q2+eur794LNW2O9cc00d1kOnh16d1D\r\nxrKWFXzfvVjGTIrSRzSr8UM6daiCVlQ2mp7K42P9vjaHkcKWInvulpsANYH0\r\nO0P4V56IITP2mIwviAmQg7O6tB5IyfAySvLWolh17iAxWvoEg9W1ePF287j9\r\n1TJKWsLL9ymIYH4jZoWIJ27ONd1q6XxN5jBxya2V35OGJtbHibaOvPnG/pj9\r\nx5gGMRTqFb6t9vjJITXQx26F1beBhUAelIrcq7pkDwrpFSIP1KBa4gBV2Wlf\r\nz8Q0PLaGyM7XVodiiMih27fXbtorXvH2tWIC7CA8IXgRr013FeSyqHI+GWv4\r\nDA/8LxZkxnfaXQq2uqP+WEJSu2l5hCYQ4o9vLQLMNwLFcX9SAcStGrtrQGrI\r\n6c/U0o3sM0PLeXbCoWxcC5n+lPgvFUGm+Z03StTlnyt69CaiX6jfT44oks5O\r\nmrxVuaz5z+NLcJA698l0bv2F2S3GXZL/l9w=\r\n=j+eP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.18.10", "@babel/helper-validator-identifier": "^7.18.6"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "chalk": "^4.1.0", "@babel/parser": "^7.18.13", "@babel/generator": "^7.18.13"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.18.13_1661184311640_0.5018557882707946", "host": "s3://npm-registry-packages"}}, "7.19.0": {"name": "@babel/types", "version": "7.19.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.19.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "75f21d73d73dc0351f3368d28db73465f4814600", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.19.0.tgz", "fileCount": 180, "integrity": "sha512-YuGopBq3ke25BVSiS6fgF49Ul9gH1x70Bcr6bqRLjWCkcX8Hre1/5+z+IiWOIerRMSSEfGZVB9z9kyq7wVs9YA==", "signatures": [{"sig": "MEUCIQDWd7/t0/c0rPB62VW1mZ9Uiz20dYIPiKE4ilX4xAMzmQIgXtzoeTbucvl3YrK3zRWxR+IWYjBmi5LUVqk8pi5AgGw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2497596, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFke3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqxJw/+O9Qa3X5+sWPvDWU2eGRsuz9RcyxPkXba/VtspGitonUBylv2\r\ncDN08MIIcYYn4uom4c+RkMNWxNnNFa0+UhpvqIH+dyMtLqYTjYgOfdlVqCJd\r\nnL71sUcpVgHJwRsQ/bVplMz85QrZlw8JpHcfu3mewEXX37lhzgNreBfwVibM\r\nVwfWPx1Via/ydD4IPpEzAPgK8yEmmN0u5ixIr5VpSzPy/TfoJUbDCWssB/BT\r\nRBubGqlRaHZk96qxVpVG/LGSaw14NCQYDTX8pWzjHC7KBrM2zoHLKP99SVHq\r\n3y+AH4L2wGo9Q9ypscaG9n71O7Fju+xwWdCGhmwJ4CKBhf4xOqXxS9HaaDhj\r\nQ+InzWWdMsyYpToAlQCJcit2Xv7HOfAos7S+ph9mLca5UFK/juuNQ/ncfqti\r\npMS9qEB6NLTWvUEf9U0H70SjXyxDsysl78HrRQx21ALExbPflFrTsBEdM2El\r\nFchfBuguc+XqgX2Jeol49qPtkF6QJhCjQOCPGDR5a9mEb/0zaRhtnwW+IYAJ\r\nMYlCH44oaODryw8Rh/D/06CWijKY6wly9Qqtrqvltze2e1AzbSSvCVTtbGwh\r\navM8ICw8qYCETVBzsDMs7+ebY9XaMQcp0utSO74/Hms8W7xX88ScD4nhLYJg\r\nttidoR+jpIebgLcqkV0v7IN3cHIPejvnxyU=\r\n=18k0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.18.10", "@babel/helper-validator-identifier": "^7.18.6"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "chalk": "^4.1.0", "@babel/parser": "^7.19.0", "@babel/generator": "^7.19.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.19.0_1662404535421_0.34760341857950317", "host": "s3://npm-registry-packages"}}, "7.19.3": {"name": "@babel/types", "version": "7.19.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.19.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "fc420e6bbe54880bce6779ffaf315f5e43ec9624", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.19.3.tgz", "fileCount": 180, "integrity": "sha512-hGCaQzIY22DJlDh9CH7NOxgKkFjBk0Cw9xDO1Xmh2151ti7wiGfQ3LauXzL4HP1fmFlTX6XjpRETTpUcv7wQLw==", "signatures": [{"sig": "MEUCID0UDAyW6Tw2IHcHeuGSXXwW5bfxPho0acPGmN+yl87sAiEA+jg99jjMRX7IBszdWPvGxWuUgJXGq2ReN8V0cs/c/MI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2497371, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjM0K9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrOnw//R8ru6zTpvMaR/ePIFuqIp3FozJ1EujfPasNW/xtM/eONxTQU\r\nn8scGiCnnCGuUXGoFtqDrRdlfH32VAL7kZTFc06//+1Gl4NadBM4vL2JbThr\r\nDih5mz8X/gD3qAPeBFRqAh3IX3UXmQUMLaq7Bu1WtveW4nPFM0XYIa7FL1dS\r\nIRRTt44V0UNG8Lm2k9PbSMrukOs4RJhR8EAp0ygWiu0zKxSY/Q0EPJ8ezMEL\r\n4gClamiwXuJkZNEzlEasNeMzjA8TVLA4dXvn9AYfyKlGGLwW4qdxk0sWMgPr\r\n4f+1oqMS7tQA3aTJ5LDmh4CbALiLILd9KmMv9dB/UQ7QhHBWRUuny4Rmn89H\r\ncVFEnPMMaZ6dVdhpsitfLlczBJ27y8cdX9Aq+mbBsv7/0nL1Vh4dLgbiEWZd\r\no0LaHfZ+UYdkB6tc9B5YKGE7j3G3ZmUan44BN4v2sxajGue4qnNXUxkZYp6S\r\nuKJvwEC5umdfRZm6MCoJO37BNeFkgw9xSFqeF73JeaC4RaS3J0l/treVJkBW\r\noCihOuUPk5HLAHiu9Ho6pVH7PLKIi7W4USw7GoWrtDL19XFusolz79LbEhjK\r\nUQvnC+7cHWM26EzinQo3Np7UTlBhkkjpMIpujr4748sWImHTu8W/KYODsi3g\r\nBJZLJRfpDXt1Tl4dHEbuVShVfSax6wWqq9s=\r\n=Re3z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.18.10", "@babel/helper-validator-identifier": "^7.19.1"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "chalk": "^4.1.0", "@babel/parser": "^7.19.3", "@babel/generator": "^7.19.3"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.19.3_1664303805356_0.9545391709251794", "host": "s3://npm-registry-packages"}}, "7.19.4": {"name": "@babel/types", "version": "7.19.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.19.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "0dd5c91c573a202d600490a35b33246fed8a41c7", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.19.4.tgz", "fileCount": 180, "integrity": "sha512-M5LK7nAeS6+9j7hAq+b3fQs+pNfUtTGq+yFFfHnauFA8zQtLRfmuipmsKDKKLuyG+wC8ABW43A153YNawNTEtw==", "signatures": [{"sig": "MEUCIQD0ENCzdgQMS2qy2XuafcIzhhA1i1R7viO6m3YC65A1QgIgH8URoOX+VBZXTwogq+81PAbD1S3VOCtyztG44GNio04=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2497028, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ/g9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoIdw//ZlVrw6h3zOitw9f0tBuSt4qJ0YgI0R0zrkDVxmM1EAPEPYA7\r\njLD77U0Mn7bbB9bZzY0uLvm3Wo+o7zuDBzHTcVtSleQcL+KGkKHelhvA6ihW\r\nWayiF1qGtTYrOuVNLlYWwMb+kLtqAz0wX13qrE+BkxB7q6vLO28KyuGaKdy/\r\nuX8BY26WSP0bxA94x0i7aCszMAYVqMk95L70DGsqPEu5tn44bNJMhSKDQLo0\r\nMY/FismoKdcn7y0KAmCAlCtV2uzfju2Cp3RIIYRAJa9fETttWcDsdPrExQvT\r\nLtYjzUY1BOKBZSbBar5MICY3MJQdvBajF7kDC2L2/8m5Hvw5/ySMir1w1mEv\r\n3yu0YgcIeD7ruv4RA1uqzKGvTxnRW2iL5mX5kMFKhHtMyUunoLqsx1PYnFAb\r\nHGoCu0BKTLNWOjnN6VGXi0B2EBLxFyZuxYKL9uNz76ESPXozBqoeqtFOSaxN\r\n9laSiR29x/BSxkG1J7mj6fnI1lfQxJLPspCWtwvZNrQq78Cf+ogyzTrgwCPL\r\nor8GvSUKiGst0Lckz522bczanGAWuQ+vNInl/qt70KIDPIvxJ4/opmr0g5dj\r\nMMwBYgq7k1gkYzPi/MKk1Wf4OS6lMvO0pGo3918LBtCKHpNmDInl5mfDMJc7\r\nNtbnGJ2IYjbRdU8cP8jkBee6q7MLF9ZJMyU=\r\n=1DWo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.19.4", "@babel/helper-validator-identifier": "^7.19.1"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "chalk": "^4.1.0", "@babel/parser": "^7.19.4", "@babel/generator": "^7.19.4"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.19.4_1665398844818_0.27112547188862113", "host": "s3://npm-registry-packages"}}, "7.20.0": {"name": "@babel/types", "version": "7.20.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.20.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "52c94cf8a7e24e89d2a194c25c35b17a64871479", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.20.0.tgz", "fileCount": 167, "integrity": "sha512-Jlgt3H0TajCW164wkTOTzHkZb075tMQMULzrLUoUeKmO7eFL96GgDxf7/Axhc5CAuKE3KFyVW1p6ysKsi2oXAg==", "signatures": [{"sig": "MEYCIQDfXpxrioMn67FdmCh7BF3uPz/o3C/KJ/5UnM26EP8VagIhAIFddz+pze0pMRqtJsTo7uZd15KqGIloQ5Zq19UhpI79", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2454321, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjWoVVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpHgg//fxva3ZIsF6iOph35nvgczErU1bsoaVsQvv2q2bb3yeeWYNm4\r\na640uBBOdXpY3CO6k9hoNS2JlVVxSF5pteQrX1eodeTJny36t6JWi4dX0eYj\r\nZLthlRrzT5yQASG4UvlpP9d8YeI62oOIsfavSEdUNVMMKyT8F4lmlueFliw5\r\nccM/uRyr0uvy53nUi/JWRiYKdFJ9kgjXcNxq/jnDPct1KV7pGCwN2XzLWwLd\r\nh7Qyt584Pl6T93Pl3d1P4D1YzIOib/kQppuUHWDYUoIgG5boR3StVI47k1TV\r\nuk2Zzq5UNoNLIFBzgEAIyur3jtSWPo8gKrezJEKjMDpjYLGjQNGE/9nXii+g\r\niIvhanA+8AsCJqFBd8iKKLeAgevAhsrd+FM3u9hytUdxTO92RpaZrHfdzJGh\r\nqZt4z2RrDmQLxV8sYfzqDuPVPeGVwq0W1qMyuvMTvERD7kgqkrR+TmYG8YqM\r\nFmYhd54GX4WYAKvLaPf/QywyzDhAg/4UWCy1nd7Njlm0BYpEbKCwzYn0NJSR\r\nWBEU0BC8YaFw6MXvsSUOQDATeiq1klb8gWN39E3ujuxPpb1TtzQvkgdJUvYS\r\np33j7/TGik3f5dFdUnGZ5ephhvIgR/B5svJP/F/mtnjmOVolJB+B6wBQczBa\r\nq9SvDZDqechez2IzMAmFb3ebgFrg0iBmMYg=\r\n=Uer9\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.19.4", "@babel/helper-validator-identifier": "^7.19.1"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "chalk": "^4.1.0", "@babel/parser": "^7.20.0", "@babel/generator": "^7.20.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.20.0_1666876757573_0.750907628833289", "host": "s3://npm-registry-packages"}}, "7.20.2": {"name": "@babel/types", "version": "7.20.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.20.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "67ac09266606190f496322dbaff360fdaa5e7842", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.20.2.tgz", "fileCount": 166, "integrity": "sha512-FnnvsNWgZCr232sqtXggapvlkk/tuwR/qhGzcmxI0GXLCjmPYQPzio2FbdlWuY6y1sHFfQKk+rRbUZ9VStQMog==", "signatures": [{"sig": "MEQCICEqtLkOVqoLbspn4Z6+meXE7FnvE32+GcrW8Ig8Osq1AiAj+nFzoL4hkZRI3PD1S76CqwVQS1AQtYdhP6qME0Bz3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2451741, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjZV8XACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmogAg//QdIOG2CZUHX9w6Y06T2YN1nXQLjmrCZjJI/YTUVnuEm4Kgvp\r\nCsTIWXb9wHbW6M6/AX3lpkyzAG9Ad5/whsgkR7mcnY8yW51RfebmxiVJpi0h\r\nrUa3R4iM0baSi8uXFKTidgO2ggHy0xwZJgP9g8tkB73QbtojvnmNqk/x6ClW\r\nRUH7pneQ06eOnQbxIYEPttAIqDrdaH3fr11lQtUMelm9kCbeOwPg2O7wc1db\r\n8Bq9lqP3GhkXyzqfvJiKIAGf96S5W0BP2HjitxId7b0fWHdAhiu9TcGDcmL6\r\nS/aP7Yk4ZCZhVsC1O9UdLGwd+0qHB1htYe0NIra8D7s6XjBP5MpwjNSxBDX4\r\nS3VueH0gyMvqed9YV4C4LaVVhiOf1goswue2qEe7MM1ZOOEQ2E7TVxclwyuu\r\nK1uVJrgZOt54/rea8It83Eg6gb5pBaeUtfkQKMHs7Kd13g5StgodaCnUvJ/+\r\nkaFUGKuq7Wpez4vbeUzX9kEPwHhDVXCmwwI2xXGL3d3rdUqEAX2TiD2P7XPl\r\na3eRC7wQIzleUo16u/DApHo4LrLJmPVjNd5xD6bxapFYwgsrIuW7CfZer/UY\r\neLfixLHzktTC9wWkjbHw2avI0QD4mbMYChm1k1WQSSib0mO14dGtAwfbWMeA\r\nwt56g+x9gf30i+0e5e9uvFCO/tAf43uModo=\r\n=TgHs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.19.4", "@babel/helper-validator-identifier": "^7.19.1"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "chalk": "^4.1.0", "@babel/parser": "^7.20.2", "@babel/generator": "^7.20.2"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.20.2_1667587863639_0.3597166423279805", "host": "s3://npm-registry-packages"}}, "7.20.5": {"name": "@babel/types", "version": "7.20.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.20.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "e206ae370b5393d94dfd1d04cd687cace53efa84", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.20.5.tgz", "fileCount": 166, "integrity": "sha512-c9fst/h2/dcF7H+MJKZ2T0KjEQ8hY/BNnDk/H3XY8C4Aw/eWQXWn/lWntHF9ooUBnGmEvbfGrTgLWc+um0YDUg==", "signatures": [{"sig": "MEQCIDw8hsIBnJjNjm0wpOCirEqYXN9rZgvHzt2ddnME1+2NAiAnHKUqDtrqcUxbHGDCZwTenHGLb3YdM3BgTzbIWATa2w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2451229, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhImeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpGPQ/+JXLxcGdH/KUTzLnbyQglEG43CnWPy3w4Nxm9loI2vuH1S+rM\r\nkYpN+5BzQj9liftQWaComWx3Ka3HBYycPgqg8vB4vPHFOxUSsyIQqyDQ38Od\r\njQ459cJnHOf0WyYXYudw9QoxsZpVSApRGvX/1Mypt9YBWOgoJb5z/eqx8MhF\r\nR/CVJSYpzYLWQi53lxns14Nfn9LYWVgUHmFBLX8Y7cv2H/K2Ku21TcA2eDiy\r\nSE2wHZz6TfbTGEuArtm5E7Yd6vrufqXor1HmWYGGZLtTI9fCrj7D2Ccntc92\r\nkXOtPQHn6FPqBys5tT1T7tJHKkvjW1FFAXZiYCTk6xZ3d58TU+/fsAsyDkWT\r\n2MpPC28D27sFcts35alILvWEZ6ZjA0ubdhzmjovO9w401m7xRXcp4TrhlMLz\r\nmuREyfZSz9ofry92aNPltUZHADsUeYvsdWFH6O2pSXZHIe3ER0uEbrkrT5+c\r\nKifH/XjdSCkD4NASRWiF4RMMx0mnQKoAh8iB4Z1RVLqZxj/zOIBMLwTa9a4F\r\nOFncFh9c40uHj696H9ZWT5MbMTxx3vJYyBb3E/CcdQrBoAb+3sGw94WB+7FS\r\nKqDsjeQHOpqBsSg3wDyr4BnqHVqlticzkgswxfXycBYHj1J71D+C6qqldUGj\r\nQ0Sp/R/w+QwqYIaIWiTzPyviEZfyYVJL8hM=\r\n=fn4v\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.19.4", "@babel/helper-validator-identifier": "^7.19.1"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "chalk": "^4.1.0", "@babel/parser": "^7.20.5", "@babel/generator": "^7.20.5"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.20.5_1669630366151_0.8825765276594535", "host": "s3://npm-registry-packages"}}, "7.20.7": {"name": "@babel/types", "version": "7.20.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.20.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "54ec75e252318423fc07fb644dc6a58a64c09b7f", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.20.7.tgz", "fileCount": 166, "integrity": "sha512-69OnhBxSSgK0OzTJai4kyPDiKTIe3j+ctaHdIGVbRahTLAT7L3R9oeXHC2aVSuGYt3cVnoAMDmOCgJ2yaiLMvg==", "signatures": [{"sig": "MEYCIQCfVl5smHOF7ihRjl2CxF7sPRBL8jMvwIZzb41BKDmUBAIhAO/o/Au4G3ircVtfXVeIrbJI20zhx0wrRga70zaji6O7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2451334, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpCc1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpeSA/8Cf7tV5nMuytMN5oUFmJp5uDtkgc0qp5k8tTqbYNzYPQFGdKM\r\nodTWGXMNJkk0MRvHLYTCwvdn16al6DWbDVTwI8bkc+YjkkGz5Rlt8Yn2peet\r\njenDD9sB4Yez8xLszE3Lo3PALfcF5zF0cQIUPvBA8ba9P/vI09EMAnF4SpH0\r\nFr8OSyJo/eYxRggzOC3x3/OktuFl5orSARKM5CKgz6YyeStHW/5cjDMTQjPt\r\nFkAed43DKL21jYLG9zwY/yTSZMLEXMiVCbmqVs7Jm7yL3Nxn62m0JVRSQ/Wr\r\nJPSDhrZMt3DldncJj5UzN/e5N+FAhhnumjMx4iMHNn/jwYja3xTknYc+ruuo\r\n5Y4GMRTk0Q6ILssB0sQNX4S9iIFXT+je3UKYTnc3Nd6WRVzvfxRIBxUxV/lx\r\nyM/FGhgSQS03LRgF/FaTp5S9iHfbitnPZptUbreq8ROdYWnhpKfrfKpfTSCX\r\nnEKuxicY1FGeqAbThrFX9SuV7AtmQLeaRmOL52UUPPmIgT0u3yHpw3NGe0QS\r\n9PYsAUQzQ/40WflKjmPPxvFrQXfFaxkrXhwMlnEnTjoFSrCWF/KW6Q6ZB6qu\r\nH79t3U8s/bS/OwJpobhs9gZpJUGlgZTUfBd4ogrdmyCNVwDKHvQE4bMmkQkT\r\nia66jp27cb3WiZEedW7ctXlttayc1tRvj8w=\r\n=BVWz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.19.4", "@babel/helper-validator-identifier": "^7.19.1"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "chalk": "^4.1.0", "@babel/parser": "^7.20.7", "@babel/generator": "^7.20.7"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.20.7_1671702324983_0.2034297875313198", "host": "s3://npm-registry-packages"}}, "7.21.0": {"name": "@babel/types", "version": "7.21.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.21.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "1da00d89c2f18b226c9207d96edbeb79316a1819", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.21.0.tgz", "fileCount": 168, "integrity": "sha512-uR7NWq2VNFnDi7EYqiRz2Jv/VQIu38tu64Zy8TX2nQFQ6etJ9V/Rr2msW8BS132mum2rL645qpDrLtAJtVpuow==", "signatures": [{"sig": "MEUCIFk8HsyqLy5rOkGVKJVEbuHxKUaz9zosxMUHBh9g4oujAiEAzGrfrZYhlB8WHADV7e5dlxdSlxsLrm8lIwG5RwktAKY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2461637, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj85I4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq+XA//U+6I/vwNgl/aPN2Qjcwt2qHNHYSYaCNLC0NMgRmP9SZJpV9O\r\nTgAtllkLhw0Ib4AM4hmyOt+/cBEDOOuVZUbB/p2pgZ5x4slJAob4sfzZ4Mbx\r\nWJG1n0B4dOX4mmChkv7nZbEI45KnzbeqhRKvjplvinDZ8G4mCEFZcZBodFo1\r\nKAq5NkzESBc08Urqd82vWciriJqzdeFGX5MMi1B98JDrrEBpBTMjFWInTaKn\r\nT8UnGWlHAPkWARFXnjUrS1FaQChToQd8P+voTde11LkzqMFYdFlxN7t2NWNi\r\nRA7LmrA1LApMsun2l4zlvP2sR8usnCLT4oDHAKQObZjZmb8pp3NyprlgmCxb\r\nWQjBtOCFFOsRNr21ij6kbh5JpvmDiolQQS9ojC4IQDjzjhmdmU3PTZsBbha/\r\nj+L4AR+zCZrl16Ma+AxUyNRp+kXL5s9oKiCidEddN359OwzWwtzd3m4jNI9c\r\n95RWb92MOH28m8164sP4HG9L5nVwY2bBGf0nI/HhlEDwOPptyLr1Wasvulff\r\nBQ3twOTvoYP3faKSu2pupIAQ1Q7UcO8sgfPuxuP5HlUi0XO+fXBqLvdP7N7+\r\nicZIJizs+ETocEvYZDmgz30LIe4M03OuH6wV6K9OG3bGWaf+O9wnYrd130BW\r\nFBCVZHByoGxqWIw3nr4URTqRVF/L4aq2h70=\r\n=hBpq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.19.4", "@babel/helper-validator-identifier": "^7.19.1"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "chalk": "^4.1.0", "@babel/parser": "^7.21.0", "@babel/generator": "^7.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.21.0_1676907064672_0.7531106013963085", "host": "s3://npm-registry-packages"}}, "7.21.2": {"name": "@babel/types", "version": "7.21.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.21.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "92246f6e00f91755893c2876ad653db70c8310d1", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.21.2.tgz", "fileCount": 170, "integrity": "sha512-3wRZSs7jiFaB8AjxiiD+VqN5DTG2iRvJGQ+qYFrs/654lg6kGTQWIOFjlBo5RaXuAZjBmP3+OQH4dmhqiiyYxw==", "signatures": [{"sig": "MEYCIQD4fPbpN5AxnWdAVse1FfM4I18RhRsIk2NQp7OFTi1PfgIhAMbchUqHx+M5wL5P0cgM+0ZO5j6SmiCE37k+nxD3VJIO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2465276, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9zJ3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmotcA/8CbRMQ0eEmTxpsHCmRVe90jYJMqASP6AzfEnJQpGb5ltQ5g+n\r\nA0cvUom3zl2r09wswCl6JoWqIrEjsXAB99YYf/P3KOzBuX0w998Fk3eQiw6z\r\nsD6iN6Ydu78MjEm9kz86hjBunqmuePZ+0unI3afCxCxgoVMcmKsMsEgqszEf\r\ngJvic7eXrlUg9n8CuFDYDEHfCQuiz5z9ysF40IVgMlvvHvWTvzT9rBXr1F03\r\nY6rrx+D/xXgj2O0dPfNPhyvtdOBkgUsLgFtn4XNb0PwrDol0L3bSNIZiND1n\r\n0Mj4zJ99sxP3nfyjyXROYnP8MIGnPY/0PSOoXP5jdPG53762/of6fhY5mNWt\r\nMm5Pu+WRFHXnz+J+y4JvhzVzYtjiKEHsLDMz/ncumNtEyjVK5RKjUQ4G9Owr\r\nmE6r1lfWOxxYFESGySgFi7abTxedRDTvUOEvW2Q7Lfd5nggxJDMDJVfFEVnx\r\nWNaB3CgCAkYmHy05BBWqLhdhI0UD6jUYIirRPS79nh9J1KhaqdO81neCDvPC\r\nBbejTpOSzHsKA4r86t1TKiBC9rDZXWhyIPpX08B+pAHRXG8Phd2G/eGiaLa0\r\n9Xw8SM463/KWTMSKqN9fuYW8XagUJS3/2Rk2pLJFQWGofpIJrygscSEs6uDB\r\neeF/9OZRYqAs5ODzLeURvJp3X7yQY4LHpQk=\r\n=tXEz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.19.4", "@babel/helper-validator-identifier": "^7.19.1"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "chalk": "^4.1.0", "@babel/parser": "^7.21.2", "@babel/generator": "^7.21.1"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.21.2_1677144694878_0.5349603778603969", "host": "s3://npm-registry-packages"}}, "7.21.3": {"name": "@babel/types", "version": "7.21.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.21.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "4865a5357ce40f64e3400b0f3b737dc6d4f64d05", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.21.3.tgz", "fileCount": 170, "integrity": "sha512-sBGdETxC+/M4o/zKC0sl6sjWv62WFR/uzxrJ6uYyMLZOUlPnwzw0tKgVHOXxaAd5l2g8pEDM5RZ495GPQI77kg==", "signatures": [{"sig": "MEUCIQC8DHyP9sGeNKJIzirDeGkmk0VSfU0D0NRYIYj0wXlX8gIgIQRCXiZIl4Ibqu4swNck6QfFPPo2SCEAY4LdMLVIxPU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2481414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkEIvYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqSSg//QLrM0H8f7sH0FpC/tehuMwxtdnte8svzqfKCRyomNpZ/QJjJ\r\nADcy0cqSa99s1+/kVEREv7zbyBedMaxdQgpmWAZpSsu6fk0GiMVlr8BEb86V\r\n+5ewpWrVKe8peUPnKLPx0ccgG4fhkp/7qUClfTTZOnuu/qVJ38jvDPNI712L\r\n8kncKDsGkYySTG0BedzLQ1brhi1btgLtjpC//m+NXyN0hmMNAFGOfS302SYH\r\nEHx4GbiLYaQRWSbYTif9VHDO9TZbBivmgctNk6nM3lL76v9CcSRZD/86fGtH\r\noEvsJjNdNMZiaSAqB4D8jFDjUuKVQ4SAcN+gMaplEKwYNM9cZrTc3Y6HBmP8\r\nDzlLbq61rUPz3+0CCcdtV87+PsUVTzVtE2OphhbiqsCXw9CIrwdNhdejwLjr\r\nCKH1jYY3gz2DNggU6/qdjnWDcvH7A2dsLRvnDMHVu4OmircuOJlymBEIz6iY\r\nbn6NZbPd2lwTaY6SYmw8Jg5lwJwpSk8RtKl7+S3TR1+6z7ZS1OTu4xD7vfT9\r\nlVoMW4JhB8jPgxTNdO1BHZpPgRlcGnyTRaBSeR3RWjMSVXafuodrM9ran0qe\r\nSWs5BippcTVHEZyONoJ+2KCGNDIv9GxASWL+X2xLrRQlOp6QZ4hMWtaZFVhR\r\nzQ2qNd+arS1jOR3W4RG9Dwe/mWYGmHx1uQM=\r\n=seS2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.19.4", "@babel/helper-validator-identifier": "^7.19.1"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "chalk": "^4.1.0", "@babel/parser": "^7.21.3", "@babel/generator": "^7.21.3"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.21.3_1678805976194_0.20029449174404523", "host": "s3://npm-registry-packages"}}, "7.21.4": {"name": "@babel/types", "version": "7.21.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.21.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "2d5d6bb7908699b3b416409ffd3b5daa25b030d4", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.21.4.tgz", "fileCount": 170, "integrity": "sha512-rU2oY501qDxE8Pyo7i/Orqma4ziCOrby0/9mvbDUGEfvZjb279Nk9k19e2fiCxHbRRpY2ZyrgW1eq22mvmOIzA==", "signatures": [{"sig": "MEYCIQD9G7n/ZUpwUwgX3aUSVBvJqutc2uU2RuHYJD3lspUWVgIhAMmkQLG2T9QzRqtY64I1393TLWiC49XkHgVD03c14ze5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2481414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkJqGCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqYpA/+PlgVHQ82A3BLosENkKQeZ98GgTiZi1plGsSODo/3zH3jLNRS\r\nKVbARrA/dtrNuD60DLlykl9CA2JK7HIYGndA9nN1jECTU094HushX918GLvK\r\ndRyP/ZutZbZ/uex0Z/mk/xzJsm1NanPT1kSs+6VCxIRlNycdKAO7Z4YnKcZo\r\npmcPd/eIcGef8JcSxcx+gB0OhDVSHPyZ43xhLvJyKCiRtw+6ATYyuJAabKtp\r\n6vID5oaIfKU1wG4xYIR6SpJLEFibZCOBnhG3584wCpSmQTt+dwSK2KyCIRrU\r\nB9J+/gYY5VKF4T6UXQ77mS3pGmDW3v0NY3YxRhZd4uREc5PV7uw9W4sQt+ax\r\nTRPazGcOgtiu+b3h9KYp9qZ6rxDN31XRvGL4/vqrgz+PQff5J2/mcJevxE9H\r\nb0JOww4GS6hei64+T83zAYrIw+999UDbslxMoqFwTnUyvYKF9NhZ35FPuWLP\r\nbF4v4YpJ649nGx1yaEGXVCbZVsewebx4LS0KW117ZZaWbRbFSBKcuGLu9+Vb\r\nUVKomluKMLQKzEwyuKRMwPrr5Gp7nlvuvlxhMbxWhirXGC0rRhYe3hITH9Vq\r\nAVfeaBk4GZE6UnssZwUTm5cdOyyAUo/nQX+x5n/VkAgEcGXbvUmZRkj55teN\r\npam/LNk1v+Rva79bHdKjx7/X6x6q+XvCgOA=\r\n=WEd4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.19.4", "@babel/helper-validator-identifier": "^7.19.1"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "chalk": "^4.1.0", "@babel/parser": "^7.21.4", "@babel/generator": "^7.21.4"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.21.4_1680253314077_0.722946803030891", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/types", "version": "7.21.4-esm", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "e3ffa6148d95819503a99c4bf76bea84bd4c9909", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.21.4-esm.tgz", "fileCount": 171, "integrity": "sha512-Y/x/Kdf7FwJBN+uMvolTPEJTcjReGq2tlGPn9gf0DDqfxHayam1VGXBoFN7ZhuXAM6khRXbFr7QyM43YskypBA==", "signatures": [{"sig": "MEUCIQCnLlSvgCmfuWS/X55u94vPGH9HNKw905lrZKUDts9X3wIgUkuh+ENMSqaL2G4trr+yjnW6Xi8ciGopt11+2/I5vjU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2481454, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+cACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqA2g//SH2kUvo7YaIZVAfOePPV6prYfmee/iNUXsckEIaymt0Siq4+\r\nDR7Fj8KM6t051/55/m2rZdz9XmNJ/VsIk6rwbdBuZkSQZFVtXTBuj8oKc3US\r\napBT/6Lc+sekhxnwu7wi0sAkr+noUP5MvdzQKumTqfkwFAPa6cgnPOpdIft5\r\n63U/7C/gdNxBPLhOrXHhhYPNn9OotyWD3L/LgpkVDPW9fG6jsBcwp025x7EO\r\n/WY2MNvCQOu3sqGdQkUW+TbYxIsVPGyMpcCwHfa7w1rAT+c4cLEZ6aAJd0kw\r\ngNxzZmiV2D/Bbx7imGr1MkRkbYaPAaxQeJy+c5lxKX2ewMz/0u9ylBKJkCdJ\r\no9HyZGTWZDFiYHu4jFAZZTKhX/7ShXPuexAmCcOHW05NQ0X5gyEpBwJ5ExDV\r\n3y7iEQtlUZ0A6oSfVDr4r6C/2IfhKWyD2ZP3YnVp4dRlj+Qf63HKelKvlGtk\r\nHnLDird4A6lych3lL62IOzB2Dr8mnseNlHjIhyAq/1ct2vOkJxW/02oALR3H\r\ncSj7zeQ/y+Ul3kjrZYKQrBUjzAx0u4uU+Yl9WESrvdpWqbcI/TFgqYxytShF\r\nWX09oy/pGBEqgSMoc3gfooVPelvWHWiQu46YC3xXRrnliEOoxBAXFi436QIW\r\nyp33Vn/c+pTJtrygSk6mzAjHmf7D30ek6Wo=\r\n=vkf5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.21.4-esm", "@babel/helper-validator-identifier": "^7.21.4-esm"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "chalk": "^4.1.0", "@babel/parser": "^7.21.4-esm", "@babel/generator": "^7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.21.4-esm_1680617372124_0.7492806026222625", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/types", "version": "7.21.4-esm.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "89902fd5c21b3b6dcbd63a53463819819c13c736", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.21.4-esm.1.tgz", "fileCount": 171, "integrity": "sha512-nk+K9zAOKQI0UojgXlq05ov62zKVtzOxFT3Gfd6u9RdVMwnA+923pl9hjo8MBwiMgELdNMFude40pDAPGlbskg==", "signatures": [{"sig": "MEUCICHVNoRIe19FraQ98CkXoJgdyn2K4CGMN/x4Mqgw2Y7yAiEA3V5UsbxS6Zb8GZzacDFxEVE3aytm7YNrPWRV1JR7MDU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2383759, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqOQA//bBObILC2VrDq2KP+ufyAf+JHfJNCC7bfV1d/lRpVOhxGdOnB\r\ntiauOIIN//KFZ5ZsQ9KOWnM1F+dLf9An6LxWRn8C7CLbVVSoIvT0U79cQuYZ\r\nKwDEw5kF9cg6ilZcI+1HlLsSL0UWr5cE2JaHg77a1tFEXGmHgIid3Nlu7fRK\r\ne3UT9F50YRcOh0HxEviT0EHdsUmNp/LVCfaYkwf/CrYrOuoiy7uFBAddXsYo\r\nC6u+ufP0xTdc0Jajpmaug3/2wHMVliL2Y0h3+JM5AVaarlsZ4en25p7ujdSm\r\nz5U5pV8FHjWDgKsHhEPiYYXgRvTxmNuV6bUyyiURcCUnoRCrkJAvugme3Lu/\r\ndvz72+z0aGAIM03WZzJuL8zmadmTsG7Uq7BqFpcRtCTvVbWYXmDsg/zBCXMz\r\nn1LZQnPuyYyL+NvXftoJUDHj33pbMYliu6ikz0QcmDCU9jOzcids4s6yahXo\r\ngBUOQpV3XaPpAbiBw7zIc7n0HEepY8+swkDikChi+wttC1GsHuWReUwMajJL\r\nkR4Xu8i5P3X7GjrDbY72gdPhTYDfeZfzFSEWsi+/CTsWxQPQLflZfg1SHyPT\r\nBjLYqYDD8lKlgkmBjoX/KBUmYd1OeK9A5jdOIGdeiBi/DGxw47CcZGqySZ6o\r\nVUrHTfg7imUK2kPaJJBFLjiek87L23aJlEY=\r\n=BWT8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.21.4-esm.1", "@babel/helper-validator-identifier": "^7.21.4-esm.1"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "chalk": "^4.1.0", "@babel/parser": "^7.21.4-esm.1", "@babel/generator": "^7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.21.4-esm.1_1680618084403_0.4529171946494177", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/types", "version": "7.21.4-esm.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "300db1a3d7185cf8bc40d8b74bf733c641ba6d90", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.21.4-esm.2.tgz", "fileCount": 170, "integrity": "sha512-62MpCfpEN/Hy3Gq2a5+P2Z4c19WfbtZJuwM1k9PMAO6bo3dWzfRx1Z2b3GjUkJpGhBqgjyqEaiiE4ephlKMFQA==", "signatures": [{"sig": "MEUCIQD+aRDfCn9+HvsA4sl0UKl/4PXt8qLyZ6vXrAUvZCD5zgIgTsFPwn01cTct3p75yLUY+UwyLQASiwBrxqAOP0kXXu4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2383735, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDadACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrtuBAAorH/EI/C1llM+Ef1JWUtojUKIer09bwoglBWLPepQWom9Cxv\r\nqzq+t32MIT7+ePgBqqq58vdBR1uVl8hZYUWlF8bjNe3JpYMmXfTq26THHRzO\r\nMCOv+ncTm3QkqBbVgPjNxrNfj04IJ7ZJkzdCQg6Ly33/7kHxAWKc9an3Dzlr\r\nKAW/GqRvR9WmWwAbNzaMwsttlFqNXtP4B5msXHYVP03jQsXj5OddzdSnFZd1\r\nnCDCBCz+YdFrsWfGYmQ3MRVbT/E+R5Iiq872TFuHyoV4kubv7PrNzQrVD/+v\r\ny6u8PT2UOlkUQtVmQhPXINC0fXtojw8YZ298dEnvU927sbhoqCXRceNMx3aq\r\n1uIweDYPmmKso6JCE9r/2WZzqDrkz5M6uH7I8SEoA/F1Xm8aH4df2Ck3O/D3\r\no8sWd/fPXs0Cl5ViE0aWAf+UyTWPtGiVgxqgdNsndDVO7XQO4gPIw5CanK4i\r\nBgI/lzNECYF2mOVU8zTq5su1AL+4GTVaZxSKvQFZmp3OWCMcPIH5bt1hdVJe\r\nH21AzVDG0vRGK2RveHhzlZ1NEyib9+y1Z9QcEjItv5dRW2A535JonrFDrjXS\r\nnJKOdoKCb5qru+nAAsJxyB/MnSc8aDjLzDrcdQ3d/406DQFk/sMR6wwRtugm\r\nLn13ocN8hr44Sp7EiJKwnQzKkpY3eOovLDw=\r\n=NyR+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "7.21.4-esm.2", "@babel/helper-validator-identifier": "7.21.4-esm.2"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "chalk": "^4.1.0", "@babel/parser": "7.21.4-esm.2", "@babel/generator": "7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.21.4-esm.2_1680619165523_0.07085172178510168", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/types", "version": "7.21.4-esm.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "281cd7b3d9bba990294117be3fba9be0a080f746", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.21.4-esm.3.tgz", "fileCount": 170, "integrity": "sha512-geFzuZyrhD+/G+WPI4QAAkrs+BhSYAXCdWol0IitDVrnmwVnmswIbBu6f0TRZT390O1SWC5wFHjvrQktx2XhCg==", "signatures": [{"sig": "MEUCIQDzmgtZyoPmQZTtp1ZqROMb+RbHI3v+Md9kPwwAr23AiQIgW/j5cErJnnU29POb3i4S9Lin096zrnR1jelynmXwLRw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2481438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoLrA/+ORPrMFqrkerh/SPUUpW+guHxDGeVJGJYm+Wy5ty9k7A7HOdJ\r\nnLPcUr84gciAcDEqHAQkYe3Se636RvdfS5Y+artOWo1bXzU2TbVQ+9PUqg2j\r\nuHVDimz3pJGEjQz6+LngKzecarfnf1zPv8w0Wz4xn3dVTSQUKRd1Q92l7r6i\r\ncrsOa0upkm6kQJE+qdIIZss1MWb6QLWzBVt8KQB1HaUb7VlhHTP6b2U7xFSQ\r\n2XbGaMVM+dFDFs0MdKlq1mKaXt9Cgxbc4zui17n58Bk3Ngrfecw6bxoSBgnO\r\nR2bNPjkXpz76YibXecL2eWW7v+0kTemyAQn0aIxj3QCF2fTT8ya2DyXo76kj\r\nfKeSIG1qHVlBtXD0b2sU64ofSfskntMj4C5DswvFWZg6IE17qTAQ4723a5s+\r\n7dxXS7uWbTl4QZABqAc1AApT5CnbfZyWskStTsVmaFhEcr0NOGOWIF5GsKm8\r\nNvIlRWTPingcJo79SBE87a3UT4AneEItct9fHgFiOxjNpDDKRZiAdFDHw9a5\r\neBZiJk+bdmETcGAk9OpvTuYAylgrCxvc2tulNd5pps+wC24HNpdFXwjoCJKP\r\nHNlCmbKPNGN280xff3s+9t3gREAcY4kX8iAXJOhAv3WvsmGd0Xe/1Tnx55E+\r\nNASXIZErZknk9Yd0hrr0bAV0ACS9y7rwO4Y=\r\n=Y3sx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "7.21.4-esm.3", "@babel/helper-validator-identifier": "7.21.4-esm.3"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "chalk": "^4.1.0", "@babel/parser": "7.21.4-esm.3", "@babel/generator": "7.21.4-esm.3"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.21.4-esm.3_1680620174333_0.9787716230355219", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/types", "version": "7.21.4-esm.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "05082c96abf388144c4f09f032e4edcce1c7405f", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.21.4-esm.4.tgz", "fileCount": 171, "integrity": "sha512-nzTg4SP0cxsLC8gPXZ6bNRI3wTSoDczp02qt8vgbMiJuJndrwqB4sklAtMmDyJMTs/6JG6Y0Xsp8upUk5x58Tg==", "signatures": [{"sig": "MEUCIHUN0E19nog2O8TetTLjtZSc3FiRjaQ9A20cyhMRlDz0AiEAjqFftDNJysOOpI5zgqeZYUqNSgA0XF9l3N9x0ZS5MUw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2383755, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6WACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr7Tw/+M4T0Z9elxdmPp2NsvuHYcncw4MhBR9AWigIqOEDf5poMQmkM\r\nHznifMDZuC9d70vO8Kgz2Dg3Qe5h0yXZCI05S+TPnUn7nk2xEZiWV1Ywjj1T\r\nQXeJJMblXpW5yCqEr0CAMa1SYlY6bCuJO2+ggMgTJyACqjC1LGujtraNMuZz\r\nUOreu4EKoCYD/fV52rjXxDjIWRRBJwaGkp3v+fdNc8F+7yFEiciAfNTzzwhe\r\nkHJY1dpAnLX5kjruHNOMEDdl4v6m/S2NHMIHYZpjPUAND+5n7qZrzMSn6PmM\r\n0vkLWERfvJCSqy0qmgh3GB7DPvRfKVaA8yDG8TVSMnlBlr1h5Xx0+v2MeCfv\r\nhSjc3+j+Bm/6pTWnhdCKO9Io30buj9wQmxKCVuLeU8nTUsY89U7mf9Zwesja\r\nX0lGRR1lzzLFPTLfR7yGpE6oxiiKDNbEDHn2vw2fhbb//HfcE6gG1DBoxQNv\r\ndJpVdQlhvFXGWDgf0YqFOeaAcSfy6dp2ePH90mhqNzP4tMTrWpsr5z+7b9xf\r\nGY47tw5E01+Hxp+zoqtFifJpV8prvzgxhqoU4PisHALw+n/Sb91hXKHk0cq1\r\nDWqoRf6qVqpkAe3cQqGEZl2CGQpMNl8cAmDHA3E1TvEtcNAOarIY5yVPDZZN\r\n9Iw0xE9R7fBhjDQlH+8PxWQsKb9XAXvnV+U=\r\n=qHuH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "7.21.4-esm.4", "@babel/helper-validator-identifier": "7.21.4-esm.4"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "chalk": "^4.1.0", "@babel/parser": "7.21.4-esm.4", "@babel/generator": "7.21.4-esm.4"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.21.4-esm.4_1680621206121_0.8255646770326568", "host": "s3://npm-registry-packages"}}, "7.21.5": {"name": "@babel/types", "version": "7.21.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.21.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "18dfbd47c39d3904d5db3d3dc2cc80bedb60e5b6", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.21.5.tgz", "fileCount": 170, "integrity": "sha512-m4AfNvVF2mVC/F7fDEdH2El3HzUg9It/XsCxZiOTTA3m3qYfcSVSbTfM6Q9xG+hYDniZssYhlXKKUMD5m8tF4Q==", "signatures": [{"sig": "MEUCIQCtCMYqR9sIKdPD9UbwUQmKg094xvwwU7blhnnVEWlRCAIgHCmuOi/7Roat+VOk458jKJbxAlwtjZzolheiTBaj04A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2385334, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkTCN+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqRyw//cSWhsu8kxIPTR385ioaw6c0reHlSJoPLgHoiTfXlFGNPNSRg\r\nrRGrq4PQ74p7VMP2AHxHVfwFIGmqoUUT1zVNlBwKqPFRH5+nn7+4Rc8nooYW\r\nCLYoRon4fvkIIQJyA88zj2s7yEBGzTNMPo8va7JYfqG/zqn59vnAqaEGTq9E\r\n8G1JinTC2GE1W7DSks2sO2PMp+UTGVwK3ri1Gv/OuystVo2eNd7H00FiG/Sf\r\ndQJ8ovKQtcVhaRMo2/QV49/RKGt//c9lhS+M3u48hc1JDHMO/NZR3GbtJyfy\r\nJGDTVixiqlkCH5xmiBiA6PwRVlbPYawZScRcbRqOSV8IwZHgcXhJyjNzaXZ2\r\nOvJtify5ozMk23w0ss3Q5cqdV11xVaIUc/alm8b11hvajgRDCUfcHYKFD4Ix\r\nJ7TDG6xTJgcxvSYz+v54FjfN3puMHguWOmAiipP3kVYL5ybzVq8J956sYZj/\r\n+QRB/07BEtodWsB0Xkzj3QwDKDfonBB7kItYnjHnsgGyPa0OcmPNWuS8NIb4\r\nSGnNJZEsgFxA0e67RyY4b1Fl8VWeOGFlhV0LKBQ1Q6n2hyUcd4dHlpStS9U/\r\nqbVU75iFPf6AhfonfDdvrSFrizzTgkvfFPE6K9LY8Jgz0xWeyXh7/TLJSSKQ\r\n6coU5kMyTEeX32I2tdr6mL5MPIxfAOMutZU=\r\n=PZPg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.21.5", "@babel/helper-validator-identifier": "^7.19.1"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "chalk": "^4.1.0", "@babel/parser": "^7.21.5", "@babel/generator": "^7.21.5"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.21.5_1682711421940_0.6451226938703989", "host": "s3://npm-registry-packages"}}, "7.22.0": {"name": "@babel/types", "version": "7.22.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.22.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "b7383f76a5fedf967c57c1f940066fb31ca3e97a", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.22.0.tgz", "fileCount": 171, "integrity": "sha512-NtXlm3f6cNWIv003cETdlz9sss0VMNtplyatFohxWPz90AbwuhCbHbQopkGis6bG1vOunDLN0FF/4Uv5i8LFZQ==", "signatures": [{"sig": "MEUCIQDsYBPm1MhzNnjpST5j2oARz4neHbOYtDRzJKAwqxKJeAIgQRn/WNGDu9tSp3K+8lU99oFf944FQMtz9invG/+Zw1U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2392536}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.21.5", "@babel/helper-validator-identifier": "^7.19.1"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "chalk": "^4.1.0", "@babel/parser": "^7.22.0", "@babel/generator": "^7.22.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.22.0_1685108727235_0.1712258985651356", "host": "s3://npm-registry-packages"}}, "7.22.3": {"name": "@babel/types", "version": "7.22.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.22.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "0cc6af178b91490acaeb4a2f70dcbf27cdf3d8f3", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.22.3.tgz", "fileCount": 170, "integrity": "sha512-P3na3xIQHTKY4L0YOG7pM8M8uoUIB910WQaSiiMCZUC2Cy8XFEQONGABFnHWBa2gpGKODTAJcNhi5Zk0sLRrzg==", "signatures": [{"sig": "MEUCIEK/UU24dwop0Doprnq1UDw5zvdqyETE0B/qPMus2rIwAiEA0MazoHynMyGKBCW1BhN2H6+d/aI347Nwtsz02D6AwSw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2392831}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.21.5", "@babel/helper-validator-identifier": "^7.19.1"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "chalk": "^4.1.0", "@babel/parser": "^7.22.3", "@babel/generator": "^7.22.3"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.22.3_1685182264000_0.4273003463495495", "host": "s3://npm-registry-packages"}}, "7.22.4": {"name": "@babel/types", "version": "7.22.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.22.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "56a2653ae7e7591365dabf20b76295410684c071", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.22.4.tgz", "fileCount": 170, "integrity": "sha512-Tx9x3UBHTTsMSW85WB2kphxYQVvrZ/t1FxD88IpSgIjiUJlCm9z+xWIDwyo1vffTwSqteqyznB8ZE9vYYk16zA==", "signatures": [{"sig": "MEQCIAXIaQTxS5IJEGNWsYIkuRkvgEL/91tL298RjQhxktQDAiBxaHEbbwPp1NzFe/2ZpPL2dI0MPrw5pnaV6kVhoRDKSA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2393439}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.21.5", "@babel/helper-validator-identifier": "^7.19.1"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "chalk": "^4.1.0", "@babel/parser": "^7.22.4", "@babel/generator": "^7.22.3"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.22.4_1685370414696_0.30695505686415125", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/types", "version": "7.22.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "cd93eeaab025880a3a47ec881f4b096a5b786fbe", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.22.5.tgz", "fileCount": 170, "integrity": "sha512-zo3MIHGOkPOfoRXitsgHLjEXmlDaD/5KU1Uzuc9GNiZPhSqVxVRtxuPaSBZDsYZ9qV88AjtMtWW7ww98loJ9KA==", "signatures": [{"sig": "MEUCIQDTFk9GNq5bDtu9hqcIeyE7dFL149lEPlZolXGxZQQFRQIgJN30usaL3oZp2JgjBBxRMuNCs3yJN5dda109BnsdUwU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2393443}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.5"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "chalk": "^4.1.0", "@babel/parser": "^7.22.5", "@babel/generator": "^7.22.5"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.22.5_1686248483935_0.8711794987135184", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/types", "version": "8.0.0-alpha.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "475e6faa6ddf2f3fdc51871572818e52caa76985", "tarball": "https://registry.npmjs.org/@babel/types/-/types-8.0.0-alpha.0.tgz", "fileCount": 170, "integrity": "sha512-m7e+Xn7+5LPRjio1V3KlBMbJJTO6LfhtqSdW9gjZdGRWDtu8M6gBAcQVMbD/GNKdOdmSmABxUfzLOfMRFzL8bw==", "signatures": [{"sig": "MEYCIQD9NCxR1pmotmuUnwXmRF9uAlSSkMmgdkLDhB7AReGnhAIhAJOJG93yMml02uoWFkK1sZaSs3maSFHl/Ekh5dRtYIwM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3378806}, "main": "./lib/index.js", "type": "module", "types": "./lib/index-legacy.d.ts", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": {"types": "./lib/index-legacy.d.ts", "default": "./lib/index.js", "types@>=3.7": "./lib/index.d.ts"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^3.0.0", "@babel/helper-string-parser": "^8.0.0-alpha.0", "@babel/helper-validator-identifier": "^8.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "chalk": "^4.1.0", "@babel/parser": "^8.0.0-alpha.0", "@babel/generator": "^8.0.0-alpha.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_8.0.0-alpha.0_1689861599780_0.8754195990577356", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/types", "version": "8.0.0-alpha.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "20cb9ece16cac7baf6dba8987d2930b0b0002448", "tarball": "https://registry.npmjs.org/@babel/types/-/types-8.0.0-alpha.1.tgz", "fileCount": 170, "integrity": "sha512-WTaZM60MnL4ynPu5q1NSoaeQdAwv/6Kw97edyAeSE527MsGh0Ef1gTK3cpFHL2ylfi20UE6NLac24nMGBvIcGQ==", "signatures": [{"sig": "MEQCIEQRYedfCnQYg1glpMWNjGZVCz4g6mP2ZgTQOGPKaXH+AiAQX+IQCtCnwkeC+7QV6V8ytv8p/mpG1pUv+4XPep4PGg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3382745}, "main": "./lib/index.js", "type": "module", "types": "./lib/index-legacy.d.ts", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": {"types": "./lib/index-legacy.d.ts", "default": "./lib/index.js", "types@>=3.7": "./lib/index.d.ts"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^3.0.0", "@babel/helper-string-parser": "^8.0.0-alpha.1", "@babel/helper-validator-identifier": "^8.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "chalk": "^4.1.0", "@babel/parser": "^8.0.0-alpha.1", "@babel/generator": "^8.0.0-alpha.1"}, "_npmOperationalInternal": {"tmp": "tmp/types_8.0.0-alpha.1_1690221126022_0.5901193696131508", "host": "s3://npm-registry-packages"}}, "7.22.10": {"name": "@babel/types", "version": "7.22.10", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.22.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "4a9e76446048f2c66982d1a989dd12b8a2d2dc03", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.22.10.tgz", "fileCount": 170, "integrity": "sha512-obaoigiLrlDZ7TUQln/8m4mSqIW2QFeOrCQc9r+xsaHGNoplVNYlRVpsfE8Vj35GEm2ZH4ZhrNYogs/3fj85kg==", "signatures": [{"sig": "MEUCIQDpyE/5P9SH1h2IaWNjVXiUb5GDIVlr00xx/cYKZUtjxAIgCI0wNayANd60tGmm5c6yC65mq2XBS2mfNv9FyLCsjmE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2395318}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.5"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^7.22.10", "@babel/generator": "^7.22.10"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.22.10_1691429117827_0.29931523793627046", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/types", "version": "8.0.0-alpha.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "c14e90f7cd99fb4f7103d846e71a0cb9b13fdc33", "tarball": "https://registry.npmjs.org/@babel/types/-/types-8.0.0-alpha.2.tgz", "fileCount": 170, "integrity": "sha512-qc/+6ALxviKBu4nmgZ20ZkNpnzyQAVexvBtojqFOhOaIACGTFLGJyd/o04OtQFBdrhPnhzpz3GSDvFH4RGo1Zw==", "signatures": [{"sig": "MEYCIQDsVSQK6xSJVEDSDsoJ8MndVw1TH0SSGdBO3A3HbfjPQgIhAKIGbM/mDGMuaTPXh097N2EcGFn4XHGPiglLJWAyE0LU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3382587}, "main": "./lib/index.js", "type": "module", "types": "./lib/index-legacy.d.ts", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": {"types": "./lib/index-legacy.d.ts", "default": "./lib/index.js", "types@>=3.7": "./lib/index.d.ts"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^3.0.0", "@babel/helper-string-parser": "^8.0.0-alpha.2", "@babel/helper-validator-identifier": "^8.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^8.0.0-alpha.2", "@babel/generator": "^8.0.0-alpha.2"}, "_npmOperationalInternal": {"tmp": "tmp/types_8.0.0-alpha.2_1691594100056_0.9975030572666375", "host": "s3://npm-registry-packages"}}, "7.22.11": {"name": "@babel/types", "version": "7.22.11", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.22.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "0e65a6a1d4d9cbaa892b2213f6159485fe632ea2", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.22.11.tgz", "fileCount": 170, "integrity": "sha512-siazHiGuZRz9aB9NpHy9GOs9xiQPKnMzgdr493iI1M67vRXpnEq8ZOOKzezC5q7zwuQ6sDhdSp4SD9ixKSqKZg==", "signatures": [{"sig": "MEQCIAPGZYieiheaowWcO7u+d3Xn7KVrMNxTQG/G4Og3DypCAiAu23ZK2LuTCxI6QRWW4i+E18u/8A5WmIVkQL2XnykSzg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2397418}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.5"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^7.22.11", "@babel/generator": "^7.22.10"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.22.11_1692882525386_0.5506995026377319", "host": "s3://npm-registry-packages"}}, "7.22.15": {"name": "@babel/types", "version": "7.22.15", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.22.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "266cb21d2c5fd0b3931e7a91b6dd72d2f617d282", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.22.15.tgz", "fileCount": 170, "integrity": "sha512-X+NLXr0N8XXmN5ZsaQdm9U2SSC3UbIYq/doL++sueHOTisgZHoKaQtZxGuV2cUPQHMfjKEfg/g6oy7Hm6SKFtA==", "signatures": [{"sig": "MEQCIELPSw8gbwBiPwiEcvhqnrZmSaKDig9uAlEMeQIjvAHrAiBZgKHHW9Qk9BhEDAO15Vkm+zqji2Z0w3lzgGIUVOioIQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2398458}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.15"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^7.22.15", "@babel/generator": "^7.22.15"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.22.15_1693830314684_0.3404581804524831", "host": "s3://npm-registry-packages"}}, "7.22.17": {"name": "@babel/types", "version": "7.22.17", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.22.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "f753352c4610ffddf9c8bc6823f9ff03e2303eee", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.22.17.tgz", "fileCount": 170, "integrity": "sha512-YSQPHLFtQNE5xN9tHuZnzu8vPr61wVTBZdfv1meex1NBosa4iT05k/Jw06ddJugi4bk7The/oSwQGFcksmEJQg==", "signatures": [{"sig": "MEYCIQDD7L3pKJLBFFIciZQXXw0UN93SHhG+xnTvevuLe3GYUwIhAMqrNvRuUS/V6JV7KXgFDF0zWhPyUp4N4kK2FwZPnfmV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2399080}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.15"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^7.22.16", "@babel/generator": "^7.22.15"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.22.17_1694181209147_0.47625671513563317", "host": "s3://npm-registry-packages"}}, "7.22.19": {"name": "@babel/types", "version": "7.22.19", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.22.19", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "7425343253556916e440e662bb221a93ddb75684", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.22.19.tgz", "fileCount": 170, "integrity": "sha512-P7LAw/LbojPzkgp5oznjE6tQEIWbp4PkkfrZDINTro9zgBRtI324/EYsiSI7lhPbpIQ+DCeR2NNmMWANGGfZsg==", "signatures": [{"sig": "MEYCIQDovH/vv8XLJ319j1/Q9vtGAv5N4bo/SHMNP43X/S2z2wIhAK4cH9Hbkw19xhC5DNTB5fJm1Muk/aDGBQsP5dYc2E3P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2398618}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.19"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^7.22.16", "@babel/generator": "^7.22.15"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.22.19_1694709125166_0.1667484176189442", "host": "s3://npm-registry-packages"}}, "7.23.0": {"name": "@babel/types", "version": "7.23.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.23.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "8c1f020c9df0e737e4e247c0619f58c68458aaeb", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.23.0.tgz", "fileCount": 172, "integrity": "sha512-0oIyUfKoI3mSqMvsxBdclDwxXKXAUA8v/apZbc+iSyARYou1o8ZGDxbUYyLFoW2arqS2jDGqJuZvv1d/io1axg==", "signatures": [{"sig": "MEUCIQCkQsRNqONSDjqe6ezdb3XLXfu+mDEL/bNw23l6pAlkyQIgddBtZkGAe2xXHnPpCWPkuV0APsIpoqitNWi/n3v+K2Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2412841}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.20"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^7.23.0", "@babel/generator": "^7.23.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.23.0_1695629467478_0.7360268678519808", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/types", "version": "8.0.0-alpha.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "bbfb08e1be8f96e522bd40efc965eda67f80abba", "tarball": "https://registry.npmjs.org/@babel/types/-/types-8.0.0-alpha.3.tgz", "fileCount": 8, "integrity": "sha512-12rQOUz7ION97nxzKNtLag1WFDcfNcw+i/55e48ces/ldgiSxBQQGw6oxbBVAiwOcVPZ2N4pSDsVF21P4DRHvA==", "signatures": [{"sig": "MEQCIHYG7kngAD5ra/LsRSsPqmEcND4iNDhanviTh5DCd0uZAiAcuCykiIzjZRCh1jdCxW16DTjFD1SkP/yCxQmD4pkgcQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2074250}, "main": "./lib/index.js", "type": "module", "types": "./lib/index-legacy.d.ts", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": {"types": "./lib/index-legacy.d.ts", "default": "./lib/index.js", "types@>=3.7": "./lib/index.d.ts"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^3.0.0", "@babel/helper-string-parser": "^8.0.0-alpha.3", "@babel/helper-validator-identifier": "^8.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^8.0.0-alpha.3", "@babel/generator": "^8.0.0-alpha.3"}, "_npmOperationalInternal": {"tmp": "tmp/types_8.0.0-alpha.3_1695740222732_0.4687559072161094", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/types", "version": "8.0.0-alpha.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "a6b83e5c55dc670c8512e54ad39910fe8363de78", "tarball": "https://registry.npmjs.org/@babel/types/-/types-8.0.0-alpha.4.tgz", "fileCount": 8, "integrity": "sha512-Vxb43Y/jeqHpe091Dw+6YeTD8Ru/R88Yp1k6k0A5i7+9pZEM7XaCLIDFTwzv5i+abgDYw7l0F5k6o7uBwTi59A==", "signatures": [{"sig": "MEUCICrLy8dUxh7Nesvqvrj18XrIsK9liRDijRJKBVKCSrkEAiEAtH3bRvBL05jTig6Gwgg3bq1eYeryeUbi73GA9JF3x4w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2074250}, "main": "./lib/index.js", "type": "module", "types": "./lib/index-legacy.d.ts", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": {"types": "./lib/index-legacy.d.ts", "default": "./lib/index.js", "types@>=3.7": "./lib/index.d.ts"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^3.0.0", "@babel/helper-string-parser": "^8.0.0-alpha.4", "@babel/helper-validator-identifier": "^8.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^8.0.0-alpha.4", "@babel/generator": "^8.0.0-alpha.4"}, "_npmOperationalInternal": {"tmp": "tmp/types_8.0.0-alpha.4_1697076383488_0.21859433206082945", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/types", "version": "7.23.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "d5ea892c07f2ec371ac704420f4dcdb07b5f9598", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.23.3.tgz", "fileCount": 172, "integrity": "sha512-OZnvoH2l8PK5eUvEcUyCt/sXgr/h+UWpVuBbOljwcrAgUl6lpchoQ++PHGyQy1AtYnVA6CEq3y5xeEI10brpXw==", "signatures": [{"sig": "MEQCIC7cjHAvrBsP4VyeqyE04KW82uD26wCfViKMesR9OlEVAiB/H9K3OZ8cdv1a2AZF0chy22zNTq9L8ui37I9YNsY3ow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2410314}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.20"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^7.23.3", "@babel/generator": "^7.23.3"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.23.3_1699513444703_0.5513979606338335", "host": "s3://npm-registry-packages"}}, "7.23.4": {"name": "@babel/types", "version": "7.23.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.23.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "7206a1810fc512a7f7f7d4dace4cb4c1c9dbfb8e", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.23.4.tgz", "fileCount": 172, "integrity": "sha512-7uIFwVYpoplT5jp/kVv6EF93VaJ8H+Yn5IczYiaAi98ajzjfoZfslet/e0sLh+wVBjb2qqIut1b0S26VSafsSQ==", "signatures": [{"sig": "MEUCICxF5fXQsVK6uUZ+f5OBOnPJ6lMO6pOpggqmmI+6NjnZAiEAgSqEhfpdulYyx2k0VRtbF/GF35e/S0FVXLh5jhOPUJQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2410318}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.23.4", "@babel/helper-validator-identifier": "^7.22.20"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^7.23.4", "@babel/generator": "^7.23.4"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.23.4_1700490135575_0.2706187290900961", "host": "s3://npm-registry-packages"}}, "7.23.5": {"name": "@babel/types", "version": "7.23.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.23.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "48d730a00c95109fa4393352705954d74fb5b602", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.23.5.tgz", "fileCount": 172, "integrity": "sha512-ON5kSOJwVO6xXVRTvOI0eOnWe7VdUcIpsovGo9U/Br4Ie4UVFQTboO2cYnDhAGU6Fp+UxSiT+pMft0SMHfuq6w==", "signatures": [{"sig": "MEUCIQDB1dXuNiMytgo/qpOySzz9YRJ/PzmbNOzjqprIB/p8RAIgGPiCLZRNJ/wJ1JSojRRFqO7gTQru0kNWNB/f4wdsykw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2411360}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.23.4", "@babel/helper-validator-identifier": "^7.22.20"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^7.23.5", "@babel/generator": "^7.23.5"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.23.5_1701253542200_0.32745604332974443", "host": "s3://npm-registry-packages"}}, "7.23.6": {"name": "@babel/types", "version": "7.23.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.23.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "be33fdb151e1f5a56877d704492c240fc71c7ccd", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.23.6.tgz", "fileCount": 172, "integrity": "sha512-+uarb83brBzPKN38NX1MkB6vb6+mwvR6amUulqAE7ccQw1pEl+bCia9TbdG1lsnFP7lZySvUn37CHyXQdfTwzg==", "signatures": [{"sig": "MEUCIQCfTVwaX1k8RnqqEehzNG5LG4F2mX7YYh4FYx+UqTdaKAIgEjeqYcIrp7McaR/v4I+H5qfdpcwNTk3nFR6jfJJWS0M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2410834}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.23.4", "@babel/helper-validator-identifier": "^7.22.20"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^7.23.6", "@babel/generator": "^7.23.6"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.23.6_1702300200758_0.8089043822658275", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/types", "version": "8.0.0-alpha.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "717d823e3cbf95c5be60435e72e49774b97862fc", "tarball": "https://registry.npmjs.org/@babel/types/-/types-8.0.0-alpha.5.tgz", "fileCount": 8, "integrity": "sha512-af5UpKGfw0mXzEKnUYeSdGnZz+hNlZZe0ebSsvRZEYgh6G+b9kmB0hK4iqQMUg5zF6t48aDjvNJygLfRefKYeA==", "signatures": [{"sig": "MEUCIQCIC8PhRoXUwZmFo6mZAg/2ReosQnwYYK79rsa+93Tq4AIgKoEnCE1uIcUfqmtefk65HydMnV4C4z5NWBLuVfvbYBw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2065486}, "main": "./lib/index.js", "type": "module", "types": "./lib/index-legacy.d.ts", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": {"types": "./lib/index-legacy.d.ts", "default": "./lib/index.js", "types@>=3.7": "./lib/index.d.ts"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^3.0.0", "@babel/helper-string-parser": "^8.0.0-alpha.5", "@babel/helper-validator-identifier": "^8.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^8.0.0-alpha.5", "@babel/generator": "^8.0.0-alpha.5"}, "_npmOperationalInternal": {"tmp": "tmp/types_8.0.0-alpha.5_1702307941932_0.008873485820462435", "host": "s3://npm-registry-packages"}}, "7.23.9": {"name": "@babel/types", "version": "7.23.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.23.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "1dd7b59a9a2b5c87f8b41e52770b5ecbf492e002", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.23.9.tgz", "fileCount": 172, "integrity": "sha512-dQjSq/7HaSjRM43FFGnv5keM2HsxpmyV1PfaSVm0nzzjwwTmjOe6J4bC8e3+pTEIgHaHj+1ZlLThRJ2auc/w1Q==", "signatures": [{"sig": "MEQCIFEP0eexpR2FcuDkihU3xwIZS1eODWqZ2veAp/Tq7NtIAiACS2beFBiFJsAgIgKowHhBf7eK8kwd2KSNYL58896/dw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2409964}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.23.4", "@babel/helper-validator-identifier": "^7.22.20"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^7.23.9", "@babel/generator": "^7.23.6"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.23.9_1706201871642_0.03621085355752807", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/types", "version": "8.0.0-alpha.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "7d3065ab4518e3f65205fccc905c91ca6a44206b", "tarball": "https://registry.npmjs.org/@babel/types/-/types-8.0.0-alpha.6.tgz", "fileCount": 8, "integrity": "sha512-JIcFklQmKMM1TD+1bEyB1QQq450YUoKe42YzdS1IIncZiqfnR0o6x8U+QaW75T9zCoTzy3JsLNBgZ6ebHWLwlA==", "signatures": [{"sig": "MEUCIAuEeABsszAKThkqAiIDcYrJXTSMQCAOIOlAN3M43LbkAiEAtXhiS15G5E4dcbC79UjC5rVeSfKro2Oeh0EfKjJ7aLs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2064649}, "main": "./lib/index.js", "type": "module", "types": "./lib/index-legacy.d.ts", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": {"types": "./lib/index-legacy.d.ts", "default": "./lib/index.js", "types@>=3.7": "./lib/index.d.ts"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^3.0.0", "@babel/helper-string-parser": "^8.0.0-alpha.6", "@babel/helper-validator-identifier": "^8.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^8.0.0-alpha.6", "@babel/generator": "^8.0.0-alpha.6"}, "_npmOperationalInternal": {"tmp": "tmp/types_8.0.0-alpha.6_1706285650471_0.17197766909066803", "host": "s3://npm-registry-packages"}}, "7.24.0": {"name": "@babel/types", "version": "7.24.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.24.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "3b951f435a92e7333eba05b7566fd297960ea1bf", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.24.0.tgz", "fileCount": 172, "integrity": "sha512-+j7a5c253RfKh8iABBhywc8NSfP5LURe7Uh4qpsh6jc+aLJguvmIUBdjSdEMQv2bENrCR5MfRdjGo7vzS/ob7w==", "signatures": [{"sig": "MEUCIQDiWMKq5bkI9qHNhrYHqzrQ/JWr4PmshPC4cfFBgrRBawIgW/haeiqNrNehB7a2n7ys5vFJwNf1Rg22gYZYeLH16/k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2412281}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.23.4", "@babel/helper-validator-identifier": "^7.22.20"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^7.24.0", "@babel/generator": "^7.23.6"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.24.0_1709120858722_0.842434391544548", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/types", "version": "8.0.0-alpha.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "1193e31e29409182141b34e953ce1be006a2e56a", "tarball": "https://registry.npmjs.org/@babel/types/-/types-8.0.0-alpha.7.tgz", "fileCount": 8, "integrity": "sha512-+Z8DsRH3sFdM6QhazqfmLOJkle2vR4CElxAwfWJR9g14HZ5xzizsET4Ir3EsHPcAWBnzyJF0ZY0ehoxL/+pj9g==", "signatures": [{"sig": "MEQCID530gTj4FOoRrK6HDEFo+x8H+MtSx33t2jqSKzhPmkZAiBBMgC+dCt4M4Ok+SJY0PGkWfCHH2BZyUHvH29rqRfmSw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2065767}, "main": "./lib/index.js", "type": "module", "types": "./lib/index-legacy.d.ts", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": {"types": "./lib/index-legacy.d.ts", "default": "./lib/index.js", "types@>=3.7": "./lib/index.d.ts"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^3.0.0", "@babel/helper-string-parser": "^8.0.0-alpha.7", "@babel/helper-validator-identifier": "^8.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^8.0.0-alpha.7", "@babel/generator": "^8.0.0-alpha.7"}, "_npmOperationalInternal": {"tmp": "tmp/types_8.0.0-alpha.7_1709129100753_0.4224836518253987", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/types", "version": "8.0.0-alpha.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "bd680374f652d970c1dd4fd08a45f87dfee95b82", "tarball": "https://registry.npmjs.org/@babel/types/-/types-8.0.0-alpha.8.tgz", "fileCount": 8, "integrity": "sha512-KaZGFkCs0nWvPTI9Wv22YB0pKeXx6Tt8hq6H6fYq+qyGXlrGWJ5fH5JgK5B8FEszH7TWTq9AOQlibLqzwU4m5Q==", "signatures": [{"sig": "MEQCIC5L/L7QW/+KrDLbjq4ghW8+gEEkX7goJb/4MO9oN5ChAiBqjft3M/CN6UtLLoHTtvBAtKvfsBTo76Wsc8eqpEbcrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2065767}, "main": "./lib/index.js", "type": "module", "types": "./lib/index-legacy.d.ts", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": {"types": "./lib/index-legacy.d.ts", "default": "./lib/index.js", "types@>=3.7": "./lib/index.d.ts"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^3.0.0", "@babel/helper-string-parser": "^8.0.0-alpha.8", "@babel/helper-validator-identifier": "^8.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^8.0.0-alpha.8", "@babel/generator": "^8.0.0-alpha.8"}, "_npmOperationalInternal": {"tmp": "tmp/types_8.0.0-alpha.8_1712236794977_0.26810598171782885", "host": "s3://npm-registry-packages"}}, "7.24.5": {"name": "@babel/types", "version": "7.24.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.24.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "7661930afc638a5383eb0c4aee59b74f38db84d7", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.24.5.tgz", "fileCount": 172, "integrity": "sha512-6mQNsaLeXTw0nxYUYu+NSa4Hx4BlF1x1x8/PMFbiR+GBSr+2DkECc69b8hgy2frEodNcvPffeH8YfWd3LI6jhQ==", "signatures": [{"sig": "MEYCIQDwgzDnk+3I36vbJaSqnU0hTvpV1JvXlVrr0q1n2ZFmWAIhAN8c/LNgZ1BJqojzB9JaGUlU88ffdhKMO6OBe/acl5ay", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2412997}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.24.1", "@babel/helper-validator-identifier": "^7.24.5"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^7.24.5", "@babel/generator": "^7.24.5"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.24.5_1714415660415_0.19378939833972386", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/types", "version": "7.24.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "ba4e1f59870c10dc2fa95a274ac4feec23b21912", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.24.6.tgz", "fileCount": 172, "integrity": "sha512-WaMsgi6Q8zMgMth93GvWPXkhAIEobfsIkLTacoVZoK1J0CevIPGYY2Vo5YvJGqyHqXM6P4ppOYGsIRU8MM9pFQ==", "signatures": [{"sig": "MEUCIQCI1QYTZAGclA637JT87ZmTelxJUbbJ5HSBUzTDa3ROSAIgZqer1zeM727ODeA8W0m1iVzw9C72DuYD6mHyi0XSqC8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2412973}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.24.6", "@babel/helper-validator-identifier": "^7.24.6"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^7.24.6", "@babel/generator": "^7.24.6"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.24.6_1716553477158_0.06526903635649539", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/types", "version": "8.0.0-alpha.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "a01fcbeb8a247fab5fb750f8b113145cc67c6b10", "tarball": "https://registry.npmjs.org/@babel/types/-/types-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-jgI3dXgRQHfLEonPcp108KITJVonE63ZwOPIiw5p5SGxWyXJJCwuqzlZdpZUxnAVnuSHwgc0rxd0y4BLICzlWA==", "signatures": [{"sig": "MEQCIAH5vAYRHyWN++hnnRs/tbxeIBWIJKMzBL7+dSy/yxxjAiALKtKKmBFCkLqXeZl+Tjz+k6R3hVWQNGjNlL0syriVzA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2065348}, "main": "./lib/index.js", "type": "module", "types": "./lib/index-legacy.d.ts", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index-legacy.d.ts", "default": "./lib/index.js", "types@>=3.7": "./lib/index.d.ts"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^3.0.0", "@babel/helper-string-parser": "^8.0.0-alpha.9", "@babel/helper-validator-identifier": "^8.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^8.0.0-alpha.9", "@babel/generator": "^8.0.0-alpha.9"}, "_npmOperationalInternal": {"tmp": "tmp/types_8.0.0-alpha.9_1717423463034_0.967278932358141", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/types", "version": "8.0.0-alpha.10", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "171cbae934458e186703581ca4492a7f8968fe52", "tarball": "https://registry.npmjs.org/@babel/types/-/types-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-ETNwd3+rYQfkrUR90T+LZFQJLHdsf1aZkzCGRPIwG1zdvIkKj4dvRYp+vXcxzg6yQaFbboaLy3p4k0Bd5DbG0w==", "signatures": [{"sig": "MEUCIQCiSOBJ7TD1fcfpoEClLbaP+PzpLd2boZffuUpF2M7PQwIgXXGq0cRhHn5lJLfb0F2fjH7f4z5PucbXkx4NjjSoL+w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2065585}, "main": "./lib/index.js", "type": "module", "types": "./lib/index-legacy.d.ts", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index-legacy.d.ts", "default": "./lib/index.js", "types@>=3.7": "./lib/index.d.ts"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^3.0.0", "@babel/helper-string-parser": "^8.0.0-alpha.10", "@babel/helper-validator-identifier": "^8.0.0-alpha.10"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^8.0.0-alpha.10", "@babel/generator": "^8.0.0-alpha.10"}, "_npmOperationalInternal": {"tmp": "tmp/types_8.0.0-alpha.10_1717500012277_0.735750929896569", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/types", "version": "7.24.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "6027fe12bc1aa724cd32ab113fb7f1988f1f66f2", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.24.7.tgz", "fileCount": 172, "integrity": "sha512-XEFXSlxiG5td2EJRe8vOmRbaXVgfcBlszKujvVmWIK/UpywWljQCfzAv3RQCGujWQ1RD4YYWEAqDXfuJiy8f5Q==", "signatures": [{"sig": "MEYCIQDTwRtnJMhOIJjTZaP8iTDPZxig/8T3cUIYcASXmFlPcwIhAPOksqi89whhfvPP8qF79HbO9TjdNVK1cSK2to7H2tJ6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2413194}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.24.7", "@babel/helper-validator-identifier": "^7.24.7"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^7.24.7", "@babel/generator": "^7.24.7"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.24.7_1717593327044_0.859427611309671", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/types", "version": "8.0.0-alpha.11", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "6f8fb33481ab4f217ab33dd98b16f195c4520484", "tarball": "https://registry.npmjs.org/@babel/types/-/types-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-QMnCVcQ3rpXQhDN0iyfwi3TLObbxws33lLljQWzz3g/4h8MqmLvvc9f6yTsNN4p9SMJiynTcVHqGcvb2zEinXw==", "signatures": [{"sig": "MEQCIH4MsbDewH74DYc2Ad9V9MUAnILe/PreRXlx/JkH2BolAiABjbcLIz+01aEmJVkx/JVtiKJi79x1O0xaZ8sVJ89X2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2065585}, "main": "./lib/index.js", "type": "module", "types": "./lib/index-legacy.d.ts", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index-legacy.d.ts", "default": "./lib/index.js", "types@>=3.7": "./lib/index.d.ts"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^3.0.0", "@babel/helper-string-parser": "^8.0.0-alpha.11", "@babel/helper-validator-identifier": "^8.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^8.0.0-alpha.11", "@babel/generator": "^8.0.0-alpha.11"}, "_npmOperationalInternal": {"tmp": "tmp/types_8.0.0-alpha.11_1717751737595_0.6726037416766419", "host": "s3://npm-registry-packages"}}, "7.24.8": {"name": "@babel/types", "version": "7.24.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.24.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "d51ffa9043b17d36622efa44e861a49e69e130a8", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.24.8.tgz", "fileCount": 172, "integrity": "sha512-SkSBEHwwJRU52QEVZBmMBnE5Ux2/6WU1grdYyOhpbCNxbmJrDuDCphBzKZSO3taf0zztp+qkWlymE5tVL5l0TA==", "signatures": [{"sig": "MEYCIQDepE++h1+YHr4ZgyiR7hZexonHPsUsBG3s30QCpYUOCAIhAMHIEMO3ZdizTR2+rdJRWZbRUwdz+1ZhOmNsaSKVQ/54", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2412089}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.24.8", "@babel/helper-validator-identifier": "^7.24.7"}, "publishConfig": {"access": "public"}, "typesVersions": {">=3.7": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^7.24.8", "@babel/generator": "^7.24.8"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.24.8_1720709690354_0.7990845240063371", "host": "s3://npm-registry-packages"}}, "7.24.9": {"name": "@babel/types", "version": "7.24.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.24.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "228ce953d7b0d16646e755acf204f4cf3d08cc73", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.24.9.tgz", "fileCount": 172, "integrity": "sha512-xm8XrMKz0IlUdocVbYJe0Z9xEgidU7msskG8BbhnTPK/HZ2z/7FP7ykqPgrUH+C+r414mNfNWam1f2vqOjqjYQ==", "signatures": [{"sig": "MEYCIQCc0X2saJNo4EuavQBeQnsdCv3Vu1xRva48xRJIO5b9owIhALRmPe/ZhnHoG2RTsYyAYxZ6iCBVc+gkgNKggzDO3pZ3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2412089}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.24.8", "@babel/helper-validator-identifier": "^7.24.7"}, "publishConfig": {"access": "public"}, "typesVersions": {">=4.1": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^7.24.8", "@babel/generator": "^7.24.9"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.24.9_1721039668945_0.15036464705880426", "host": "s3://npm-registry-packages"}}, "7.25.0": {"name": "@babel/types", "version": "7.25.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.25.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "e6e3656c581f28da8452ed4f69e38008ec0ba41b", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.25.0.tgz", "fileCount": 176, "integrity": "sha512-LcnxQSsd9aXOIgmmSpvZ/1yo46ra2ESYyqLcryaBZOghxy5qqOBjvCWP5JfkI8yl9rlxRgdLTTMCQQRcN2hdCg==", "signatures": [{"sig": "MEYCIQDzbdfWNFmoisF4+0BIPawz0E9LtVTgDdJP7YAQk2kVvwIhALofMHqGW5LMtrNLUUoEhNbX4ocYUCorqGNMYX+Olke4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2424814}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.24.8", "@babel/helper-validator-identifier": "^7.24.7"}, "publishConfig": {"access": "public"}, "typesVersions": {">=4.1": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^7.25.0", "@babel/generator": "^7.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.25.0_1722013163193_0.5773607004585608", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/types", "version": "8.0.0-alpha.12", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "878b00779511d07ee4b19b4197aab819308c9b08", "tarball": "https://registry.npmjs.org/@babel/types/-/types-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-sZiedI/E7MuhJURZX6xZ6/O/6SStBH4Kz++ip77bylJgRq39DA0v3tGOd83xKX4sQJO8XwI7gbOgkDajiAlNfA==", "signatures": [{"sig": "MEUCIQC8nbnhXpl2U7mpzeZIEhzdC+eWfRgRcbDKXDn/zuUWVwIgbkjVyxTIQEqcoFEDv3kWIuexiS4vYP9OH8tHBbyUFHw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2076002}, "main": "./lib/index.js", "type": "module", "types": "./lib/index-legacy.d.ts", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index-legacy.d.ts", "default": "./lib/index.js", "types@>=4.1": "./lib/index.d.ts"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^3.0.0", "@babel/helper-string-parser": "^8.0.0-alpha.12", "@babel/helper-validator-identifier": "^8.0.0-alpha.12"}, "publishConfig": {"access": "public"}, "typesVersions": {">=4.1": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^8.0.0-alpha.12", "@babel/generator": "^8.0.0-alpha.12"}, "_npmOperationalInternal": {"tmp": "tmp/types_8.0.0-alpha.12_1722015214196_0.6930013963001045", "host": "s3://npm-registry-packages"}}, "7.25.2": {"name": "@babel/types", "version": "7.25.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.25.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "55fb231f7dc958cd69ea141a4c2997e819646125", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.25.2.tgz", "fileCount": 176, "integrity": "sha512-YTnYtra7W9e6/oAZEHj0bJehPRUlLH9/fbpT5LfB0NhQXyALCRkRs3zH9v07IYhkgpqX6Z78FnuccZr/l4Fs4Q==", "signatures": [{"sig": "MEUCIG/F/PoUCsug3V1qD/ghQsjbJQHxCTUwRW3hFYMZHiDOAiEAuQrce371gdElqb0F0H9r6hIqza6TUFyv8GkjmvpUSG4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2424804}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.24.8", "@babel/helper-validator-identifier": "^7.24.7"}, "publishConfig": {"access": "public"}, "typesVersions": {">=4.1": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^7.25.0", "@babel/generator": "^7.25.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.25.2_1722308089559_0.10324747480774055", "host": "s3://npm-registry-packages"}}, "7.25.4": {"name": "@babel/types", "version": "7.25.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.25.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "6bcb46c72fdf1012a209d016c07f769e10adcb5f", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.25.4.tgz", "fileCount": 178, "integrity": "sha512-zQ1ijeeCXVEh+aNL0RlmkPkG8HUiDcU2pzQQFjtbntgAczRASFzj4H+6+bV+dy1ntKR14I/DypeuRG1uma98iQ==", "signatures": [{"sig": "MEUCIGbfPLkhPAg+cpkkCTDd8V2yUWZLfyTcCEbmw1v6RkDrAiEAkEI4SyNYzhdHE5PcqUC8W+r5K68mCjMvviNy2e98JyE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2479271}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.24.8", "@babel/helper-validator-identifier": "^7.24.7"}, "publishConfig": {"access": "public"}, "typesVersions": {">=4.1": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^7.25.4", "@babel/generator": "^7.25.4"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.25.4_1724319269664_0.2873444472295552", "host": "s3://npm-registry-packages"}}, "7.25.6": {"name": "@babel/types", "version": "7.25.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.25.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "893942ddb858f32ae7a004ec9d3a76b3463ef8e6", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.25.6.tgz", "fileCount": 178, "integrity": "sha512-/l42B1qxpG6RdfYf343Uw1vmDjeNhneUXtzhojE7pDgfpEypmRhI6j1kr17XCVv4Cgl9HdAiQY2x0GwKm7rWCw==", "signatures": [{"sig": "MEUCIQDdAo9vhjuVA90odPuZxipDOQcskwKPOLzqUd0u6yJQMwIgFjQefNKXRRWaftaxwp38T3AxrL4Xr5UF1Pvff5WNowA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2479713}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.24.8", "@babel/helper-validator-identifier": "^7.24.7"}, "publishConfig": {"access": "public"}, "typesVersions": {">=4.1": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^7.25.6", "@babel/generator": "^7.25.6"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.25.6_1724926463190_0.16399708404282887", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/types", "version": "7.25.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "1b7725c1d3a59f328cb700ce704c46371e6eef9b", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.25.7.tgz", "fileCount": 178, "integrity": "sha512-vwIVdXG+j+FOpkwqHRcBgHLYNL7XMkufrlaFvL9o6Ai9sJn9+PdyIL5qa0XzTZw084c+u9LOls53eoZWP/W5WQ==", "signatures": [{"sig": "MEUCIB8pU4DwEHFu0gCQjUjlOrhD58B9l+18trxmoZWjq5cCAiEApzkdYtmnF6TqCeu6O5ZBxA3aZ5GhX+ZYjPK2n1HQUtg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2605441}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.25.7", "@babel/helper-validator-identifier": "^7.25.7"}, "publishConfig": {"access": "public"}, "typesVersions": {">=4.1": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^7.25.7", "@babel/generator": "^7.25.7"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.25.7_1727882095556_0.15514912071962672", "host": "s3://npm-registry-packages"}}, "7.25.8": {"name": "@babel/types", "version": "7.25.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.25.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "5cf6037258e8a9bcad533f4979025140cb9993e1", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.25.8.tgz", "fileCount": 178, "integrity": "sha512-JWtuCu8VQsMladxVz/P4HzHUGCAwpuqacmowgXFs5XjxIgKuNjnLokQzuVjlTvIzODaDmpjT3oxcC48vyk9EWg==", "signatures": [{"sig": "MEYCIQCImnwzhz4JXW+It2Or5vOM71Jy9rCWgXxi8cjU9BswXgIhAJ/eAOQR+/0ibyhk/VeT3WHLRwIAHjlCOxmLy0g8k0Io", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2605903}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"to-fast-properties": "^2.0.0", "@babel/helper-string-parser": "^7.25.7", "@babel/helper-validator-identifier": "^7.25.7"}, "publishConfig": {"access": "public"}, "typesVersions": {">=4.1": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^7.25.8", "@babel/generator": "^7.25.7"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.25.8_1728566713529_0.6414746142804986", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/types", "version": "7.25.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "620f35ea1f4233df529ec9a2668d2db26574deee", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.25.9.tgz", "fileCount": 176, "integrity": "sha512-OwS2CM5KocvQ/k7dFJa8i5bNGJP0hXWfVCfDkqRFP1IreH1JDC7wG6eCYCi0+McbfT8OR/kNqsI0UU0xP9H6PQ==", "signatures": [{"sig": "MEUCIC99p0smruISximcRD5B0rOTvVjMg/8AvJBJ+aWw0JzZAiEAyHmLeJXHR9IaT/EqqedIaJNj7gvrySqIEspP+W/TcPc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2543681}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"@babel/helper-string-parser": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9"}, "publishConfig": {"access": "public"}, "typesVersions": {">=4.1": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^7.25.9", "@babel/generator": "^7.25.9"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.25.9_1729610474133_0.3451816358195454", "host": "s3://npm-registry-packages"}}, "7.26.0": {"name": "@babel/types", "version": "7.26.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.26.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "deabd08d6b753bc8e0f198f8709fb575e31774ff", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.26.0.tgz", "fileCount": 176, "integrity": "sha512-Z/yiTPj+lDVnF7lWeKCIJzaIkI0vYO87dMpZ4bg4TDrFe4XXLFWL1TbXU27gBP3QccxV9mZICCrnjnYlJjXHOA==", "signatures": [{"sig": "MEQCIB/ss5qZ0So8574a2mbQHrQP6mUl1TOC7XklGasRvMXxAiAVlx/22ST62mxDQaswQinurWGLw8rSrmjVi9na+SY5jg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2545656}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"@babel/helper-string-parser": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9"}, "publishConfig": {"access": "public"}, "typesVersions": {">=4.1": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^7.26.0", "@babel/generator": "^7.26.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.26.0_1729863013723_0.37111804554982575", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/types", "version": "8.0.0-alpha.13", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "4dbbc85f967233c4276603ff6a721f9886008d7e", "tarball": "https://registry.npmjs.org/@babel/types/-/types-8.0.0-alpha.13.tgz", "fileCount": 8, "integrity": "sha512-Kt8oBPvsc0h5t06LfUPkBtkPk6t58FzCcpf4Qsjg142gEgaDwFcg+ZPeBCJySLqenk+ISUkdTXsvhbLXj74kIA==", "signatures": [{"sig": "MEQCIGMXwmOZlwk0GNa3GZFxzr5IwzIU6WLANMQkhl3vGwT/AiBcVnYvD3WuPo/HNUv0PGxMJ2Un0VjYqmS+N3jqrJEcoQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2204779}, "main": "./lib/index.js", "type": "module", "types": "./lib/index-legacy.d.ts", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index-legacy.d.ts", "default": "./lib/index.js", "types@>=4.1": "./lib/index.d.ts"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"@babel/helper-string-parser": "^8.0.0-alpha.13", "@babel/helper-validator-identifier": "^8.0.0-alpha.13"}, "publishConfig": {"access": "public"}, "typesVersions": {">=4.1": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^8.0.0-alpha.13", "@babel/generator": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/types_8.0.0-alpha.13_1729864456596_0.6886891155292172", "host": "s3://npm-registry-packages"}}, "7.26.3": {"name": "@babel/types", "version": "7.26.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.26.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "37e79830f04c2b5687acc77db97fbc75fb81f3c0", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.26.3.tgz", "fileCount": 176, "integrity": "sha512-vN5p+1kl59GVKMvTHt55NzzmYVxprfJD+ql7U9NFIfKCBkYE55LYtS+WtPlaYOyzydrKI8Nezd+aZextrd+FMA==", "signatures": [{"sig": "MEUCIFR3g6ZOc8/QnwjEtTpWnrJZny3DvhTEGHnTRaa0lMnjAiEA0L3Kg1CDXDA7fCeW9QAdNGHIfieqxVUXuuBFJUyvA7Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2545927}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"@babel/helper-string-parser": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9"}, "publishConfig": {"access": "public"}, "typesVersions": {">=4.1": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^7.26.3", "@babel/generator": "^7.26.3"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.26.3_1733315738051_0.15038093417155207", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/types", "version": "8.0.0-alpha.14", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "63d3fb5018db9a2551f117d28faba9232947b439", "tarball": "https://registry.npmjs.org/@babel/types/-/types-8.0.0-alpha.14.tgz", "fileCount": 8, "integrity": "sha512-c0O6/uhITDCNdbLkOpG28Isk5uLPr7nKFCtUxwgL/0F1KEIyekC37gd4BkmMKZ1OT8sLvbSE91a9rjvD9WYQfw==", "signatures": [{"sig": "MEUCIQD5JtKAtIDbFPOYbFC662kPD+Ws/gyPjs59WTOyQdJ4NQIgdiynOZsAyRd6Jf8HNBjgL9gJqZkxpEKRC8EEPmFj07U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2196703}, "main": "./lib/index.js", "type": "module", "types": "./lib/index-legacy.d.ts", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index-legacy.d.ts", "default": "./lib/index.js", "types@>=4.1": "./lib/index.d.ts"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"@babel/helper-string-parser": "^8.0.0-alpha.14", "@babel/helper-validator-identifier": "^8.0.0-alpha.14"}, "publishConfig": {"access": "public"}, "typesVersions": {">=4.1": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^8.0.0-alpha.14", "@babel/generator": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/types_8.0.0-alpha.14_1733504047704_0.11253030732385638", "host": "s3://npm-registry-packages"}}, "7.26.5": {"name": "@babel/types", "version": "7.26.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.26.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "7a1e1c01d28e26d1fe7f8ec9567b3b92b9d07747", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.26.5.tgz", "fileCount": 176, "integrity": "sha512-L6mZmwFDK6Cjh1nRCLXpa6no13ZIioJDz7mdkzHv399pThrTa/k0nUlNaenOeh2kWu/iaOQYElEpKPUswUa9Vg==", "signatures": [{"sig": "MEUCIQDGCJxmUZdlXY0/wbfyX61n7viT2KkDsp3/myxDl/yq4QIgM7HP0ewp7FikUf/J37aKk4tC3zMHzPMuty4Q+slmqmE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2556351}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"@babel/helper-string-parser": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9"}, "publishConfig": {"access": "public"}, "typesVersions": {">=4.1": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^7.26.5", "@babel/generator": "^7.26.5"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.26.5_1736529108193_0.307901113885344", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.15": {"name": "@babel/types", "version": "8.0.0-alpha.15", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "a6eaadfa70a680bf4ad8e73ed7ba25a4fb16a90a", "tarball": "https://registry.npmjs.org/@babel/types/-/types-8.0.0-alpha.15.tgz", "fileCount": 8, "integrity": "sha512-wS0ag0rqzZYZxx5Bho0iBJr23HPwz+04sXZuiwoke/asgeO4qfDuIpNPGqlV2v0xAU/rND5B4nA34P1ZcdgRmg==", "signatures": [{"sig": "MEQCIGNcMFG72WB2cUJzaTwMVB5InLdqZc1eIlMYO8zl50mdAiAXER/VAwsmesrK48xSScg+PEjflThcbNbrbQ4A6xoMcQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2204007}, "main": "./lib/index.js", "type": "module", "types": "./lib/index-legacy.d.ts", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index-legacy.d.ts", "default": "./lib/index.js", "types@>=4.1": "./lib/index.d.ts"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"@babel/helper-string-parser": "^8.0.0-alpha.15", "@babel/helper-validator-identifier": "^8.0.0-alpha.15"}, "publishConfig": {"access": "public"}, "typesVersions": {">=4.1": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^8.0.0-alpha.15", "@babel/generator": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/types_8.0.0-alpha.15_1736529874148_0.5406872872474733", "host": "s3://npm-registry-packages-npm-production"}}, "7.26.7": {"name": "@babel/types", "version": "7.26.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.26.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "5e2b89c0768e874d4d061961f3a5a153d71dc17a", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.26.7.tgz", "fileCount": 176, "integrity": "sha512-t8kDRGrKXyp6+tjUh7hw2RLyclsW4TRoRvRHtSyAX9Bb5ldlFh+90YAYY6awRXrlB4G5G2izNeGySpATlFzmOg==", "signatures": [{"sig": "MEUCIAsLMzlrhcQhJgQdJO7wj0jLRVHZDVeLxt5wzkUFuwdyAiEAveLbnBUDT/SWlJYFVweQ7GtM6D3wSrz0wTw3QVekxVU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2556754}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"@babel/helper-string-parser": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9"}, "publishConfig": {"access": "public"}, "typesVersions": {">=4.1": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^7.26.7", "@babel/generator": "^7.26.5"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.26.7_1737731095512_0.987713231021156", "host": "s3://npm-registry-packages-npm-production"}}, "7.26.8": {"name": "@babel/types", "version": "7.26.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.26.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "97dcdc190fab45be7f3dc073e3c11160d677c127", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.26.8.tgz", "fileCount": 176, "integrity": "sha512-eUuWapzEGWFEpHFxgEaBG8e3n6S8L3MSu0oda755rOfabWPnh0Our1AozNFVUxGFIhbKgd1ksprsoDGMinTOTA==", "signatures": [{"sig": "MEUCIHwWyZx6gQNAIjTX7GjHHfdqi1+BmXfF9GPXAWfLR3sRAiEA6nscxIuG+HxeEAOuxklTwYU3M4n2vsyK3wqV4VtBbc8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2568922}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"@babel/helper-string-parser": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9"}, "publishConfig": {"access": "public"}, "typesVersions": {">=4.1": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^7.26.8", "@babel/generator": "^7.26.8"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.26.8_1739008763479_0.3375087927850975", "host": "s3://npm-registry-packages-npm-production"}}, "7.26.9": {"name": "@babel/types", "version": "7.26.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.26.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "08b43dec79ee8e682c2ac631c010bdcac54a21ce", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.26.9.tgz", "fileCount": 178, "integrity": "sha512-Y3IR1cRnOxOCDvMmNiym7XpXQ93iGDDPHx+Zj+NM+rg0fBaShfQLkg+hKPaZCEvg5N/LeCo4+Rj/i3FuJsIQaw==", "signatures": [{"sig": "MEQCIFh8gXymXgCrirkINhOnKBHcqPHZtOPwMIaPpapY2gGdAiAkz+xDbC8Uw+/DaminCFQeZ83SL8mWZaFQT972kR6org==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2577162}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"@babel/helper-string-parser": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9"}, "publishConfig": {"access": "public"}, "typesVersions": {">=4.1": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^7.26.9", "@babel/generator": "^7.26.9"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.26.9_1739533682075_0.8327157723836025", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/types", "version": "8.0.0-alpha.16", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "6dde6ca5fdf5e900d56d71efd2f42ce4dcfffefe", "tarball": "https://registry.npmjs.org/@babel/types/-/types-8.0.0-alpha.16.tgz", "fileCount": 8, "integrity": "sha512-TYCtEyrWmgebnV0U2mJKMDXZ7nPW0gsorE6OwPs5cvlSRdFtqQ9Uk23xQLsTvIJGb0nKYBUI1CgRGMB7Fsts8Q==", "signatures": [{"sig": "MEUCIQDkxicyY2YH+CJuPE9vzk0Z6ggFTbxGAEOVWfsTCQ+XVwIgDQpruUhmev+tjdps2juadYU3Ft8malBrgUh6F/L1qkU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2214754}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"@babel/helper-string-parser": "^8.0.0-alpha.16", "@babel/helper-validator-identifier": "^8.0.0-alpha.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^8.0.0-alpha.16", "@babel/generator": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/types_8.0.0-alpha.16_1739534350015_0.21389803144890496", "host": "s3://npm-registry-packages-npm-production"}}, "7.26.10": {"name": "@babel/types", "version": "7.26.10", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.26.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "396382f6335bd4feb65741eacfc808218f859259", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.26.10.tgz", "fileCount": 178, "integrity": "sha512-emqcG3vHrpxUKTrxcblR36dcrcoRDvKmnL/dCL6ZsHaShW80qxCAcNhzQZrpeM765VzEos+xOi4s+r4IXzTwdQ==", "signatures": [{"sig": "MEUCIDp5FiQc7hm7SK1IQ034/u2zo9B+Il8fExNMYIAgd/QcAiEAwAoffXgQyh6gJaJA/YGKfz0aV+X0p4w9qvmCuXPsJrk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2577099}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"@babel/helper-string-parser": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9"}, "publishConfig": {"access": "public"}, "typesVersions": {">=4.1": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^7.26.10", "@babel/generator": "^7.26.10"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.26.10_1741715707389_0.1657351881298974", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/types", "version": "8.0.0-alpha.17", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "9db00d26db2c11b2332e7b0d21828e73127f08c2", "tarball": "https://registry.npmjs.org/@babel/types/-/types-8.0.0-alpha.17.tgz", "fileCount": 8, "integrity": "sha512-+9mQ43tTM3ga2mc+XHb4Fr2pNj53x4gwl5hc4b5MbsuvZ3E4RiPVUUXAfTqRIO4wj0Z+tqVK+mp2qUWO+LteTA==", "signatures": [{"sig": "MEYCIQC70amrjlcUDhOZcgy9OOiALhWoNZQ8mFQ6wfgRn8nofgIhAPKDTCD6SE5+wC4RtOdsteGRb0VGmxLa3rKIhN98R948", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2216692}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"@babel/helper-string-parser": "^8.0.0-alpha.17", "@babel/helper-validator-identifier": "^8.0.0-alpha.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^8.0.0-alpha.17", "@babel/generator": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/types_8.0.0-alpha.17_1741717503089_0.07951697601893315", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.0": {"name": "@babel/types", "version": "7.27.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.27.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "ef9acb6b06c3173f6632d993ecb6d4ae470b4559", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.27.0.tgz", "fileCount": 178, "integrity": "sha512-H45s8fVLYjbhFH62dIJ3WtmJ6RSPt/3DRO0ZcT2SUiYiQyz3BLVb9ADEnLl91m74aQPS3AzzeajZHYOalWe3bg==", "signatures": [{"sig": "MEQCIClSU2BqL8RQxi4PWJ0T5iqiETqQLPZNr6+SL22TqCSnAiA9frf/HJFGAP052K/yH4ZkFbuAT+5D9oeIXkiIu2cq1g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2583295}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"@babel/helper-string-parser": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9"}, "publishConfig": {"access": "public"}, "typesVersions": {">=4.1": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^7.27.0", "@babel/generator": "^7.27.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.27.0_1742838105056_0.09647206277127718", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/types", "version": "7.27.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "9defc53c16fc899e46941fc6901a9eea1c9d8560", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.27.1.tgz", "fileCount": 178, "integrity": "sha512-+EzkxvLNfiUeKMgy/3luqfsCWFRXLb7U6wNQTk60tovuckwB15B191tJWvpp4HjiQWdJkCxO3Wbvc6jlk3Xb2Q==", "signatures": [{"sig": "MEUCIQD9OaQ0guMKdvoBLsDAhwtQ7mUgSPTzTHAOO68a4rx2MgIgKbiVeaFyG/VCsqut+/y4hmP/kjxZhHV5GOW0p3pliZ4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2597469}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}, "publishConfig": {"access": "public"}, "typesVersions": {">=4.1": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^7.27.1", "@babel/generator": "^7.27.1"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.27.1_1746025739858_0.14995327699764394", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.3": {"name": "@babel/types", "version": "7.27.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@7.27.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "c0257bedf33aad6aad1f406d35c44758321eb3ec", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.27.3.tgz", "fileCount": 178, "integrity": "sha512-Y1GkI4ktrtvmawoSq+4FCVHNryea6uR+qUQy0AGxLSsjCX0nVmkYQMBLHDkXZuo5hGx7eYdnIaslsdBFm7zbUw==", "signatures": [{"sig": "MEYCIQCkpOOP7G44YHiXbJpsBtMv56VqFC2aPZInJ2HuuotpPQIhAK/I2cgX3/u9avxYfwQQsdmtooHAMBwV5zSd36bi/vZt", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2597634}, "main": "./lib/index.js", "type": "commonjs", "types": "./lib/index-legacy.d.ts", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}, "publishConfig": {"access": "public"}, "typesVersions": {">=4.1": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^7.27.3", "@babel/generator": "^7.27.3"}, "_npmOperationalInternal": {"tmp": "tmp/types_7.27.3_1748335158118_0.06251988007755038", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/types", "version": "8.0.0-beta.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/types@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "dist": {"shasum": "a64feb87c95dea7fc1f37d1e153c71385b655851", "tarball": "https://registry.npmjs.org/@babel/types/-/types-8.0.0-beta.0.tgz", "fileCount": 8, "integrity": "sha512-djhZqI9fBlsHXiCpPFsydn6laWPRg+Zg6clvh4FsBjmUjK61bMg8ySnpmjH2bFGTwo4MX5pitzxvCQOgHDLM7w==", "signatures": [{"sig": "MEYCIQDeGJXTSPAvonG3XmN8HzVkQ98dxeb7Yk0o05LfB6DBYAIhAOgPCINwmffuDJu7FiQavEqh4c+MiamJFBawIBWWthDF", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 2269956}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "directories": {}, "dependencies": {"@babel/helper-string-parser": "^8.0.0-beta.0", "@babel/helper-validator-identifier": "^8.0.0-beta.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"glob": "^7.2.0", "@babel/parser": "^8.0.0-beta.0", "@babel/generator": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/types_8.0.0-beta.0_1748620271259_0.8360769809383786", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.6": {"name": "@babel/types", "version": "7.27.6", "description": "Babel Types is a Lodash-esque utility library for AST nodes", "author": "The Babel Team (https://babel.dev/team)", "homepage": "https://babel.dev/docs/en/next/babel-types", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-types"}, "main": "./lib/index.js", "dependencies": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}, "devDependencies": {"@babel/generator": "^7.27.5", "@babel/parser": "^7.27.5", "glob": "^7.2.0"}, "engines": {"node": ">=6.9.0"}, "type": "commonjs", "types": "./lib/index-legacy.d.ts", "typesVersions": {">=4.1": {"lib/index-legacy.d.ts": ["lib/index.d.ts"]}}, "_id": "@babel/types@7.27.6", "dist": {"shasum": "a434ca7add514d4e646c80f7375c0aa2befc5535", "integrity": "sha512-ETyHEk2VHHvl9b9jZP5IHPavHYk57EhanlRRuae9XCpb/j5bDCbPPMOBfCWhnl/7EDJz0jEMCi/RhccCE8r1+Q==", "tarball": "https://registry.npmjs.org/@babel/types/-/types-7.27.6.tgz", "fileCount": 178, "unpackedSize": 2597685, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCICffAUVEojo/tJ27txqW7zEuC4SYTpLYPDvgpuHNAnbFAiA8J6Q2Fg4Xbshao3jeKY8Eto5Ag1DNa7ilXgl2ybE83A=="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/types_7.27.6_1749112639967_0.6992930151457497"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:34:55.832Z", "modified": "2025-06-05T08:37:20.422Z", "7.0.0-beta.4": "2017-10-30T18:34:55.832Z", "7.0.0-beta.5": "2017-10-30T20:56:35.712Z", "7.0.0-beta.31": "2017-11-03T20:03:35.736Z", "7.0.0-beta.32": "2017-11-12T13:33:28.719Z", "7.0.0-beta.33": "2017-12-01T14:28:33.599Z", "7.0.0-beta.34": "2017-12-02T14:39:33.460Z", "7.0.0-beta.35": "2017-12-14T21:47:57.804Z", "7.0.0-beta.36": "2017-12-25T19:04:52.122Z", "7.0.0-beta.37": "2018-01-08T16:02:39.412Z", "7.0.0-beta.38": "2018-01-17T16:32:05.820Z", "7.0.0-beta.39": "2018-01-30T20:27:40.473Z", "7.0.0-beta.40": "2018-02-12T16:41:50.514Z", "7.0.0-beta.41": "2018-03-14T16:25:38.092Z", "7.0.0-beta.42": "2018-03-15T20:50:12.878Z", "7.0.0-beta.43": "2018-04-02T16:48:03.644Z", "7.0.0-beta.44": "2018-04-02T22:19:47.274Z", "7.0.0-beta.45": "2018-04-23T01:55:34.172Z", "7.0.0-beta.46": "2018-04-23T04:29:58.691Z", "7.0.0-beta.47": "2018-05-15T00:07:22.780Z", "7.0.0-beta.48": "2018-05-24T19:20:41.778Z", "7.0.0-beta.49": "2018-05-25T16:00:25.774Z", "7.0.0-beta.50": "2018-06-12T19:46:46.536Z", "7.0.0-beta.51": "2018-06-12T21:19:12.932Z", "7.0.0-beta.52": "2018-07-06T00:59:09.284Z", "7.0.0-beta.53": "2018-07-11T13:39:59.252Z", "7.0.0-beta.54": "2018-07-16T17:59:49.504Z", "7.0.0-beta.55": "2018-07-28T22:06:49.289Z", "7.0.0-beta.56": "2018-08-04T01:03:23.756Z", "7.0.0-rc.0": "2018-08-09T15:56:38.226Z", "7.0.0-rc.1": "2018-08-09T20:06:31.880Z", "7.0.0-rc.2": "2018-08-21T19:22:37.230Z", "7.0.0-rc.3": "2018-08-24T18:06:32.503Z", "7.0.0-rc.4": "2018-08-27T16:42:42.852Z", "7.0.0": "2018-08-27T21:41:54.223Z", "7.1.1": "2018-09-28T20:02:55.056Z", "7.1.2": "2018-09-28T22:19:49.822Z", "7.1.3": "2018-10-11T15:52:22.137Z", "7.1.5": "2018-11-06T22:21:33.218Z", "7.1.6": "2018-11-13T21:10:36.019Z", "7.2.0": "2018-12-03T19:02:02.657Z", "7.2.2": "2018-12-15T10:05:23.067Z", "7.3.0": "2019-01-21T21:42:36.800Z", "7.3.2": "2019-02-04T22:23:12.458Z", "7.3.3": "2019-02-15T21:14:38.228Z", "7.3.4": "2019-02-25T18:35:36.516Z", "7.4.0": "2019-03-19T20:44:54.560Z", "7.4.4": "2019-04-26T21:04:24.383Z", "7.5.0": "2019-07-04T12:58:10.190Z", "7.5.5": "2019-07-17T21:21:36.402Z", "7.6.0": "2019-09-06T17:33:47.806Z", "7.6.1": "2019-09-06T20:53:32.535Z", "7.6.3": "2019-10-08T19:49:41.251Z", "7.7.0": "2019-11-05T10:53:20.464Z", "7.7.1": "2019-11-05T13:47:33.661Z", "7.7.2": "2019-11-06T23:27:24.629Z", "7.7.4": "2019-11-22T23:32:26.851Z", "7.8.0": "2020-01-12T00:16:04.750Z", "7.8.3": "2020-01-13T21:41:07.115Z", "7.8.6": "2020-02-27T12:21:22.402Z", "7.8.7": "2020-03-05T01:56:04.314Z", "7.9.0": "2020-03-20T15:39:32.780Z", "7.9.5": "2020-04-07T19:25:20.880Z", "7.9.6": "2020-04-29T18:38:04.749Z", "7.10.0": "2020-05-26T21:43:23.488Z", "7.10.1": "2020-05-27T22:07:32.498Z", "7.10.2": "2020-05-30T19:25:10.450Z", "7.10.3": "2020-06-19T20:54:17.005Z", "7.10.4": "2020-06-30T13:11:33.453Z", "7.10.5": "2020-07-14T18:17:56.405Z", "7.11.0": "2020-07-30T21:28:49.765Z", "7.11.5": "2020-08-31T20:02:16.565Z", "7.12.0": "2020-10-14T20:03:07.761Z", "7.12.1": "2020-10-15T22:40:43.370Z", "7.12.5": "2020-11-03T22:34:22.668Z", "7.12.6": "2020-11-04T11:09:23.978Z", "7.12.7": "2020-11-20T21:05:44.258Z", "7.12.10": "2020-12-09T22:47:58.949Z", "7.12.11": "2020-12-15T23:59:27.864Z", "7.12.12": "2020-12-23T14:05:24.894Z", "7.12.13": "2021-02-03T01:09:59.786Z", "7.12.17": "2021-02-18T15:12:07.260Z", "7.13.0": "2021-02-22T22:49:41.358Z", "7.13.12": "2021-03-22T15:47:07.849Z", "7.13.13": "2021-03-26T21:20:27.565Z", "7.13.14": "2021-03-29T14:15:35.756Z", "7.13.16": "2021-04-20T11:21:15.078Z", "7.13.17": "2021-04-20T23:19:12.567Z", "7.14.0": "2021-04-29T20:10:05.665Z", "7.14.1": "2021-05-04T01:56:03.672Z", "7.14.2": "2021-05-12T17:09:28.845Z", "7.14.4": "2021-05-28T16:59:53.844Z", "7.14.5": "2021-06-09T23:12:18.907Z", "7.14.8": "2021-07-20T18:02:43.334Z", "7.14.9": "2021-08-01T07:53:20.669Z", "7.15.0": "2021-08-04T21:12:58.257Z", "7.15.4": "2021-09-02T21:39:28.573Z", "7.15.6": "2021-09-09T19:35:17.766Z", "7.16.0": "2021-10-29T23:47:38.517Z", "7.16.7": "2021-12-31T00:22:14.271Z", "7.16.8": "2022-01-10T21:18:29.318Z", "7.17.0": "2022-02-02T23:04:53.076Z", "7.17.10": "2022-04-29T16:37:39.620Z", "7.17.12": "2022-05-16T19:32:14.046Z", "7.18.0": "2022-05-19T18:16:36.127Z", "7.18.2": "2022-05-25T09:16:30.218Z", "7.18.4": "2022-05-29T21:50:12.589Z", "7.18.6": "2022-06-27T19:50:06.111Z", "7.18.7": "2022-06-28T20:30:32.358Z", "7.18.8": "2022-07-08T09:32:35.889Z", "7.18.9": "2022-07-18T09:17:25.394Z", "7.18.10": "2022-08-01T18:46:44.267Z", "7.18.13": "2022-08-22T16:05:11.854Z", "7.19.0": "2022-09-05T19:02:15.703Z", "7.19.3": "2022-09-27T18:36:45.502Z", "7.19.4": "2022-10-10T10:47:25.037Z", "7.20.0": "2022-10-27T13:19:17.756Z", "7.20.2": "2022-11-04T18:51:03.839Z", "7.20.5": "2022-11-28T10:12:46.378Z", "7.20.7": "2022-12-22T09:45:25.229Z", "7.21.0": "2023-02-20T15:31:04.874Z", "7.21.2": "2023-02-23T09:31:35.164Z", "7.21.3": "2023-03-14T14:59:36.447Z", "7.21.4": "2023-03-31T09:01:54.326Z", "7.21.4-esm": "2023-04-04T14:09:32.345Z", "7.21.4-esm.1": "2023-04-04T14:21:24.694Z", "7.21.4-esm.2": "2023-04-04T14:39:25.794Z", "7.21.4-esm.3": "2023-04-04T14:56:14.605Z", "7.21.4-esm.4": "2023-04-04T15:13:26.382Z", "7.21.5": "2023-04-28T19:50:22.207Z", "7.22.0": "2023-05-26T13:45:27.499Z", "7.22.3": "2023-05-27T10:11:04.279Z", "7.22.4": "2023-05-29T14:26:54.971Z", "7.22.5": "2023-06-08T18:21:24.136Z", "8.0.0-alpha.0": "2023-07-20T13:59:59.993Z", "8.0.0-alpha.1": "2023-07-24T17:52:06.314Z", "7.22.10": "2023-08-07T17:25:18.067Z", "8.0.0-alpha.2": "2023-08-09T15:15:00.360Z", "7.22.11": "2023-08-24T13:08:45.701Z", "7.22.15": "2023-09-04T12:25:14.955Z", "7.22.17": "2023-09-08T13:53:29.467Z", "7.22.19": "2023-09-14T16:32:05.596Z", "7.23.0": "2023-09-25T08:11:07.654Z", "8.0.0-alpha.3": "2023-09-26T14:57:02.993Z", "8.0.0-alpha.4": "2023-10-12T02:06:23.832Z", "7.23.3": "2023-11-09T07:04:05.055Z", "7.23.4": "2023-11-20T14:22:15.752Z", "7.23.5": "2023-11-29T10:25:42.501Z", "7.23.6": "2023-12-11T13:10:01.142Z", "8.0.0-alpha.5": "2023-12-11T15:19:02.234Z", "7.23.9": "2024-01-25T16:57:51.834Z", "8.0.0-alpha.6": "2024-01-26T16:14:10.709Z", "7.24.0": "2024-02-28T11:47:38.939Z", "8.0.0-alpha.7": "2024-02-28T14:05:01.006Z", "8.0.0-alpha.8": "2024-04-04T13:19:55.241Z", "7.24.5": "2024-04-29T18:34:20.680Z", "7.24.6": "2024-05-24T12:24:37.441Z", "8.0.0-alpha.9": "2024-06-03T14:04:23.229Z", "8.0.0-alpha.10": "2024-06-04T11:20:12.607Z", "7.24.7": "2024-06-05T13:15:27.307Z", "8.0.0-alpha.11": "2024-06-07T09:15:37.842Z", "7.24.8": "2024-07-11T14:54:50.614Z", "7.24.9": "2024-07-15T10:34:29.149Z", "7.25.0": "2024-07-26T16:59:23.600Z", "8.0.0-alpha.12": "2024-07-26T17:33:34.499Z", "7.25.2": "2024-07-30T02:54:49.797Z", "7.25.4": "2024-08-22T09:34:29.943Z", "7.25.6": "2024-08-29T10:14:23.415Z", "7.25.7": "2024-10-02T15:14:55.865Z", "7.25.8": "2024-10-10T13:25:13.846Z", "7.25.9": "2024-10-22T15:21:14.432Z", "7.26.0": "2024-10-25T13:30:14.024Z", "8.0.0-alpha.13": "2024-10-25T13:54:16.920Z", "7.26.3": "2024-12-04T12:35:38.251Z", "8.0.0-alpha.14": "2024-12-06T16:54:07.961Z", "7.26.5": "2025-01-10T17:11:48.424Z", "8.0.0-alpha.15": "2025-01-10T17:24:34.386Z", "7.26.7": "2025-01-24T15:04:55.972Z", "7.26.8": "2025-02-08T09:59:23.715Z", "7.26.9": "2025-02-14T11:48:02.357Z", "8.0.0-alpha.16": "2025-02-14T11:59:10.277Z", "7.26.10": "2025-03-11T17:55:07.716Z", "8.0.0-alpha.17": "2025-03-11T18:25:03.328Z", "7.27.0": "2025-03-24T17:41:45.284Z", "7.27.1": "2025-04-30T15:09:00.154Z", "7.27.3": "2025-05-27T08:39:18.416Z", "8.0.0-beta.0": "2025-05-30T15:51:11.488Z", "7.27.6": "2025-06-05T08:37:20.182Z"}, "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20types%22+is%3Aopen", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-types", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-types"}, "description": "Babel Types is a Lodash-esque utility library for AST nodes", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": "", "users": {"fangbot": true, "flumpus-dev": true}}