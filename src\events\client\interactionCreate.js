const { Events, InteractionType, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle, StringSelectMenuBuilder, ModalBuilder, TextInputBuilder, TextInputStyle } = require('discord.js');

// Helper function to create the embed for a specific page
function createPageEmbed(page) {
    let embed;

    switch (page) {
        case 2:
            // Section 2: الرومات
            embed = new EmbedBuilder()
                .setColor('#61607e')
                .setTitle('الرومات')
                .setDescription(`
                    ⚠️ **تنبيه هام لكل الأعضاء الجدد!**

في مجتمع سهم، لكل روم قوانينه ونظامه الخاص. لا يوجد روم يشبه الآخر، فكل قناة تقدم محتوى مختلف، أنشطة مميزة، وأسلوب تفاعل خاص بها.

لذلك نرجو منك قراءة أوصاف القنوات بعناية لتفهم طبيعة كل روم قبل المشاركة فيه.

وإذا أردت استعراض أوصاف القنوات، يُرجى اختيار الفئات من القائمة الموجودة بالأسفل، وسيتم عرض كل المعلومات التي تحتاجها بكل وضوح.
                `)
                .setFooter({ text: 'صفحة 2 من 4' })
                .setTimestamp();
            break;

        case 3:
            // Section 3: الرتب و الرولات
            embed = new EmbedBuilder()
                .setColor('#61607e')
                .setTitle('الرتب و الرولات')
                .setDescription(`
**🎭 وصف الرُتب (Roles Description)**

لكل رول في سيرفر سهم فكرته الخاصة، وميزاته، وحتى قواعده التي تميّزه عن غيره.

لهذا السبب، قمنا بتقسيم الرولات إلى فئات واضحة لتسهيل فهمها.

🎯 اختر إحدى الفئات من القائمة أدناه، وسنرسل لك وصفًا شاملاً لتلك الفئة على شكل حزمة منظمة.
📦 بمجرد اختيارك، ستصلك التفاصيل مباشرة.                `)
                .setFooter({ text: 'صفحة 3 من 4' })
                .setTimestamp();
            break;

        case 4:
            // Section 4: الأسئلة الشائعة
            embed = new EmbedBuilder()
                .setColor('#61607e')
                .setTitle('الأسئلة الشائعة')
                .setDescription(`
👋 **للعضو الجديد – دليل المساعدة الأولية**

بصفتك عضوًا جديدًا في سيرفر سهم، قد تُواجه بعض الأمور التي يصعب فهمها في البداية، سواء في طريقة استخدام السيرفر أو في التعامل مع مميزاته وأنظمته المختلفة.

📚 لقد أنشأنا قناة خاصة تحتوي على الأسئلة الشائعة وشرح لهياكل السيرفر، لتكون بداية سهلة وواضحة لك.

         `)
                .setFooter({ text: 'صفحة 4 من 4' })
                .setTimestamp();
            break;

        default:
            // Section 1: المعلومات
            embed = new EmbedBuilder()
                .setColor('#61607e')
                .setTitle('المعلومات')
                .setDescription(`
                    **مرحبًا بك في مجتمع سهم!**

كعضو جديد، قد تواجه بعض الأمور التي تبدو مُربِكة في البداية، سواء في استخدام تطبيق الديسكورد نفسه أو في .التفاعل داخل السيرفر

لذلك، وبعد جهد واهتمام كبير، قمنا بإعداد دليل يحتوي على أكثر الأسئلة الشائعة التي قد تدور في بالك. هذا الدليل سيساعدك على فهم كل ما تحتاج معرفته لتبدأ تجربتك معنا بكل سهولة ووضوح.
                `)
                .setFooter({ text: 'صفحة 1 من 4' })
                .setTimestamp();
            break;
    }

    return embed;
}

// Helper function to create the components for a specific page
function createPageComponents(page) {
    let components = [];

    // Create navigation buttons
    const navigationRow = new ActionRowBuilder().addComponents(
        new ButtonBuilder()
            .setCustomId(`server_map_prev_${page}`)
            .setLabel('◀️')
            .setStyle(ButtonStyle.Primary)
            .setDisabled(page <= 1),
        new ButtonBuilder()
            .setCustomId(`server_map_page_${page}`)
            .setLabel(`${page}/4`)
            .setStyle(ButtonStyle.Secondary)
            .setDisabled(true),
        new ButtonBuilder()
            .setCustomId(`server_map_next_${page}`)
            .setLabel('▶️')
            .setStyle(ButtonStyle.Primary)
            .setDisabled(page >= 4)
    );

    // Add back button
    const backButton = new ActionRowBuilder().addComponents(
        new ButtonBuilder()
            .setCustomId('back_to_main')
            .setLabel('رجوع')
            .setStyle(ButtonStyle.Danger)
    );

    // Add page-specific components
    switch (page) {
        case 2:
            // Add dropdown for channel categories
            const channelDropdown = new ActionRowBuilder()
                .addComponents(
                    new StringSelectMenuBuilder()
                        .setCustomId('server_map_channels')
                        .setPlaceholder('اختر فئة القنوات')
                        .addOptions([
                            {
                                label: 'Pinboard',
                                description: 'معلومات عن قنوات Pinboard',
                                value: 'pinboard_channels',
                                emoji: '📌'
                            },
                            {
                                label: 'Community',
                                description: 'معلومات عن قنوات المجتمع',
                                value: 'community_channels',
                                emoji: '✨'
                            },
                            {
                                label: 'Hangout',
                                description: 'معلومات عن قنوات الدردشة',
                                value: 'hangout_channels',
                                emoji: '🔊'
                            }
                        ])
                );
            components = [channelDropdown, navigationRow, backButton];
            break;

        case 3:
            // Add dropdown for role categories
            const rolesDropdown = new ActionRowBuilder()
                .addComponents(
                    new StringSelectMenuBuilder()
                        .setCustomId('server_map_roles')
                        .setPlaceholder('اختر فئة الرتب')
                        .addOptions([
                            {
                                label: 'Staff',
                                description: 'معلومات عن رتب الإدارة',
                                value: 'staff_roles',
                                emoji: '🛠️'
                            },
                            {
                                label: 'Notifications',
                                description: 'معلومات عن رتب الإشعارات',
                                value: 'notification_roles',
                                emoji: '🔔'
                            },
                            {
                                label: 'Community',
                                description: 'معلومات عن رتب المجتمع',
                                value: 'community_roles',
                                emoji: '👥'
                            },
                            {
                                label: 'Creators',
                                description: 'معلومات عن رتب المنشئين',
                                value: 'creator_roles',
                                emoji: '🎥'
                            },
                            {
                                label: 'Colors',
                                description: 'معلومات عن رتب الألوان',
                                value: 'color_roles',
                                emoji: '🎨'
                            }
                        ])
                );
            components = [rolesDropdown, navigationRow, backButton];
            break;

        case 4:
            // Add dropdown for FAQ categories
            const faqDropdown = new ActionRowBuilder()
                .addComponents(
                    new StringSelectMenuBuilder()
                        .setCustomId('server_map_faq')
                        .setPlaceholder('اختر فئة الأسئلة')
                        .addOptions([
                            {
                                label: 'Frequently Asked Questions',
                                description: 'الأسئلة المتداولة',
                                value: 'faq_general',
                                emoji: '❓'
                            }
                        ])
                );
            components = [faqDropdown, navigationRow, backButton];
            break;

        default:
            components = [navigationRow, backButton];
            break;
    }

    return components;
}

// Function to show a specific page of the server map
async function showServerMapPage(interaction, page, isEdit = false) {
    const embed = createPageEmbed(page);
    const components = createPageComponents(page);

    // Send or edit the message
    if (isEdit) {
        await interaction.editReply({
            embeds: [embed],
            components: components
        });
    } else {
        await interaction.reply({
            embeds: [embed],
            components: components,
            ephemeral: true
        });
    }
}

// Store embeds being created or edited
const embedBuilderSessions = new Map();

// Handle embed builder button interactions
async function handleEmbedBuilderInteraction(interaction, client) {
    const customId = interaction.customId;
    const message = interaction.message;
    const currentEmbed = message.embeds[0];

    // Get the current embed from the message
    const embedBuilder = EmbedBuilder.from(currentEmbed);

    switch (customId) {
        case 'embed_title':
            // Create a modal for title input
            const titleModal = new ModalBuilder()
                .setCustomId('embed_modal_title')
                .setTitle('Set Embed Title');

            const titleInput = new TextInputBuilder()
                .setCustomId('titleInput')
                .setLabel('Title')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('Enter the title for your embed')
                .setValue(currentEmbed.title || '')
                .setRequired(false);

            const titleActionRow = new ActionRowBuilder().addComponents(titleInput);
            titleModal.addComponents(titleActionRow);

            await interaction.showModal(titleModal);
            break;

        case 'embed_description':
            // Create a modal for description input
            const descModal = new ModalBuilder()
                .setCustomId('embed_modal_description')
                .setTitle('Set Embed Description');

            const descInput = new TextInputBuilder()
                .setCustomId('descriptionInput')
                .setLabel('Description')
                .setStyle(TextInputStyle.Paragraph)
                .setPlaceholder('Enter the description for your embed')
                .setValue(currentEmbed.description || '')
                .setRequired(false);

            const descActionRow = new ActionRowBuilder().addComponents(descInput);
            descModal.addComponents(descActionRow);

            await interaction.showModal(descModal);
            break;

        case 'embed_color':
            // Create a modal for color input
            const colorModal = new ModalBuilder()
                .setCustomId('embed_modal_color')
                .setTitle('Set Embed Color');

            const colorInput = new TextInputBuilder()
                .setCustomId('colorInput')
                .setLabel('Color (HEX code)')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('#0099ff')
                .setValue(currentEmbed.color ? `#${currentEmbed.color.toString(16).padStart(6, '0')}` : '#0099ff')
                .setRequired(false);

            const colorActionRow = new ActionRowBuilder().addComponents(colorInput);
            colorModal.addComponents(colorActionRow);

            await interaction.showModal(colorModal);
            break;

        case 'embed_footer':
            // Create a modal for footer input
            const footerModal = new ModalBuilder()
                .setCustomId('embed_modal_footer')
                .setTitle('Set Embed Footer');

            const footerTextInput = new TextInputBuilder()
                .setCustomId('footerTextInput')
                .setLabel('Footer Text')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('Enter the footer text')
                .setValue(currentEmbed.footer?.text || '')
                .setRequired(false);

            const footerIconInput = new TextInputBuilder()
                .setCustomId('footerIconInput')
                .setLabel('Footer Icon URL (optional)')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('https://example.com/icon.png')
                .setValue(currentEmbed.footer?.iconURL || '')
                .setRequired(false);

            const footerTextRow = new ActionRowBuilder().addComponents(footerTextInput);
            const footerIconRow = new ActionRowBuilder().addComponents(footerIconInput);
            footerModal.addComponents(footerTextRow, footerIconRow);

            await interaction.showModal(footerModal);
            break;

        case 'embed_image':
            // Create a modal for image input
            const imageModal = new ModalBuilder()
                .setCustomId('embed_modal_image')
                .setTitle('Set Embed Image');

            const imageInput = new TextInputBuilder()
                .setCustomId('imageInput')
                .setLabel('Image URL')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('https://example.com/image.png')
                .setValue(currentEmbed.image?.url || '')
                .setRequired(false);

            const imageActionRow = new ActionRowBuilder().addComponents(imageInput);
            imageModal.addComponents(imageActionRow);

            await interaction.showModal(imageModal);
            break;

        case 'embed_thumbnail':
            // Create a modal for thumbnail input
            const thumbnailModal = new ModalBuilder()
                .setCustomId('embed_modal_thumbnail')
                .setTitle('Set Embed Thumbnail');

            const thumbnailInput = new TextInputBuilder()
                .setCustomId('thumbnailInput')
                .setLabel('Thumbnail URL')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('https://example.com/thumbnail.png')
                .setValue(currentEmbed.thumbnail?.url || '')
                .setRequired(false);

            const thumbnailActionRow = new ActionRowBuilder().addComponents(thumbnailInput);
            thumbnailModal.addComponents(thumbnailActionRow);

            await interaction.showModal(thumbnailModal);
            break;

        case 'embed_author':
            // Create a modal for author input
            const authorModal = new ModalBuilder()
                .setCustomId('embed_modal_author')
                .setTitle('Set Embed Author');

            const authorNameInput = new TextInputBuilder()
                .setCustomId('authorNameInput')
                .setLabel('Author Name')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('Enter the author name')
                .setValue(currentEmbed.author?.name || '')
                .setRequired(false);

            const authorIconInput = new TextInputBuilder()
                .setCustomId('authorIconInput')
                .setLabel('Author Icon URL (optional)')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('https://example.com/author-icon.png')
                .setValue(currentEmbed.author?.iconURL || '')
                .setRequired(false);

            const authorUrlInput = new TextInputBuilder()
                .setCustomId('authorUrlInput')
                .setLabel('Author URL (optional)')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('https://example.com')
                .setValue(currentEmbed.author?.url || '')
                .setRequired(false);

            const authorNameRow = new ActionRowBuilder().addComponents(authorNameInput);
            const authorIconRow = new ActionRowBuilder().addComponents(authorIconInput);
            const authorUrlRow = new ActionRowBuilder().addComponents(authorUrlInput);
            authorModal.addComponents(authorNameRow, authorIconRow, authorUrlRow);

            await interaction.showModal(authorModal);
            break;

        case 'embed_timestamp':
            // Toggle timestamp
            if (currentEmbed.timestamp) {
                embedBuilder.setTimestamp(null);
            } else {
                embedBuilder.setTimestamp();
            }

            await interaction.update({
                embeds: [embedBuilder],
                components: message.components
            });
            break;

        case 'embed_field':
            // Create a modal for adding a field
            const fieldModal = new ModalBuilder()
                .setCustomId('embed_modal_field')
                .setTitle('Add Embed Field');

            const fieldNameInput = new TextInputBuilder()
                .setCustomId('fieldNameInput')
                .setLabel('Field Name')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('Enter the field name')
                .setRequired(true);

            const fieldValueInput = new TextInputBuilder()
                .setCustomId('fieldValueInput')
                .setLabel('Field Value')
                .setStyle(TextInputStyle.Paragraph)
                .setPlaceholder('Enter the field value')
                .setRequired(true);

            const fieldInlineInput = new TextInputBuilder()
                .setCustomId('fieldInlineInput')
                .setLabel('Inline (true/false)')
                .setStyle(TextInputStyle.Short)
                .setPlaceholder('true or false')
                .setValue('true')
                .setRequired(true);

            const fieldNameRow = new ActionRowBuilder().addComponents(fieldNameInput);
            const fieldValueRow = new ActionRowBuilder().addComponents(fieldValueInput);
            const fieldInlineRow = new ActionRowBuilder().addComponents(fieldInlineInput);
            fieldModal.addComponents(fieldNameRow, fieldValueRow, fieldInlineRow);

            await interaction.showModal(fieldModal);
            break;

        case 'embed_remove_field':
            // If there are fields, create a select menu to choose which to remove
            if (currentEmbed.fields && currentEmbed.fields.length > 0) {
                const selectMenu = new StringSelectMenuBuilder()
                    .setCustomId('embed_select_remove_field')
                    .setPlaceholder('Select a field to remove');

                currentEmbed.fields.forEach((field, index) => {
                    selectMenu.addOptions({
                        label: field.name.substring(0, 100),
                        description: `Field #${index + 1}`,
                        value: index.toString()
                    });
                });

                const selectRow = new ActionRowBuilder().addComponents(selectMenu);

                await interaction.reply({
                    content: 'Select a field to remove:',
                    components: [selectRow],
                    ephemeral: true
                });
            } else {
                await interaction.reply({
                    content: 'This embed has no fields to remove.',
                    ephemeral: true
                });
            }
            break;

        case 'embed_preview':
            // Send a preview of the embed
            await interaction.reply({
                content: 'Here\'s a preview of your embed:',
                embeds: [embedBuilder],
                ephemeral: true
            });
            break;

        case 'embed_send':
            // Get the target channel from stored information or message content
            let channelId = client.embedTargetChannels?.get(interaction.user.id);

            // Fallback: try to extract from message content
            if (!channelId) {
                channelId = interaction.message.content.match(/<#(\d+)>/)?.[1];
            }

            let targetChannel;
            if (channelId) {
                targetChannel = interaction.guild.channels.cache.get(channelId);
            }

            if (!targetChannel) {
                return interaction.reply({
                    content: 'I couldn\'t determine which channel to send the embed to. Please try creating the embed again.',
                    ephemeral: true
                });
            }

            // Check if we're editing an existing embed
            const editData = client.embedsBeingEdited?.get(interaction.user.id);

            if (editData) {
                try {
                    const editChannel = interaction.guild.channels.cache.get(editData.channelId);
                    const messageToEdit = await editChannel.messages.fetch(editData.messageId);

                    await messageToEdit.edit({ embeds: [embedBuilder] });

                    await interaction.reply({
                        content: `Embed updated in <#${editData.channelId}>!`,
                        ephemeral: true
                    });

                    // Clear the edit data
                    client.embedsBeingEdited.delete(interaction.user.id);
                } catch (error) {
                    console.error('Error editing embed:', error);
                    await interaction.reply({
                        content: 'There was an error editing the embed. The message may have been deleted.',
                        ephemeral: true
                    });
                }
            } else {
                // Send the embed to the target channel
                await targetChannel.send({ embeds: [embedBuilder] });

                // Clean up stored channel information
                if (client.embedTargetChannels) {
                    client.embedTargetChannels.delete(interaction.user.id);
                }

                await interaction.reply({
                    content: `✅ Embed sent to <#${targetChannel.id}>!`,
                    ephemeral: true
                });
            }
            break;
    }
}

// Handle modal submissions for the embed builder
async function handleEmbedModalSubmit(interaction, client) {
    const customId = interaction.customId;
    const message = interaction.message;
    const currentEmbed = message.embeds[0];

    // Get the current embed from the message
    const embedBuilder = EmbedBuilder.from(currentEmbed);

    switch (customId) {
        case 'embed_modal_title':
            const title = interaction.fields.getTextInputValue('titleInput');
            embedBuilder.setTitle(title);
            break;

        case 'embed_modal_description':
            const description = interaction.fields.getTextInputValue('descriptionInput');
            embedBuilder.setDescription(description);
            break;

        case 'embed_modal_color':
            const color = interaction.fields.getTextInputValue('colorInput');
            try {
                embedBuilder.setColor(color);
            } catch (error) {
                return interaction.reply({
                    content: 'Invalid color format. Please use a valid HEX color code (e.g., #0099ff).',
                    ephemeral: true
                });
            }
            break;

        case 'embed_modal_footer':
            const footerText = interaction.fields.getTextInputValue('footerTextInput');
            const footerIcon = interaction.fields.getTextInputValue('footerIconInput');

            if (footerText || footerIcon) {
                embedBuilder.setFooter({
                    text: footerText || '\u200B', // Use zero-width space if no text
                    iconURL: footerIcon || null
                });
            } else {
                // If both are empty, remove the footer
                embedBuilder.setFooter(null);
            }
            break;

        case 'embed_modal_image':
            const imageUrl = interaction.fields.getTextInputValue('imageInput');

            if (imageUrl) {
                embedBuilder.setImage(imageUrl);
            } else {
                // If empty, remove the image
                embedBuilder.setImage(null);
            }
            break;

        case 'embed_modal_thumbnail':
            const thumbnailUrl = interaction.fields.getTextInputValue('thumbnailInput');

            if (thumbnailUrl) {
                embedBuilder.setThumbnail(thumbnailUrl);
            } else {
                // If empty, remove the thumbnail
                embedBuilder.setThumbnail(null);
            }
            break;

        case 'embed_modal_author':
            const authorName = interaction.fields.getTextInputValue('authorNameInput');
            const authorIcon = interaction.fields.getTextInputValue('authorIconInput');
            const authorUrl = interaction.fields.getTextInputValue('authorUrlInput');

            if (authorName) {
                embedBuilder.setAuthor({
                    name: authorName,
                    iconURL: authorIcon || null,
                    url: authorUrl || null
                });
            } else {
                // If name is empty, remove the author
                embedBuilder.setAuthor(null);
            }
            break;

        case 'embed_modal_field':
            const fieldName = interaction.fields.getTextInputValue('fieldNameInput');
            const fieldValue = interaction.fields.getTextInputValue('fieldValueInput');
            const fieldInline = interaction.fields.getTextInputValue('fieldInlineInput').toLowerCase() === 'true';

            embedBuilder.addFields({ name: fieldName, value: fieldValue, inline: fieldInline });
            break;
    }

    // Update the message with the new embed
    await interaction.update({
        embeds: [embedBuilder],
        components: message.components
    });
}

// Handle select menu interactions for the embed builder
async function handleEmbedSelectMenu(interaction, client) {
    const customId = interaction.customId;
    const values = interaction.values;

    switch (customId) {
        case 'embed_select_remove_field':
            const fieldIndex = parseInt(values[0]);
            const message = interaction.message;
            const currentEmbed = message.embeds[0];

            // Get the current embed from the message
            const embedBuilder = EmbedBuilder.from(currentEmbed);

            // Get the current fields
            const fields = [...currentEmbed.fields];

            // Remove the selected field
            fields.splice(fieldIndex, 1);

            // Set the new fields
            embedBuilder.setFields(fields);

            // Update the original builder message
            await interaction.message.interaction.editReply({
                embeds: [embedBuilder],
                components: message.message.components
            });

            // Acknowledge the selection
            await interaction.update({
                content: `Removed field #${fieldIndex + 1}`,
                components: []
            });
            break;

        case 'embed_select_channel':
            const channelId = values[0];
            const channel = interaction.guild.channels.cache.get(channelId);

            if (!channel) {
                return interaction.update({
                    content: 'Invalid channel selected.',
                    components: []
                });
            }

            // Get the embed from the original message
            const originalMessage = await interaction.message.fetchReference();
            const embed = originalMessage.embeds[0];

            // Send the embed to the selected channel
            await channel.send({ embeds: [embed] });

            // Acknowledge the selection
            await interaction.update({
                content: `Embed sent to <#${channelId}>!`,
                components: []
            });
            break;
    }
}

// Handle help command button interactions
async function handleHelpButtonInteraction(interaction) {
    const { EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

    const category = interaction.customId.replace('help_', '');

    if (category === 'back') {
        // Show main help menu
        await showMainHelpMenu(interaction);
        return;
    }

    // Show category-specific commands
    await showCategoryCommands(interaction, category);
}

async function showMainHelpMenu(interaction) {
    const { EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

    const embed = new EmbedBuilder()
        .setColor('#0099ff')
        .setTitle('🤖 Bot Commands Help')
        .setDescription('Welcome to the help system! Here are all available command categories:')
        .setThumbnail(interaction.client.user.displayAvatarURL())
        .addFields(
            {
                name: '🛡️ Admin Commands (3)',
                value: '`audit`, `logs`, `setstatus`\nAdministrator-only commands for server management',
                inline: true
            },
            {
                name: '⚖️ Moderation Commands (22)',
                value: '`warn`, `ban`, `kick`, `timeout`, `purge`, `lockdown`, and more\nModeration tools for server safety',
                inline: true
            },
            {
                name: '🔧 Tools Commands (2)',
                value: '`ping`, `embed`\nUtility tools and testing commands',
                inline: true
            },
            {
                name: '🔨 Utility Commands (2)',
                value: '`command`, `embedcreator`\nCustom commands and embed creation',
                inline: true
            },
            {
                name: '📖 How to Use',
                value: '• Use `/help category:<category>` to see commands in a specific category\n• Use `/help command:<command_name>` for detailed command info\n• Click the buttons below to navigate categories',
                inline: false
            }
        )
        .setFooter({ text: 'Use the buttons below to explore each category' })
        .setTimestamp();

    const row = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId('help_admin')
                .setLabel('Admin')
                .setStyle(ButtonStyle.Primary)
                .setEmoji('🛡️'),
            new ButtonBuilder()
                .setCustomId('help_moderation')
                .setLabel('Moderation')
                .setStyle(ButtonStyle.Secondary)
                .setEmoji('⚖️'),
            new ButtonBuilder()
                .setCustomId('help_tools')
                .setLabel('Tools')
                .setStyle(ButtonStyle.Success)
                .setEmoji('🔧'),
            new ButtonBuilder()
                .setCustomId('help_utility')
                .setLabel('Utility')
                .setStyle(ButtonStyle.Secondary)
                .setEmoji('🔨')
        );

    await interaction.update({
        embeds: [embed],
        components: [row]
    });
}

async function showCategoryCommands(interaction, category) {
    const { EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require('discord.js');

    const commands = getCommandsByCategory(category);

    const embed = new EmbedBuilder()
        .setColor(getCategoryColor(category))
        .setTitle(`${getCategoryEmoji(category)} ${getCategoryName(category)}`)
        .setDescription(`Here are all the ${category} commands available:`)
        .setThumbnail(interaction.client.user.displayAvatarURL())
        .setFooter({ text: `Total: ${commands.length} commands` })
        .setTimestamp();

    // Add commands in chunks to avoid embed limits
    let commandList = '';
    commands.forEach(cmd => {
        const cmdText = `**/${cmd.name}** - ${cmd.description}\n`;
        if (commandList.length + cmdText.length > 1024) {
            embed.addFields({ name: 'Commands', value: commandList, inline: false });
            commandList = cmdText;
        } else {
            commandList += cmdText;
        }
    });

    if (commandList) {
        embed.addFields({ name: 'Commands', value: commandList, inline: false });
    }

    const row = new ActionRowBuilder()
        .addComponents(
            new ButtonBuilder()
                .setCustomId('help_back')
                .setLabel('Back to Main')
                .setStyle(ButtonStyle.Secondary)
                .setEmoji('⬅️')
        );

    await interaction.update({
        embeds: [embed],
        components: [row]
    });
}

// Helper functions for help system
function getCategoryColor(category) {
    const colors = {
        admin: '#ff0000',
        moderation: '#ff9900',
        tools: '#00ff00',
        utility: '#0099ff'
    };
    return colors[category] || '#0099ff';
}

function getCategoryEmoji(category) {
    const emojis = {
        admin: '🛡️',
        moderation: '⚖️',
        tools: '🔧',
        utility: '🔨'
    };
    return emojis[category] || '📁';
}

function getCategoryName(category) {
    const names = {
        admin: 'Admin Commands',
        moderation: 'Moderation Commands',
        tools: 'Tools Commands',
        utility: 'Utility Commands'
    };
    return names[category] || 'Commands';
}

function getCommandsByCategory(category) {
    const commands = {
        admin: [
            { name: 'audit', description: 'View detailed audit history for a user' },
            { name: 'logs', description: 'Configure and manage server logging' },
            { name: 'setstatus', description: 'Change the bot\'s status and activity' }
        ],
        moderation: [
            { name: 'ban', description: 'Ban a user from the server' },
            { name: 'come', description: 'Call a member to come (Arabic)' },
            { name: 'delete', description: 'Delete messages or content' },
            { name: 'giverole', description: 'Give a role to a user' },
            { name: 'info', description: 'Display server information panel' },
            { name: 'join', description: 'Join voice channel commands' },
            { name: 'kick', description: 'Kick a user from the server' },
            { name: 'leave', description: 'Leave voice channel commands' },
            { name: 'lock', description: 'Lock a channel' },
            { name: 'lockdown', description: 'Lock/unlock server or channels' },
            { name: 'moveall', description: 'Move all users from voice channel' },
            { name: 'moveme', description: 'Move yourself to another voice channel' },
            { name: 'moveuser', description: 'Move a user to another voice channel' },
            { name: 'mute', description: 'Mute a user' },
            { name: 'nickname', description: 'Manage user nicknames' },
            { name: 'purge', description: 'Bulk delete messages with filters' },
            { name: 'slowmode', description: 'Set channel slowmode' },
            { name: 'tempban', description: 'Temporarily ban users with auto-unban' },
            { name: 'ticket', description: 'Manage support tickets' },
            { name: 'timeout', description: 'Timeout a user for specified duration' },
            { name: 'unban', description: 'Unban a user from the server' },
            { name: 'unlock', description: 'Unlock a channel' },
            { name: 'unmute', description: 'Unmute a user' },
            { name: 'untimeout', description: 'Remove timeout from a user' },
            { name: 'warn', description: 'Issue warnings with auto-escalation' },
            { name: 'warnings', description: 'View user warning history' }
        ],
        tools: [
            { name: 'ping', description: 'Check bot latency and response time' },
            { name: 'embed', description: 'Create a sample embed message' }
        ],
        utility: [
            { name: 'command', description: 'Create and manage custom commands' },
            { name: 'embedcreator', description: 'Interactive embed builder' },
            { name: 'help', description: 'Display all available bot commands' }
        ]
    };

    return commands[category] || [];
}

// Handle embed builder channel selection
async function handleEmbedBuilderChannelSelect(interaction, client) {
    const channelId = interaction.values[0];
    const channel = interaction.guild.channels.cache.get(channelId);

    if (!channel) {
        return interaction.update({
            content: 'Invalid channel selected.',
            components: []
        });
    }

    // Check permissions
    if (!channel.permissionsFor(interaction.client.user).has('SendMessages')) {
        return interaction.update({
            content: `I don't have permission to send messages in <#${channelId}>!`,
            components: []
        });
    }

    // Store the target channel
    client.embedTargetChannels = client.embedTargetChannels || new Map();
    client.embedTargetChannels.set(interaction.user.id, channelId);

    // Create initial embed for customization
    const { EmbedBuilder } = require('discord.js');
    const initialEmbed = new EmbedBuilder()
        .setColor('#0099ff')
        .setTitle('New Embed')
        .setDescription('Use the buttons below to customize this embed')
        .setFooter({ text: 'Created with Embed Builder' })
        .setTimestamp();

    // Get the embed builder components
    const { createEmbedBuilderComponents } = require('../../commands/utility/embedbuilder.js');
    const components = createEmbedBuilderComponents();

    await interaction.update({
        content: `🛠️ **Embed Builder** - Customizing embed for <#${channelId}>\n\n📝 Use the buttons below to customize your embed:`,
        embeds: [initialEmbed],
        components: components
    });
}

module.exports = {
    name: Events.InteractionCreate,
    async execute(interaction, client) {
        // Handle embed builder interactions
        if (interaction.isButton() && interaction.customId.startsWith('embed_')) {
            await handleEmbedBuilderInteraction(interaction, client);
            return;
        }

        // Handle modal submissions for embed builder
        if (interaction.isModalSubmit() && interaction.customId.startsWith('embed_modal_')) {
            await handleEmbedModalSubmit(interaction, client);
            return;
        }

        // Handle select menu interactions for embed builder
        if (interaction.isStringSelectMenu() && interaction.customId.startsWith('embed_select_')) {
            await handleEmbedSelectMenu(interaction, client);
            return;
        }

        // Handle new embed builder channel selection
        if (interaction.isStringSelectMenu() && interaction.customId === 'embedbuilder_select_channel') {
            await handleEmbedBuilderChannelSelect(interaction, client);
            return;
        }


        if (interaction.isButton()) {
            if (interaction.customId === 'server_info') {
                const guild = interaction.guild;
                const serverIconUrl = guild.iconURL({ dynamic: true, size: 1024 });
                const serverBannerUrl = guild.bannerURL({ dynamic: true, size: 4096 });

                const serverInfoEmbed = new EmbedBuilder()
                    .setColor('#61607e')
                    .setTitle(`${guild.name} Information`)
                    .setThumbnail(serverIconUrl)
                    .setImage(serverBannerUrl)
                    .addFields(
                        { name: 'Server ID', value: guild.id, inline: true },
                        { name: 'Owner', value: `<@${guild.ownerId}>`, inline: true },
                        { name: 'Total Members', value: guild.memberCount.toString(), inline: true },
                        { name: 'Boost Level', value: guild.premiumTier.toString(), inline: true },
                        { name: 'Created On', value: guild.createdAt.toDateString(), inline: true },
                        { name: 'Server Icon URL', value: serverIconUrl ? `[Click Here](${serverIconUrl})` : 'None', inline: true },
                        { name: 'Server Banner URL', value: serverBannerUrl ? `[Click Here](${serverBannerUrl})` : 'None', inline: true }
                    )
                    .setFooter({
                        text: 'Server Information',
                        iconURL: interaction.client.user.displayAvatarURL()
                    })
                    .setTimestamp();

                const backButton = new ActionRowBuilder().addComponents(
                    new ButtonBuilder()
                        .setCustomId('back_to_main')
                        .setLabel('Back')
                        .setStyle(ButtonStyle.Secondary)
                );

                await interaction.reply({
                    embeds: [serverInfoEmbed],
                    components: [backButton],
                    ephemeral: true
                });
            }
            else if (interaction.customId === 'rules') {
                const rulesEmbed = new EmbedBuilder()
                    .setColor('#61607e')
                    .setTitle('قوانين السيرفر')
                    .setDescription(`
                        📜 **القوانين**
                            _________________________________________
                        1. احترام جميع الأعضاء والآراء

                        2. يمنع السب والتحريض على المشاكل والمظاهرات بكل أنواعها

                        3. يمنع نشر التحذيرات التي تم ارسالها من الادارة او نشر محتوى التذاكر الخاصة بالدعم الفني في السيرفر

                        4. عدم إزعاج الإدارة إلا للضرورة

                        5. يمنع التطرق للمواضيع الخارجة عن حدود الآداب العامة أو للمواضيع الشاذة وإلخ

                        6. يمنع طلب الرتب أو التلميح لذلك , الإدارة العليا لها الحرية باختيار وإعطاء الرتب وهم اعلم بمن يستحقها

                        7. يمنع طلب المال أو أي شيء مماثل له سواء مال في لعبه أو غيرها وإلخ

                        8. يمنع التطرق للمواضيع السياسية , الدينية , العرقية , العنصرية وإلخ بأي شكل من الأشكال

                        9. يمنع وضع صورة او اسم مخل بآداب وعادات مجتمعنا

                        10. يمنع الحرق  المسلسلات والأفلام والإنمي والمباريات ما عدا البثوث

                        11. لكل شات وظيفة مخصصه , يمنع استخدام أي شات بطريقة مخالفة لوظيفته الأساسية

                        12. يمنع إعطاء حسابك لأشخاص آخرين

                        13. يمنع البيع والشراء والترويج بجميع أنواعه منعا باتا

                        14. يمنع السبام بكل أشكاله (تكرار الكلام ، الرسائل ، النقاط)

                        15. يمنع تشفير السب و التحدث او جلب سيره الاجندات مثل المثلين والمتحولين والإهانات بأي شكل من الأشكال مثل كلمة عمك ، طفل ...الخ

                        16. يمنع التدخل بالقرارات الإدارية وتعاند عليهم وتقليل من شائنهم ، فنحن أعلم بما نفعل

                        17. عدم انتحال شخصية إداري
                        __________________________________________________________________
                    `)
                    .setFooter({ text: 'آخر تحديث للقوانين' })
                    .setTimestamp();

                const rulesButtons = new ActionRowBuilder().addComponents(
                    new ButtonBuilder()
                        .setCustomId('chat_rules')
                        .setLabel('قوانين الشات')
                        .setStyle(ButtonStyle.Primary)
                        .setEmoji('💬'),
                    new ButtonBuilder()
                        .setLabel('Discord Terms of Service')
                        .setStyle(ButtonStyle.Link)
                        .setURL('https://discord.com/terms')
                        .setEmoji('📜'),
                    new ButtonBuilder()
                        .setLabel('Discord Community Guidelines')
                        .setStyle(ButtonStyle.Link)
                        .setURL('https://discord.com/guidelines')
                        .setEmoji('📋')
                );

                const backButton = new ActionRowBuilder().addComponents(
                    new ButtonBuilder()
                        .setCustomId('back_to_main')
                        .setLabel('رجوع')
                        .setStyle(ButtonStyle.Secondary)
                );

                await interaction.reply({
                    embeds: [rulesEmbed],
                    components: [rulesButtons, backButton],
                    ephemeral: true
                });
            }
            else if (interaction.customId === 'chat_rules') {
                const chatRulesEmbed = new EmbedBuilder()
                    .setColor('#61607e')
                    .setTitle('قوانين الشات')
                    .setDescription(`
                        💬 **قوانين الشات العامة**

                        > • يمنع السب والشتم والألفاظ النابية
                        > • يمنع نشر الروابط بجميع أنواعها
                        > • يمنع إرسال الرسائل المكررة (السبام)
                        > • يمنع استخدام منشن (@) بشكل مزعج
                        > • يمنع نشر معلومات شخصية
                        > • يجب احترام جميع الأعضاء والإداريين
                        > • يمنع المناقشات السياسية والدينية
                        > • يمنع نشر محتوى غير لائق
                        > • التحدث باحترام وأدب مع الجميع
                        > • استخدام القنوات المخصصة للمواضيع المختلفة
                    `)
                    .setFooter({ text: 'قوانين الشات' })
                    .setTimestamp();

                const backButton = new ActionRowBuilder().addComponents(
                    new ButtonBuilder()
                        .setCustomId('rules')
                        .setLabel('رجوع للقوانين')
                        .setStyle(ButtonStyle.Secondary)
                );

                await interaction.reply({
                    embeds: [chatRulesEmbed],
                    components: [backButton],
                    ephemeral: true
                });
            }
            else if (interaction.customId === 'support') {
                const supportEmbed = new EmbedBuilder()
                    .setColor('#61607e')
                    .setTitle('Support Information')
                    .setDescription(`
                    📋 **قوانين وتعليمات التذاكر**

            1. عند فتح تذكرة إبلاغ عضو / إداري ، يجب إحضار دليل واضح

            2. منع منعا باتا الاستهبال داخل التذكرة ايضا احترام الاداري

            3. منع فتح تذكرة بـ داعي الاستهبال او تجربة التذكرة

            4. منع ازعاج الادارة داخل التذكرة يجب عليك التحلي بـ الصبر الى حين رد الإدارة عليك

            ⚠️ **للتنبيه:**
            يحق لك فتح تذكرة بـ الاسباب التالية:
            • التبليغ على أداري او عضو
            • أستفسار عن شيء داخل السيرفر
            • في حال يوجد خلل بالرومات وما شابة داخل السيرفر تقوم بفتح تذكره

            نتمنى منكم عدم مخالفة القوانين لـ تجنب المشاكل بالسيرفر و ل يكون سيرفر نظيف خالي من المشاكل و من يخالف القوانين سوف يتعاقب

                    `)
                    .setFooter({ text: 'Support INFO' });

                const supportButtons = new ActionRowBuilder().addComponents(
                    new ButtonBuilder()
                        .setCustomId('back_to_main')
                        .setLabel('Back')
                        .setStyle(ButtonStyle.Secondary)
                );

                await interaction.reply({
                    embeds: [supportEmbed],
                    components: [supportButtons],
                    ephemeral: true
                });
            }
            else if (interaction.customId === 'social_media') {
                const socialMediaEmbed = new EmbedBuilder()
                    .setColor("#61607e")
                    .setTitle("Social Media")
                    .setDescription(`
                        🌐 **Follow Us**

                        • <:icons8tiktok100:1350564524499144893>**TikTok**
                        https://tiktok.com/@ssahmx

                        • <:kick:1350897732159799346>**KICK**
                        https://kick.com/ssahm

                        • <:icons8instagram96:1350563125925249095>**Instagram**
                        https://instagram.com/5p7s

                        • <:icons8twitter100:1350565921000787968>**Twitter**
                        https://x.com/4e56

                        • <:icons8whatsapp100:1350564766560813126>**WhatsApp**
                        https://www.whatsapp.com/channel/0029Vb7oMy0HLHQeiupoGg3X

                        Stay connected with sSAHM community across all platforms!
                    `)
                    .setFooter({ text: "SH startup" });

                const socialMediaButtons = new ActionRowBuilder().addComponents(
                    new ButtonBuilder()
                        .setLabel('TikTok')
                        .setStyle(ButtonStyle.Link)
                        .setURL('https://tiktok.com/@ssahmx')
                        .setEmoji('<:icons8tiktok100:1350564524499144893>'),
                    new ButtonBuilder()
                        .setLabel('KICK')
                        .setStyle(ButtonStyle.Link)
                        .setURL('https://kick.com/ssahm')
                        .setEmoji('<:kick:1082916486240292975>'),
                    new ButtonBuilder()
                        .setLabel('Instagram')
                        .setStyle(ButtonStyle.Link)
                        .setURL('https://instagram.com/5p7s')
                        .setEmoji('<:icons8instagram96:1350563125925249095>'),
                    new ButtonBuilder()
                        .setLabel('Twitter')
                        .setStyle(ButtonStyle.Link)
                        .setURL('https://x.com/4e56')
                        .setEmoji('<:icons8twitter100:1350565921000787968>'),
                    new ButtonBuilder()
                        .setLabel('WhatsApp')
                        .setStyle(ButtonStyle.Link)
                        .setURL('https://www.whatsapp.com/channel/0029Vb7oMy0HLHQeiupoGg3X')
                        .setEmoji('<:icons8whatsapp100:1350564766560813126>')
                );

                const backButton = new ActionRowBuilder().addComponents(
                    new ButtonBuilder()
                        .setCustomId('back_to_main')
                        .setLabel('Back')
                        .setStyle(ButtonStyle.Secondary)
                );

                await interaction.reply({
                    embeds: [socialMediaEmbed],
                    components: [socialMediaButtons, backButton],
                    ephemeral: true
                });
            }
            else if (interaction.customId === 'back_to_main') {
                await interaction.deferUpdate();
                await interaction.deleteReply();
            }

            else if (interaction.customId.startsWith('back_to_server_map_')) {
                // Just dismiss the message without creating a new one
                await interaction.deferUpdate();
                await interaction.deleteReply();
            }
            else if (interaction.customId.startsWith('server_map_page_')) {
                // Just dismiss the message without creating a new one
                await interaction.deferUpdate();
                await interaction.deleteReply();
            }
            else if (interaction.customId === 'support_info') {
                const supportEmbed = new EmbedBuilder()
                    .setColor('#61607e')
                    .setTitle('معلومات الدعم الفني')
                    .setDescription(`
                        📋 **قوانين وتعليمات التذاكر**
                                     **<#1348124235381342208>لطلب الدعم الفني (تكت) عليك الذهاب لروم**
                        1. عند فتح تذكرة إبلاغ عضو / إداري ، يجب إحضار دليل واضح

                        2. في حال لم يتجاوب الإداري معك او تم ظلمك يمكنك فتح تذكرة إدارة عليا او التبليغ على الاداري

                        3. منع منعا باتا الاستهبال داخل التذكرة ايضا احترام الاداري

                        4. منع فتح تذكرة بـ داعي الاستهبال او تجربة التذكرة

                        5. منع ازعاج الادارة داخل التذكرة يجب عليك التحلي بـ الصبر الى حين رد الإدارة عليك

                        ⚠️ **للتنبيه:**
                        يحق لك فتح تذكرة بـ الاسباب التالية:
                        • التبليغ على أداري او عضو
                        • أستفسار عن شيء داخل السيرفر فقط
                        • في حال يوجد خلل بالرومات وما شابة داخل السيرفر تقوم بفتح تذكره

                        📢 **ملاحظة هامة:**
                        نتمنى منكم عدم مخالفة القوانين لـ تجنب المشاكل بالسيرفر و ل يكون سيرفر نظيف خالي من المشاكل و من يخالف القوانين سوف يتعاقب
                    `)
                    .setFooter({ text: 'معلومات الدعم الفني' })
                    .setTimestamp();

                const backButton = new ActionRowBuilder().addComponents(
                    new ButtonBuilder()
                        .setCustomId('back_to_main')
                        .setLabel('رجوع')
                        .setStyle(ButtonStyle.Secondary)
                );

                await interaction.reply({
                    embeds: [supportEmbed],
                    components: [backButton],
                    ephemeral: true
                });
            }
            else if (interaction.customId === 'Level_System') {
                const ranksEmbed = new EmbedBuilder()
                    .setColor('#61607e')
                    .setTitle('**نظام التفاعل لـسيرفر sSAHM Community**')
                    .setDescription(`
                       **الرتب الكتابية:**

                        <@&1372373892252827678> **: level 3**
                        استعمال رياكشن على الردود

                        <@&1372365461286686730> **: level 10**
                        ارسال ايموتات والستيكرات من داخل السيرفر

                        <@&1372368162770653245> **: level 20**
                        ارسال ايموجيات من خارج السيرفر 

                        <@&1372369068664946709> **: level 25**
                         إمكانية إرسال الصور والفيديوهات في الرومات العامه 

                        <@&1372370196613828658> **: level 30**
                        تغيير اسمك في السيرفر

                        <@&1372381623969910825> **: level 45**
                          فتح روم الالوان

                        <@&1372371133785178122> **: level 50**
                        رتبة خاصة بلون وروم كتابي وصوتي وشارات متميزة من اختيارك خاصة بنظام القروبات القادم 

                        **الرتب الصوتية:**

                        <@&1350906086793936916> **: level 3**
                        استخدام الساوند بورد من داخل السيرفر

                        <@&1350906260756627499> **: level 10**
                         استخدام الساوند بورد من الخاص بالسيرفرات الأخرى

                        <@&1350906618522632362> **: level 15**
                        فتح ميزة Activities

                        <@&1350908587907485778> **: level 20**
                        فتح الشير سكرين والكام في الرومات

                        <@&1350906795178328116> **: level 25**
                        ارسال ايموتات وستيكرات بالسيرفر من خارج السيرفر ، استخدام التسجيل الصوتي أو الرسائل الصوتية  بالرومات العامة

                        <@&1350906915697201173> **: level 30**
                        فتح روم الالوان

                        <@&1350907476039700663> **: level 45**
                         رتبة خاصة بلون وروم كتابي وصوتي وشارات متميزة من 
                        اختيارك خاصة بنظام القروبات القادم

                        <@&1372331434701750474> **: level 50**
                         كل برمشنات الرتب الكتابية
                  `)
                    .setFooter({ text: 'SH startup' });

                const backButton = new ActionRowBuilder().addComponents(
                    new ButtonBuilder()
                        .setCustomId('back_to_main')
                        .setLabel('Back')
                        .setStyle(ButtonStyle.Secondary)
                );

                await interaction.reply({
                    embeds: [ranksEmbed],
                    components: [backButton],
                    ephemeral: true
                });
            }
            else if (interaction.customId === 'server_map') {
                // Show the first page of the server map
                await interaction.reply({
                    embeds: [createPageEmbed(1)],
                    components: createPageComponents(1),
                    ephemeral: true
                });
            }
            else if (interaction.customId.startsWith('server_map_next_')) {
                // Get the current page from the custom ID
                const currentPage = parseInt(interaction.customId.split('_')[3]);
                const nextPage = currentPage + 1;

                // Update the message with the next page
                await interaction.update({
                    embeds: [createPageEmbed(nextPage)],
                    components: createPageComponents(nextPage)
                });
            }
            else if (interaction.customId.startsWith('server_map_prev_')) {
                // Get the current page from the custom ID
                const currentPage = parseInt(interaction.customId.split('_')[3]);
                const prevPage = currentPage - 1;

                // Update the message with the previous page
                await interaction.update({
                    embeds: [createPageEmbed(prevPage)],
                    components: createPageComponents(prevPage)
                });
            }
            // Handle help command button interactions
            else if (interaction.customId.startsWith('help_')) {
                await handleHelpButtonInteraction(interaction);
            }
        }

        // Handle select menu interactions
        if (interaction.isStringSelectMenu()) {
            // Handle channel category dropdown
            if (interaction.customId === 'server_map_channels') {
                const selected = interaction.values[0];
                let embed;

                if (selected === 'pinboard_channels') {
                    embed = new EmbedBuilder()
                        .setColor('#61607e')
                        .setTitle('📌 Pinboard Channels')
                        .setDescription(`
                           📢 **قسم الرومات المهمه في المجتمع سهم**

> هذا القسم مخصص للرومات المهمه و الخاصة بالسيرفر. ستجد هنا كل ما تحتاج لمعرفته حول الرومات المهمه في المجتمع!

🎯・<#1272745870982647902>
>  .المعلومات الرئيسية في السيرفر. يمكنك من خلالها الاطلاع على قواعد السيرفر، أوصاف القنوات، الأسئلة الشائعة، بالإضافة إلى معلومات التواصل والدعم إذا كنت بحاجة لأي مساعدة.

👷・<#1328257513723789342>
>  .هنا يتم نشر جميع الأخبار الرسمية المتعلقة بالسيرفر من تحديثات وصيانات. تأكد من تفعيل التنبيهات حتى لا يفوتك أي جديد!

🎉・<#1325724553162391663>
>  .روم الفعاليات والمسابقات! تُنشر فيه الإعلانات الخاصة بالسحوبات والمفاجآت، وأحيانًا يتم تنظيم جوائز وسحوبات مميزة للمشاركين.

🎫・<#1316945742241333256>
>  .عند مواجهتك لأي مشكلة، أو إذا كان لديك سؤال أو استفسار، يمكنك فتح تذكرة عبر هذا الروم وسنكون سعداء بمساعدتك في أقرب وقت ممكن.
                        `)
                        .setFooter({ text: 'قنوات Pinboard' })
                        .setTimestamp();
                }
                else if (selected === 'community_channels') {
                    embed = new EmbedBuilder()
                        .setColor('#61607e')
                        .setTitle('✨ Community Channels')
                        .setDescription(`
                             قنوات المجتمع

                             <#1380019409346891827>
                            > .قناة للمحادثات العامة والتفاعل مع أعضاء السيرفر.


                             <#1272745871196426255> | <#1272745871196426259> | <#1316939966835851304> 
                            > .رومات لمشاركة مقاطع والميمز


                             <#1373135306324185098>
                            > .قناة لتقديم اقتراحات وأفكار لتحسين السيرفر.


                             <#1319567874020343871> | <#1272745871196426256> | <#1361452203927404857> | <#1373454801463021661>
                            > .رومات لمشاركة إبداعاتكم من رسم و تصاميم والتصوير


                             <#1272745871196426252>
                             > .روم خاص ل استخدام الكوماندات 


                             <#1371210711749103656> | <#1325728998919114803>
                            >  .رومات خاصة لكسب الاجر والثواب


                             <#1325724553162391663> | <#1316280812928368741>
                            > .رومات خاصة بأخبار الفعاليات و المشاركة في الفعاليات

                            
                            <#1272745871435628545>
                            > .روم خاص لمشاركة الذكريات الخاصة ب المجتمع و حفظها في ذا الروم الخاص 

                        `)
                        .setFooter({ text: 'قنوات المجتمع' })
                        .setTimestamp();
                }
                else if (selected === 'hangout_channels') {
                    embed = new EmbedBuilder()
                        .setColor('#61607e')
                        .setTitle('🔊 Hangout Channels')
                        .setDescription(`
                            🎧 **القسم الصوتي في سيرفر سهم**

هذا القسم مخصص للرومات الصوتية التي تتيح لك التفاعل والتواصل مع الأعضاء الآخرين بالصوت، سواء للدردشة أو لطلب الدعم أو لإنشاء رومك الخاص.

🚨・<#1316288346456195164>
إذا كنت بحاجة إلى مساعدة من الإدارة أو ترغب بالتحدث معهم صوتيًا، يمكنك الدخول إلى هذا الروم، وسيتم نقلك تلقائيًا إلى الروم المخصص عند توفر أحد الإداريين.

🔊・<#1272745871435628547> | <#1272745871435628548> | <#1316264384011440169>
هذه هي الرومات الصوتية الرئيسية الخاصة بالسيرفر. يمكنك الدخول إليها للدردشة مع أصدقائك أو التفاعل مع أعضاء المجتمع بحرية.

🔊・<#1351041982545526857>
إذا رغبت في إنشاء روم صوتي خاص بك، فقط ادخل هذا الروم وسيقوم البوت تلقائيًا بإنشاء روم صوتي مخصص لك.

⚙・<#1351041983992434798>
ملاحظة: للتحكم الكامل في إعدادات الروم الصوتي الذي تم إنشاؤه لك، توجّه إلى هذا الروم لاستخدام لوحة التحكم الخاصة بك.
                        `)
                        .setFooter({ text: 'قنوات الدردشة الصوتية' })
                        .setTimestamp();
                }

                const backButton = new ActionRowBuilder().addComponents(
                    new ButtonBuilder()
                        .setCustomId('back_to_main')
                        .setLabel('رجوع للقائمة')
                        .setStyle(ButtonStyle.Secondary)
                );

                await interaction.reply({
                    embeds: [embed],
                    components: [backButton],
                    ephemeral: true
                });
            }

            // Handle roles category dropdown
            else if (interaction.customId === 'server_map_roles') {
                const selected = interaction.values[0];
                let embed;

                if (selected === 'staff_roles') {
                    embed = new EmbedBuilder()
                        .setColor('#61607e')
                        .setTitle('🛠️ Staff Roles')
                        .setDescription(`
                        

> في كل سيرفر ناجح، لا بد من وجود طاقم إداري يعمل على تنظيم المجتمع، مراقبة المحادثات، الرد على الاستفسارات، والتعامل مع المخالفين للحفاظ على بيئة آمنة ومرحّبة للجميع.
> فيما يلي توضيح لأهم الرُتب الإدارية ووظائفها في سيرفر سهم:

<@&1363205974554447962>
> .أعلى رتبة إدارية في السيرفر، مخصصة للمسؤولين التنفيذيين والمشرفين العامين على كل ما يحدث في السيرفر

<@&1363205974554447962> | <@&1325812386036191264> | <@&1348827936362598490>
> .رُتب إدارية عُليا تُشرف على جميع أقسام السيرفر، وتقوم بمتابعة الأنشطة اليومية والإشراف الكامل على إدارة المجتمع

<@&1379302045814751272> | <@&1379296594465263687> | <@&1370961090258927716> | <@&1348765399147089981> | <@&1379305210543083530>
> .طاقم إداري مسؤول عن الإشراف على المحادثات النصية، وتنظيم المجتمع، وتحذير أو معاقبة المخالفين عند الضرورة

<@&1379313653211463691>
> .فريق خاص بالإشراف على الرد على استفسارات المتابعين ع منصة كيك

<@&1359093960173289492> | <@&1379296245121679501>
> .فريق مسؤول عن تنظيم الفعاليات داخل السيرفر، وضمان تفاعل المجتمع والمشاركة في الأنشطة

                        `)
                        .setFooter({ text: 'رتب الإدارة' })
                        .setTimestamp();
                }
                else if (selected === 'notification_roles') {
                    embed = new EmbedBuilder()
                        .setColor('#61607e')
                        .setTitle('🔔 Notification Roles')
                        .setDescription(`
                             رتب الإشعارات

                            <@&1371979813283168276>
                            > .إشعارات البثوث وكل شي يخص سهم

                            <@&1371991345794777248>
                            > .للحصول على إشعارات عند نشر إعلانات جديدة من تعديلات للسيرفر

                            <@&1359093960173289492> | <@&1379296245121679501>
                            > .للحصول على إشعارات عند إقامة فعاليات جديدة

                            <@&1371978050492829820>
                            > .تنبيهات الأذكار
                        `)
                        .setFooter({ text: 'رتب الإشعارات' })
                        .setTimestamp();
                }
                else if (selected === 'community_roles') {
                    embed = new EmbedBuilder()
                        .setColor('#61607e')
                        .setTitle('👥 Community Roles')
                        .setDescription(`
               > يوجد الآن عدد كبير من القوائم التي لا يمكن تصنيفها في فئة معينة ، لذلك تم جمعها هنا للتوضيح

                             <@&1329629923450556487>
                            > .رتبة للأعضاء النشطين والمميزين في السيرفر

                             <@&1272745870743568476>
                            > .رتبة للأعضاء الجدد في السيرفر

                             <@&1277361514646339668>
                            > .رول للأشخاص الذين عملوا بوست في السيرفر ويبقى معك حتى سحب البوست

                             <@&1372322714437812255> | <@&1372278730583576627> | <@&1372322952682668092>
                            > .رتب للفنانين الذين يشاركون أعمالهم في السيرفر

                            <@&1379242588363817060>
                            > رول للشخص الفائز في الفاعليات التي نظمتها الإدارة في السيرفر

                        `)
                        .setFooter({ text: 'رتب المجتمع' })
                        .setTimestamp();
                }
                else if (selected === 'creator_roles') {
                    embed = new EmbedBuilder()
                        .setColor('#61607e')
                        .setTitle('🎨 Creator Roles')
                        .setDescription(`
                             رتب المنشئين

                             <@&1319920793999769611>
                            > .رتبة لمنشئي المحتوى على منصات التواصل الاجتماعي.

                             <@&1372322714437812255> | <@&1372278730583576627> | <@&1372322952682668092>
                            > .رتبة للفنانين الذين يشاركون أعمالهم في السيرفر.

                             <@&1320607426017497220>
                            > .رتبة للمصممين الذين يساهمون بتصاميمهم في السيرفر.
                        `)
                        .setFooter({ text: 'رتب المنشئين' })
                        .setTimestamp();
                }
                else if (selected === 'color_roles') {
                    embed = new EmbedBuilder()
                        .setColor('#61607e')
                        .setTitle('🎨 Colors List')
                        .setDescription(`
من خلال تعزيز السيرفر بالبوست، يتم منحك ميزة تغيير لونك في السيرفر عن طريق إضافة رول إلى حسابك ، ولكن أحد الشروط هو أن تقوم عمل بوست السيرفر . لمزيد من المعلومات ،  ، وإذا كنت حاليا من الاشخاص الرائعين المشتكرين ب التعزيز السيرفر ، يرجى الضغط على  "Colors Channel"
                        `)
                        .setImage('https://cdn.discordapp.com/attachments/1333340696253300797/1386566971704868974/Screenshot_2025-06-23_002509.png?ex=685a2c9f&is=6858db1f&hm=57e80b6bc01414f951600c7bd7f535c8dff19f6fa4e4e77db7af9fcae5b61511&') // Replace with your uploaded image URL
                        .setFooter({ text: 'رتب الألوان' })
                        .setTimestamp();
                }


                const backButton = new ActionRowBuilder().addComponents(
                    new ButtonBuilder()
                        .setCustomId('back_to_main')
                        .setLabel('رجوع للقائمة')
                        .setStyle(ButtonStyle.Secondary)
                );

                await interaction.reply({
                    embeds: [embed],
                    components: [backButton],
                    ephemeral: true
                });
            }

            // Handle FAQ category dropdown
            else if (interaction.customId === 'server_map_faq') {
                const selected = interaction.values[0];
                let embed;

                if (selected === 'faq_general') {
                    embed = new EmbedBuilder()
                        .setColor('#61607e')
                        .setTitle('❓ Frequently Asked Questions')
                        .setDescription(`
> الأسئلة التالية هي الأكثر تداولاً في سيرفر سهم، وقد قمنا بجمعها مع إجاباتها حتى إذا كنت عضو جديد هنا حتى إذا كنت عضو قديم هنا
> الإجابة

    **كيف يمكنني الاتصال بفريق إدارة السيرفر؟** ✉️ •
> .للتواصل مع فريق الإدارة، ما عليك سوى فتح تذكرة في الروم <#1316945742241333256> الروم المخصص، وسيتم فتح تذكرة خاصة بك وسيتواصل معك أحد أفراد الفريق


    **ما فائدة البوست؟** 💎 •
> . يعد البوست جانبًا من جوانب الدعم ويساعدنا فقط على التقدم في عملنا ، وضع المزيد من الجيف اوي وأيضًا خلق بيئة أفضل لك أعزاءي ، وبالطبع نحن أيضًا نشكرك على دعمكم
   

    **كيف تحصل على الرولات للمنشن؟** 🏷️ •
> .للحصول على الرولات ما عليك سوى الانتقال إلى قسم القنوات والأدوار الموجودة في اعلى السيرفر والحصول على القوائم الخاصة بك او عن طريق الضغط على زر


    **كيف ترى قنواتنا المفضلة فقط؟** 📋 •
> لتغيير قائمة الرومات السيرفر، ما عليك سوى الانتقال إلى قسم استعراض القنوات وتحديد القنوات التي تريدها فقط


    **ما هو المستوى وكيف أزيد مستواي على السيرفر؟** 🏆 •
> .لإظهار نشاط أعضاء السيرفر الخاص بنا ، نستخدم مستوى النظام. وكلما كان مستواك أعلى ، زاد ظهورك في قائمة الأعضاء ، مما يعني أن لديك مزيدًا من المعرفة حول السيرفر ويتم فتح المزيد من الميزات لك. ؛ من أجل زيادة مستواك ، يجب أن تكون نشطًا في دردشة السيرفر والرسائل الصوتية ، وسيزداد مستواك تلقائيًا وفقًا لنشاطك ، ويمكنك رؤية مستواك وملفك الشخصي باستخدام الأمر /rank في روم الأوامر


    **كيف أشارك في الجيف أوي؟** 🎁 •
> .يجري جميع السحوبات السيرفر في <#1272745870982647908>، آمل أن تكون من المحظوظين في السحب القديم
                        `)
                        .setFooter({ text: 'Frequently Asked Questions' })
                        .setTimestamp();
                }


                const backButton = new ActionRowBuilder().addComponents(
                    new ButtonBuilder()
                        .setCustomId('back_to_main')
                        .setLabel('رجوع للقائمة')
                        .setStyle(ButtonStyle.Secondary)
                );

                await interaction.reply({
                    embeds: [embed],
                    components: [backButton],
                    ephemeral: true
                });
            }
        }

        // Handle custom command modal submissions
        if (interaction.isModalSubmit()) {
            if (interaction.customId === 'create_command_modal') {
                await handleCreateCommandModal(interaction, client);
                return;
            } else if (interaction.customId.startsWith('edit_command_modal_')) {
                await handleEditCommandModal(interaction, client);
                return;
            }
        }

        // Handle autocomplete for custom commands
        if (interaction.isAutocomplete()) {
            const command = client.commands.get(interaction.commandName);
            if (command && command.autocomplete) {
                try {
                    await command.autocomplete(interaction);
                } catch (error) {
                    console.error('Error handling autocomplete:', error);
                }
            }
            return;
        }

        if (interaction.type === InteractionType.ApplicationCommand) {
            const command = client.commands.get(interaction.commandName);

            // If it's not a regular command, check if it's a custom command
            if (!command) {
                if (client.handleCustomCommands && client.handleCustomCommands.isCustomCommand(interaction.commandName)) {
                    try {
                        await client.handleCustomCommands.executeCustomCommand(interaction);
                    } catch (error) {
                        console.error('Error executing custom command:', error);
                        await interaction.reply({
                            content: "There was an error while executing this custom command!",
                            ephemeral: true
                        });
                    }
                }
                return;
            }

            try {
                await command.execute(interaction, client);
            } catch (error) {
                console.error(error);
                await interaction.reply({
                    content: "There was an error while executing this command!",
                    ephemeral: true
                });
            }
        }
    }
};

// Handle create command modal submission
async function handleCreateCommandModal(interaction, client) {
    const commandName = interaction.fields.getTextInputValue('command_name').toLowerCase().trim();
    const commandDescription = interaction.fields.getTextInputValue('command_description').trim();
    const commandResponse = interaction.fields.getTextInputValue('command_response').trim();

    // Validate command name
    if (!/^[a-z0-9_-]{1,32}$/.test(commandName)) {
        const embed = new EmbedBuilder()
            .setColor('#ff6b6b')
            .setTitle('❌ Invalid Command Name')
            .setDescription('Command name must be 1-32 characters long and contain only lowercase letters, numbers, hyphens, and underscores.')
            .setTimestamp();

        return interaction.reply({ embeds: [embed], ephemeral: true });
    }

    // Check if user has permission to create commands (optional - you can modify this)
    if (!interaction.member.permissions.has('ManageMessages')) {
        const embed = new EmbedBuilder()
            .setColor('#ff6b6b')
            .setTitle('❌ Permission Denied')
            .setDescription('You need the "Manage Messages" permission to create custom commands.')
            .setTimestamp();

        return interaction.reply({ embeds: [embed], ephemeral: true });
    }

    try {
        const result = await client.handleCustomCommands.createCustomCommand(
            commandName,
            commandDescription,
            commandResponse,
            interaction.user.id
        );

        const embed = new EmbedBuilder()
            .setColor(result.success ? '#4CAF50' : '#ff6b6b')
            .setTitle(result.success ? '✅ Command Created' : '❌ Creation Failed')
            .setDescription(result.message)
            .setTimestamp();

        if (result.success) {
            embed.addFields({
                name: 'Command Details',
                value: `**Name:** \`/${commandName}\`\n**Description:** ${commandDescription}\n**Response:** ${commandResponse.substring(0, 100)}${commandResponse.length > 100 ? '...' : ''}`,
                inline: false
            });
        }

        await interaction.reply({ embeds: [embed], ephemeral: true });
    } catch (error) {
        console.error('Error creating custom command:', error);
        const embed = new EmbedBuilder()
            .setColor('#ff6b6b')
            .setTitle('❌ Error')
            .setDescription('An error occurred while creating the custom command.')
            .setTimestamp();

        await interaction.reply({ embeds: [embed], ephemeral: true });
    }
}

// Handle edit command modal submission
async function handleEditCommandModal(interaction, client) {
    const commandName = interaction.customId.replace('edit_command_modal_', '');
    const commandDescription = interaction.fields.getTextInputValue('command_description').trim();
    const commandResponse = interaction.fields.getTextInputValue('command_response').trim();

    try {
        const result = await client.handleCustomCommands.updateCustomCommand(
            commandName,
            commandDescription,
            commandResponse,
            interaction.user.id
        );

        const embed = new EmbedBuilder()
            .setColor(result.success ? '#4CAF50' : '#ff6b6b')
            .setTitle(result.success ? '✅ Command Updated' : '❌ Update Failed')
            .setDescription(result.message)
            .setTimestamp();

        if (result.success) {
            embed.addFields({
                name: 'Updated Command Details',
                value: `**Name:** \`/${commandName}\`\n**Description:** ${commandDescription}\n**Response:** ${commandResponse.substring(0, 100)}${commandResponse.length > 100 ? '...' : ''}`,
                inline: false
            });
        }

        await interaction.reply({ embeds: [embed], ephemeral: true });
    } catch (error) {
        console.error('Error updating custom command:', error);
        const embed = new EmbedBuilder()
            .setColor('#ff6b6b')
            .setTitle('❌ Error')
            .setDescription('An error occurred while updating the custom command.')
            .setTimestamp();

        await interaction.reply({ embeds: [embed], ephemeral: true });
    }
}

