{"_id": "@jest/globals", "_rev": "131-5d978df3abd44947f14654e4770fa63e", "name": "@jest/globals", "dist-tags": {"next": "30.0.0-rc.1", "latest": "30.0.2"}, "versions": {"25.5.0": {"name": "@jest/globals", "version": "25.5.0", "license": "MIT", "_id": "@jest/globals@25.5.0", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2e36037e0e96d34870f99a024d4dc1485bcbd775", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-25.5.0.tgz", "fileCount": 5, "integrity": "sha512-yC+WlD1ytYPZvTSbmSeZM+BNbkFXtkTBBjtmoFDYxjznwugl2Qv2KW7csxL7nTxJOxyjkffy6ngLZ6YMqAe7MA==", "signatures": [{"sig": "MEUCIGIi7s4UPWiI1JhYx0g/x3opZTyiw7UbZoVUr61UQ9JOAiEAnHBwprVwW/FOq17+I+DIcsUll1FmOGlfyReElNiDaHA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4680, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqIfrCRA9TVsSAnZWagAABfMP/R4tEJo5v1gsQW+quUi0\n2e0NoJiJZ3mItSBr1LhUZEY/Jc3znMPmOqfy5TXLxWUCf0oxJK0jBPG/baao\ne4pmMviwl8Af2wlpATnS7rBY/bU44jJ5NFGkd/HQRQRLnhoc85wrG+l2cvxL\nP0z1vlkW6tsjF3mCZURbuZH3UkIN+BBBIWZxiFhitgImbjTYH48A1BFrK3zH\n4lZ5dBfBBX6/RrQp2GTGFu0g1sEMikRvs79+480wNX9+Xvl9jAQybwmNn7fr\nuTzC48CvQbaneWLhL3ZOzrmK4dNFavQcqeWpG1WjL6A/Ob9GgCwuBWXuzK29\nB9PAo/4Mqp/SWRqo+el72DSoObdDsuwqE1Y/Blogjav7eYPUje1d3ujdpPzA\nCJ+TkHh+WQ7YNJ8HnyCLXk1APLYxCd/eOgZyzdMTPbdiRjdFEB7+3kny6kBP\naT/esgI35ffCT0scOH1F+LXVUMzeP9/pqN1o+q5N6GK0T7oQECV3JQ1Hp+Uy\n0OvjRuK8zI3POJ/dzGnO45gRdy9OxgZisQtEtYdq70hy7r0/X+f1wU/hNozK\np4RZP0mmGaQuRCSXy82FiBRM8mclBK2n/z3/797zjPx7I2CHGUV8KrP6M+pn\n8NZL2rZjePQVj86ZWkUROF7zd76j6ZKPmgjtEVjjm7NseZlBbtB6YEhEoCes\nPhXj\r\n=gnV6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "ddd73d18adfb982b9b0d94bad7d41c9f78567ca7", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"expect": "^25.5.0", "@jest/types": "^25.5.0", "@jest/environment": "^25.5.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_25.5.0_1588103147129_0.5216041719153275", "host": "s3://npm-registry-packages"}}, "25.5.2": {"name": "@jest/globals", "version": "25.5.2", "license": "MIT", "_id": "@jest/globals@25.5.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5e45e9de8d228716af3257eeb3991cc2e162ca88", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-25.5.2.tgz", "fileCount": 5, "integrity": "sha512-AgAS/Ny7Q2RCIj5kZ+0MuKM1wbF0WMLxbCVl/GOMoCNbODRdJ541IxJ98xnZdVSZXivKpJlNPIWa3QmY0l4CXA==", "signatures": [{"sig": "MEYCIQC1dJjX0iZK/tdlYWMKqNf2EFDF57280KlGRDsd7spyuQIhALvpxarQYOb3RYCymXSuE9I6p0Og0sqAOUZxfs6KThy3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4680, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqfaLCRA9TVsSAnZWagAAeTcP+gKU+SZJ/bg8nilfZexS\nv6gpyzzt2M/DwNQg31TtHEPFUFed2rsxoYF0MS2Kk7eDc75nDSqaLnPza+0F\nKe2t56AmZKeSM9Gq053+9QxjT3X5QAo9r675+hvm0G9QRcEkcOC7B/Z7U+9C\nr8gY83bbfUOSdA1RKyFIRqJIjgE/IXnrxe6yxi7/P83wapejfMu51VlSjyPt\nPLJw61NfDei/qp8t/EWKc+hexXVC/SrFz21kEeDArA4lXOlzhjHZkJsdb9DU\n2fhyQLbvZgPT3eJ2r1drz8mW2dtC3tr71C9RycPl/sApLcliszaY5DQ0zNzM\nL/ZfmFeJPQbqAk7HHN35R/4qobBMHq48Tsqq7uo3gtXIRTZ+d6qBAMzPHH2z\nzoogsDUpFW4BZV3ZgCSfuUU23aGD7T8Wcj0WxBcApOc7xfLgCG2kL4GIK1IJ\nAWtlGLGEYGsUXkgM6vKE5xnibyvAgAkGFU6hJ0MmXJnA09ItGTnMTTEvwoY8\nX0AlZzg42PqWpkPsOomEPeLT8dy1HA5qNB5UcmcyN/vS02PtUpauvAgfmVe+\n9xZVFLYzztqgNrEgsxWzJI+1+dojOCUdiVft2W3F94Kw7NDiS6Nw/M24hau+\nq1/YAXFaVwJVtWUcnrdQtl3fQLKiyKim1g+CF3SYUGeYspjm9IUJcA1g0yPq\nHpnY\r\n=joAm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "ad1b9dc090922a8ed5632ec7382ac999e6b8cac1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"expect": "^25.5.0", "@jest/types": "^25.5.0", "@jest/environment": "^25.5.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_25.5.2_1588197003426_0.7913389145395335", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.0": {"name": "@jest/globals", "version": "26.0.0-alpha.0", "license": "MIT", "_id": "@jest/globals@26.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "78cf80edd5d9e016627475fe504bcdda1a1800a2", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-26.0.0-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-Xy6D9ntP/Zn399+KzpkIYCiDVme/3jvKC4w9Vdd13lxLUnXnZ4tF3JnQKtURvMffdvT4BLsBDfoh4aL49JYzyw==", "signatures": [{"sig": "MEYCIQDfHepgITMFBFj26FQ8O8cD27sD7rNJiwjj7eOCJTvv5gIhANFQnPOllB5chec4k1NQ9VLqqVIK7ES+VT7o8JWm4t4V", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3408, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerWPiCRA9TVsSAnZWagAAyEYP/Ak4JBGwJjpY7IETwBcl\n7g/MCQJNDYMhYmwk0TrvF5Bg98w0xldEcbDcE8fdDvT7OmBrlml1H4BrbeqV\nRoC+T4yXum46e+pKLjUdM6TYONFQ1q+Z2bYaNWmOaQg++FFdvSNJUG5vfLl3\nrXrjZGzHoC+8VNAyccCjCBe/gguJYvvjOAl16XB0A6c0Vm2rw+vzbe+JtzEb\ngHhweKLwPFLvFVRlVgm3RoNG2oK3fIpkVF+Q6IYs9ZKG4shHro41js7jMKd7\ngzgQQ05kPNcXZIftO/VgDubxNeY/iPiJ7Y5gaDAWX46+cdDvpXgzjidIyaea\nNITpJSwJ0+nXGu3uFYYlXe/DNifRxZkCI+cT7SmvVNu7LeTpL3omBagXL8Fz\njYhDURkTGkhPMnAHNAjXbvjQ1ReKo3jLiSGiLBiXRMbMK/MgOjhuvrlZqj85\n17/RPaPg0rqCIaCGWlywLyDRL470cuLHTYUA8dObsEOSHZLBg3ZXUYhIzRVx\nZADzFWDcUw7OkrzQTADDaib242j8GJEi8h/KbL5rlojJBD+LROHYGfrpt1Md\nRxu2V4fJTUNr+m0Bd4FfnR7Jeom3Cg4WJp4w7xwrqpJ5YeGCi8coW9WQ3JVx\nEArdU6Wb9H8O+Op6t7rZl2c4l3yo1QF34EF0PVO9+ZrEt0Vfcgx7lGNGtk42\nNbpT\r\n=II54\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "ba962e7e9669a4a2f723c2536c97462c8ddfff2d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"expect": "^26.0.0-alpha.0", "@jest/types": "^26.0.0-alpha.0", "@jest/environment": "^26.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_26.0.0-alpha.0_1588421602500_0.6414594841630468", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.1": {"name": "@jest/globals", "version": "26.0.0-alpha.1", "license": "MIT", "_id": "@jest/globals@26.0.0-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4503bfcd1f1362d2eddffa3cdfad6ab64451be58", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-26.0.0-alpha.1.tgz", "fileCount": 4, "integrity": "sha512-/w3rNsC+ExkSho47VWU03lHSdHG6d9cujF69v3/v0rUsnO750sUMUq01YAge3hHC2hWYTHkxBP4yIfEinLF9Cw==", "signatures": [{"sig": "MEQCIB1NujAwoCxfijsFSj/23xiaMW5XRAaJse7Eyn5q1TNYAiBTvxoZh3afev7B0h/S7lnNSSqXd7de9Fm7MsWRFY+6Lw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3408, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerxH9CRA9TVsSAnZWagAAvxgP/14i6oe2AkQBvzyvrEDe\nBhRTRscD+WW1PTgy9LgrRprZgCp/ylWjhd+k0mSLymuvoPHzMRjih5gIQNdD\nTekk8MlCUpSmLrS8zKKKluufIjdeoX47EoZTgv8eLxjjBdnlO9FGpIYrbwcY\nxhm7UTyh7x7JJ5QPY/DUygAL42ee6HuxeDZPUJV7IyeG4dr+NabmZemhkKXp\njMbhUjzsWmqcqdHGzPQi6ay9LpwE4Wvimi+5JjxAwtUO9ObSJklfaglheWYb\nQK2D5WzFB8A54H/uDBK++6qFTq8K7EtEhrT6nSyOe0SOpN3Aki0t1SlbqU4O\nNkjcEKOc9IA5YSACrA5f0yC/O+RnCJ1afKzxHnK0tK/aRqWVctU4im+uCZEF\nStAkHpGPUIn2piAUSz8IlWMmR6ULYSx9zphp4jgIp8vmSTP2X80eej6m6n/F\nYM9cftOj56eadDhMhEZGltJ4qOlnwPvkqwxKc8aP04iwIqzT+zoLqNyAcNWQ\nM3ZcrNgSdL/SNrPridHA/2CRBQjPRNBTEEwswFFTvaD3PEaojMU0yZ5MT6lm\n2sIIKyWwTL4aS8ClZkp3Bdsdz3rozJvvLX5+IfvC7XTEPzpjN7C3RSajOJBp\nQGU/cTqXOHOjjYEYqqVNIbRZVUwbTPUf/vrdnn0zV0+SyQEwJJ0juU5oXGTp\nkURF\r\n=Lqwh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "2bac04ffb8e533d12a072998da5c3751a41b796f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"expect": "^26.0.0-alpha.1", "@jest/types": "^26.0.0-alpha.1", "@jest/environment": "^26.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_26.0.0-alpha.1_1588531709181_0.36085842091893183", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.2": {"name": "@jest/globals", "version": "26.0.0-alpha.2", "license": "MIT", "_id": "@jest/globals@26.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "abc0d6cf6c0ede9013c68e5cd383e9ff3579cf13", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-26.0.0-alpha.2.tgz", "fileCount": 4, "integrity": "sha512-kNiNqfilsXBknai4WtgyrzbUQWwtY3EhKvLfKNHPlMIaJVsgZ0hBppN7jY7IA5gdSd0FmWUaaCbasX2d/wl9Rg==", "signatures": [{"sig": "MEUCIGQ7Yal+r35Q1iIzIag+nNj3hpPZteCiGLKbFWpb3vwAAiEA0f22nffYwjiOo/2UJUMcMWfcbP1Hr9oDlwK1BgUggyc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3408, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesD1iCRA9TVsSAnZWagAAuLsP/ib5GrjFHU6iXAoe4vVo\n3pRy887DHJnLtibpH1eTbsKJjyZ19C5Z1LKyc6ms8iKupOZ4NPp96YbXKRsq\nshY8xjLX3RaJKLNFe06cY64TURU5sR4dJrkU1ttHYOD2piJfQV31M8HzK1Zt\nS1PA2lYulphGSyRwh3igXxLZpCCyfvstNUE4+b4i9TEJR4PHXsiUxeIwcXrX\n9+z3b0UN/a/RDlsbmkjxZl7VpKC6B61SSbY3zP1n4lkci8nxEl8sc3H6wnf1\nwKmakpi/EgIluQYLJpNfR28dXuLEObWFZksEjJhVFspzpdbmJ0MFe9NyIZ3w\noCBqs0oqcXDc/HTzdZfm7W77zhsimqoSqjteci97ru+K3nIhSK92+YqCw6pO\nxv0kxvr5GpJNHYJ4PtIIFlnR2sRUkpUWf7reZo3dSTQ7PtkwrvI7WAJCAqd7\n2g5+aWZZXCYmbiMCOe439fkcWrZu8zLTbN35wVZongBJupR9tv9CzmsQn36g\nuJr/EtkiRyDCn4Fjm4t/6FEJdhmih5YCCjkZ7Ir45gkobGENQI1ZSjeQ1K1Q\nm1xoxVNK8DW7+dw9Ulhez/SWnHimWauohI+Q1CEJPzAX0ujyKoMabRTcO9AC\nAgpuZbvo1mKd53MUOqXyqvlyOEncpRYfeei2x0KLSDiimTnGIyjOwMR0enNP\nuEjP\r\n=lgSu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "68b65afc97688bd5b0b433f8f585da57dcd1d418", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"expect": "^26.0.0-alpha.2", "@jest/types": "^26.0.0-alpha.2", "@jest/environment": "^26.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_26.0.0-alpha.2_1588608353626_0.5445693130385272", "host": "s3://npm-registry-packages"}}, "26.0.0": {"name": "@jest/globals", "version": "26.0.0", "license": "MIT", "_id": "@jest/globals@26.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5072debfe66f4618e5ccea26055f1b6293b9b0fd", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-26.0.0.tgz", "fileCount": 4, "integrity": "sha512-CqNDh4I/5Y+QFoHSzpeEcgdgqzRF2UdJ9VgVGeuDid8bZdhxd4FQoaCbrdmWnY0GdYiORVWAwcI0UrMQwq6dUQ==", "signatures": [{"sig": "MEQCIAoBLiA68RBkHjRMRh06k7jFQ8AjEeFzutlts2vCzuLvAiB2moTaFWUstpIA6SrSCuEAkEVbgVJeptR++wjEFQClWQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3376, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesFaaCRA9TVsSAnZWagAAYj0QAJAhT2MQQOjzE9piPA2d\ngybthAgM6CkDp14mRaddYMqzyU0NZiZZpgAWA4t7oukfXaTXb8DKOvspDSke\nwP1tREDG9XXVlHmVwhciyUbsHDxbjPam+copuclawjiWNjcuoXPq9wAsGrcL\nxfXNtkXZ6lqI+f42ES3DeE0b48ik+/h7j5w1EJNCoy3h2DZrD/2o7PiWRgxz\nUW7Wy5fdMIDlKJ46CJisg2Iobuf08x0VLRY0t5XpwILwp6fYyhs8BYB2h06A\nj2eonEzXUWOA0wiey16NX6r2oNe6nKr/dMXLrs+muW8Q9OcrZRMRBxedxPOB\ndEzyUegmJJMGuwKkqGc+1oohGBN+GwEphtCVpSKZc3pvfk39QoS4IwxcWt9j\n5q7p+LtdAYDiQcw8iTWONyUGTBeOguwu7bqY0kam+OUhi0OsYcWiMrt6Sc0b\nAfT/9jyc81dMNDhqB7MAe5HB4/AGDpW1T73G39+4DGYvZ3dYqXpYNhPeBhI+\nFS74AG/+jYVGgHaQWvL9qHTDJVKjFoSs1fcStzjTm/V+I+/ru3MUDOU0KJ3K\nb8tDoIWMtFlEqT2Ikzotu+cc3wFmY4f9iLGfudFWNOVQIqb0s0dL7N1BgFMv\nAw0x1c0JdMrDSUk2/O0D98YC6jwbF6eNLG8uWMUvuur0NRYcnnHL1L2mva/L\n4Lgc\r\n=zWmM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "343532a21f640ac2709c4076eef57e52279542e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"expect": "^26.0.0", "@jest/types": "^26.0.0", "@jest/environment": "^26.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_26.0.0_1588614809738_0.2653185192769356", "host": "s3://npm-registry-packages"}}, "26.0.1-alpha.0": {"name": "@jest/globals", "version": "26.0.1-alpha.0", "license": "MIT", "_id": "@jest/globals@26.0.1-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "758df54cb31ad7782e9d1fffb10e09632398e82e", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-26.0.1-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-FAva08j0qg65hbM9pOVvrjQyDyHCXF0Vzm98Qv4mey+bbxzczNBSUhNXZYWyLpDbJ3Ycw4SL8LmjnjzdaXipTw==", "signatures": [{"sig": "MEQCICNZ+xNoVovYv22A+I96Y4nNJ6MTpvNkM25e83MmqoGBAiANvt6YmWDIYZfg/sw2rEoAvWkPst6S0DE/MCHu+SXM6A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3408, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesJQ0CRA9TVsSAnZWagAAi70QAJfCk4lXSX7nOHJfsEIG\nYV1hPkpFQrSTvwAF/DTFon4XZbJr4xCzD1mhU0fQHPrIUYUhLh3kFV2ghVZT\nnGr9tquh1w8WBrDZ7mj8rcXdkUDEKFGzdsx8CSQQO1Su6Nqpawdb5lxONcWf\njELmeHGXvowt+En+1xYN1JjB2eHWJjrC7b+09eM5GflZiIj4V7ZsCny4t7SJ\nUcqZLXHQAm5TtaUbRRcL9+4TCwSlWOOxwbYuWo3O06ajjGwlLViCXPeJfIEq\n0CsT2lohB58Zr+aSdA8CPdRNuGqgdx0e2HkfyM0lZkq+q/mAZ3C5pAmk3iA7\nWUMwB7NVpD0F9qXUiMfb0SrfN/GNmKoaZOPTy0/XG4nlIPiP2YmnpXZXgXhJ\ndjQVvYTlG5b/f4amJEWT51PAN4u0MBHZLWmEc8Wcb3R1txatLmSg3JcGVH88\n0sQRoo+VdzttIaw3GUMZpHG1DG1Lty0PUoYjoMwJv+sH24vPrm5svaDMP0BZ\nu2rPORILRdxSD2wpQz2ZI6PjnprqT4ORyCjbibF1Fxfk584VcDJFQMTghRpM\nnYipXLoBlpsqD9DcXhKYICPGsy6QQKwVZfUD9cNxvYNK1AqW6zklyNwccsPb\nf2NBrffHVEHHQJCBOuASDbxJMZuQuuwlcqSE75oCL7wZ+xvDjn5/gpl8u0bx\ntBiR\r\n=80er\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "fb04716adb223ce2da1e6bb2b4ce7c011bad1807", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"expect": "^26.0.1-alpha.0", "@jest/types": "^26.0.1-alpha.0", "@jest/environment": "^26.0.1-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_26.0.1-alpha.0_1588630579556_0.015083947126639519", "host": "s3://npm-registry-packages"}}, "26.0.1": {"name": "@jest/globals", "version": "26.0.1", "license": "MIT", "_id": "@jest/globals@26.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3f67b508a7ce62b6e6efc536f3d18ec9deb19a9c", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-26.0.1.tgz", "fileCount": 4, "integrity": "sha512-iuucxOYB7BRCvT+TYBzUqUNuxFX1hqaR6G6IcGgEqkJ5x4htNKo1r7jk1ji9Zj8ZMiMw0oB5NaA7k5Tx6MVssA==", "signatures": [{"sig": "MEUCIQDxQiMIDsNzNIw0H8KlluFuA6hiG/p05YQ8SY8IYKL+eQIgbQbqGie79T4aAuUR/35xZi0mBE+VCIIkUM4eRvv/lmU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3376, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesULRCRA9TVsSAnZWagAAracQAKCF1l3Ce26XsEARM5Ba\nxhgvL8S8hBqf8peIPPjqYRIm0e+AqwRV8pbOcF42XX8zSXp4+oQfssicFws7\nxhC3YVbNswfhrYpJOzjyxwpYEt2XnuJ1Kn9/AgwzMB78bjQMmM/n1YUwMJo5\nYAO3uK/+UO61YAM2STzrR6VVEXWy7yal7qr+R/H6cFf3+sRt09ubguJ+YrYg\nhcZbWn2kikBFAyKraQQM/zCKZh2/IeaX8QNNd05d8tQtIovLLOzGYwHHEhtX\nNHo5Hlj2b7kBqppa++RyR3BCILH7i/rIgY7tFK/bKXtaqi9deNegkOpHjGj2\nQkIhAAFWIGbLnEsos5XdBFqhAOZuS6GE9l3EAXG/qDax0O6tqgX5RHMNLJmE\nN2XpRwctk4+R2PZbSAqEj13sxueKoq0lBKKl9uXtSW+yZS7LefXLactnpoiy\nVMhn+m3x0ijCriDZ10crgOey/HHwyfI7aC8T3T5HFsRGXYrL3E2HOYge53dB\nKZU8CEcDAXwfbh5aONWef04o1YAekA/wyNrf4yxHSpRu3lNfjEL0NUrjo8m6\n1vEmiRJ+7eSxojTDnSrOPuHom/8sMCOiGIxDstmWrRP841wt9FI2Qdk7/olM\n7ghA1ogt4195xAwWM3ZEZDoXXk8i8kqVyuFWD4K7ajqLsmFhPla/fo9Y1e9X\nbcFL\r\n=/9IS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "40b8e1e157c9981dda5a68d73fff647e80fc9f5c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"expect": "^26.0.1", "@jest/types": "^26.0.1", "@jest/environment": "^26.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_26.0.1_1588675281256_0.44481329803663594", "host": "s3://npm-registry-packages"}}, "26.1.0": {"name": "@jest/globals", "version": "26.1.0", "license": "MIT", "_id": "@jest/globals@26.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6cc5d7cbb79b76b120f2403d7d755693cf063ab1", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-26.1.0.tgz", "fileCount": 4, "integrity": "sha512-MKiHPNaT+ZoG85oMaYUmGHEqu98y3WO2yeIDJrs2sJqHhYOy3Z6F7F/luzFomRQ8SQ1wEkmahFAz2291Iv8EAw==", "signatures": [{"sig": "MEUCIQDjtanPN+TQ072++LYYrywbqGfHMF6vkkmwg976PnL+jQIgBWB8a8SNmfFrhxZLD7ORNhWBbzMchGHBTNkQNmeBbm4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3376, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8hygCRA9TVsSAnZWagAA1OUP/A3ZKksRMM3oM4BBmBLY\noI4XalThnfpZVMr7Ra/nd3v1WDlmEZLxyK2cdKBmI87wGA7UT6K8/q82V9n9\n5Ss/ZJNwYGRtDl2avScIaE2iLUIyuSlceJUR3Fo6FCT3vg9qnzJoRr7nzP8d\nI4ozE/lDCPlAyQd75O8WtVDwepOhELFE/DMfX5dZeH53zUp+7K+8xB6Hr6H3\nL+7KYcST1IupK0iek03cu0KYH6vV34AZguvTjMoJHFtY7AzUQD+cZdi3X1w6\nZQRpBhRoDUQ3DLygJA/+TdQzNKez6xTxayg9lHqj2xvyf7YqGHUBwNe7kGEW\nuDvjZ0e483A1mZSFqP92IQLqSvT8DnxWHH6sZIhw+O+xrzHVQvIt5uBFO7hV\nRND7q0DVaNBX/cSVMuxeT9x3sxvWXeA2vdp7opR8H4CTJIg2+mXxmQJHf6Ue\nWtX9aJj+o31dV2FHr79/vfUhCpWUBNkSBPrx/IIDEFKLZtnNaRDRFBRs4H3P\nyf1VbeCi2hdeKjZnOen73WCV5+mcjhYg37RV30aUls+B799yLRogqBpabav4\nfBcgQmx/G7wtdx5GDSU9pVtYY9GVR7u8YJwrFTqXSFE4jlJ0AEoznLtUsbCg\n4fprs54HVEwMfwtHjN6kBykTuXd6zXTF1XoND6tEbsRBLJkzpJoEBmYi8LK7\nyKz0\r\n=gXG/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "817d8b6aca845dd4fcfd7f8316293e69f3a116c5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/3.20.2/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"expect": "^26.1.0", "@jest/types": "^26.1.0", "@jest/environment": "^26.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_26.1.0_1592925344116_0.5012942683780524", "host": "s3://npm-registry-packages"}}, "26.2.0": {"name": "@jest/globals", "version": "26.2.0", "license": "MIT", "_id": "@jest/globals@26.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ad78f1104f250c1a4bf5184a2ba51facc59b23f6", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-26.2.0.tgz", "fileCount": 4, "integrity": "sha512-Hoc6ScEIPaym7RNytIL2ILSUWIGKlwEv+JNFof9dGYOdvPjb2evEURSslvCMkNuNg1ECEClTE8PH7ULlMJntYA==", "signatures": [{"sig": "MEUCIQD+yJpEuP5GKV94gZ3I/Xo+eb2a6fYEBYVr7D0aK5dF5wIga1LTPaS/ymW5SkhPdVufe8U7CjgK8B8zNXlZW7FuTKg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3376, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIpz3CRA9TVsSAnZWagAAEdsP/Rnn2HrEi0YZ5zIqmWFP\nlSAbzNWLn0TUTDFIaVePURMTgTGTmaEEDNMvCP/NR/orazjg5lhScsTqqMmk\nibds+WtnK3aL9lDsw+BSkq19vdp5cFHWT8XqovZO+UzK4I3R8xWiIak15QeZ\n67ooh7/IhpmwQ6qiCdiJnVQioBagBaDe43rhUO1fD4vYYbGmBGWkdOaaS4Ro\nh5AeOWilbDzUZsDrdsLiuC+UlkvnO9vhPFZ3L3uXKYkynN2WuYY2JNk4T7LT\naKQakcYix8GYnQ864N4tScwKnfmM/ETP7GIXzk93VQfgj1chuu6h+de8TSS2\nfZrdIqzWan1rd0/V7sFzjgtQl+IjFapeBjdwebCbMrrmc44enBsXh3QasyHs\nOr3QTiMn1pQiaDJKg/nsk269oXWdfAoHYyp9/hl0w3UTCocqo+bPMxbpSwgf\nfzsLYoWkH7mNtaf7IsBmE0NWgFNh7YxnC2PnXObig23GxD5+FD/vIFcreivX\ncbB8vhZ/PxzdA1aevQPc0pryRI/Pl8nurpRdrCWU6T+wct7+p/wkLJDFbRA+\netSNdPlw0nUTTbmw2xMJmgXTrALYZiO8zUbUgwHk1f0VOWwnIGFZ2wADwPeF\nmXY0xa/Ex1YQPwX5ECj8tcWrQkzumiHd5KR312Np5eOqN0C66ugpIP7Wmrop\n+vj2\r\n=fhzZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "4a716811a309dae135b780a87dc1647b285800eb", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/3.20.2/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"expect": "^26.2.0", "@jest/types": "^26.2.0", "@jest/environment": "^26.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_26.2.0_1596103927182_0.8906319620709295", "host": "s3://npm-registry-packages"}}, "26.3.0": {"name": "@jest/globals", "version": "26.3.0", "license": "MIT", "_id": "@jest/globals@26.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "41a931c5bce4572b437dffab7146850044c7d359", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-26.3.0.tgz", "fileCount": 4, "integrity": "sha512-oPe30VG9zor2U3Ev7khCM2LkjO3D+mgAv6s5D3Ed0sxfELxoRZwR8d1VgYWVQljcpumMwe9tDrKNuzgVjbEt7g==", "signatures": [{"sig": "MEUCIQC/fob01v6ZkPHVSKSZfIl3N0wCJiyYmmDg+cEZ8ydZ0QIgIvIFnpvl1WDtJS96SOar1QzeJIoyhvLr1Lw0NPL0Rhw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3376, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfMTA6CRA9TVsSAnZWagAALH0QAKRLbIzynoaR7kpf6aml\nCSwtYIFpfOaaMK5j29JpIck0SvsVqiU8U19wuXMhwJWrJA1gPZ6iiSyUzCE3\nwEyfN3w4lxY5ajPEUuPOI2VpKpg/VNRMUdwk5lRUcMwPx6KwgwUNsDc7zKOi\nJoaF6zNeODjK6hy3gT0381rvtAXDAiB+uR2OG/MLV/88T4rNPk4C+XPWk/Qv\nRIUda/etoRW9HfZQeEkVGl2yykr6kHYctHZQIMsEahj4j/ArgGBBU/6c84JG\n5AuSyyhyEMsq8lzLg23oChMr8oEgNs4/IMVPAJvkAwwF57H9lhQeLyARc+Jg\nOC1c2rD9vf3AfOhA1e06pZPBk6cGOevXJFLbhzqLBBCNtgPZPnGfuVNQW0nv\n7pgaM89F9aSqh9LsGnL4ORhchnl7RIBSFu8w07cvbeZMxI6hE8AR0aQwmkoH\nNrOTECiiuN5Qgud/p0Nh7q3G1A1LY2Tb25itYapVgASj9DSmhU9JXew/rTjg\nFyVVyYbyz9inv8AAjxATQTcu5YkDmVMF2t5V+evZg2h2tSgoh0ZDeHMjfNMJ\nNYHYdrLgARv1TCwoX5lr8XsnQYENX96zVz9wNALmPzQHFGvnjxDjqu5e4EHv\nZPJJa1X2/z5h9KBIMWMariHoCk2HuMP96xvOf/WALzAALc8dC1GC8jPp5Dhc\ngzoA\r\n=c8Kb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "3a7e06fe855515a848241bb06a6f6e117847443d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"expect": "^26.3.0", "@jest/types": "^26.3.0", "@jest/environment": "^26.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_26.3.0_1597059129761_0.5207658364777561", "host": "s3://npm-registry-packages"}}, "26.4.0": {"name": "@jest/globals", "version": "26.4.0", "license": "MIT", "_id": "@jest/globals@26.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ebab3ba937a200a4b3805f2e552bdf869465ffea", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-26.4.0.tgz", "fileCount": 4, "integrity": "sha512-QKwoVAeL9d0xaEM9ebPvfc+bolN04F+o3zM2jswGDBiiNjCogZ3LvOaqumRdDyz6kLmbx+UhgMBAVuLunbXZ2A==", "signatures": [{"sig": "MEUCIA//y4KJm0YhdhsZyFBGXFOW8JipYQJfmTHbV02SnjfDAiEA/T2KKvu4D6vwnr4xR7vbJhuA8qdi4/gnmel31nhGcBg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3376, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfNFh3CRA9TVsSAnZWagAAMk4P+QGh63jQDF7v9h0SAAM8\nVEPZDM9WPnFGBEwfeNdQKgSargYrzpjGVOflLin2Vf8u0ck7riLPT9zHIYZv\nXTr2AKFa0YHykp8aeQGAbkTASwate0l++LWV95bPuuA7NyX6e1U1Qs6d3fe/\n+I51AyLKkBiSeVqedDzqI+GDyDRkqniTLaZe+8eJhqjU3hZ+wI+s4jhZkgy4\na1IPGGDoe8DrYuBNJz2h/9NaovpdJusmsWePxbiLOrKYRkfj/Wq+BCgrVyUG\nHdMo1TBekwu/+kX7j9OosQ5M0QaNOpi7rQYHzhNKv3twnStSNOrzHHwMN3pW\nEwRSSbncxzwhDIhs0IC4+Z4CgoWKuZQ+gcDrYPN+H8Vc+GhSgwlV/g0RPK5S\n0rGqRc87MqtgH2gDi8OLl9GSGhm5sRiJqREwYyyMNmp1/RUDO4bnfggd8Q5c\nimQ1CaCyyQxLNlpAxJCssZDa+9JzISNOAR1+c/oZE34pAH6XiWTqUTv52kFo\ndbmDrDZxDTMWMLW1wr0gGX7HauDgMF1fDBLq3In8tODfFMw5I5ATbhGfRoUs\nf31mSPpY3UXVfagPjECrH5C8alWMBg1bIAfpJyzzyBdw0vE3rLEFVy2lM5Uk\nmicIdQS95A42J3M7qdXkzDmpTsCw+H7RuTYRPHbtk2g+V2T4BzWdxjR044Gn\nJw1v\r\n=2cLU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "0b1e41d1d93ce4d15646f4a39fd5a7ffae5f43c3", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"expect": "^26.4.0", "@jest/types": "^26.3.0", "@jest/environment": "^26.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_26.4.0_1597266038867_0.2592833633534106", "host": "s3://npm-registry-packages"}}, "26.4.1": {"name": "@jest/globals", "version": "26.4.1", "license": "MIT", "_id": "@jest/globals@26.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4e8f6721f081444eda86a7c3e4ceefcf2baa5de1", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-26.4.1.tgz", "fileCount": 4, "integrity": "sha512-gdsHefnwjck+AwDUwW+6rmctmKEcZEEZ4F3PB5kKnub7r0dUoN1KVSyNRXtB5qpZgRYESnxgDXhpw/XYKIsAeg==", "signatures": [{"sig": "MEUCIQDhtrZlp/C13Y0Bj60+CVpKk5VrWga1JGzzEBHFXte3rQIgK5aquNHbuvbwlBoms22HlQPxnHwsWunvSa6jW7RGWS4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3376, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfPjTwCRA9TVsSAnZWagAAMNcP/31AxjAs7RT5b+6gBw45\nnzN1vw5BO5XQNH7U/n/EjCKZA448P/8BrJKu/VYwG62K/qHQ3DrNupsGTe53\n5pmNjBt8hlXBQxgJ8Cjs80Ha6xmQkTcR3h1B9LSXybjFKyZv0WDlQ2Y34QZs\nbMg2f0yKBK9OSl/iq+faoMxDf+GUuIJ/NV7jjbckUKkYT7tui9GP4+dAL5tn\nniZjwFoo8nMbEJRA6oWVvinnSAxsbLHYppfOvLQBvxTbT5IKMdsKQu3F6Xrj\npZ0AmlYbG3puyNXl7jdbwKJUREbl75WeeEGTQghoXHCNc4NN9+Vi+SZilKX8\nZQyDbPQ3cnBj/ljGysLvbcX9QzXtDUpo4FzDWsJdM+c0B+0WM0+yCKrqbUOd\ng1QwNyD/XkmJ2TDAPNOqZ2W2AvhgujILSpuYxv+GeiD1Q2+QjAtS7U/OWVFW\nZqrwaQmJRxfN2aXyaN0mjut98VsdeUj9N8oahb4JIZaC1yosXnuLhIlINuO8\nh3I7YOjNuDT2FZgr+0EOo1swC/Gbdt2yRv2ZDZBzqp5wfcO2hr7kHS56YkBt\nq4UU1YrwglfoPa8zdrjtHPdxN/2fF4sQuCzAP2whDd6yr+USN6RChsgxMcNL\nk904Oq5QNu2pXaFDStrUw9mQUOKj2vT8EySZZuowN2oAE7O0F3V5C5tQHJfp\nmkSs\r\n=Q12H\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "e1d51deea8d089a06f28b4dbe9287a4428508610", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"expect": "^26.4.1", "@jest/types": "^26.3.0", "@jest/environment": "^26.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_26.4.1_1597912303681_0.5145410370094037", "host": "s3://npm-registry-packages"}}, "26.4.2": {"name": "@jest/globals", "version": "26.4.2", "license": "MIT", "_id": "@jest/globals@26.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "73c2a862ac691d998889a241beb3dc9cada40d4a", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-26.4.2.tgz", "fileCount": 4, "integrity": "sha512-<PERSON><PERSON>5ouAlehhHLRhc+sDz2/9bmNv9p5ZWZ9LE1pXGGTCXBasmi5jnYjlgYcYt03FBwLmZXCZ7GrL29c33/XRQiow==", "signatures": [{"sig": "MEUCIQCX28tnXxDc8hHOthH01qs1dJi+ArCihJrNUj6sZK7VEQIgds0SyQrdaHW8R0rWc3EmkSUYLrkXKdKl01tNrqzdK8s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3376, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfQQskCRA9TVsSAnZWagAAX7MP/jUwW+PYVuobj5E8vMQL\nMIe0jY0MgaV1iWS7QsIYqBlaf+tbprDXvjRMllCVWXDfS6w50vi0fOxIdiyD\nwSSau2/WGXn1a8FW1dT7TVcibiwXEFS71Qsir+Q8ns1QstGkV3Dv7dy2DRpW\nTqM2DcAarx1lEz5Ngbh9zSSRmumtBsnkBIAOey53hT6Magg+fONyapBG2oSL\nlhMollIhuRrDizxoI1htWw9MBLlg3tVVhB9CbWa2WEqfeEZlcqbAgTddnix8\nEH6pf/D7XrGV3GJon4MkBWXKmrDRXACVAavfgG3me+bqfEoMLFwqMmvXPvOV\n+oPlb7XpeMaM60ITVvBbnLA95t95PoPOTDTW1qVwEkNpaMSPCkW/Vgh1xJE5\n5oAdV8roxfhdrt/WGdXzz8dL+BtY277HwGgxETcdzTJjwV4OqYRMDZkWZIOG\n3RFDSLNTiLTJllJir1ELLLGMYsIV21xKBPZUobgG219yXgk7B9YmELRZ4i0v\nOXIH8mUPETKIXb0Zcu9uT+OrJv58ISUpwQh+c96JiPfs1N1m+/KFXDqJ24xB\nxSZ7XJ30NJ4bHy5zr1L7UAhrCiqqZcRjhzqwv4c8ZaKbXpIvz3x2cCQWsj1f\n12EMve6rt//U8vNGjsQkCnwm3zp0Hetku3AjeNlCrldGUUJpYkb0Kp2y/dUq\nMqee\r\n=Zr6W\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "2586a798260886c28b6d28256cdfe354e039d5d1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"expect": "^26.4.2", "@jest/types": "^26.3.0", "@jest/environment": "^26.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_26.4.2_1598098211927_0.6771415592910912", "host": "s3://npm-registry-packages"}}, "26.5.0": {"name": "@jest/globals", "version": "26.5.0", "license": "MIT", "_id": "@jest/globals@26.5.0", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b9b7d05ee6722c894ce67aff216ed6b04d3fe187", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-26.5.0.tgz", "fileCount": 4, "integrity": "sha512-TCKx3XWR9h/yyhQbz0C1sXkK2e8WJOnkP40T9bewNpf2Ahr1UEyKXnCoQO0JCpXFkWGTXBNo1QAgTQ3+LhXfcA==", "signatures": [{"sig": "MEYCIQDQFD8PI70NmXrN4Rl62qUIFbkVDFb2YQ2Ke9q54L6cFwIhAMSS33zZi4wU8dchBM2sHjPwPt17g1nXj+7m57AwS6hB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3376, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfeudCCRA9TVsSAnZWagAAxjMP/1JC7BLJ1TPrQ1D5R8GL\n8xdleJu/mEMTtDAoEBqbpJAaGdsyabVJUbBycM2JGaOPmrTmfSOYFEOKmOD9\nKxbXvlrKuUEp38RbwBQQ1KCLDLPfFlP7siPi3UuGzBzMogr3nj/E9pNt2RdF\nDSW0rk28hui8BnTedDjHOVi9g5pDhfQWWudzAtO2cDrWgsO1u2VykZy1FUux\nJ6i1JocCh/CneCHzIj8Fdf8plji3AqZ7RkACdHcXuVTb0FJZcepLLKDD/DWZ\nSZMMXHf+8yzCrGL1uat7/XtXk4D8m7ifnR1G9wOB/gt+OO5PoQT3leLRm+JZ\nehqJqUhUEY6aj7h0P8mbevAOMK0W6davEIf91C9pYSC86oJEjbAmXPH7RniG\nNLrhMj5Sj+STlmQn9RiP46DBatlNInNvwnUrGumj+D8iIN7+uys8iS5fl0mj\n0l1IGzvm94cHgvugnBu1jd6xInwUJUZ83AVa6W0mFrpd5mUIEe0k0G/2ncrJ\nkL/Ouig7ckCifBbK41zASd+rd7ruBBwlLrLBLa+BuyyyIj+pfgjwfXd4s2tP\nT9SgYqqnJbqQUx/mH1BIzD0qU0JWVgV3ZhnAndAej+RTTR3D21GB318gJcXi\nF4ygyc4WmzA4BtLjwx78rIDmr1R7j+H35lfNighUBi0grv3mquBZVXulrXnu\nWjxn\r\n=uz5m\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "68d1b1b638bc7464c2794a957c1b894de7da2ee3", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"expect": "^26.5.0", "@jest/types": "^26.5.0", "@jest/environment": "^26.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_26.5.0_1601890114238_0.9086891249863451", "host": "s3://npm-registry-packages"}}, "26.5.2": {"name": "@jest/globals", "version": "26.5.2", "license": "MIT", "_id": "@jest/globals@26.5.2", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c333f82c29e19ecb609a75d1a532915a5c956c59", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-26.5.2.tgz", "fileCount": 4, "integrity": "sha512-9PmnFsAUJxpPt1s/stq02acS1YHliVBDNfAWMe1bwdRr1iTCfhbNt3ERQXrO/ZfZSweftoA26Q/2yhSVSWQ3sw==", "signatures": [{"sig": "MEUCIGDOLv7oMoejZZHjvxfTa284hPBeDh1MWSbDYy07E/txAiEA1IKKzLPnTVEJklBDDs5Tc0mOtayvHUS3nGWp1AfaFl4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3376, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJffEyYCRA9TVsSAnZWagAASGIQAJKPqriU1W2izHVjV73+\n02UpT0Ta84r5LdzzShtXsoMAdLyWTNRCbQyWzlXDZFNlkT/iIX3F9heFrmqE\nvoL5Xs03q5HVGFwjOmu76UKElkASb2XYRLyiKHIbjLrqoN0ybH6+ZDRk6HTB\nBV12JLbQ51VfF3mV99f0VEIUSq8YriNp/LlnonVRZdex4MtaHDEWh13+HmMM\npTp7F6EQ0HKKnfkWGvf7pEx4ZlnAO+pnKfSv5UNsaE3NLep03Tx1t424eBRv\n93HaIkIRwdSzReq+ZHfcsdZmbpy4nKgfONYidfz0dfVVuBIN6dchW2aTIv3t\nS564lVyspM3PKt6y+yzB8E6xNhkp/+XP2ucqs3GBbfX9ooM/FtMFupDnTkR2\nI6G8w1KrFBm4PPS51H0kE6A61YyWqEiKDyp1owDfnxycMxOoNZXCK1wdVenA\n1WQRRGfFGWaZ4zU6aWIWSg7C82bUtyveWUfAHmEtDeHACd1Sz7c0w6xU/lQR\nGOuzWyyAvj9jU0hJnKYRJ6z9p9K9SEgn/+f1RAazavApT3ufdxeb+UvqsSJo\nWUQ77srsLnITUujiyXars8bb2bpi08CE+7yRSfYwVSO6/KT99FHGSct1de7u\nMwqByRfGS16juetoes1ylV15JR/4GsbUR3C1TJiV0/FP/LiPAbSQnN1jCdEN\nYedq\r\n=qowY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "d2bacceb51e7f05c9cb6d764d5cd886a2fd71267", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"expect": "^26.5.2", "@jest/types": "^26.5.2", "@jest/environment": "^26.5.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_26.5.2_1601981591813_0.19367034432706842", "host": "s3://npm-registry-packages"}}, "26.5.3": {"name": "@jest/globals", "version": "26.5.3", "license": "MIT", "_id": "@jest/globals@26.5.3", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "90769b40e0af3fa0b28f6d8c5bbe3712467243fd", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-26.5.3.tgz", "fileCount": 4, "integrity": "sha512-7QztI0JC2CuB+Wx1VdnOUNeIGm8+PIaqngYsZXQCkH2QV0GFqzAYc9BZfU0nuqA6cbYrWh5wkuMzyii3P7deug==", "signatures": [{"sig": "MEUCIG9LOWFPaHOE7ZGFdYzlNLI8oogTIIxjetYESlGA6UjSAiEAu4COBDUT55NHRNQoV2xCGeog971yx2c5P7nCszBupVA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3376, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfg0WiCRA9TVsSAnZWagAAKVkP/2gWVtwU60iKWglXt6zg\n11l1lIlqErPaIVG55zrmVXXWJqPCggo+D/AwHw7fVp2m6mJGjB96AjRviaak\nu0FcqQo891oc/ase3MPtyMtGdEldo8fcTxc02ZrFGdseQ4pPF3Rpa4VYl0ep\nNQ2KJzBLYBjLdTkf0edQ1C8EnUUnMS3FYoZ+vL3qsQlAePoOXZZpVGEWO6ob\nb1+8XheUbYJwcHa9nejtDlZsbNmaA3WOz+KV6k6O+6uVF4JUEOK509qZGKn7\n2nYFMgALZY0JxjbTCJErreP/wFiopxClEQmAn5RJJV9yHQeeNKNgDYQQNWNE\nzj+Jh23wSOtgq0tv+p0T5bFBgGBJ6hJz6/OA+utaa1U3BVdwX/Yq1S3z9uCI\nbH0DbMm24lZOIF2uYYlXz83JJqSIRQM+Z9aJ8eDcyAyyOsmQZZjzVrxF+SwQ\nZJeWog3hq8bEV/D0rWctybrTK/Fpe9aue0aDhpJ4DSdwxS92TLBrrKz07Vg8\npv7RI+ijn7ZYkHk5fy1Bv8WXqVV7GNAshXVuveBu8kY1A5DZmTb6Fs14ckW8\nR5dJM9hTPcbt/F8NIgCCrpky3zsJeMJrtin307JdSisoHhhwBDQ7uMmOxmVk\nXk/EXJ3rF1v84d13OnzOcwyR4qRifW6qd6IIWDFwISkVtgQ2LGtyU0vqiqlD\nwA48\r\n=u47a\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "71152afbbda76fd09ddb2527b54c365d753f42aa", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/3.22.1/node@v12.19.0+x64 (darwin)", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {"expect": "^26.5.3", "@jest/types": "^26.5.2", "@jest/environment": "^26.5.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_26.5.3_1602438562048_0.2943750328882271", "host": "s3://npm-registry-packages"}}, "26.6.0": {"name": "@jest/globals", "version": "26.6.0", "license": "MIT", "_id": "@jest/globals@26.6.0", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "da2f58d17105b6a7531ee3c8724acb5f233400e2", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-26.6.0.tgz", "fileCount": 4, "integrity": "sha512-rs3a/a8Lq8FgTx11SxbqIU2bDjsFU2PApl2oK2oUVlo84RSF76afFm2nLojW93AGssr715GHUwhq5b6mpCI5BQ==", "signatures": [{"sig": "MEUCIGFJV29eb1F8xTsI4QivDWb5i0o/akIBWufB5QaxKsG5AiEAhgnRv9/GZ2gZlsxRes4mERysVF0y/82Vfr56l/FPiLI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3376, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfjX+JCRA9TVsSAnZWagAAuhwQAJgwVsxDNBBIYek2Mnzf\nJx669L5gp/GTyykgVCDdcxY5jj1fOQoziKn7EpmZ3Mt2sksvJm0Nkcdl7X1w\nBEjxKvMcCeYe6ASpxHkjYADvUZbL6PAk0agDlfsfd6XI37WVjX2NSo7zdr3x\n9o14ZIqInBWDXV9MU5jtxNlM+MWZeynCQE5GaQCOtTBqKkGr+ikBJlJIQUfe\n04KzimTdmuHVEx8l6+pGnKhU8jLbEwlTOSXwVzREUY7w0vCZbzS2JwnarfLP\nlw16WdSzE9+BVE/ysONmZj97q+tEdWJXxR78S6NJ7CaIUwpVzNCy4Sa7oM0Q\n7nl+EDlKXBRM5R72sRFS5UerLtD5KaZpJ5CkMsRb9QSAO0Rpm7j3Eg4E1n9m\n2+wipPdr8Iky+Fk/cgHxdQCkpLjBS13ca+qtqAvWaR2irFv9A7l84qcQc6ga\nhZa0g2sXWHzGyBIeezF2XnUQosXaLXyJ86Afvqz1omrTpvzctZLffvMYi1RZ\ne+MhZRAowM66OPoOaqm5Au+oDs/dSJh1tbSi55NOY3z5MfjGxVRZ9LDyc7dG\ndxUCXY4JmyiZCl6IOABW9nKH4a0nOU0ETDl8H2xdF3f+TJ03ZnfrFcdc5pmY\nOwXEVwv0lbO1ik34QMBHvCLgFRUhH1poABimivon36EAKd9OpEXH2AyTCI9W\ncN8V\r\n=aOm6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "b254fd82fdedcba200e1c7eddeaab83a09bdaaef", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/3.22.1/node@v12.19.0+x64 (darwin)", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {"expect": "^26.6.0", "@jest/types": "^26.6.0", "@jest/environment": "^26.6.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_26.6.0_1603108744844_0.4239822554298216", "host": "s3://npm-registry-packages"}}, "26.6.1": {"name": "@jest/globals", "version": "26.6.1", "license": "MIT", "_id": "@jest/globals@26.6.1", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b232c7611d8a2de62b4bf9eb9a007138322916f4", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-26.6.1.tgz", "fileCount": 4, "integrity": "sha512-acxXsSguuLV/CeMYmBseefw6apO7NuXqpE+v5r3yD9ye2PY7h1nS20vY7Obk2w6S7eJO4OIAJeDnoGcLC/McEQ==", "signatures": [{"sig": "MEUCIEbWjG+RGSYpMDXWz1a6t5xEXUqlmerynx8KDPh3/NVqAiEA1cyWDa+sys4zI6zTdPG0KLsv6RSOs0FLXExgtzEkMdw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3376, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfkp0xCRA9TVsSAnZWagAAu+YP/2PgflDAi91PBTMws/hx\nY9BkUChM2MTql+7si9TfcRHuPxsHLzQ3pl0AMylEnl0k5+7NtAxdcJLeouMC\nZKRfFRfp760Pj/F0UYMmQY+gXvviMkwFZKtIUVNua+ncg8ynZdJnUsq8h58m\njrEzc708hvsVFHeSPIzRy8kzCXm4+FMaRWcXq/md4Gb9aQntXSE0IySUGb3W\nG9jzwhL6zjiumxDQmO6GTMjxQCa8cVZy09Zi4hauizAlCcVgyjhRuX7OH6nM\nbIB/g/zHlMlCKLns1y7qQAMSoYgkoPYdOufghmVKcbaE3HqQps1r1KD7NQzX\nZcIZEFsE5A83KnhbgYYPcqCXeaDPl1MHjaNLbgradrPuhQOheVeq55uJn5zq\n3z+3pxrUA0kQJuAntADMkgyzFfj0zKQxo5j5Big7X+47NlfCUb8kDsnN9sej\nzxpK44SFFo9bf4WBFSFNvkFrOvqEOuGUn63swuz4lXFJNgvAklfe4wmbjQNq\nKQTMH+vNbcSqIpwGypBC2A0Mh715OS1P12WkIzdz8Em1peWclqLZd2ZpmpI0\nV05DzNPH/5orNQkBolTYA2n5BbYhdjTfkm3bWNCFTd8MIT8iqnsL1qtvdhx5\nBBBwYWST7hqbntDPjVyfAYMbIVuyZtKkSFNUe+Z+i4SwNURxOc42R+uooY4c\n4yY5\r\n=izYD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "f6366db60e32f1763e612288bf3984bcfa7a0a15", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/3.22.1/node@v12.19.0+x64 (darwin)", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {"expect": "^26.6.1", "@jest/types": "^26.6.1", "@jest/environment": "^26.6.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_26.6.1_1603444017001_0.8234297589003661", "host": "s3://npm-registry-packages"}}, "26.6.2": {"name": "@jest/globals", "version": "26.6.2", "license": "MIT", "_id": "@jest/globals@26.6.2", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5b613b78a1aa2655ae908eba638cc96a20df720a", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-26.6.2.tgz", "fileCount": 4, "integrity": "sha512-85Ltnm7HlB/KesBUuALwQ68YTU72w9H2xW9FjZ1eL1U3lhtefjjl5c2MiUbpXt/i6LaPRvoOFJ22yCBSfQ0JIA==", "signatures": [{"sig": "MEUCIH7fOmzFf8a6oHyalTfrMO/aidniEFWzXlg+ei9Yl3x4AiEAyUBw2U/SB3GO07jixToAUYMGj9/5HPijig7iHkolO6M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3376, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfoADqCRA9TVsSAnZWagAAwZEP/2eW7e/2Cf2MTdP+Kkyj\nBcbwcsMN+0cQZi893vkY6PUTzaI2xT57nfrmeNx7UMtJcWEASjY92XySdTnY\nRw4iijAHKsUk4PYVb/5mqd0Hjnk+hDhH22lEOBCgHm2KsAkTrthYUHlkBbHg\nYwcld11FCqpDyiG2sEZdUmgScDAz4WE0XWleuU3L04/ZX6eq8XmMQPEq4YFc\ntJXaFpv5sJyFjYHEApI4xIqcfm8XMKi2BmR2+0oUpXP/Xm0XlUtJA6jqu8+U\n8j9vCh2Pg8L8Kd4Py55Eh197QvHdUDHYfiDp1GmlfoXVpKut+9X15frMAR72\nEaCVXHudNf7avnoVoICwuPBNrbGYZ5T7/Ki/GViNBfxpikJZveBdJhfdxX0X\nannnzQP8SYvzHZOJDRIQliYGhtnS4Ot1Amrf5UuC7LgDIZWP8WwMLurLLd93\nEZ3zTVmJajgeUEw2KnGO/POxTXuYFx3yl05tuEOw+eSJUYbTum99+jPwV7pc\nS/2uPsN+EfAEpEx/4vfsZmK/Jz6Xl058UpzVI7UelVC9eo/qFLgXzz3rtCX4\nisvHxLXi4yOztm3iguMkfhisKSsv4pFywims8oJhSlo4uukh4+mM6Xd+m2sA\niXn5PfmS7HVgEpmiMKDRBRswPO1X2Go6+srWUR5wjH/Zmtv7DTtcn1ZB5rZL\neyUT\r\n=767B\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "4c46930615602cbf983fb7e8e82884c282a624d5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"expect": "^26.6.2", "@jest/types": "^26.6.2", "@jest/environment": "^26.6.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_26.6.2_1604321513950_0.3401030633173945", "host": "s3://npm-registry-packages"}}, "27.0.0-next.0": {"name": "@jest/globals", "version": "27.0.0-next.0", "license": "MIT", "_id": "@jest/globals@27.0.0-next.0", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2ab36e20d7bc7627642d5cf35e53665ee7b6e509", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-27.0.0-next.0.tgz", "fileCount": 4, "integrity": "sha512-B3AiUd3SaKD1692G3Lv29SdtyPdBtnZGJLymdy1XrZMSJizx0uolE8Cx7aePYW6TxKYKxKANRqAHabAhbXemEA==", "signatures": [{"sig": "MEUCIHsiEsHAPGI+ufZJKu3smExmWpIYPMoEAt38itqC4XcGAiEAv0B60Y+XwZXXWl6BXohURCAf07xz3AJ6RAMGs0QTs5E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3530, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfy8KUCRA9TVsSAnZWagAA7RgP/0XbrNHL4iw3FlTToYet\nE7TnUAu4ldjjzOOWlHYMPWKZ3eaGPnJ6f5NDbOnQpctrVofpICKNDPyr0PlA\n2IXHoEfvc3kyCJN95MDUSDOYDRU6sJ1VzM3RIfx/i3XTS9nlDmG1nQVY+vZ1\ncNVCNChPi5qeINj+r5YVlJ53mMu0vb5f7jT1JKcR01nGkxVYj+dvgxg7fyZk\ngN/4B8H8kz6zLHSTjdvGHXUHX0G47N6XUtvwUd4mFh2pt9kduV194qPTiP+N\nxi5/s2JDOWjR69D+wVhaLwLS1ZwMlle5WouOiWh5k0lthhGbjZNNBd4mI8av\nQSOHHqqPaBj0PN5ATmYzGm+i/OzdMdkpYwHw1k3pNAfY9SQKA6RR0wcGga5m\ngLjPwCOm7VEhD36FEe6VapmiCTDy6q6larW121BhMXYuQI3qqu7q6OE8chpF\nRhqYdpVk2U8OOJXd9FGsbBdcrg/7SNI6RyaosKOzVbW4NP9H8DHc8KK3CHHi\nhk3KA3L9R0e19dQzxjbohlKVlf00DtjfoF7apQmkYHuIqUPMdnqxxEKqNyYB\nFqaPujSMYGm4VxL0ZCMG0nXGXQP/Vz+8bAuELQArR6SvbS/3S5nyv831NXIN\nFw/giXdOw2jbglOloYgkAceynPiu2ta/rHD3NSy/an4LgCh5t9giD6ZjxnhR\nlfCp\r\n=wMqa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "4f77c70602cab8419794f10fa39510f13baafef8", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"expect": "^27.0.0-next.0", "@jest/types": "^27.0.0-next.0", "@jest/environment": "^27.0.0-next.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_27.0.0-next.0_1607189140356_0.25438779173640147", "host": "s3://npm-registry-packages"}}, "27.0.0-next.1": {"name": "@jest/globals", "version": "27.0.0-next.1", "license": "MIT", "_id": "@jest/globals@27.0.0-next.1", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "46ec2f4285e844b03d163fc72f7f24f91c6615ae", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-27.0.0-next.1.tgz", "fileCount": 4, "integrity": "sha512-iklr8SnXF7cjhjMih9sBvVQfztciW5xzEaWvm6PwxQO4KnHaEOku3KdSLQ+selzfvgEw/Kryj0IDvreObPu5tQ==", "signatures": [{"sig": "MEUCIQDoYqqvFvCZHBGVbdJoZjEOZJK2+pHJ9M/e/rAvK52K3QIgWYSkk4/kKv/PqQvIgq0PRvuYXZNoe81N9MBaIcSYpho=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3530, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfziOACRA9TVsSAnZWagAAPpEP/27/8TJuttu8L92QUbXN\nztCyev8l/kJs7c1miaOS3OJOntXSYc6+5ZY4amIkEOR7VFxQfKNLNQRAoMB+\nfgzT6hISQ0WjJ9dYaaaVcMYbwN/pAARGUNVT8qpBVG9Q+xjE5TJorwnI38Ec\noftR4NyyPZK5lyKylp/cn95ecpkI6j4BOR9GWr/Pmb8wH2/UQNI+EypxmZth\nNmYWfJSXywxWnXgAUc8LvQcNC9LAtN0MVP/13XJJf1m7ZrsMlALCyiB4+H6p\nf7AfXsPLMsQNlc7RFEuH3FWTJZ2HA5wv7yEIxdDrudwUXG9hTdAWFTKCAqUt\nOggAt4qpURlwaj3+54uIw+Sd69koJihw4MfAxhc1vquhHDc+cavthrHxfRRG\nlfmyQiOoJKleJS/j57TDwIZxz82tWVKgACNpz2yqLxMEJOQvCO/gsLyHqkAS\nzRul+Trzw9SJSSSfyYAh6nV8fru8iwyaBIZp62C+uLSeej6aYYqUoDvQJp7c\nsV3nkdIODrwHPLsF6XHK/UJ7mpy2UCoXZS9JREXg6/4vXyP8Gywz3gu5sd7H\n5cMKr6hihww6jqGaAcI9h0x13K+NRXTFyWspNOM1Qv44vanPmh4vym7hPwDk\nJIf4G7javiDNmHZ3cqLcQmzPNaH3ZyhLzHSGxabQ9rRwJE4fhqMSD+djQCmo\now82\r\n=hr0d\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "774c1898bbb078c20fa53906d535335babc6585d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"expect": "^27.0.0-next.1", "@jest/types": "^27.0.0-next.1", "@jest/environment": "^27.0.0-next.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_27.0.0-next.1_1607345023762_0.162336455832798", "host": "s3://npm-registry-packages"}}, "27.0.0-next.3": {"name": "@jest/globals", "version": "27.0.0-next.3", "license": "MIT", "_id": "@jest/globals@27.0.0-next.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "85bb6ec377abdce95f4d486cf59a5defdb735760", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-27.0.0-next.3.tgz", "fileCount": 4, "integrity": "sha512-eUefUaQrPZJPAt/xiRH5MCi/qn6yC0hkMxX0t6ceRliKsHBJjUh5/fO8dV+FkzZVMEVchl4iFrsykYdJP+QC3Q==", "signatures": [{"sig": "MEYCIQDTdO3OVn/S9WXYlSfkVYV7o+AAaWph/GzXIwSwTIBrzQIhAK/CGyqifxTXInupe89q89lhnaWpkhO5V3wUoe1DJwCH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3530, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLuXKCRA9TVsSAnZWagAAEzcP/i2VAxvMwXpFXHVQhaAb\nwFhYNj/U9TudEJAsxe2ORNw8E/3gbT+e3NFetSk+d63JuDwGq3+EEMTuIYRO\nZT25LTmmuGi6zCMIFF+1rxruIjwcMgW8+Qlcbp7P7tU6T/LsnJ9dAEK4RMOz\nNY5GWodCoNNarxShIX/m8CS6THfUIPkkDKaRXjQ/HhYbxEqYaayva2aYgD+A\nUdw+Tn/MCEF6VaVJdiMRULY5OD64HMwABaHXrBBroGYMsM54iT6Ktf9yO1T/\nPMqyynD0Or1GwHYAGFlbPqhbpuhPuLulD8cKlAfCxmHX/+/3e4BPI5aSc/Ba\nPGpp61+PrThT6X8boY7fusxa3qx4YHNPyx1mwlivvIkaAgN3taczXBySqXa9\nd/CeGin4HMEljnbY4MkANzUhxxSu9WTug3nLPlXd4li8bwBPnlK97HF9+KN/\neqojoaTh8MXbhza6QTq4lAh5a5p8kPX9puafwXwLysmoDT1TU/PSTVZQ5zMB\ntp9k34sjwRYi3CVHopFgX0Y3+CHzBhwdHG8fwqDHST0C6/hk4YMImVObMEwX\nrN6u0VuF8Fm8ofrqq5FzvUdKhXUg5Ul06wqZsDcGkN0a/avuo0+oKMMM/GTA\nIoG9j2vMzoqOsmTqZTlMv+VVyxgGZKVYEaurQ2cuiRT4q2kxCFWIoBGf2Da8\niNFR\r\n=riJF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "2e34f2cfaf9b6864c3ad4bdca05d3097d3108a41", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/3.22.1/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"expect": "^27.0.0-next.3", "@jest/types": "^27.0.0-next.3", "@jest/environment": "^27.0.0-next.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_27.0.0-next.3_1613686217867_0.6914003404883629", "host": "s3://npm-registry-packages"}}, "27.0.0-next.4": {"name": "@jest/globals", "version": "27.0.0-next.4", "license": "MIT", "_id": "@jest/globals@27.0.0-next.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f65e0f9f08464f2813a7cec665c6b1a8213979e9", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-27.0.0-next.4.tgz", "fileCount": 4, "integrity": "sha512-daRh+uqRa1QinY4FuyKa0gBlh1iAwbiBc9THsaNe+0l4HO0acWvGhOp1yq3DfuCg1zz6Vv+EjhxnKbOtZlNuzg==", "signatures": [{"sig": "MEQCIDI7/r57nFW3RufWJqG5Ei5DJ2Hhs9PxvulABzp9vQrtAiB9xik3KDe8kXP1L17tmjWeDdT+n6b85tLJd7mKh9Lw9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3530, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgRipqCRA9TVsSAnZWagAAgLkP+QGthp50YNfwMZauDTSS\nZMk/uPb2MKP50GFwxCwQtKAQhKHdPgrjhyigqDe8B5IUqyq1yKuiFnwwrImk\nhoxgHvoSTuDYZ9iGA/xVt0xKFRwA4dgx4S3Hk04xTjnC46iDxPsFeovvQVaB\ngd+PyhlVMOi04SU9pbU29xACyX4XdNCwWBX36enZ6OMRuu4kc/atrS0uIyLn\nxdPYYYLmqIgBnJ8W6F3JGb4qu8sI1hKeXyfbRShfvG/MuXEXKmnR3dV61xcw\no90Ly9YU9KW1VefvXn087Jdjc/KbQiRPRO9BGeVras2Dip4RfMUHp2EVXhN+\nCA+WAXpNdHSWe5uZ5GoMDONhXI0uw+uTlEBBGfjs1D8oK1NqMdiDx6ePcY48\nd2oWQlw02v8YeRFIQIe9xbq2vIY9mkxLLOxvZTtpKQODlKZRhdbaMkgjx8TC\nvlydI2HuA5Kk7MEoudbc4qR709yRt7OffJqdlsuqXRfmPRwYeC9a8vkcSQBX\nvM1sA7pUYEEA2jGAy9MfHCG9zg1MrwALgLoTtC7z9LdQIt6InmR87UIgAB2A\njZBW79ADFB4XERMnAUqUeW2ucAIvA/4DxBA4uJXEApLDXjMVXBnxn8STm7rH\nDkePdwf5i62QBCCHLBDtCWbcIArmJ5fU2yvGN9x7arXuoQQ1g3nhdAoSanLA\nJA38\r\n=Lief\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "941c414f0b757fdc619778c46c21fda26b3e5504", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"expect": "^27.0.0-next.3", "@jest/types": "^27.0.0-next.3", "@jest/environment": "^27.0.0-next.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_27.0.0-next.4_1615211113860_0.3435661273758439", "host": "s3://npm-registry-packages"}}, "27.0.0-next.5": {"name": "@jest/globals", "version": "27.0.0-next.5", "license": "MIT", "_id": "@jest/globals@27.0.0-next.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a063da9a63d51b21ee1dab4acb0260af4e33b649", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-27.0.0-next.5.tgz", "fileCount": 4, "integrity": "sha512-1H6HSquqA51WUF51zh2RXTT+1Acz/umsnvyTmPY69ySrUJojM2vBy2DSEYmTxiIWvwg5jtrSsmqBqnuKhlQ9uw==", "signatures": [{"sig": "MEUCIQCdtT3cCADyivUDEkOjHNrCqShZhYFNftWxiGnlEnv6AQIgdSEJDC/a1ePRwPl5w/1tTxa26jYm+Q0MaP3e6RBbGBc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3530, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgT1srCRA9TVsSAnZWagAA874QAJvqpURJ577o+99YBcBQ\ncmM+d8PiIM20jDPpFPHThG6DoKdOsnmKYv4XIwbc5k1Jsk7njLozZQp/GPrf\nvp3v5G4t8UVBzB6RSQ7lOLIKtQ1b7ZXxG+aHZfsYvuQFChJDbAIUfcujVmOE\nnWbdlQWjM8rv+wnLiWHztYUxIKqm5nOUHZaA18mjHMhF5/wZVYDbZ19wGoqf\nUjr/U8RkKOKac/5BZYYFJZ/78NKF0fjXJqWphCNVL+NIb2gL8+aBIPmXgap1\nf6wAt0Fz4YSAwxmwT5c6YPWmhZ55F0v3l9usWz4WjIf5tsTqkp9OFJ75/hO9\nutMLkx4Qs0XVGQSDwzZjCkoCTR+SUndA+mkviP42o80GKVjWzob8kL0ncQbF\nPLGE6cvv8KdilamcmlKox3SI84bWHue4mBMEPxHLgNOOEt/JRbftKRas1GxS\nY/QoCSzhDSvrifwzeCV4YaYz17q2/1ezKRMs+KKsIKhtCoed/B8SVy1AUNJL\nwCpTQMkDtgikXmx3q1+MJ+MjDJz/QFBB8SRmEfmZ3NwEuxO1oHVsdVESASrs\n+3wVVsrynijTW39P+sc5ljTmd3qevEie7OHNP287uyLzyE6vdLD3bSMsi+Sq\nL83i/oOnVMvqxLI4GdXVIA3XDq6Se94Sy2YwKNDKKVNZkd6D0XIUWh4CX8fx\n8FpE\r\n=vH2J\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "0a2b94282170b6d4cc26c2d2003cc04ffebe5e3f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"expect": "^27.0.0-next.5", "@jest/types": "^27.0.0-next.3", "@jest/environment": "^27.0.0-next.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_27.0.0-next.5_1615813418986_0.8576320915571878", "host": "s3://npm-registry-packages"}}, "27.0.0-next.6": {"name": "@jest/globals", "version": "27.0.0-next.6", "license": "MIT", "_id": "@jest/globals@27.0.0-next.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "740c1309211e55dffec2eb369d53e55604b6d298", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-27.0.0-next.6.tgz", "fileCount": 4, "integrity": "sha512-yVybEgvSAHzZivBYvXFQ9xEcsNIzF13HLS+Dc1z1t7yv5+luTmEIkuRMY5c63rrC1D4L4yS2xfEVUiVV3daI9g==", "signatures": [{"sig": "MEUCIQCu+xKg58LKDG72rlTjV6cxG/RVE59Ce0JKQl4JzX3a8QIgGz6sXAByjK9SFxPrzgwSshSx9JriDwrcLHww8BcSLLE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3530, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXOcjCRA9TVsSAnZWagAAR2QP/37N/tx55jZ6MoaISlFy\nNlk7YVxDtouNa6CkcbMYxcXjAQ0lQ7MX5xZ6T8GY5r1y+716cp+cOhcIDD8w\n2tmg5lyRzW6Pf63y7RC+btXTRt3gLaP7a0pQ92ZJMcvRa+zugBO76Rpn/sAT\n/1uIfDPmWHCEkgGrWSIdBlFOk/d94Y99yILIuZUll6gNvv43oWRa6qm3pc7b\ntowgzpCwkAThnQnm3uxyZEF82VVq6QBohMi90+Ddmvn8Jal6IwUjwBPUNOPr\na9Z425KATheNV/E7v5fAF1jZ1HBFOSlFn5mfnRr7MxYC8uqK83eWrvMir5mq\nqKtGH1BOktcKPqFtVXqyUj2A5nc9RCeI5+jXKl8LYZ/NtN0it81NFIat/G8j\nK1rNLLk2P8YDEAcKoeFQv/BDAMfm2SyA0g+DXtUxIT9+yipLKy4ZdJGNdLDl\nzhGLvHprbMLKx2ATg0/TDDrZIx3ni9bQ8tgXhabocp2IJLQB8DD2r4cOMv9L\nG3eGlm20+ZJqKa/9L/WTibXZMykDfApuSfcCUwNrvtHdE6Lxfh+spcbW+C6w\n3jg+L6nID8muAfFppDTxTn5eJ+Poi1Q9SbGVgB058vl5CMhmFobAiRdqOFpr\n2L7D5pkITA//r+BYcVG5jvfVteVdqeqf4aEB67WGbbfxiDsky4DV6tWNNN3d\nnW2h\r\n=RLOA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "974d2f22b7deeb4f683fb38dd1ee3a0e984916df", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"expect": "^27.0.0-next.6", "@jest/types": "^27.0.0-next.3", "@jest/environment": "^27.0.0-next.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_27.0.0-next.6_1616701219374_0.6132314562639607", "host": "s3://npm-registry-packages"}}, "27.0.0-next.7": {"name": "@jest/globals", "version": "27.0.0-next.7", "license": "MIT", "_id": "@jest/globals@27.0.0-next.7", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9c8d426221fe93b320a221235b963df772f87794", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-27.0.0-next.7.tgz", "fileCount": 4, "integrity": "sha512-HrwXcnF8iBxsP0L/+SWBGWwdRCAl2DRtNf6orR7bACrbUfCtgptpcKJ1ZfebhwuSfDTiF/HiEt8M0uX/ebULsA==", "signatures": [{"sig": "MEUCIBCHJtAOqEioQOX8LY/GosexqfzDFIWrNp2o3MYanVuIAiEAoWjla2JYrvcWGkJN5mpxyU5qOY5erF2rljsFrGxabRU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3530, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZyCiCRA9TVsSAnZWagAA5aQP/31u0my27drKCbO1s8or\nHNQ1fpwGzmeiz+771vBKTwIn0thMw83J338w4SWbMwyoyCT1oWE4mIE2KDPJ\nUBtlVODJK0y2uJF+90Xu+koifzQ5U7G8fuiQ7WuyLgg+2pw66wcBPF6AW3Xa\na0KWR+/zFwMvIa7YIwUwZIsWzRNp6cD0wX78OzByun0fDAgcSOmbrnzIAzLv\nnxpBO53xymRx4FNMxqGd3yIGxYN4KYXdE8wuLb6V8vD3HGBrqWOiITOTHJHJ\ndwmc8vsrCHrT3K3Upkv1zkgFpP4mRLdF/Hd6IFIA3Mt1zNZrkzBODg29Wl51\niHu2azLguQkLklJAiyP0S8VyO8BnRjIwfFK+J7TOmxibDj1IHjgYIWnVS6EH\nJngaQgUV0JWmtjAYavFLsseX9JbMSnaPXwq9VgpsCWCg1F4uaQppQ7Py98jT\nory7jHpMuM23/YxFDx462ZxgCrVcbu+AvocM5w8CRB5ZsmBlR2OITeAy+IyQ\noN3H9gLbSpCgAa56EN3MYLAji3fjVdVR3lTs2q+5xE8IceyuInJT2c9CvCxt\nf3JiYjtuZt6JRyDOjTc4xQvvswgCMb/EAUQnFwrqteVLZ3Bd4b1PepMU2Aqt\n1CAu/AmyNiy9KHoETnZOrKUphpJJ1azJn85MnbvDp4WeEmmwNrGvnWEjvecy\n1smP\r\n=3usP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "28c763e6be8f57bda89238b95dc801460c2d6601", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"expect": "^27.0.0-next.7", "@jest/types": "^27.0.0-next.7", "@jest/environment": "^27.0.0-next.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_27.0.0-next.7_1617371298118_0.8525516571041885", "host": "s3://npm-registry-packages"}}, "27.0.0-next.8": {"name": "@jest/globals", "version": "27.0.0-next.8", "license": "MIT", "_id": "@jest/globals@27.0.0-next.8", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4fd830dcda6883c07aa90fb20ebac5634c295999", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-27.0.0-next.8.tgz", "fileCount": 4, "integrity": "sha512-qzAsfQ2ZP21xCe7B5CUNYnaNr8dYBlTxUV4lTvmb9pf8eIxWWLuau46P296mfMSZKdYGRb2AH6Ch7IYgK6qu7g==", "signatures": [{"sig": "MEYCIQCJskQmMcy9T4GBvN9t3fx4B/k5+tt/Ddv/XYSXN9k5EAIhAPUZQvOa1tZRBHuoL3aLC2oTx6f1j624/OpErhnSPiMM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3530, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgdMzlCRA9TVsSAnZWagAAEmsP/2FaLRNUP2r3Ezsn8+p2\nGe6nq+HQD60KPMsr3MzfgIfjAL5xfWOPdvmXkhc++xchglCgzXENBRav35C9\naF7GhCWDDxn+mxGxFyw4gMSW6uXr4T6vZCI3/wyisb3oH5KLLZQwXvGv9uHP\nbyG6HfLXZbI2NyU5SwZKTkcv9YzNjD2BwZpQ2qXrWd7fsLxQzLX1KaIpXpLx\n3gdrH/x8wzXIRRkwUNioGBvnpiSN+dlRRSd8kNr5J5pZy0HzXzZKywsQqlUA\ns3o6Ap4KxNwoPAhmSdlu8Qg0pdt3QVcmgXYA1PxT5Zr6DvPA7MhjVRFApwRX\nRpvehpHbSPPQfvWChgFaFG2491K19kwHoTdw7MoBGZBh/0MUfVFpB6hzikzt\nyWxlLFM2XgHe8C/G6FSdGO6UK6rVGSlfuQW+JqVRecx4AJQBsTs2RUmx0PEL\nOTpsl/qFnLJA4tfGzz8agSOGdC6O9+7JKw/QXpGxKPmiH2LpUcw8k0D2kiUU\nyQ7VdNiNx7dSdGUQi0bXBizojGE7PL76wL0Xl3rTjUtJ02sN8VRgtajbojLE\nejVeVyQGWKo14sFl+yGooAIsaO7jaqI0i/GBjabiUNo1NU29BAqMQra8iDZk\nMMbpINXLrwPbqXsGnFWHZPC/6owxpmyjw3UtLfyxEuZcU3RoQzak6cAS9xgc\nu9X5\r\n=nPqy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d7ba5030e274b52f029179dfdb860349a36eea37", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"expect": "^27.0.0-next.8", "@jest/types": "^27.0.0-next.8", "@jest/environment": "^27.0.0-next.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_27.0.0-next.8_1618267365296_0.2026198351273596", "host": "s3://npm-registry-packages"}}, "27.0.0-next.9": {"name": "@jest/globals", "version": "27.0.0-next.9", "license": "MIT", "_id": "@jest/globals@27.0.0-next.9", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1bca91514f95437191be8eee060c1d27fe07d822", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-27.0.0-next.9.tgz", "fileCount": 4, "integrity": "sha512-sbIOdct6Y0xp0X2pZxljsKbCDaWrccZXNYqYnfMiYkSuQ21wdFQLyQJPm2dlrLqisLYPgX+F2SZ0SFwSBDA90g==", "signatures": [{"sig": "MEQCIEtYC5syGX9fOB/+SAYXTmLeiPyok/KYzlvfNBOe22B2AiAq/RncB8gS13nXcB1MZoSprkxfN3A9V2ims9epOc/X2w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3530, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkOjVCRA9TVsSAnZWagAARZoP/RGvVqVnkh+Jj6JwCWqT\nvvuoC7FZ9DLFZ4UviUvvS/Two/3fecvxzULXWkX1fQ/heELkkep7RLzeZD65\nna28lDMUdRsrrFzWhZug/26vfkRS4KomJ2UyZYS3N7nJ+BQw4KlpIae9J1e4\ne+HTkBghacwGTRstEhk6yd3NsYjOB5YvIT+LzcCTSbaCC+pSUZFnsGo5AMMu\nfR222KHnaXqmz/41dL1tRGjTJZpf+ijkWrRVtvAiYywBiQVzKoqluv8s8ops\n97qLDf3qFaoFxSc0ykJuIugAE2nqfYuBCAQy/J/SV9jSPsAOPpTyOM+43PlO\nkcCuNl1zn/0xImT/AoAuCyvQcz7eIqANRiz7wzVwzEu2ZyGMDT3zXQ8Xfzr+\n574Z79kg4NAk1Ouj+bsK6Ev99k0+246YJDe0Sle5w6PjrJ79thom2fwnrPhC\n1ryX4NC8IHMf0wLWisbXF3Kt3qA3E/qADzIZxNZZzqXyzpHWosG8JP3yLob9\nHcPVRcjcp8P3TCNutiXz8TLFQJvUK0/BpSpaSD+cscChWvxAiLjTihV1FVoO\nnfPErw1twwIaHxY0lTiBjPBNgeUwDmFN5mLuXydUabJZyN5Y9c+pQu2xMlGF\nu6PugN9K4nKmzMoaOfNS5NetaV6Z6g0TMiv7iJTtR9G3j8L/WD1ff0maBvD5\nhPYi\r\n=aBPE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d836f33f98845794b4eae8149548a81ddcfc6521", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v14.16.1+x64 (darwin)", "directories": {}, "_nodeVersion": "14.16.1", "dependencies": {"expect": "^27.0.0-next.9", "@jest/types": "^27.0.0-next.8", "@jest/environment": "^27.0.0-next.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_27.0.0-next.9_1620109524480_0.23789045469597503", "host": "s3://npm-registry-packages"}}, "27.0.0-next.10": {"name": "@jest/globals", "version": "27.0.0-next.10", "license": "MIT", "_id": "@jest/globals@27.0.0-next.10", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a500aa81e99dbed04c276e2ac2df94978655a4d3", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-27.0.0-next.10.tgz", "fileCount": 4, "integrity": "sha512-wlQYnyl4N4n4RV4MXCSHqsLFlc1gKS2zgt5s/I/k1iiFW6VayV+bwGN10bfeT4aK2UQal4zPd50H3Tf5/pNp5Q==", "signatures": [{"sig": "MEQCIAXXuqVbROOGGMYaEB3O8MNHfw7d1jMFZtFmi3N7EYJeAiBzn2wVpwIRKJy7deAzwt78dqjwWuvXdiG/7IOPd0WVqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3534, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpm4YCRA9TVsSAnZWagAAhbUQAJKuxT4+15IMRQcLMORA\nn/JCAh/LLzK87piZ90b6MiWt2gIIEBVYhOFqHZe3NGdaU/A527RO7NcdO9dM\n6Qrx2n+PurMxZcve5DAOKt0QUoWaZnq1DdgP7gj0Hw43PK3XWmZeUfSecllE\ndtOz8xEpjhl8cJ9rqjsc9oO/AcPfBXXra9wpmpoD+vUTFrJKkkWERo7YWDyL\nbwvzLY6vidnbf/J2gXy2oidLUC/mGe1n5osGks1iLhUcpxg7eEJTtO2t80yG\nvBS3XZ2HVk7ouGzuexXO2bmxpWAPH4ldv2wOnlsKGoM1a4kRI+f8GBj4yCzv\niwaFdqj7HkSCMPNZPtGRJF3orB3i3jR2cFwhbAgmDy5FrINqoID7PkrmF25o\n6W7o0rQBBsoKr97YpxaK14jbh4YCa++UXRTLX06K0SAFYUVBUF1vCkIMPJT+\nD8L8EsJ04Pf07mxH0yXkpMbujXCS25ih/quW0jnGHCf+TNpKRxiZdAD2qGIR\n4CYU9/cNHLzKiwnbH2L9+HZ1hY7EvkUrgfkLH/qU5cSMCK6vi+1u9pPvitml\nIWM4nDlymce5K3k3EycTulEQ2ygKTpGjCrEZgPGwcavlf90bJm+R31TbHC+E\niE2Q+MXiaRUxcKauiQYol0Oko2lJt2vSpiXfeG37q/p4pTtr9IuhF+iNXcfl\ngedi\r\n=+2dj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "6f44529270310b7dbdf9a0b72b21b5cd50fda4b1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"expect": "^27.0.0-next.10", "@jest/types": "^27.0.0-next.10", "@jest/environment": "^27.0.0-next.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_27.0.0-next.10_1621519895818_0.5312876562171673", "host": "s3://npm-registry-packages"}}, "27.0.0-next.11": {"name": "@jest/globals", "version": "27.0.0-next.11", "license": "MIT", "_id": "@jest/globals@27.0.0-next.11", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "48e8c1bd3162ebeb41133b45e1b648a229547ff6", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-27.0.0-next.11.tgz", "fileCount": 4, "integrity": "sha512-RhmmXgdU+9CZFzspRa75jQplM89Dg/6r22Z0llNsXXFz60R+0ZenigBT8oc4TjtlgWi80Jyi9CooCeeZIN2R3A==", "signatures": [{"sig": "MEYCIQDFrVZXtp57KwnjbAqXKxQsKTHd5hkCMpI4n9W0juzsjgIhAL+hEM9pveBg4vS7ekM+1OeZOeOtlsNJ8SeCQtmqaQU1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3534, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpuKwCRA9TVsSAnZWagAACDgP/3SBTrvMPDnT0SGrpdYv\nb6VsEDVBdZrkFWmLbRW4ZNbguKkkREULnPtUtRRbOJkbg8ylmsbRZ7yjZ1lS\nO6opYfqiQf2P+wzGYTO/gk18+D9LSRK35VUwaSoXKJOO/s8uOkMk8EjC52hx\nhR7NCAZEaPIEFz6IbPhwrsGfrTn5wYuG8wnIkcpCgY+qOpGd8ik+S0g7QcTI\nh3/kFHHeem1t7ughqo2qtv9Rm31C8kMuznLp2EWC4shLIolnJqx6ZUX3NOMB\npQidM5Q+1z3RRrE1RT2pXnfOZ5LPOj6bbmFq1Tc86LNMzAr6yEto2b0rYOJD\nYL+Uk/spjsDEO6UXWGTilps+bkkY5symyWE7KqYTz44glOPERCF/qF/UCfJ3\nrjhU0A4JImjwrED9npFwuBVQite1mZf8cB5AxrR2SlZ8RzQlqI49rIrV9cOR\ncAAMQUKMQUu7uCoFKA+DSZ7KWNNyWiUlchWaOxio+DpSJL2FzHmfBL18oQ1c\nPJHvXYEjlVQkzwK0nFB1QmNx7/4w8NnEY5Svsb0niFQEYFA0OSQT2CNaLG8F\nGYALirxF5NHbQDhGW7eapA1QdkXF/y1rWkdB4QVPjFFdoD+f+p1BTowoRUp2\n4JmGmMPMM+h11oK4eaVzChJZvrpPFUY9aLvgYn1XAd9iPRjxQZDoEnLvzqxC\nYWy0\r\n=nF98\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "e2eb9aeee8aacd441f1c8ac992c698ac4d303f60", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"expect": "^27.0.0-next.11", "@jest/types": "^27.0.0-next.10", "@jest/environment": "^27.0.0-next.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_27.0.0-next.11_1621549743660_0.571266777062587", "host": "s3://npm-registry-packages"}}, "27.0.0": {"name": "@jest/globals", "version": "27.0.0", "license": "MIT", "_id": "@jest/globals@27.0.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "289a2f93294fbb69d251152f8a45b1c7a6e64f9b", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-27.0.0.tgz", "fileCount": 4, "integrity": "sha512-inm6BShl4QscJtdUt1CkQtEw41J4bAyF7ly6EBLCFniSCi8bkt1R5EFAxf0ZAa/9Beyt/R9RqV08Enc3DiRFXQ==", "signatures": [{"sig": "MEQCICnBkxRzI3qNnbS2tEPiMpL5lb90RxIF5ZM5G0LfAqLrAiAc+M6Dtz66zrssoml70crBo9gajX+HXzV1hoqr0CA+zA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3510, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrLInCRA9TVsSAnZWagAAIccP/RYKrDboX9nyS3NrEsbe\nWzvXmBkDZXDG5hraXZz2L8dMUhYsnHpQRHZ6gj7OzpBzLWpkz6JYus8aURbv\nqqfToA17PxnfQJt6nD7nIqFdslYMX7xecSTPflGS42CUmwWtyxq8EoGLRwRh\nyL/7X18o/T/rMQbApafogHiFdsz140QkjGK4pSI1pUsbNVr106B6/02YRIub\nErDlq4F/hxr2biLOwHqImLjMYQFSHOKddXuiHzekPVKyk1E6szM2uBeoc0EQ\npJUHi1RBEnE5lMQ6aLLu4Oo+AETFDScfd54NUEM3RuMaxa4zWuTAND2/mX1O\nvv9BnbkE6WcdI2CNVGUSC2p8dgyuVPF/H+MCUerkXL/Zv/Mg/2kCUex2lJet\n9sy4K9XgD7EmF06DGmD1WQclr/3bK9Fx2v2gWmnEFk2ZM/8kCGVYYKLb7/N4\n3YH44EXMBCPAEPsvJ3vj6QlKK9skFzgKLmSOrPts139/zUBWgVjLIcXnzb+g\n7sb5shTxWWnpHIX3eHXUyVUrw4hIE31LR4Ikwk/8b4BX34sZKxQDvyHnGqSk\n4teqf5VQpZgrJfuRyWElfvyJfU5Kp1WE/Y6qJa+pR391hxNGO5ewH1K25fuB\nmGGpx44umfVphZn2Q1KUK1/qW4hxGXJqH7pxcYR0y9KOvuUejo7Fd2nmoodH\nfbxY\r\n=7y3r\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "be16e47afcc9f64653b9a47782cb48a5ca243e65", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"expect": "^27.0.0", "@jest/types": "^27.0.0-next.10", "@jest/environment": "^27.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_27.0.0_1621930534888_0.6868129852321174", "host": "s3://npm-registry-packages"}}, "27.0.1": {"name": "@jest/globals", "version": "27.0.1", "license": "MIT", "_id": "@jest/globals@27.0.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "14c776942f7047a04f2aea09b148065e2aa9d7e9", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-27.0.1.tgz", "fileCount": 4, "integrity": "sha512-80ZCzgopysKdpp5EOglgjApKxiNDR96PG4PwngB4fTwZ4qqqSKo0EwGwQIhl16szQ1M2xCVYmr9J6KelvnABNQ==", "signatures": [{"sig": "MEYCIQDJ7br2U15PrRq53nIPUhjYgiJX5IEBc6H/lrcffbXzAgIhAIOXt6ujisZyIhGAYGkWfyGgA27/3NOJGPABDksEMtDP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3502, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrMw8CRA9TVsSAnZWagAAUx0P/2fz399g5ciy+Nj6NkOL\nQQ3qB3IDO2Sjr9sawgB07aIhuUz2WLwS/Sa6Wjd+Z6TiOVYqhpb2JD8Alqrg\nxcu6tYcIOuM6SRIHaBJKxYpWvTV5I71gkdnBCZbvrddmU+7w8538TC2UbGOB\nO81MlaQg+TCWTfphSBju2YBGWA+WqPFrHUw1fs981CCM9rGQa70LHtsYkXWp\nJEC5HE0wJKa5xC0t+ZwNOOo9J85uAZiqqzUMaMVCBVdDUJ+eOqEWNhr4Off9\nVmPTtUqnAj/RAFv272KANdQr6Wybo62gyPL3PjuTbQ/7cDiYdVFuS/RGEPen\nFgvx4L6TsQz0pXxrOT58x13V4DxqfhupeWpDdrtgHJ30m0PUDeioH7EbTWbx\nUwFAHbxjJXPY1t0cEp2gIzp9PcdzhPT+HIsPJzbgSnR8ehOm4CKWIN8cKmK9\nNjxRR+hhZfOl2ykLwzEBpq4A1Gim4eUtqP3Toxt/qmvGYkrwLiIGDq9DSyPA\nID8xqR151VpTnUxU3q0h2+ysRFU99S8nZOvndEDMTzC5uSCIr9LnoKck3wbZ\nWrxVSwBsBvfbe0Og74Q1gd1e+RKM+H/ttxrLvWaiC/oR+RL9Zw9Aaf4aeIT+\nZNwiy0W/vmdNEABzIUcGjLiMItmD8UXIsKyj962qhBeWEmuPs1T3/cYG6m0i\nhxLI\r\n=UO/u\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "2cb20e945a26b2c9867b30b787e81f6317e59aa1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"expect": "^27.0.1", "@jest/types": "^27.0.1", "@jest/environment": "^27.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_27.0.1_1621937211983_0.5676186834779777", "host": "s3://npm-registry-packages"}}, "27.0.2": {"name": "@jest/globals", "version": "27.0.2", "license": "MIT", "_id": "@jest/globals@27.0.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b52b11af58b49472037470441829829fe70d58ce", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-27.0.2.tgz", "fileCount": 4, "integrity": "sha512-2V2/xg02iNcDu/H2zylqMBQEyfwv9X7oOB2BMpfk18wVST7QxmXRLHwuLZGGTQgnqNwZ9xtePqsFSl/JOZAQrA==", "signatures": [{"sig": "MEYCIQDYjvHgn3ZqcJHuMTgc/sYFX5sm4E2sI2f+TtmO0Si4pgIhAJVPWqVns2qH9Jdh3CIWxEB7tySILAv632FzaXGpb6qy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3502, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsi6LCRA9TVsSAnZWagAAcBAQAJIH+62uwMdH7VMz1iL1\n9Ah8Y09jV5Dy8e2TsNlt3qmAKrLO8EZVPyMrrL6CMipCwMcvGjNzy+U3VKL4\nYw7Wris51yomDb1brSyNRbsp2XP63leZ8Shply1hl+q1nav3QNHf8XJweeVB\nyUtkjUe2wZHIXxyI0IDAPpyqtF1KKvxoaCbhTssK9RvEZMgpTcmPF+wNzoOg\nnzwk40axzGI/u89w1QyeB9niBoKwqIWdjZwSQh2lMP+wPXC0njsDMyFXZNrC\nvJXNTp1aVXDx7bfpBAmgymebqKtq9RVrftn+tRXdvuxVpOohrL//n0IyzQmY\nC3hzEL9u6W+P2nXNcrJzlu1tC+WvtTQ/kJuriqRh19L2yIl8Yg6dGH5XOD+S\n6tga+EIcPfpkgllY5Mn5GtTndefldMNzJIRk/w7WCe6widKBO0A554ua4lHg\n90kERYLHPGVk0C3ogaIwunuQsrUUwiKIg5DyV5Wedl0S/gKt1m6B5gkgZAz9\neDGXa2ABNMcXvGQ71jvgZKELjyYP2Xle/wKKuVzxI62EbljXIMujcW7k8/qO\n9gvPNS+rxFhV+4pFg8A+aVTxKdzzLMvy4cn0vaQkEhTaLk4kGPwKM5UGq2mi\nwQpLqxowc0Q864paCALRgKLXs7ttLbyF8UBADofPLRwz5rr2Hd/SxdXumIh1\n14tH\r\n=Wadi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "7ca8a22b8453e95c63842ee6aa4d8d8d8b4f9612", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"expect": "^27.0.2", "@jest/types": "^27.0.2", "@jest/environment": "^27.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_27.0.2_1622290059428_0.884065385225143", "host": "s3://npm-registry-packages"}}, "27.0.3": {"name": "@jest/globals", "version": "27.0.3", "license": "MIT", "_id": "@jest/globals@27.0.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1cf8933b7791bba0b99305cbf39fd4d2e3fe4060", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-27.0.3.tgz", "fileCount": 4, "integrity": "sha512-OzsIuf7uf+QalqAGbjClyezzEcLQkdZ+7PejUrZgDs+okdAK8GwRCGcYCirHvhMBBQh60Jr3NlIGbn/KBPQLEQ==", "signatures": [{"sig": "MEUCICjy9a0xlpMONWrfpQe822qdAFUCx6y2wRQHxcy5+xSCAiEA4v2RHPLa6j2ijI8ltgau+Qz4SkJwDlkOnPAEkhPrK4g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3502, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsn5BCRA9TVsSAnZWagAAEOUP/A4CR1fNQ9X2DbHFlFUj\nd2M5KNkl4yQxxlsjPN82s8+d9ftnA3d9ou1dPrbcLg4DMytoizNsmVDvIZN7\n08wpcx84XBoYFVnDyIVtq/Le4bgmMgCS47JKgQzFslWktorqjkMg14ZPiLQd\nXSxPueNV7VnhU5vMrzJhobsdWvC5wUU9EyPdVyBBHXyzEBEIbK9MXQnP8CXR\noF8m53Q5n5HxX3QCvPPsxP3KGMlILl2z4WN0RN/5fydvzov4gdsuskX1Px22\nHhvyhHjNU5HxaDm6wGxOyqs0095kBNhPS2Kn1hnXcjlQqinjvbzgw0uwn4wu\nvRH5gA0pi4y8TOSQOjZ4Rq3fCNiKBvcZ4OuCxFgaoQKdykoRZbs0rERh/OzV\n//dlXO9LSM/4c0ABOMk8To2ljfBRyzg5eWpzLgIf6tn+lnQaADRdAjwMzYY6\n0Ee/c28yjwyTzqm4UbWslHOcEbvSAly+FC+zY5ExQ7G+z4Fzha/XL/aictFi\nwM7uc4HIA6idEE7ANHZOIfPWFPkMrgUXSfL17JV3BP2sV03NU9UDgRLUqzAF\n1dT35MVGkU+RPIgrl8MSLujgZRHhPW1Oo4GIJXak9KZEe7Ck30d8zECyuH/9\nYrh2J5goGSFGTKRvHaMftKp/VetMnzyttx8rIN4vnaXP2CIGRfUxJOHA0Bgr\ndUWe\r\n=MTrv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d10f645b04ba9febb7308392e635c0351d0f027c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"expect": "^27.0.2", "@jest/types": "^27.0.2", "@jest/environment": "^27.0.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_27.0.3_1622310465143_0.4131671546743947", "host": "s3://npm-registry-packages"}}, "27.0.5": {"name": "@jest/globals", "version": "27.0.5", "license": "MIT", "_id": "@jest/globals@27.0.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f63b8bfa6ea3716f8df50f6a604b5c15b36ffd20", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-27.0.5.tgz", "fileCount": 4, "integrity": "sha512-qqKyjDXUaZwDuccpbMMKCCMBftvrbXzigtIsikAH/9ca+kaae8InP2MDf+Y/PdCSMuAsSpHS6q6M25irBBUh+Q==", "signatures": [{"sig": "MEUCIQCi+bzI0fadu1ne3NpIRmzvG5I3rS0oyRFDz0MYpTpT+QIgAjLT1thhGAwhAWEo/trXLs9cGu91ZyGUckLmKZNSy6s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3502, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg0cU5CRA9TVsSAnZWagAA3skQAJGscKLhMvWXEwPmEGng\ne73mh9V6cRattaJXQh0OmxdxScia3+Uwn3cD3D0cOwk78hbhibaLjTDNRkGm\nv3B4Vw3zam9omg6oYEcY3LIoGArh9oR6qSvDDnDExySWvLvtrFTs/xzYMoW3\nTUi4QxUQLNM3K0U5WsIu/9b228Iowevgqa3ERys+IYxe24nRelXYIVcchDHG\njnn3T8JEgZnRPvXBRjJaJiSNp8q834NokUcFmT/g+LGYiieFNegv7n+wuuKf\n9Xa+/AdujUlbTG4gSUBIGzKfVDzpJKGaiMzR90LrFlxEzrlVu7WiwdxN7Vaw\n+i4t3WJ2+aSUGSH1DGsHR3cCLgkhuI5DtBfucO8hANZ4U3IFukUfgb9ho148\niEJ63wW9ii3tu+0PkW4VeLJ4AmUs1d5ENH6rXN600phVut6bLJpYNExl6p0o\n0FVh0/FIbDjhX/YNVaV5755G/72n8/NUOWkjFTTOCTMgBz7Z094I27rNGqez\nmlUvKKvI+rFunk1g4pFU/4HbbEMXPhc5/hSjX/iJETS1xgN0JaAf5WMuuZef\nFTt8JXGtlgtClg1szQb17l25BrWyem9foG40J3rK9sLGwC0+rTrXNNd5NsoJ\nnEGREInsYmDmNsqtbPymbw3iLV/3bBbXcTp1e4BTRFS7r1BR6Wol04oYERfS\nUKIP\r\n=51qX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "dafa4737dc02887314fd99e4be92781c9f8f9415", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v14.17.1+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.1", "dependencies": {"expect": "^27.0.2", "@jest/types": "^27.0.2", "@jest/environment": "^27.0.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_27.0.5_1624360249223_0.39037851480922114", "host": "s3://npm-registry-packages"}}, "27.0.6": {"name": "@jest/globals", "version": "27.0.6", "license": "MIT", "_id": "@jest/globals@27.0.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "48e3903f99a4650673d8657334d13c9caf0e8f82", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-27.0.6.tgz", "fileCount": 4, "integrity": "sha512-DdTGCP606rh9bjkdQ7VvChV18iS7q0IMJVP1piwTWyWskol4iqcVwthZmoJEf7obE1nc34OpIyoVGPeqLC+ryw==", "signatures": [{"sig": "MEYCIQCUJglp8+sIN0LAYvO4SVFoZf3R1++2Fabl/aKsueTwkQIhAO6XGoZw1CI2+Vq01g25aEloIHSJU7EguapOb6cRVj7s", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3502, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg2gF1CRA9TVsSAnZWagAAqtcQAKUZvjN+ppQPYuc3njzk\neqPY8lkeWYqVy8lfUZfk4Toi4LpMbZeAuxtHL4fEAJwRAtAbBylrCzoSXIu2\n+neFp9hGPso3elrHGZuXWzelEuCifQ3Dmtl47lYLwbmbO92Z4AiEpuI/rh/N\n6l54M9pgXEb4Cp52dkGtAAOMkosBeUog3wzAAOW5VYfTNkw8sKNcbDYeNBKb\n8PToFoavVFzMcuM2AG7SU4Bb0Aj06SJeWHDpsU0i70HrbGOdgxGz3xXB6pgU\nDKzPZLgBrqgvQQX9k+EVYL7K+z1UpYp65AlwQV0DGUm2K6zUaS/oTE2aI5MS\nD35lLgAfNLWp3YToJXY/6ROAO+xX7RkOw3c6HkYx0vaVWYOcan2MA8LQ+1oB\ndjtDMdJDAPHjJXXgxuCOjJoi+gF16YiK1IwIzdWXKbNT47aJiP8mZazmxAmH\n2DYEOa5n7Us0v5gkYiv8TfDmdUIvwOC8KVUvjLAETglZCg5RywLieJ0JdmNU\ncY22XRiQSBZxav4aVyYSzXJQ26Ots+vSG48X1penZKcQHzQA5qRonjTIoFN5\nHIxiT5sy5zFpaI2dMB8zHB10jv7IOJKmuc7EenZSas/MoPI2fpMojSpeXif9\n2Ykyhs9rga1Px/nLwWhnyz+OIJBQTRR0ijvFuhmjpC+LxZxZWwGh0wDnNYFS\nvQYL\r\n=KAsY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d257d1c44ba62079bd4307ae78ba226d47c56ac9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v14.17.1+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.1", "dependencies": {"expect": "^27.0.6", "@jest/types": "^27.0.6", "@jest/environment": "^27.0.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_27.0.6_1624899956772_0.7651687937685527", "host": "s3://npm-registry-packages"}}, "27.1.0": {"name": "@jest/globals", "version": "27.1.0", "license": "MIT", "_id": "@jest/globals@27.1.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e093a49c718dd678a782c197757775534c88d3f2", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-27.1.0.tgz", "fileCount": 4, "integrity": "sha512-73vLV4aNHAlAgjk0/QcSIzzCZSqVIPbmFROJJv9D3QUR7BI4f517gVdJpSrCHxuRH3VZFhe0yGG/tmttlMll9g==", "signatures": [{"sig": "MEYCIQCi4QVpQ15U+exYoGYN1/WN3grmQWi5J0RXz5vaRNd5WgIhALsDZSJmHuyvCwA99OZ5TJeRxSfUbHdqiza8Buupb76j", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3502, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhKLebCRA9TVsSAnZWagAACGAQAIMVMdp4bAADzSMUejfq\nTqsHgiTlBjilr3BvBmDYSIsNksaAoBkTcN/RR7x9fN/POCy0huFWzpVELwXm\nJET197RPgjyVdqx8Ocq/RftnWHj1kBnUf1BMwVE0Xr4PIWxy+uLB0b0oHRDJ\n+xCHPw+pnlK3QltoVJlROSFiOtJqPZQCxpKtC9X0TfKgjcHbPeouz6pJAS6i\n6EKbk6ymrFoX7UEeuyBNB+IAxgiWBEy7rQtEUXCiW4Wh7GrABG9yXQKE4crz\nALCE/TuLKG07TgHyhyyyCUYSdSAkfsQ87+wTyQDj45y3XH6Z9NxTENyKE6B+\n8TScEt8/A1AQ4OtA+dm8Yg4knYRWTQs3Vy8wuJMAHHrJKwFiilXArvW1HGa2\nkDOxf8lD1JrBDls7jYMsmuavOHX/ANCfIWo3TsNWUZgO1NV7OWDyn71QtLzy\nXANJExteoTY8xIN+hQD5fAg1/1CcQGzweYAahVk7vFniwUiUXisKnq7PuFIT\nHY9rxeOPuPsmBqMx/I1E/0cx4zz2s9TlC/oDaF1ff6C6/OY5ptvcVV81I/xg\nFsNvvSNY3VNcQsYWPmZBeQJGRVoTQyGWhGhcEyJR/A0G8Bkj+b/bnR9GOYj4\nBhsgLC5ncuI2BM1TXW6kiWunNCQ0nbV6+Q1AbGkcJhx+UOIR0YYS8DQT3iBv\nCavq\r\n=ABqE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "5ef792e957e83428d868a18618b8629e32719993", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"expect": "^27.1.0", "@jest/types": "^27.1.0", "@jest/environment": "^27.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_27.1.0_1630058395467_0.26700269424207423", "host": "s3://npm-registry-packages"}}, "27.1.1": {"name": "@jest/globals", "version": "27.1.1", "license": "MIT", "_id": "@jest/globals@27.1.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "cfe5f4d5b37483cef62b79612128ccc7e3c951d8", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-27.1.1.tgz", "fileCount": 4, "integrity": "sha512-Q3JcTPmY+DAEHnr4MpnBV3mwy50EGrTC6oSDTNnW7FNGGacTJAfpWNk02D7xv422T1OzK2A2BKx+26xJOvHkyw==", "signatures": [{"sig": "MEUCIQDn5NU5GSqq3mKhtjjGAmATPAX39FRk3L3cgMTbttURkQIgQdWwy9B3kC06clykeCNK0hwrui5tm2hqFtaJf3SIBw4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3502, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhOIyKCRA9TVsSAnZWagAAjPsP/Aye+wFjyr+miE6i2aUR\nakrrqqXwEpr+Vj093RrVcgUg6zB4PM7AgbtNaTTEmaqKogSFx/G3n3PRlGHN\nKLNmlULMGcAlaWdNiNZSPAb1Q46J3lSrBzuzwxBhzJITYCbBsnEcocMxUOXu\nsUfiQhRMx3hPNcVPs2afqfBRAOU7RzS9BlhVIPOzQ8uXQV+wCRsiz0Bs1wwS\n6aS4jrdzYH6zPvmt1I8AP7WfXo1pLrTzWNfgEGnQ68DCHOepNrlALFHSg3+k\nbM4gYa8L49+V5nq8FpC7FqImdygwCVtoHYM5MUHsWkZxuBq3WIbspDf7Y7Ua\nhYVtHOKoLYR6ekFch9+bf8+GmnDX3VBx9RwWm6j8GVMNkZXWgvix9L2TF5Oh\nKTw0MadkqZRze6b7y5MpwFZmTxnjFbsYsPWZKwBIgDgR+4iv0c2gY/qqDAsy\nqLwXtjvfhiZRsOMgbWEnKErzluQA7TSyMzyNwuJYY/XKQrkQ4ybluY7QzC4n\nUHFM8/i7eqVvobte+aG4gtoCOcSQ19wO2/u0I+CTNI8PPFsV/ZXaGqis8q/p\nZSz5fYbkB6iu7AGijkbu10HCFv2Zf6YX1oqYuRzT1xQOx+EbDfPLSdBFlkri\nMCMoidBovq8iMotFtBFxqIGwJGx1LrsAATYjwls6glmuBnxU1OTMWS0RPexQ\nq3Fm\r\n=cG/b\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "111198b62dbfc3a730f7b1693e311608e834fe1d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"expect": "^27.1.1", "@jest/types": "^27.1.1", "@jest/environment": "^27.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_27.1.1_1631095946722_0.5947640118973749", "host": "s3://npm-registry-packages"}}, "27.2.0": {"name": "@jest/globals", "version": "27.2.0", "license": "MIT", "_id": "@jest/globals@27.2.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4d7085f51df5ac70c8240eb3501289676503933d", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-27.2.0.tgz", "fileCount": 4, "integrity": "sha512-raqk9Gf9WC3hlBa57rmRmJfRl9hom2b+qEE/ifheMtwn5USH5VZxzrHHOZg0Zsd/qC2WJ8UtyTwHKQAnNlDMdg==", "signatures": [{"sig": "MEYCIQCWdU+UkjxGpNXfM60A94u0pebYsC85w3MVPKST8adXYgIhAMd/Dcfnq0pYw+bVuCEh2Xg7A45rFxyY2iUJmz7/ybrb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3502, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhPwahCRA9TVsSAnZWagAApeoP/3XuThXGoTm8xOOs73xK\nrIBVtRMmeXuBNyhvdu6Xn7y3g9IdoyCNa23SVekoLuO1nKGrCh+F+Bnbi+Bx\nBgyKHLyXC3rBp1CoNGAA+Nd5c/oRmZR6/U/4XGIiP5HOUrl95JMB0LuZ2cp0\n32EWjwqjegXjYdYPgsM4dRbVJqW6tlePYGTeUmV4XrNWjFwSfbhoq2YGZfYC\nIIFlTCsnBMRXqLWIm550qQQHYNhjAWxGGJc7m2wO9O5iq7LkfQstbGXqzUvD\nywHcHq9GkyZL/pchJTweQZJu5FHlo0uRkITVMjdRU8zDQ6sR2Ncc8LX0CEyO\nWLppczlI3X8JJF715WLgKeMva3jEIvK8auSy7NztjOdAEthLhW8k+qdMgKY4\nXPBrXGSenje58qsfcwK7YubuaNnIhnObDtOEE3dMmdtZ3RV/DBrRtm75Il2U\nNjlStJrE092s4739PYSfhny+m5y3I8uyu2yvMTrf0hcezH6Gutr8Wif6r7OC\n+lJaFQvIokEgTKkAkxn48BfUtPpyNeGfGEpxB4WcNadD9IFUjCmkGyglcNP+\n12W/7kqOj1D8telPx6eNPcu2aLhDgVGk5jmhrt49IzxhJ7FWr8kGGHtuCXvK\ntFVK/2iStEzLy8GyscsAjb7Lmql71E/m0uMyeYmfPiSE9E8T3R6ZOr51az0K\nm+Yl\r\n=8Bhi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "b05635c539f8f673dfed5bf05ea727a8d5d7bbe2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"expect": "^27.2.0", "@jest/types": "^27.1.1", "@jest/environment": "^27.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_27.2.0_1631520417175_0.7725364463228852", "host": "s3://npm-registry-packages"}}, "27.2.1": {"name": "@jest/globals", "version": "27.2.1", "license": "MIT", "_id": "@jest/globals@27.2.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6842c70b6713fbe2fcaf89eac20d77eeeb0e282c", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-27.2.1.tgz", "fileCount": 4, "integrity": "sha512-4P46Zr4cckSitsWtOMRvgMMn7mOKbBsQdYxHeGSIG3kpI4gNR2vk51balPulZHnBQCQb/XBptprtoSv1REfaew==", "signatures": [{"sig": "MEQCIEKhAD2qTC81hgATx7MTdXL3YrRFVQH+uMlozcI/7rN3AiBLp2cCIKTZw8rxeL0nEmR1zo3NZ7rOqlu8j3jHXM0B8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3502}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "50862b410c358107ec893cfdd9bb9a689ad8e622", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"expect": "^27.2.1", "@jest/types": "^27.1.1", "@jest/environment": "^27.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_27.2.1_1632144483319_0.8376883370251278", "host": "s3://npm-registry-packages"}}, "27.2.2": {"name": "@jest/globals", "version": "27.2.2", "license": "MIT", "_id": "@jest/globals@27.2.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "df66aaafda5c69b2bb0dae548e3cfb345f549c31", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-27.2.2.tgz", "fileCount": 4, "integrity": "sha512-fWa/Luwod1hyehnuep+zCnOTqTVvyc4HLUU/1VpFNOEu0tCWNSODyvKSSOjtb1bGOpCNjgaDcyjzo5f7rl6a7g==", "signatures": [{"sig": "MEYCIQCObrlgyqSM3IjBDREQ8EeBDbUYAWNmaUGA/oKcmq81CAIhANQJa1bgfHO6tfIBl/B/Xm2twkEIK+U+ZishwLQ7n+aG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3502}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "f54d96fec55518640b900d6994b2c4153316d1ed", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"expect": "^27.2.2", "@jest/types": "^27.1.1", "@jest/environment": "^27.2.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_27.2.2_1632576911729_0.7844046633852302", "host": "s3://npm-registry-packages"}}, "27.2.3": {"name": "@jest/globals", "version": "27.2.3", "license": "MIT", "_id": "@jest/globals@27.2.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6b8d652083d78709b243d9571457058f1c6c5fea", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-27.2.3.tgz", "fileCount": 4, "integrity": "sha512-JVjQDs5z34XvFME0qHmKwWtgzRnBa/i22nfWjzlIUvkdFCzndN+JTLEWNXAgyBbGnNYuMZ8CpvgF9uhKt/cR3g==", "signatures": [{"sig": "MEUCIEo2h1N21qdeHLUJlorQy1aWHdxzRTHYiuiVCw779yV6AiEA69OC5SmASvO6aoKJJJ+XyJO8bqdGsvxlDHJa4LoOfew=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3502}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "ae53efe274dee5464d11f1b574d2d825685cd031", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"expect": "^27.2.3", "@jest/types": "^27.2.3", "@jest/environment": "^27.2.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_27.2.3_1632823889144_0.6772043967934944", "host": "s3://npm-registry-packages"}}, "27.2.4": {"name": "@jest/globals", "version": "27.2.4", "license": "MIT", "_id": "@jest/globals@27.2.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0aeb22b011f8c8c4b8ff3b4dbd1ee0392fe0dd8a", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-27.2.4.tgz", "fileCount": 4, "integrity": "sha512-DRsRs5dh0i+fA9mGHylTU19+8fhzNJoEzrgsu+zgJoZth3x8/0juCQ8nVVdW1er4Cqifb/ET7/hACYVPD0dBEA==", "signatures": [{"sig": "MEYCIQCJJVXd7nOBv4GUo5iSoUt9wBCWuk7gVvmw77AGtfxsSgIhAKpRACBKryJpH2iQLUuZ9ups6zVup0+pzE39BuA6npDZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3502}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "5886f6c4d681aa9fc9bfc2517efd2b7f6035a4cd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"expect": "^27.2.4", "@jest/types": "^27.2.4", "@jest/environment": "^27.2.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_27.2.4_1632924297262_0.3522335272954844", "host": "s3://npm-registry-packages"}}, "27.2.5": {"name": "@jest/globals", "version": "27.2.5", "license": "MIT", "_id": "@jest/globals@27.2.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4115538f98ed6cee4051a90fdbd0854062902099", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-27.2.5.tgz", "fileCount": 4, "integrity": "sha512-naRI537GM+enFVJQs6DcwGYPn/0vgJNb06zGVbzXfDfe/epDPV73hP1vqO37PqSKDeOXM2KInr6ymYbL1HTP7g==", "signatures": [{"sig": "MEYCIQDq9SKzSLD1+6xgWD+hwUL5JLLEqX1q6NJU1xAnR85EBQIhAMivJXiHzEaR7ZqMiP6ZGA5sWF7QVc/kOtnGggb2u95g", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3502}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "251b8014e8e3ac8da2fca88b5a1bc401f3b92326", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"expect": "^27.2.5", "@jest/types": "^27.2.5", "@jest/environment": "^27.2.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_27.2.5_1633700370238_0.5440323203147901", "host": "s3://npm-registry-packages"}}, "27.3.0": {"name": "@jest/globals", "version": "27.3.0", "license": "MIT", "_id": "@jest/globals@27.3.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8822f9a72aea428e3f11a688ff13c7992bfe1ea4", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-27.3.0.tgz", "fileCount": 4, "integrity": "sha512-EEqmQHMLXgEZfchMVAavUfJuZmORRrP+zhomfREqVE85d1nccd7nw8uN4FQDJ53m5Glm1XtVCyOIJ9kQLrqjeA==", "signatures": [{"sig": "MEYCIQD6ckVnWoU0snL6UjI2UmEdOTfkYFx84sv25pBixiET3wIhANuw5gs7ZNMSXqAGxl3GvI1XBfq1FaPI2uupqU0zgDf+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3502}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "14b0c2c1d6f81b64adf8b827649ece80a4448cfc", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"expect": "^27.3.0", "@jest/types": "^27.2.5", "@jest/environment": "^27.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_27.3.0_1634495690809_0.5620399197203538", "host": "s3://npm-registry-packages"}}, "27.3.1": {"name": "@jest/globals", "version": "27.3.1", "license": "MIT", "_id": "@jest/globals@27.3.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ce1dfb03d379237a9da6c1b99ecfaca1922a5f9e", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-27.3.1.tgz", "fileCount": 4, "integrity": "sha512-Q651FWiWQAIFiN+zS51xqhdZ8g9b88nGCobC87argAxA7nMfNQq0Q0i9zTfQYgLa6qFXk2cGANEqfK051CZ8Pg==", "signatures": [{"sig": "MEQCIAjKs3vvXElO7oUy8LwspGz25FJWyVF2fRJY7FvhLk3FAiARdZUyFOZoxAs//R905HvOuf5qYq1LpCC9d5AlA/FIyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3502}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "4f3328f3227aa0668486f819b3353af5b6cc797b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"expect": "^27.3.1", "@jest/types": "^27.2.5", "@jest/environment": "^27.3.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_27.3.1_1634626657779_0.9135343156710503", "host": "s3://npm-registry-packages"}}, "27.4.0": {"name": "@jest/globals", "version": "27.4.0", "license": "MIT", "_id": "@jest/globals@27.4.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2daa3ad0cb7e44ae7845b4de053866a6f0e051e8", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-27.4.0.tgz", "fileCount": 4, "integrity": "sha512-jIkd2RSV18wvOqFx5climVkwONuxqNKD8jHMvIumj8+E0qqWqymBcWymidjbxmJ3L3Zr60l0lAJGKw0BstREeQ==", "signatures": [{"sig": "MEUCIQCMSfq6HNJ/axgvncTjH18MQn4CThiqh+f1EO4xYeQ7aQIgfZqcwF5gmLkT4fo5pWawIRY3Ivs5EKJ4pNbq+Fb+JJg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpNelCRA9TVsSAnZWagAAhPUP/0KE2DSUqA/5sQakK/fk\nIvrW4B67ZczutW9G+bIbGO9p2dAF18b9ahUPuGhZn+kfylioFcyTnLpcysXx\nmzDlBKTzYX0uIonKRlgEpvsw+5lL/I2jEWgjrCczd4F5NPvfKAsGD6yKV3pE\n+zAuXz0KRMFm++KgpClmbPtq6RQYNSCnZFup4vIzx01gYsRbqFbPuuLlQs2J\n4DDRbbrL42fHF146OiBFZi0EedrEnhKJNI/qIVayedVtOvkeyYN2mBny71qs\nriJg8qu1hbnTARNaM05iP3wMTxA9jgUVU58FGosQHUOOQEffn0+GuL9WLk+d\ncEglrASuIRZ8gG+kQH/ZN0DlST1zRYS+3S0fNqTgWU9PEbqioHur9Zk6XavX\n1zYuSUI9X7VPT89DguBWXgdrJl0eOb9oysx0WtH3SwkQHYM2Gic3KMxC3/K9\nEQQ2sNBQnGsWwhVejn8WrNaFZjsTMJWRuLquIsFbYgRSvJIxYHf5OlcHtydM\nL4EV4jMG/8IErf13NHJ+Fcd1r1EE9r945rzLdREkCQLqbNo6MCfmHUZiXpMH\nzzh2SR/QqDF6hihWivEyUSAhQgGWbSF8xGCs1843L63Q9JIZVqOVdIm2Is8u\nvo1/6J9hnC5ua7wu3FsPcsAfbG18f0gzGfJPugpSDhjJ/BLZjNDT1sDmL0AF\nVh8P\r\n=cGdP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0dc6dde296550370ade2574d6665748fed37f9c9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"expect": "^27.4.0", "@jest/types": "^27.4.0", "@jest/environment": "^27.4.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_27.4.0_1638193061245_0.3092925688295014", "host": "s3://npm-registry-packages"}}, "27.4.1": {"name": "@jest/globals", "version": "27.4.1", "license": "MIT", "_id": "@jest/globals@27.4.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a31ec6a759ba1a45639a89a4a22419c1b26f0998", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-27.4.1.tgz", "fileCount": 4, "integrity": "sha512-q0quGdhSvuIptQZyf07DuUk4xrn2mrbX8aeRTEbvE7qDQNvF0NF41tflDthJ394DnaeiW7pLON/k6K/wuO14TA==", "signatures": [{"sig": "MEUCIQD9mKJuQwWahwZpceZ0/BwKK/s8QFNOLSbiagEzzd4ueAIgDPQ8rusiRcINk3BoEBqysdGJXSQTWk6vANRrBd5q2Qw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpeLBCRA9TVsSAnZWagAAUYgQAJL8mSYeDHzBgP4+1nJk\nlWc6eyVtQ36fkTfvjNqxXqhMF2pBepJ3h+uDIUf3eZYYkq6DRfZpXzm1xn6W\nz2AhY2eTXe5aR0NmGyJzH2CkAQQB2zlr3vE/ceIQV9hzYmSxj2o4GOBeR/09\nOEFe7U1VrPhMYRVGQntjksouWKfOcY3LF3g70g7rEzrxm3D6GP8a27eUXsL2\nmckyIBd2X8Cjx3REN1P9vQVFUFEGRKYES11FH6Ww1WQLq2kCB9gsim5ih9em\n4GTP9ZT0AiG06vJD7N2+7ZRwbYXuZPw6TOAMFiIKnZz6cnJcP01TsdO9m161\nVZaTKA65wsP6brntCyy2rldcmtLtB0Kj6Esacpbf6ZN9YthVVdcFK+oUEQba\nCGlX1rIcZ8ZpJCXoyzUavYcxhXsOGCC9DuVtWrBNIAGnD/e9ZV9vTUxesDtT\n6T1ETjri16G/Aa+PWQe0GOqb0Tv0NMIsH97F1+VRq8bvEcgN8GjSQO816pb0\nS1weh0feAeFSOVpB++Y8pFBRM/9vuwbmQVnvAV63NH2aD9eHB6yxBNfJ0bNX\n3aUQ2y3yq9RvZ6eufXK/RMliwQvicquBIiHt/ClnGXb+PmYUqWfH2uUuBZgG\nGT9xDNLk+3Zdbl63NkZ4x78vSSHMSOvIjpKrIhVXNnHYs51kXdkRbrQRfYP2\nNwOR\r\n=ryhL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fa4a3982766b107ff604ba54081d9e4378f318a9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"expect": "^27.4.1", "@jest/types": "^27.4.1", "@jest/environment": "^27.4.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_27.4.1_1638261441165_0.8125119685627284", "host": "s3://npm-registry-packages"}}, "27.4.2": {"name": "@jest/globals", "version": "27.4.2", "license": "MIT", "_id": "@jest/globals@27.4.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "56a402c5ebf22eba1d34e900772147f5126ea2d8", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-27.4.2.tgz", "fileCount": 4, "integrity": "sha512-KkfaHEttlGpXYAQTZHgrESiEPx2q/DKAFLGLFda1uGVrqc17snd3YVPhOxlXOHIzVPs+lQ/SDB2EIvxyGzb3Ew==", "signatures": [{"sig": "MEYCIQDqhpCUiDZEBGpV1Hd3Q3PL6fB43OVjWHim9L8uBJa8dQIhAOhb9gvjEeCkYpxnjm1ljepnDOQRfV/MbSyvht4xlvw/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhphDWCRA9TVsSAnZWagAAp4oQAJ/mq7jeuMPdtPt3aqzv\noidNPjXUom2/K3FEQj5BQX7yXxpRVqgm58kt4NItLppv9nKm/rXhjl6nCp+G\nRERXsW0Ggzu0auUhwLr5Z42HgP+cql3aGbYzDrrWxDTg3v5XnvVIv6lTwWtw\nkDxqh+eeV4r9fWbXPI6VVizeQozEMlspI7/oGKlr3dvZcywulCwXvmrdvao6\nH48H1/kDO3RPhnePSsclvk/DuR0H29XELkg3bdZgPR/sIs/YzBUzL4vdKN5p\nz6Uvw6RkvjIrKT1wn15/7NZFxg/sQdXjgqCeoxhkPFwAb7lAAL+Yv2GNsnJS\npiQAQIr38tBWL/dnLGmqh4iKmiS6OjSEobLGIXJT7oFu8+244depqdDUkz2v\novDDkLS1IJ+sE1fzJ0hSCtn2SLieGVv2ol+OzmK54pPTiilFKM87wqCUfcXZ\nEJ1TO0Q5ND7SzxMYq+sm4zK+pNrWRGSmRl7ysCBzqP93eDGO03FE+8/RIqeo\njOSICEn4Ko0KeXta6AUJbgS/mgrDve7wNKpWOsVGVcMvfTLlzk74nQ1HeDhs\ncHgt0jeHPfYIxdiWGgZmz83aaWJo1wO4Y1MwEtMRAUBrPDlsBET92tLOgYTm\n1l7yFQy+wINTqE+97cgPwjNSIPef5knPcuMM2nuG9JCL5JTMbheef1MMxorM\n7/qX\r\n=ynGx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "7965591f785e936ada194f9d58f852735b50ab1c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"expect": "^27.4.2", "@jest/types": "^27.4.2", "@jest/environment": "^27.4.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_27.4.2_1638273238801_0.20347588110663795", "host": "s3://npm-registry-packages"}}, "27.4.4": {"name": "@jest/globals", "version": "27.4.4", "license": "MIT", "_id": "@jest/globals@27.4.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fe501a80c23ea2dab585c42be2a519bb5e38530d", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-27.4.4.tgz", "fileCount": 4, "integrity": "sha512-bqpqQhW30BOreXM8bA8t8JbOQzsq/WnPTnBl+It3UxAD9J8yxEAaBEylHx1dtBapAr/UBk8GidXbzmqnee8tYQ==", "signatures": [{"sig": "MEUCIEVejRIzcUt2OTF94KDjDj4vMKTYqfc/bJvnR1x8qgjcAiEA0kXAu2Enw61u71iqepUY5FZc4w6ot8zP725sAUDC8J4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhstreCRA9TVsSAnZWagAAeEYP/ju7r+rokdH0ljEn3YPX\nBMdcMt+mSqE+ngApZNZFZq2nEZfV0uqRze6fjCEIjetZW/ffr3wDecMy0Ry9\n20HSnyGBa5pwFH60qhvVP864pGKP69VRuDLVu3BPsDdeDBH3TrLvHaQ5YsIC\nvs5OTV5FNEOaJk7XQL0cPjTaLUsjRWS7h5S4FAEGOIjCJc1Cis5N3SK1nBSy\nzE+p309XPuzpnFpFMpVRJ/XbCFZDO2VUpmNP1xUH/Qnc61aHF6gMBs5g1oGI\n2lhlk8lqZv1gKGYSNsI1TR7AdDno1pypcRKtSXk2mQuEG97cJiRus0+/Jbs0\nxxx8w9lkByKbORA/Gl+QORV2Dya6wzMkcT0lzqm2uONPyqWboM3Tr8wFczpj\nReJu6byUMOrwAsOvBifdKolkeFzZ84W+yoe8/SJUgyqCSNeUvIxnpy+VPKOw\nH/qLsE7wBgzYUaEAALyPdJkqlTtNMTtcHxt04f5aYYXRDzOq6CJCpv7RiYHR\nDvLARebsvazfnXQDqTZ5aP76wBDW7MMWbTC51dDutnaOV1EWjbZjnmZFruju\n1WlIbhFy/p9cul9+QUkXmpWOlkUqTnncA1aFa25KKZbcmDrH0D5zhgPJPHFN\nytq3lMSy8DOMXMiMwZMVpWNArIoY6S/+/DEmwDirq0VhkwFJoc5hEpms7pL/\nxnWC\r\n=OmHn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "e2316126b2e4b9b4272e5a0b651c3cb5b0306369", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"expect": "^27.4.2", "@jest/types": "^27.4.2", "@jest/environment": "^27.4.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_27.4.4_1639111389851_0.18388711211999142", "host": "s3://npm-registry-packages"}}, "27.4.6": {"name": "@jest/globals", "version": "27.4.6", "license": "MIT", "_id": "@jest/globals@27.4.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3f09bed64b0fd7f5f996920258bd4be8f52f060a", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-27.4.6.tgz", "fileCount": 4, "integrity": "sha512-kAiwMGZ7UxrgPzu8Yv9uvWmXXxsy0GciNejlHvfPIfWkSxChzv6bgTS3YqBkGuHcis+ouMFI2696n2t+XYIeFw==", "signatures": [{"sig": "MEYCIQD7nCsoFL55A+M9nu7wC421gS9j8e2WGcKhcu1OZh0W3gIhAMrk+g9NBxpugUYKvjfL3Alfw8pZH9hMDeBmb9TQ32KB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh1NJWCRA9TVsSAnZWagAAEWMP/AhoU902GwobL26QL9Yt\nkdyR4xy42hmJPVFIPGa9nRAXrtJl60C5aJhPEeIPjLW7zCuxomKmbiePb+jv\nxhUCyX4Og7xy4R5vsAAqrHRfmLtUoANa+lPuWUsuBQAosIJLamgw8oPHIGT3\ndBnu3na678bgJPpF6vR8MlLJw8DHfVOtYoooqjQOQONCiCqBl6za3jkb+MTx\nljviycG53BAkqw8QjxhzoC9NLBuV6+RJPVLuymB4Ihx6olpyWKGunaySHhQc\n+njP2tvf1OoOXO49XoLrlnbus1tOIzv3/SIsxjyRWqzH+3WnE9E/IYoLvTJn\nYP3UzNkMcz2fv80ya89koyI9afv3bjr3jufVdt7Qv8JBXiAA9dBq8RB8I+P4\nE6+Ag1TuEy1ivwWpAvI2urWgLLjmb1Ap96cze7KYjZDfVfD/80E7qrfIh2qL\nbWKdX5QlCLLRlQRQdy1pKANP5EBnVGbkFvpWaAYvTQ0GMbCybt/U1fvWJXmz\nY9XaIYuF97339ztWXX9t1hLr+Uc1f2ETaOwEAAqcwu+XVnxEHuHSYPmDmyju\nRjwLwzHQfe9v6M4h9fPw0UgV3WI6Czvsd3o+82H7qMzRaQrbV7AbEtwIay7i\nmiD8PlZaqnAFAiSA/WTVwY2dtlOGworPaN+b8iAvjBcNaWNlRU0sD/hNmE7L\nozTg\r\n=gg6t\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "644d2d3e53536b0d67e395c0f35f8555a67beb1e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"expect": "^27.4.6", "@jest/types": "^27.4.2", "@jest/environment": "^27.4.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_27.4.6_1641337429979_0.14513753080135872", "host": "s3://npm-registry-packages"}}, "27.5.0": {"name": "@jest/globals", "version": "27.5.0", "license": "MIT", "_id": "@jest/globals@27.5.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "16271323f79e3b0fe0842e9588241d202a6c2aff", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-27.5.0.tgz", "fileCount": 4, "integrity": "sha512-wWpMnTiR65Q4JD7fr2BqN+ZDbi99mmILnEM6u7AaX4geASEIVvQsiB4RCvwZrIX5YZCsAjviJQVq9CYddLABkg==", "signatures": [{"sig": "MEUCIQC9KbEYHstl1FxVP5q5yHl4RxBh0UMVsJT3lfCSzuDLFQIgK3F80/4oE5jv8lM8x5VBq0Xg6eshnWDsdzp3b0fY65Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/kqFCRA9TVsSAnZWagAAWRsP/2B/T7XcDI7u1tr2Zeh1\nVWWj8CpQhwE7CvuiQ/uDvRuq6yp1gJGH84tajlyDl5lDC0dmuGY2DndYobBI\n9TeYg3c0ZjluttvW7UaiAhzUm56wxs0TPJuNWV7QDItbeG9AfWQaz4Fb1cGz\nm2PigeOCXQQ7cfFldpmjeJV/xjc/++d7Y+1OECRRicZnOvsiaphk/diL6RyM\nxltVdFFi9NeVZbtopFESxhrgz+ouZsF4PNMIwG5pyL4HYzyBUnHGA6QQEd7U\nM8MJzRVDLcalvxZzJnOJ78CavZmaIi3KWLyzwoZqzkVDwPoj6uzua77Qdi/z\nrSBgmfeKolv+meC0OjcBD+4IWWcCeLYoX/Zo6OrgeUGVh/fNp5NM3UAUJLCC\nlTFmrxwqttM4VDqihlaQn0iKJiV+8ZgOpFRuycMF0DPYGKQXc2sr0FP6KI5X\nzA+JW8OuQBb/aXC44sjWnfy/CKZz5jmcw7lvv4h8Y3FOahPy7g34vbsnR6fv\nukKCjHEPITSKBGfoBD6ZkEESGrIKkmp1cpOXISkpzLZrim0olyn03hI37mIY\n7QccebcGNJd6hGzzHL2eYFkIBnQQfYioCPdV/4Fws4rZ2yBgPO70rkfMkmTD\ndy9i31oU9207criBhqS6LXtD21V2BBBj7MaemF1/qDR+BkN7JfcIwb7z5qxs\nZ5sg\r\n=ovZo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "247cbe6026a590deaf0d23edecc7b2779a4aace9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"expect": "^27.5.0", "@jest/types": "^27.5.0", "@jest/environment": "^27.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_27.5.0_1644055173518_0.26841283959621176", "host": "s3://npm-registry-packages"}}, "27.5.1": {"name": "@jest/globals", "version": "27.5.1", "license": "MIT", "_id": "@jest/globals@27.5.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7ac06ce57ab966566c7963431cef458434601b2b", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-27.5.1.tgz", "fileCount": 4, "integrity": "sha512-ZEJNB41OBQQgGzgyInAv0UUfDDj3upmHydjieSxFvTRuZElrx7tXg/uVQ5hYVEwiXs3+aMsAeEc9X7xiSKCm4Q==", "signatures": [{"sig": "MEYCIQD0PpvY1LzOyrmbCutjgKFW/EPaKBfS6LjSj/YmM+ZB/gIhAN91uKKJlC9sy+0SZ9YVfz7w8EbTkM2/k0ySbbelILQG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAktvCRA9TVsSAnZWagAAdKYP/iS++88U9k1gwIKZSLwG\n9Y37g54ouHydrC/nmyCLM0ZQARQv4eKxWYtW6l12jPfbuK+rTAAUipJaSFV/\ni1ZbduMJPve8/+H/InEFEzmPYtVPs8y3qx90RikT5GzuP7npfIyZdIdhbVmE\ng4uVp4AzNWGS2SzBIIl/QTmzI19Tz/5q720ZkgxCIDOsEMhwI6RNZ0sVy0zj\nadycuMcgn04zPis5TCM8717aEQd/xmD/96GoTgtk8vUUgyRzglT2nP5NSJKY\nOsLc6KL5eLj2LY9yJ/4dXs47MINFsGFU3F3MEfj+Tykh9GT5/YFAnV/PDWJv\nYW/aD0qDvONu+UygWzGQUSOIGW4A8r/spKdSor4hYFxOQP4ZvWILb598oI9Y\nzcjKOz9Bp+NDVjcBMIGDfS+WVajLzbVRvtN1Hk9z2CTCFLsKr8SBUxHIxqW8\n+L1HxhYf0cAJhlLvBWS1ELhj3xwVM8DvCyh6OLpQ2gveHpGGzscHxDKhjY5P\nCru5m7d7SaqNGJvDKhlVkbFz3P6KB4aVc4DFewBF+v6yXqk3CSMba4PKogaf\n1MWeHTnAKodsUjQNnVtCHQe6vnws0cp9BsNxVBHQxuguZEBe60UfnM9AUEd/\nOV7Jcp4FoWjVgdfIzZEO6MLWmgU1gwVzoY/cww3XYCNCFYeaYH0oNrZCfwti\n8fPR\r\n=26p8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "67c1aa20c5fec31366d733e901fee2b981cb1850", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"expect": "^27.5.1", "@jest/types": "^27.5.1", "@jest/environment": "^27.5.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_27.5.1_1644317550918_0.1424923498349504", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.0": {"name": "@jest/globals", "version": "28.0.0-alpha.0", "license": "MIT", "_id": "@jest/globals@28.0.0-alpha.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "596282e0ccfde240b9bc20afd7d26fa79009ad75", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-28.0.0-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-zh0XjyGCaiE2xJW3FFCl0Qt/ewu9UyMftjKyZ3r4dKd29wsXiFKWAtQWiqY0Bz6JMphac/NHlHDchQPVYaLB/g==", "signatures": [{"sig": "MEUCIQCLSn2+QHAFKWGTCo4vMjALRVaNhT5cfbCf1BZSLGE0rgIgdJTBYy1ALaTtfoDHpI79YRH07bQolY91Ji6Rjx848eM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3698, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBVbICRA9TVsSAnZWagAA2loP/2WVXfyhvDT3e4tOlWMK\n1sF0/Y62kR5LejgYokH69otFVaN3OHystRbx/klikk5tjcyyvFyTA8z9OGMW\ngTbmqbhZXXpbDehoP6ltb69yCqgOm7XgZNhAegwn7l3O6IFeXFGi0pFq87PB\nZqNzYl5nlUPz60Kumh6dhfb67ngCGq05TTXSvKHSvlDESYll3XKF6RWF2nMK\nxQRhoeuWAJENYCufgz2B7zByPAzXitn+FoUCdG9ap8M37cB9p3zgXPUXrC8q\nhiAwWPfHGFSWjSd4s+LwiSN+GwTCfIAtkk5W3kkHnZ4+TWRHmdvMpep0dAQj\nW2xMbD16K9xAgZl0gDKAzOZkAKdTLh2wI/5D2PrUAjplGwqJ3XFHx9P1ftgs\nG/zSvJ6bL3ysrJBPj7C823XagOFLL9IjEQCQRKRUhAuWTddnXiN2Pavz1x/f\niQNt4AhRDPh0QiLY4/fLjd2JaPLfsHYzsXr63kH8EkTkCA47/t9otJ07hOLc\nllXRJoPzMxmCeC0pmJodsk2+8U3nI3MfJKL9hk6glUOfqVMDvDCEej+7rbvZ\nFDc90rfYov8T5r6rYGPEzR2kWsPqvaihUkFjbQ3eWRjXmw/KjkN64ClVH9V9\n6+5X2YYhcHpEL9ZpAs5FHpMT1mE/TqWDOsnCAKwdu8m2cldhdCBZHJNTWXg5\n5i2m\r\n=4TMe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "89275b08977065d98e42ad71fcf223f4ad169f09", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"expect": "^28.0.0-alpha.0", "@jest/types": "^28.0.0-alpha.0", "@jest/environment": "^28.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_28.0.0-alpha.0_1644517064656_0.8595337290685718", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.1": {"name": "@jest/globals", "version": "28.0.0-alpha.1", "license": "MIT", "_id": "@jest/globals@28.0.0-alpha.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3eed109edc94f95e367f344c68facb570d96cf9c", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-28.0.0-alpha.1.tgz", "fileCount": 4, "integrity": "sha512-GTkwrQIqr3LHtEyo4h0G3UepBL4UyIhBwtfM/jKQzN+k9n59N8mrIuLpxYg/SBPt9ZoF7DsvCaKDUFL5tSbuCg==", "signatures": [{"sig": "MEUCICV9KFdFpM1wC4JEKvy7AgCB1MLZqaeyMgCez1eto7gSAiEApx6JIzioZdf4INZsInGHib/0Ae2uHpdZsG1d17bXSlg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3698, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBqtCRA9TVsSAnZWagAATUMP/15ceM8vcFaJJzbAqv2t\nNkQtbGd5N7ZDLPf/1JhmJB+MI4+n0fKh+Am29gBdYRPuh6Khf5ZaJVzD3pzX\nYEa5IHlSTtUYTWAF8v0mqcHSvUZ+57q6ezB38LM+Y+8P9b+RWSgAbE9MpZhQ\njgot22asfTaKASNFQiZ26UHATT+5/lX2rgXFhkKbJ8Snaq4Az/B1ZjvRDrib\nfuhHgistulXfJSLyhqeV/X35UIg5YGnkHyZsi2HIGXOPdqV81r/kK4ZXjr4y\n+nbAcgqcxPrKZcNfb40o1am4EWlUkkVas7LSlFbvM6ow0vLRRcCzMphMjsyH\n6mFU5+Qb3vzIaPkvxsGQdEUp+w0UQJZvk3x73rf1V0bCZ0LyU4mYHLwTQoqz\nmhA97nQa6qkgTVR7AC8e1vN3xBCHjYJsOlHfTxSXEy1HGQQrRvwSC9TpEYxU\ncREtiWqkesom5DaLCp0nJk88hru/ZK6d1G2bXkYVnL4w9fNcebcdBaixe7Dh\n0LeOSF6nhaXJDzgmNUS4KXcfceYvHhNY3Mknfte33RRuTLMGixLMtNWIJigF\nolPCgQc82EPocPBi2nrZ+IyXWZQBu9AEx9qoYvkyevB9JMlUEdwkdaSpvHLd\ncP19NyhNJDP/UH65bLU4F/GWbkdHAfRqyZUWCQ5yUh4f+xq35qq26Jecst7o\nrxab\r\n=7fEC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d30164dde1847166fa0faec98d20abffd85e6ffd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"expect": "^28.0.0-alpha.1", "@jest/types": "^28.0.0-alpha.1", "@jest/environment": "^28.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_28.0.0-alpha.1_1644960429376_0.11608058821178524", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.2": {"name": "@jest/globals", "version": "28.0.0-alpha.2", "license": "MIT", "_id": "@jest/globals@28.0.0-alpha.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "349070ad8ee3f01749117550e6bc82463205497b", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-28.0.0-alpha.2.tgz", "fileCount": 4, "integrity": "sha512-gYm4a18y2VAhsGenQYsM9JtSFfhNPG1w96vy4oDP7QDXmjPMZ2rKkDIFYRVuWrc1XqSzQS6+9qHXbE5xBAfcSA==", "signatures": [{"sig": "MEUCIFyjjzKsbBpFasGCXrCxJlaz7BY6dMtyajAyAKEPfkDwAiEAg3ZTrfu8u1iEpTqnzRcS6XpJNcakZumD9boBq6DdHQc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDT6KCRA9TVsSAnZWagAAMk4P/2+sIWDn6ziW/bkloatB\n4Na6qgM6urB6FEusCKJaOzE+pLBX14VRvk4YvHUxojXFWa7M90elnDEhCYIC\ns0GSe4rGbaMb4UtAAVL+0VDpOXCSw+rrf4fFuYgt0FHhNbkuaH2o7Zcyqd/D\nmpm8j93rF+CmRLesC69vJzzQia5ZaS1kUqYCVoeezsuW+NkSjuUu96HUOYkF\nxkZWjcRioCOz9YxLKD6pdoN/PdW5ti17bWvSbI6S9alrtUD0dtyIRXDAIrJ/\naSVgcrsvvPBt4/hNtXx1cRM6gJs6CiAPht1863Nog8iCK67l/auiZCRWj4JA\nsOlQALczFB0fDadmOEBRd7VtfwpuV9tVii/aFssnAjDIsaLlsvzeV9j8OTRl\nBLvJ48nazoEY6VY0J5JI3vW+RsRUuROHImuoxqX0ve/9KZp+YjtgqfhlKTBX\nWw/80z4xsIrpE6aSw2Jn07SYqkyOKc3+13VRGSiVY7IsekU04Bk9V1Tlk3Lg\nmLhCuF+xCrkoxEhC5TP+NaqWFKKztyddKLs2bh7Iuvldx4Sm6uIW91Z+oDQq\nFV4UCTWTZbk1IRUi6WsZp29ldKo8Mnn4vYSmD/QCTNQLL8s0kwOTk06EDSFH\nn0lbKEiiRX/yiM6tu1lds2uNyxdsLopS/ARlSlUwg9GJs7WSVmXOLfPHEeeJ\n6j04\r\n=bp0A\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "694d6bfea56f9cb49d0c7309cdbfff032da198c2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"@jest/types": "^28.0.0-alpha.2", "@jest/expect": "^28.0.0-alpha.2", "@jest/environment": "^28.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_28.0.0-alpha.2_1645035146472_0.47548894735580594", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.3": {"name": "@jest/globals", "version": "28.0.0-alpha.3", "license": "MIT", "_id": "@jest/globals@28.0.0-alpha.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d31d8d4041565e910baac1bd7051411733034b88", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-28.0.0-alpha.3.tgz", "fileCount": 4, "integrity": "sha512-/7HobeB/LZeSFC4SfsYfms41YpAPqg4L/1e4Nocyd0GqiJJcmWrPr5lBDotyltsXGBt0fMibajHt9RsthAX3qg==", "signatures": [{"sig": "MEUCIQC+VbcFyorZL3yOga7MswLDV0CTtX8BcQo9QXQ1Og4vTwIgQ/LrUedwjXwKjGshKqa6qyZiwfRBT6nC0sA3dovGycE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDmzmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoolg//RbdgZoGqKEpuou/GGgc0NpwYsex7z3o4WjNIucCFr6MToXJj\r\ntAMRxPnd/sBLbz1B+A6WvygEwBF43Wbwf5IsYxFyUdzBt85K0gANzNEZ8DqQ\r\nb57rkT0ioxcMWDO7qk3NyHpLaSKlpNGmvj1mQeLkhZjSAPSp/NFSeyuRC4P/\r\nbUMmz6zl70EH718KRb9g0SNOV+KER5hFGpT1Gzb5psadORVMoJjtZSP16oyj\r\nhR84pCYdZwoSEKDJXN4GQHW878XZCePFlPd09M2WRaIt0+9rx4Hw0fQNEwIG\r\nAwY8CSLDP+TNGwycsPf4pIjyuaVeWGmogS5ob+6Z4RMulPCYYYKuIVxgZAf0\r\nEfhRF8Qc6gNCq9kcShSG8US58KYLC4ID5t8/FYavM+D3qSrgxtVZSVjOF2y4\r\n6Ge8rOZTciXbj/cjvB63u/Jms3GtV1zqCtb3LehNUK+99TqcwONEhss0HFXi\r\nuccmlRZY7e4vFfNsTMiAvpGzahgRVdoXeTTBmTevWR8KMbJKZ7L+cTMX/5g8\r\nQtB0tiT6MVOzFGHJCV9KBtzJXms9XZRHw5tKlafq10uFRUiv6IiggX+kc+KP\r\nlmYzjK/+FPJPBRIx37DkATtduYk0adyMNjooHUXyufMqVcPUz6MgvPL6BFJ8\r\nr6TjU6/pIkjRln8K7oa+LlL/iaGe4lNIlY8=\r\n=TCKu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fc30b27bd94bb7ebeaadc72626ebbdba535150d2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"@jest/types": "^28.0.0-alpha.3", "@jest/expect": "^28.0.0-alpha.3", "@jest/environment": "^28.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_28.0.0-alpha.3_1645112550837_0.802204608551677", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.4": {"name": "@jest/globals", "version": "28.0.0-alpha.4", "license": "MIT", "_id": "@jest/globals@28.0.0-alpha.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "208232c2ed5344d85d78123659136f976f44a2d8", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-28.0.0-alpha.4.tgz", "fileCount": 4, "integrity": "sha512-HyQJzQNJWjWg4NCHPeLGIt+AhVNh5k8ZWJW9OtOu65Ib5kfnKW28HWW0NwErHO50SkaYh6cC+hr+6rXWIJ6CmA==", "signatures": [{"sig": "MEYCIQClW+r1Dxq2taAOHGHjLtGWFXYO0KjhqMJXpRdM8hWXLwIhAOtg63zWD9j54GKkmKe8taIWxgE+rP5J9lWkPs+3tUSb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFNOKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoHXQ//fC5mJJJFJCWO9EkNGWeQKGuMQCEHGIJGTPwsLwyEYCA/0BND\r\nZdL1eFgWgrpni0Nb4OahxCPS4wXvPuya/U3V64vEDzYUTWp9ysJFbRZwsnKf\r\nRQD2o1DauIaDOzECzN8O9jDjCsGEU+CggC42ShNsYdX1tQzS+9vxOw7DrvQN\r\nEkPj3mIzyxE9pGFX6JAC2ZYmOOGzIhcb3HboPRvpuW4hdEXLmrU6tnJnGQ9B\r\n1sZPFsrMU7Ck1eA9VtM0PMPHTdk4aMzE80+4PXB7f+UaI1lMvYVCrxeMZ79K\r\nCdE2HrFkyKtQktv6+bu/TR/vRgC5rARtwswik847iX4mOy2nz0w431+2OCCO\r\n2DpUxuQvxjIUfnqeXbpi7E5+Btm9vi5ofet0pz+jKHWVjLdgXjNvzHGY2M+U\r\nhikonSNE8wBoL0oanwrn/KRF1IDii5/A3t/HKR+U1sETeBBUFGU2YZCaF5bW\r\nOXWz16F5fqH6t+8qXLZKbmWbMdiflX3Cw49E1H2uDx2x62/8+Szqsww35o1v\r\nWUgVzFYYnFp4Y3EQDEYrKMSEn08X8THKI8XqcN4TinKcqNlGhcGI9HhBM902\r\nG2UC2SDfzsg5qraU983HCykFYoqoCyXj0F9AOIVl/zyYxCldLDENWueTA2aF\r\nS8igPnBQ7/0F7RnGw1YwlGHK4aZjxPHtTSA=\r\n=8GXu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c13dab19491ba6b57c2d703e7d7c4b20189e1e17", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"@jest/types": "^28.0.0-alpha.4", "@jest/expect": "^28.0.0-alpha.4", "@jest/environment": "^28.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_28.0.0-alpha.4_1645532042444_0.6897962979540242", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.5": {"name": "@jest/globals", "version": "28.0.0-alpha.5", "license": "MIT", "_id": "@jest/globals@28.0.0-alpha.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "da61b139dc4562818ec8c9dfbb127e14cb1888a2", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-28.0.0-alpha.5.tgz", "fileCount": 4, "integrity": "sha512-OI9XR/xiWmL4/roeK7Bt+tv2nSexgCrgHGvydBQk6CmMGf57pAJcwSTsh/sLWzhYGRkkLg4JAksrcauprrR1Fg==", "signatures": [{"sig": "MEYCIQDtZebZb4t1OiQRGMuITCOultF5kVv4TKE3BsWT/BGLTgIhAO76NmSganibPixcA448VCFOZBU9pCdZlpqD3hXXhMph", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF/E7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr6IxAAo3T920GbaFjiJ92ux8CYzHu3WrxHWUXQooVK82CZa22uhbxO\r\nIL/mWpLG4A0p0g0Sy0RqxwC4PaCEGgZjWS7N0eb6vh50N1UAboqqeHfELkmA\r\nZ9AZXVMiQA933PW6CDG7EF6WA4HmDJ99sY6FY/Jj9wi66GMKdp/PjVWnKtF+\r\n1XIla1kp9FnD1mAWGUmnbsdTRMX9F3wbLcIrp0tMSOpv6MVX8MV23M1PTiXs\r\n2EzHgWfCtId+4kmehXVkdcbMJRnlD8t9ASSLjeDRLl17JDOeA/Vl3W9uZGxs\r\n/75Z+1sSFb/qdGENK2lqISRbuO0VDMTuve9rhMCJxeP2NusbZfIr8Uaf8t1r\r\nuYWN3Tk+pWdLz3BisQLMs8tNkhUJwk6AgFeD14vjt8N4Jrkjo5QU1u3e2tQP\r\nXdaEL5IRzd7Xh3D+6qTEEmF9LlEq8ctGphLLlrj0xxeMB2suUzcLI3VMEtAv\r\nMr5aTzERxmMcIextdvgQc/woLjaKFjy4o2gG9r2aRZP1OGxv08Xa+8gjGk7g\r\n8Iw0++CdSt/LxtwSpJfslhYS+nMsU+zyyP1aa1nOrQzjtSGlxyjjHnKhCMY+\r\nt0mM0DgUbkmnGSSsmefAhdmG97KbTlPkHeUCmL+Mb2Nqd5KKIpZjaTO5UOQ0\r\n1xTcRbM3x+ZSbRLkZfzqdGNcfXyiDbL8hWk=\r\n=eXcI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "46fb19b2628bd87676c10730ba19592c30b05478", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"@jest/types": "^28.0.0-alpha.5", "@jest/expect": "^28.0.0-alpha.5", "@jest/environment": "^28.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_28.0.0-alpha.5_1645736251197_0.04740132391368901", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.6": {"name": "@jest/globals", "version": "28.0.0-alpha.6", "license": "MIT", "_id": "@jest/globals@28.0.0-alpha.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f2a36d0e20d7f708abba640b2dfcdf1d0c646625", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-28.0.0-alpha.6.tgz", "fileCount": 4, "integrity": "sha512-85WvMoGFHoQCoklN9ubQc0vABNmrb7oTojEwBcca4sW+Vdt5F+4qSNhby2RkEwtMJfBlnINSTsqp0An7+Ok04Q==", "signatures": [{"sig": "MEYCIQDXIia3+nsD5h3uY4ZiogJJVxf9xS2o+H6Ay+MZjYg/8AIhALFYYr5O3ovO1hoO5yNTRY3lRD2Qb0RpAFzpyJbTztxX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHdogACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrSVhAAkeu6Xb7HAFQiVodqoOM9TBoNT/QaU3rJr48IT/teleHh32FT\r\ny38NCcMSNFZu8zB5Q9W7ZuSWNVh1/0flOGyyviHr6fXFT/Lmj8LNba05MbSK\r\nmRPQsPYdro33SCxMZttoi9GVm0Rf181bFoEADF1C/hPPS1fDN2MUJmLXEQtn\r\nDsZce1X+xHCOma2yJ9XfSmqQiskYXZANqwZwdrjPoVhRXpgNEuvmsIltb9de\r\nSzkWF1qBDgGWvmUDu/OTE5Dtc6twELObHLLnqDSBHnUDxQbJbXqpD+z3E2FF\r\nHVwM3pHZNxqxmTnc16wIqJToTq3ilbBFylh4hMZsr6XhNl1vsgYTfk1i7SdO\r\nQaiCOQ3v3GO+iU4xy5fDTT1gKxcFmuYUlkZJqx9SIDBfIVBeBl/E7AmDVbzm\r\nrgof0wawJd6IwT3W92U+6H4g6kyCFD9bZiz24tRRiNxzpd0wqNofkpo2A/MF\r\nEgZqOIkFaaNMFs0+NQ1ayZOWGMHpn7WlETynLpOqk70zZkONjeTV1xFpqbfX\r\n9mrO4U02PhT2c5YbLJhtbkau3xs1d8zFVV8dKpKHl2JH38tGE8jkvkAQW2IQ\r\neKr2IpKgYFqVwub5ikgRCpLNZ0SYxeIBUnSJLNusgqXUUxmKSp8skpxITQC6\r\nuiIHtXs7CwLk8peg+ZfMR3drHAlt2uL0JHI=\r\n=9MVF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6284ada4adb7008f5f8673b1a7b1c789d2e508fb", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"@jest/types": "^28.0.0-alpha.6", "@jest/expect": "^28.0.0-alpha.6", "@jest/environment": "^28.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_28.0.0-alpha.6_1646123552531_0.7330759793492176", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.7": {"name": "@jest/globals", "version": "28.0.0-alpha.7", "license": "MIT", "_id": "@jest/globals@28.0.0-alpha.7", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6f3ea437aa9056cebc3eaca35afc71ea01292837", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-28.0.0-alpha.7.tgz", "fileCount": 4, "integrity": "sha512-4Br7kryFtEJ2/HUJGyyYeyai4WoYZ08PFMUFHWbEnjTztdAkcln8D40OjtvZx2vED4N60I4yQE/OFSDi8XZYgA==", "signatures": [{"sig": "MEUCICd86lSKGxOrZiEeuNbFJWYDbpXcFmc/+2VYmVX2KfFOAiEAvCML7M/o/axoupZcC58JlBnHvqb0bWXqljtrvD3ddCo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiJIbKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqKYQ//QFV9BebbzKYvaoZARpCndZQ5k83FpuLJZKjIb/iAfeZ8WVFg\r\nQGAN2Si3C4IGhrcrkxLBUQ21xKCGBcnoNyG/cX2krH58oVeMbRi3U3axuHJl\r\nxYGUE0HYnvCQO747EjMTgA4zHdxponL2wtq24P8Ty8l4/9IPyl8K4o7I6eTm\r\np9UvbdeAnA+V7L76KsaZMudVJVF5Bj+WR8tEQBcfGGcjmEWoNDugXZfTrYYw\r\nf/EQZV3Mtqxs32m06bK6oFD+FYnY5nIkkxufJsFxiu4YrriBYBFjeZcoc/oG\r\n+KyjK4gocKzd/pdBQf+6oqkGa/ywwAoPYofnB5eb8od6F+j66okiVGhdh/uW\r\nOyO5yPE2wn0efzWkauWwL363G7erjjeVuMhxAfd1qt7zZkYXd/4VyNQKUvvP\r\nW1bqX76Qb2VK7WX3Z+q0kvdUTYjpt2/qAw0Xs1hhS3/MHZtTE3jbIcepivhH\r\n+TN5w1KlS9BPWa9EeW5yFqVFckJbaMdUHjGQ22Ycx+0UYFOCEzJns4lPQpHt\r\n/MlkPm9rHHfK7DJPWLtCw4DxpKTQdmW8MolfUkFNQJi/taCeGFs2ZTludGVO\r\njWEDPQupCuJdsqRb6SmhAo+UrOIMUDLKtRD+a820IJeSi7QTk4s+cyeO61pE\r\nk0yBNegyOdx4181OEsP+QzvXvCbwZasaYe0=\r\n=5+U3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "06f58f8ca70abc9c09d554967935b58ce85c48d6", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"@jest/types": "^28.0.0-alpha.7", "@jest/expect": "^28.0.0-alpha.7", "@jest/environment": "^28.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_28.0.0-alpha.7_1646560970231_0.3612767190209667", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.8": {"name": "@jest/globals", "version": "28.0.0-alpha.8", "license": "MIT", "_id": "@jest/globals@28.0.0-alpha.8", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "31f621485d67de8fdfa638d702c056701801e247", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-28.0.0-alpha.8.tgz", "fileCount": 4, "integrity": "sha512-4/RBTpsYHYkeD/ENEQKVz6U1A3wVw2H9ZaqVEW03dgzZsFzgjAce+V3+1uQFjrvLQZRHSymBdn7HA4+nM2tE+w==", "signatures": [{"sig": "MEYCIQD5IPgOSrJlO0LGZTLv0QLIIBkUipnePX4qwdvpWujKzAIhAIumSNy1n4CqDpMNbiU0pi3fWcjOuQCmv/y/7YDU++5h", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTFmCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrNtA/+IzCpbnFnO7KwwuwCCetK5icRhnNMtSdYWkYnD0A1syQyWJF1\r\ni14KQ/a4zNs0oC1XvlX+6m3q5fGWr7kXpbDr1c2W1uCX5s3wuTJOOwBO6sHe\r\nMd1d1RIaeZwP71IUhevmzBssp+T+YZvQaw25KNeZKZEUqR0mbiQNax2QITSa\r\n08TyymcT1sGDDlLn77J96FkGpG8p43e8vmi+MPBmqK0YmXEHwrU/zn8mZXXX\r\nr4Shu165+rc00Vtr5adEM//X4mc9ZbcSbItF5QmWHzxPmYsSvCE3pIL8e41f\r\n1U0cNO2ggyO6ir538FsRCZ63/njZyFJ+Akp8KJgzwo2e4hu5dglI7klhUIOl\r\nh5LvUpJIJBhAP0tHKDhHse/+lwKC3vNoBIh3ZwDaaJ8h9pf5XOkT9sVssBRx\r\nrL1L3050bVK61gxlQihVA8/HTcGbnMpDYXlFuRlUa5ZKi1F22tZlHkpV8P/7\r\nN7+BALXyUcQoC8c/1rQOTzQVVMeeIIMUQ4PW9F7BZkA8qPx9z0GV2LtaBR6c\r\nquDqWAOYkPMpPY1Bkh5p+X9Sunn3w+sxeCv02dU3vi6eMFT/Cu4swiLg1bJg\r\nohu566wy2Sy4v6z32NfEZFitiV0Fl4x5aLLhKocmuTm1Yx+HMvO32JYlClHB\r\nJlrEOuttgbaBjvoMamSERZTSb1ufpmQhYuM=\r\n=FkpC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d915e7df92b220dbe6e124585ba6459838a6c41c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"@jest/types": "^28.0.0-alpha.8", "@jest/expect": "^28.0.0-alpha.8", "@jest/environment": "^28.0.0-alpha.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_28.0.0-alpha.8_1649170817942_0.8470621780169787", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.9": {"name": "@jest/globals", "version": "28.0.0-alpha.9", "license": "MIT", "_id": "@jest/globals@28.0.0-alpha.9", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "05eaf3853a86f5044c6352ea2e9a0375ae8ee8bf", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-28.0.0-alpha.9.tgz", "fileCount": 4, "integrity": "sha512-N0AJurcnJTIz2xaqMTpcUfNI3giDMGdgFl9N8N4oXAWzMWQ1QKNU27H3LgPDL8lP4P1VkdZrbQ5ciqpag0yJnw==", "signatures": [{"sig": "MEUCIQDxulYZZHPo96WbOvnnP+sgxr7xJ+4yhZ226huAe1elugIgDb+QzQlJL1dcIaaOv2KriNM49nYPjpkWwknvMa4RFrU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiXpYJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmox9Q/+Nzwa78r04wAN6ufQuQOWaNucR2DbzAp9eTO3zUjYaGZy8uue\r\nWLPJSLqeg8r5YKpNBgFktaL2R6yw8ZwuUIibmrSNFHkAiqfWpakrndzJ1Hc+\r\nmPpufvzQQo1ja1uCGzpF+7cO0GPhED5s9cBhrFwfYSQ89E1X7erFXtQvBVku\r\nsxdLEXHp3cu3ZeeFjnaDNZCnTqZ6bFujg0L15J+iz4tomsd0NemHQmDW6ZBo\r\nmSPrMavOD4D9HMm+vDsoiZjMjG03KUc2MB7rtbIOiBxCFV8nzBlmD3MxtZVO\r\ntKno597MXaZnLekl99h9IhIvEiFQW07+nb1k3/UI3R+6GT6hUGoa3rVeA1J4\r\n432rsJOGhy1EZrHfvFu7AaSmiD86xkrP5tKoyJu5PBwDrLBUgzathUpo3+rN\r\niNUI4voEZ+JuH6UjYIarw7aQUgsphXmnX4GA5wUxqYQUJYrNgrTwUYEvsIMM\r\nG9hcFVGKVF3mO3jS6ycNWqq39iFX9mv8LT7i9qXSqK/OXaVlGJ7C1iAa4p7v\r\n+nKpZ6bFoPI+zdNOib5H3SAag8kaLhGGjPFsGkIP6MU0uXvKGmiSeuBNFPpG\r\nE1Fb3fNReZM0uwuh02H9a8vfOT0BxyVDUc0niyhyQ4lNea06ZdSsAMGvwKK6\r\nBL4VhccJDJ8QA77Vmr436I2ThX5gQ4/k39o=\r\n=1h/O\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "7c63f5981eb20d4b89a4c04f3675e0050d8d7887", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"@jest/types": "^28.0.0-alpha.9", "@jest/expect": "^28.0.0-alpha.9", "@jest/environment": "^28.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_28.0.0-alpha.9_1650365961504_0.35062773162393235", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.10": {"name": "@jest/globals", "version": "28.0.0-alpha.10", "license": "MIT", "_id": "@jest/globals@28.0.0-alpha.10", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6d2f11ddda45ba356eeb13756cf42cf53af0976e", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-28.0.0-alpha.10.tgz", "fileCount": 4, "integrity": "sha512-sfqecG6KTtESysjsBPpA7cFoPlcXM9N2jIuqx1YW2tcPHF3nDyP4ZSK8E3hFBa8CTFQ8A1nJ8fCE17zYR2yztw==", "signatures": [{"sig": "MEUCIGq6LFEux/U/STRmLh2ljsv4Mmr8R2kb2XFs0ybMg4lFAiEA4Tt3kBlcX4mHJKEti++2WyRu20Mpo6nZ/uXs1tiyerk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3720, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiX7g+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmowSQ//UdaXjSspKQi4utMe+uRUSnrKg/AKrHDV0KVIxSU986YImOK8\r\nfRrsiNT3roN5oy2TuKlvjb82qUTw4pxuSjO0j1LEU5eUBlke4WHU6q7Uv9m+\r\nNE7TYcl2Y7EvpgvMja+WmNofqsHW/vHiXZeEA2wHNeQG9usWNfUSOsXSebAK\r\nv7+hYEdsvRCWAOWmy2kc9u9cTLBcT2t5SrN4mw++b2VJ1mySDpk9crgzXtOg\r\nwuF3dlTZtLlzQXwOxAQiDOuqPm7i3l7RcRMTE85rAVMJUkRMxHwJyyM6xNLX\r\njRd7xeSpXxMeaIn1QOIwzCtKJEk3M2M/Cj58MVFgjm5URjQiGaig6FTro7Rm\r\nGT3r1s/mFORoYo3Dz1TdMDL/00kBuIyIVXC6M86F6Kb0Nj4oJRIHMGjfRDKv\r\nO3ByCYF1mA+e78MOP4qXS/KU3LUerfUBHTNn8Qq8PTRC3qFR5PKo0AalG+i+\r\nKX0xmGJsBiLm7wBJQSMaQ68uCBK6Z+3gJQrPJg1ATdV+Mp5bOE+Eo48+EtGR\r\n4e2l8mIuPtEfMCCRTKdoeKyaIyMn4OyvIQ6xEzPN/CnWT/lm2VoLIuGl90Df\r\nzs9/E7EPGgrvc+Q5yX+VdhOrG3C0UHUFJWjsa3+LoNn4T8LTjMI5wMUIxsMF\r\nRNAlGQhsa3pxLl2LSOX9Mx0qnc6J/T1zXnE=\r\n=UqS7\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d5eda7f23571081cea24760261d209ff3e01a084", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"@jest/types": "^28.0.0-alpha.9", "@jest/expect": "^28.0.0-alpha.10", "@jest/environment": "^28.0.0-alpha.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_28.0.0-alpha.10_1650440254409_0.6277049703339916", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.11": {"name": "@jest/globals", "version": "28.0.0-alpha.11", "license": "MIT", "_id": "@jest/globals@28.0.0-alpha.11", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "cb6ae5148826d4ad7d3392ecd75848b1309324a1", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-28.0.0-alpha.11.tgz", "fileCount": 4, "integrity": "sha512-t2WiH6OiycjJZHzuoazWFB6FMpq5GipiiX6m+pc/nWvut/azT27l1hMySUwdQSv9mXQWFeqbfEQE8Jm8TYWrbw==", "signatures": [{"sig": "MEYCIQCrV/5gfo0n0LmtqZtzGdiGz9bsjm/O8a+1O18fTaEo9AIhAPAgXoh3shoJ1XVtdnek3R7ttVP7NiYtPAI8TBiEMVg4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3721, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiYAsZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqOsg/+Ll2ygZN3mNI10omj8Ss2zOmurp/5x+zFyJ8J49oqJ0HtOZrv\r\nXsOCNBBkCNOtE4oizhfgwk1GOkXPh3v/dI7Jn4NIqLHou5Jj3qL/zPzhGDgk\r\niV5+rHwV2Xx5JcnoptkpGKpUQaLJXnYTAFwDqi0/A9fH+i+r20OHXoXQItYr\r\nzwcEogp5yndcb1Vhdu0AyWknXJ7t+PaoVrB9a3v253E99aQQENTN7MsDwcdP\r\n6TdogTB00r8MUrsvPW7S/GoGHQUURZ5KMBQdd5xQt0199n+1S0gMhO6B163+\r\ne4+PHYnMFCLOJty8CkdWxxo7A7WFbJ7i3aZz8gDeZF9ni0210AZ6MOnJY9XJ\r\nlPaKgdhnklIEB+9rkAWYhyRnZuuMpIhMZbhabjrtD+fyHG+QpWyLVn7FHKq6\r\n7iItDDVh0Z0DYyt1M1THDAJPNYwxm/XkUhIOCZL5PtkJBJIe+b4/ZNFIvPAL\r\n/DJZpUBfMG//pSI25/a+wvIk2oIhjeWzJMQML/z2cbZc0FPlikfFcy6FW0tF\r\nJucLPdvizxRaI54Y8aMkqUPmJAMC5wL5S03phm+8PLL2PlnuBjjaE8kTH+RL\r\nlaGaEzhtPDJVYDnAkpEBwoKXXI0zVBX4exy2fqAEUMzM+VQ5bfeB3seuvENz\r\nPeT/OeQAYdUlhMrHd06GZ7FNfWzNodaOOeU=\r\n=xG1u\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "8b4b78759d255746f11e396efe7d06ac93dbd05b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"@jest/types": "^28.0.0-alpha.9", "@jest/expect": "^28.0.0-alpha.11", "@jest/environment": "^28.0.0-alpha.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_28.0.0-alpha.11_1650461465751_0.11413136076370223", "host": "s3://npm-registry-packages"}}, "28.0.0": {"name": "@jest/globals", "version": "28.0.0", "license": "MIT", "_id": "@jest/globals@28.0.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "16784fa6331e84ea36463ffabcffb2e9ef19830f", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-28.0.0.tgz", "fileCount": 4, "integrity": "sha512-WnvQWgHWa6rdn3NIADBpaQDirVuynriOe9b/55dbw+5UH5FqWU8wedclfYTS+yh3Z9FHWpAIxoE38TmVabqyRw==", "signatures": [{"sig": "MEUCIGJ98Zor5tB7ZjV92Cz8Kg6fxgfDTLiMHfrt/7GsTlCNAiEA5T4fGkCywXDECa5ljClB2UEWL7YrFFwZWuR5v+Q7XgM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3320, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZo8xACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmosng//dbJzI2HuUlqz0FHSotYnLGq+rfkTCbQXEk0KdsUtsrEyjRjL\r\nWfbetVUCsrSmEQ1oA9CF7qymZAniN2Cw0fMSruWBSDurH+M3H4lZuvIp7BwA\r\nqW5i3WuLj25AI91AvRi5KrIB2U6jjGlNe+VhUVTr5B4ZRFwt7vc7RAUvh4q8\r\nbsZHWLx7GgdzEILz+jzQdSOTSUmEhoQaqyz2h4PXW4eWInOWvb6qqHdPOkXy\r\nhy2tKPIQvn9OTx1tlNd18hHE6lTytoPUpf8Ea8sZiIk5kQk1yTrNYSOz4Rrm\r\n7xCZLGh8tDlHvexYSjGoVoCPbJPNGYRlxkGyZJZjf9EJInxDqVg7HxKwOYo/\r\nNE4m87ni9XSfLul4DYEFGBub5HJvSjfTuCVd+cRpQyI4QUaWMFIUgsvUbPIZ\r\n3OVN352nH/DTPDq+ZZrnCheNguC9LSomtwviSgEDBzoIai5eU3nAx7cd6ZEv\r\nSYhQ/aho7Rg7j79qV01dIlgt7LF7g8RFccHcunEjz41gwWHpIrlziFkXeuHq\r\nNth4ft1HwWDxs+jawvpORUuxcDyss/F6J6tRyYoSj8jYBDFSrTOUxc/huzsi\r\nxF2Ih5ZM5RM8TMR1wVrenRO+NckgiyYMPVV7wlK19Zzwsm5f4zOSjxxsu73K\r\n4pjG+M41d9acUvIOmnuPSSgO3rdgXjvPqIQ=\r\n=NACt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "8f9b812faf8e4d241d560a8574f0c6ed20a89365", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"@jest/types": "^28.0.0", "@jest/expect": "^28.0.0", "@jest/environment": "^28.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_28.0.0_1650888496816_0.6885754814405443", "host": "s3://npm-registry-packages"}}, "28.0.1": {"name": "@jest/globals", "version": "28.0.1", "license": "MIT", "_id": "@jest/globals@28.0.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "165eced7e768c595c2eba5ae8e6d9784224328e9", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-28.0.1.tgz", "fileCount": 4, "integrity": "sha512-KBWuQ1PQjm8IKUObSSQAGlGguJZHKaVCHWY99FSGwjyf58hT9yCYH2wFfLhWocy4Y5otK2gZbsCwWVX6WXft2Q==", "signatures": [{"sig": "MEUCIQDnMP2IruzOkK1OxvXJzG7TffDdT8TUU84TGJX0ugp10wIgQHxmxra5DNyG8OvXudWrHsADM1TYvxa2EhjaH+DazBI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3320, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZ8NFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqbjw/+NdxG6j1l6IukxK4f8KUi7AIbhuIOyfGNSWKY92kCw57ZtgiF\r\nIJBd7wxUnbiBo3SCxWh5p+NSBe7xeu0AgIYAW+KAvCZ2xR6nJ8eIstlbKU4H\r\nmTg+LtQX2z+aQ+mMd5As1sITSQXP1N8gxoY4aZvYM+eNCBrhQ3HlsCleqbfM\r\n+1D91sktj2gvSjKA2PvHHSzDtBgHZHwwBxzToZ4wIx9JVDvukP3JTq9YXBn5\r\nIS9e6/d1UupC0YYfoIRJQKef0+uQ6KxIl6/JnnNC7E3dMBdCnvBJ3QmKnQpr\r\ncaPFgI7cacUDDvfW8CZet9JYg9rQVak6K7+XcPgvFbdFlr+uSVmNE+7LHo3x\r\nLuez485CCDgy7mmwpak8yQKSVsFlz2SK9RMSe7EA2TLj5iN0bydM26sPUcTa\r\nDHvcjmQPMf1amA7u/Ww8iG70E8P9DSrKG7HiDk6CyGcuIwOGRj4wQaSpcq2O\r\nqzZC31ivOE3dZJGvSxvQLHNxKje1CGaA1tycu9XIHQ/Fhlq7sjapnPvEfnCR\r\nLPyAY4wAcQ90DyBM8gsGNJ9kon+UIU7FByH1HceYEBzdRUYi8HjfrU2mbr/W\r\nBQzzazX1ml1pr8SBtoIWjl37KomQZBf3rOwPZ/6b1UDceBy2SlHEL+1fXPLE\r\nRAioyfuSjQQuxZ0BBiCOgLl18oF9vpRIi0c=\r\n=GObJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0a08639e4299f07becf1020a761adfec83536018", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"@jest/types": "^28.0.1", "@jest/expect": "^28.0.1", "@jest/environment": "^28.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_28.0.1_1650967365739_0.6776803102935867", "host": "s3://npm-registry-packages"}}, "28.0.2": {"name": "@jest/globals", "version": "28.0.2", "license": "MIT", "_id": "@jest/globals@28.0.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "999164fb1b09f2cb5ba47c890d185722876b2ec1", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-28.0.2.tgz", "fileCount": 4, "integrity": "sha512-gkOd1rTTLoZGM2OqOtf5wyzf8HNoM2a+dGbyWgqO3spQiA/OBE+d1kQlZ6mYs9NtJwJ1/TNAJNyBaPXIeo7xEw==", "signatures": [{"sig": "MEUCIQDQdvJXdfDkvV4+mVwS0piZfOdQZm7OS0m5mPKQuJ0XbwIgWOPvSN3rmvpgu1S4GqttdJN91MxXhgw07Mgki0ROh6Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3320, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaPRLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp2QQ//U4TTe5AQQzW8DLkfi4o5ZHIfvmi3Tw5xI3uwhmbC4z/APJqT\r\nlXOc79X2bYhfE6nXAedh7TpCjpPihU3WucMkBxb0JNCtECxhAfvMLH14q5SR\r\nz+bRGU+Ri27OupHtQAthOvCx9+2ewaMwkQBqPuvsJs897dCwa/wEq2HXhDbf\r\nKEI9S9zzPw+FAa2yXC2u7JkrOzLoXVYX2SAGBEvLuXlWYrPPTdPi9tNfDPpK\r\n6V9BKdcyPcKEzRCknswuOBmaNrIxf1GrAMXPuoQFrbNDIPN+tIgD3E181kYU\r\ntxmEnXnUSVtl33ucTv7A5r7c1R3orHg1jihDNRPkAi691KBbUqRhHMPOolxr\r\ncB43cuVyIOW4Wx+0XSnmwafGSFXL7+IZ++IEOuMO2osq9wBlx7eo7qhF3dTF\r\nm8OBE6Nre0ABaMR5n8mhDVb3SBSdgwQ0hv4gGp5E8GhNaVt7norbAYmA+LJM\r\naRzKcJVr9UoJ4ARhnL6q9BtT1ZPtAp0D3wrNstp0tjEzdoRrW5TTMyH4kmvf\r\nAsU82wCKcVgJGmYMTdBurogxscy18Cw27Vrm9Pkw6ghTwOslbVl0UBUsWEgG\r\nvNITm5E7iDDg/qIn0oiCamPUCJD+DKWNpDGa/aN415/F+FDKPWInPp3ERLNj\r\naQuKY0ERouuGE7n0MqZ55OInBOmV3leagwE=\r\n=Sgw6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "279ee6658d763f024d51f340fab6a37c17d94502", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"@jest/types": "^28.0.2", "@jest/expect": "^28.0.2", "@jest/environment": "^28.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_28.0.2_1651045451440_0.517438720917534", "host": "s3://npm-registry-packages"}}, "28.0.3": {"name": "@jest/globals", "version": "28.0.3", "license": "MIT", "_id": "@jest/globals@28.0.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "70f68a06c863d1c9d14aea151c69b9690e3efeb4", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-28.0.3.tgz", "fileCount": 4, "integrity": "sha512-q/zXYI6CKtTSIt1WuTHBYizJhH7K8h+xG5PE3C0oawLlPIvUMDYmpj0JX0XsJwPRLCsz/fYXHZVG46AaEhSPmw==", "signatures": [{"sig": "MEYCIQCPibs4Z/PEr5csdVPkUy3AdurayntQCaM+aIr0Lrrx/AIhAMOcQUPnXSIQm63LddaHStujp9nmLzpBKnQhLocW6lmN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3320, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia8GMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmokjg//UFWYJaN6H/VtoOrbGxd5rcnwoNrKZHc9p2n2PXo75i6/HdBf\r\nBLBfYXscyansXN7w8OLN4+piN7vj+7xTnSfs/F9Vf1Mwg0MBjwB3tM5I9SSC\r\nE4q/nIjBsz7xZIyolDwi6sQ9WRc/Z8do6FR0d6ySRb/G92FxSR++nh00baUq\r\nUgN510Q20x8jG9ciY4cX+CQJZafaJaCWfnQ665ieFujpqdxhEjuOnanNykYJ\r\nrPIjeBKxO5mfbKM18nDsthT3D5RuS7NcJoCirM1CbuZdYKYp0TIuaLfdGGkx\r\nNpNRfSf74Dttpa0ZDIiwJNW5pLpYnRamJJvWt5WNm/3tP9Drf1Fo7pQnaRoG\r\nJG7sHLmPD/rhdg9qSfFt38J6+PSgO5b5m/FdACj49h5HSHgg7xLx+iSp4TfH\r\ntvzF8zwkhmMDurLj9Mfp40wSRNvI0arX13xVm+2PDlgWmM2/wNkTY0sKcGPQ\r\nE+XjmQpWBpDDmW+1bzTKdWBLivhkQZ3rqdxVM9q4sNbwzhi71jbTPuv9QnwV\r\nVH+uuVFZ3g/qUud4YOBlWB7028/8v9WeSDIhsPHVqalzOzX66elDaj8p14ig\r\nrVGd8Dv3CKkrKVyEinJroo3OD7LO49QHuHXD0EYGXdl38AQKVbU5sLQuw/E2\r\netk92KCah8QUBHS7zKIm8PyqCSjCXmCpGrs=\r\n=4cSE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "1bea778422dc049b0fb3d7da809ffec39be549ff", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"@jest/types": "^28.0.2", "@jest/expect": "^28.0.3", "@jest/environment": "^28.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_28.0.3_1651229068649_0.5303401596577646", "host": "s3://npm-registry-packages"}}, "28.1.0": {"name": "@jest/globals", "version": "28.1.0", "license": "MIT", "_id": "@jest/globals@28.1.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a4427d2eb11763002ff58e24de56b84ba79eb793", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-28.1.0.tgz", "fileCount": 4, "integrity": "sha512-3m7sTg52OTQR6dPhsEQSxAvU+LOBbMivZBwOvKEZ+Rb+GyxVnXi9HKgOTYkx/S99T8yvh17U4tNNJPIEQmtwYw==", "signatures": [{"sig": "MEUCICTl4p5i6FVdqWUP/HvJH5s4+ha54rkgPb7+oboK+Dq6AiEAzEZNkgWkexdu0r7FPLIE17tm/Ey1TskHBS0FSIUHmhM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3320, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidP0dACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrrqhAAk+GYIdBPQQA1j2PZYgBiSRR5PWg3lGI/Ijy/5mrrYH/t+F3j\r\nKeF9O9hPLDqBAyAW8g9OM+bOh9NHBC0gmnlEmuCr2eM8/PYbbPHAsKO0soDs\r\ncZPYQ2iWZ7qleqvVIpre9Un8KIji4rCaPWDgvnTntvqN8EP4Jir1Q5keE95b\r\naewQGBpcf3yh2jqAWPTtHAROjCSvUPjvsOPAw6qql4F6mj17Q7+C9KIe8xaf\r\nRq6iZhzeLyYp6ofWhJt6eOffjuXvu6+N53HCCGliKBlp4PdkHncruGn0Gpvk\r\nkk9d7rm8IehQS7dRoKYDmM5fs/FZU/HZkA7yZxqDvb14EmgZzk3uWhxj+XB8\r\n4EFsON7jUaHG0j+U9wPVbpRUeYI79fCdxI68+SeFZOApG2hcZUlh1z3Xqo0m\r\n2cxOZHFqf3uC9xkPXhdFoMNQvMPje7SBseBAD8GorZo+vZPm51D3QIQaVkZ+\r\n0QTbJ2xtnOmQg7cS3RvQ7gO2QRTpkDD9HfJGPl52kJPWefoAVRO6KNSWGcPo\r\nhXLHOg47p18aDDD1Q9ZRWb6XKXBV8uHx+USiRCKjb8oO16Q1W4J5PVmhniUe\r\ndcd36M0mDqJXE0vZbxvHPU/tCodNWXFWiVd2/KOHT4EHqgHswQOK11/Unsqe\r\nZR3lizpraDwxHebPfhDcU1OrLeUUTjPBWlw=\r\n=maK3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f5db241312f46528389e55c38221e6b6968622cf", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"@jest/types": "^28.1.0", "@jest/expect": "^28.1.0", "@jest/environment": "^28.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_28.1.0_1651834141070_0.28142278094226736", "host": "s3://npm-registry-packages"}}, "28.1.1": {"name": "@jest/globals", "version": "28.1.1", "license": "MIT", "_id": "@jest/globals@28.1.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c0a7977f85e26279cc090d9adcdf82b8a34c4061", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-28.1.1.tgz", "fileCount": 4, "integrity": "sha512-dEgl/6v7ToB4vXItdvcltJBgny0xBE6xy6IYQrPJAJggdEinGxCDMivNv7sFzPcTITGquXD6UJwYxfJ/5ZwDSg==", "signatures": [{"sig": "MEQCIDDE+NUp9QZEw6AqpI3FrUJkU7BQO0HWiXzdM5TkSoXFAiBESemGCsx/nApf2ze6WZ7pQ1bzBdd05Hh87PU+x3lhhA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3320, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJinuunACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq3MhAApKipbOatcqgehwJtW51O1jvif0w6w491eLxZtSTdz7Shf/Fc\r\nFWpfH2hz/RLUSMhdg1aOAWJtuJnouTwE89WK4UsIw9wZQ2St+7T3lo/o0Oeb\r\n6tTLMztMdugXpeB2wKT0Td5ZrKj22yAPuH9Ov9YDzss4BAcHfKtW5OnIABR6\r\nbC8KRmJumZNvoBI2jcGBr+LXHH4cjDHFDk2EtvM07NwcVx0mibTtylmjxnGp\r\nbMSJMXL40ykgnnXVA98JJm7iPitidyAt1ONJfIRLwvoBIDYibUu6lCZvwJjD\r\nTb+CKA9uKCtN1ekiGbLC2P7wjGlx0aGo6rkSBw0fgmO7rYVseKYrDbHkOGIC\r\nrWLQcl/PB0y4vnXwt4IIl5y8zUAOTqj7sZDYTA8iT7VM2ucHNXaTpBMbJj7L\r\nsMzm+D2A0PntCUGudge4eEv/3gwnNVP67s5Z8MhhExxXJ0UztQ/WJLAUz/6X\r\nwPqYGMkv9GEpUoxp9FCj7aQRExWioP2ADgqjp6yiNHgGiV4LKcA+xRk52j+h\r\nUlSPIc3FEdAjq6z0KPKzEEOjQ4cA/1ukwl2CMKNbcJguLhmTlEmDBK/Dc7hZ\r\nJ4e13ZPcrd/1Jd5xB1DYtQFHrLcpgoyKxcbhxy0xvmTFfwEiTFa5ePzt9NfP\r\nj86ZKAZZei+r9MX683xRE6v2d3dn9rdFRH8=\r\n=aqd8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "eb954f8874960920ac50a8f976bb333fbb06ada9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@jest/types": "^28.1.1", "@jest/expect": "^28.1.1", "@jest/environment": "^28.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_28.1.1_1654582182881_0.015644628609064126", "host": "s3://npm-registry-packages"}}, "28.1.2": {"name": "@jest/globals", "version": "28.1.2", "license": "MIT", "_id": "@jest/globals@28.1.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "92fab296e337c7309c25e4202fb724f62249d83f", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-28.1.2.tgz", "fileCount": 4, "integrity": "sha512-cz0lkJVDOtDaYhvT3Fv2U1B6FtBnV+OpEyJCzTHM1fdoTsU4QNLAt/H4RkiwEUU+dL4g/MFsoTuHeT2pvbo4Hg==", "signatures": [{"sig": "MEUCIALpeY7fNU9WjhTVPNnS2sfdaqe/feF1u7l8xwIeaTIiAiEAzz87Afy7Yp2lzYH0MPaVqPzyyrmTzVytJhiq9UJ0ch0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3320, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivCqaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpoTQ//RrixdbwIF4lB7Tz6ApHu4ujSdqNnki4g6NFAkT89SzgDIgGI\r\nDVh54ClCgP7pLYE1QAH+eGSKTQm4nfl58iNJ6tY3CD+yQna3siyR991cHA6J\r\nPEj8QEtP/oFga1fkVwYSXNBlv3bHRwBiIs/mwNenHQBG3aKea+i4Rt8kGBJ2\r\naW6bdaJXxtC+/3A6BfTBYwHrr2QOlYDaZ/8ZOdVpZ7Ma6jb2mMEZqXTfdalT\r\nZdMStOk1QautCzhPjEn1+3rWeGveD/PoQWiO8p+2iYCdtXJm2IA9xeEb58b8\r\n3S47mCexRqNX24ihu61bHUSwWlJX4zXHaYupzP5U8B65gd0ZY+tyytuHgYRq\r\nIaNVJTQeb+TXjkk2F529tajD58Ah2u9F0BiwC66HS4U7VAj+d77oVlczaZr+\r\nauhoRHqifW3NlWkMsHwA6DI40K8aKbE9KR/i4HdzrzlNh4PmaTNNjbU9le8x\r\nMTCw7z3JYLcmBS4VxFpwqebnZbOIQ1OeYhbbTWYpxQjDZAcfWtG/sqZU3fwL\r\nY6rW81MaSPTQOoXEx2eGMQQu5tGOZ7Hbm9V7hQHSpV87AplqGgj4RjebP7XN\r\np7Jnka/4WJftfchfV8eKK1nYMrFFTa2g9fF4q6S6Fsm+Vng0d/K91ZsKO2a3\r\nWK3kp3xlyYT2OQaQb1ikhz4V6JDLO6tiiao=\r\n=e7Xf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "789965efec4253fc54ceb3539711b3a3a6604d94", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@jest/types": "^28.1.1", "@jest/expect": "^28.1.2", "@jest/environment": "^28.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_28.1.2_1656498842006_0.23789085533764398", "host": "s3://npm-registry-packages"}}, "28.1.3": {"name": "@jest/globals", "version": "28.1.3", "license": "MIT", "_id": "@jest/globals@28.1.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a601d78ddc5fdef542728309894895b4a42dc333", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-28.1.3.tgz", "fileCount": 4, "integrity": "sha512-XFU4P4phyryCXu1pbcqMO0GSQcYe1IsalYCDzRNyhetyeyxMcIxa11qPNDpVNLeretItNqEmYYQn1UYz/5x1NA==", "signatures": [{"sig": "MEUCIHXutLdyIHmgjQQbf9OExRvSeZ00eftlyYFMoM5EbDjgAiEA3w1YVzU+cptUy3vcoKJVQUfCnIFzLWBU1owFnnv7c9U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3320, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiztLVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoFGw//f9HZe7VvcOy5o7UYnZWQpRw17K0+mG0kTNU26+mMyAcnkKBU\r\nysSNLY65CFvhHCOZuUbgVHVg+V8QBo1oPbEzCRDJnlm7isp1jSrPGQ55P1T8\r\n+rLybM4mA907OOfr3lOKZEtZwcRbfSOso/03nEG5ImwaGZT6c1BIS5L/kcFF\r\nrSqnUUez1wf/J1Nypy3fGCRyySq2stcqLuX0sZZYoY3kvBeht9TJMK361g0k\r\nUWfTOhLl0HtLcYzFRVpLDEQ/9qBfnSsNMPyzFpBKU3FsmY0XSNHv0xHXt5Z3\r\nhEKZlqUO63GvJd9uYsXfBknKTxNfrwl+zE3zU7awOra3byq4oBi1YGqGmvTf\r\nEg4YE6gNjIeVNy2PW3JkKLLs+e3J1fcsz5yRsldD4dnjSqu6t4DZ501T7x/C\r\nRBbzmh97yHeBSyRe2olMJGZC71Sn5e//IwN5BPjx/YyCmY5I/bc4yoLHtbhN\r\nj9ztCm0Rbr8fRUOesr+TKr94Z7d5feafwFOJdTas9EPZd8tDjmmOSsgwusGl\r\nm/LZC0XykHumb/2tIP1tWxgm7PpQl6zR1yP0CpNwla7FroRceYnX3tTmXaHc\r\np6szN0zQysWCG2+WIPMd9OxP3qRk8lJxMaJzqa752i90FPMMmoKMqHkSENrK\r\nnPyFXb6F+qQ8kolVs/4Ma/3QYfZXdwQedNo=\r\n=RHZv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "2cce069800dab3fc8ca7c469b32d2e2b2f7e2bb1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@jest/types": "^28.1.3", "@jest/expect": "^28.1.3", "@jest/environment": "^28.1.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_28.1.3_1657721557200_0.1635375689712577", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.0": {"name": "@jest/globals", "version": "29.0.0-alpha.0", "license": "MIT", "_id": "@jest/globals@29.0.0-alpha.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a00982bcca8a18f335ab05cb85fbf2ce6ff2a04f", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-29.0.0-alpha.0.tgz", "fileCount": 4, "integrity": "sha512-4Bd1L5U34kQbppymcVYOC1BuSDRyhVL/BcvygOtbmXAcv/S+NZZWLsU0C//opRwgmhlQd4/gzT8Bo9yMpcXpAw==", "signatures": [{"sig": "MEUCIQCK1leBZTVd1BVu0A1q55ZMUqo145eeB6/oYY0bxhrG2gIgJtJ/nd+KTQvcgXOuCCBq9lATbzhf2JnBI8Gu2CnUpN0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3340, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1IgSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmow6w/+N0KnkcZi6eDmh+DQaA+ylyev0fSbpPN10I30DeAb1bzL2dDP\r\nZPfDqBXyb464t5+bkK2DC5OiA93iYG117qYW9LZ1Yiv5F8VtinFNkIn+66ld\r\nYxqQbCoCUPap28ZdsEIe2T28cDM7tghLozHRavjGkZbFuE8ItKyV8kv1+oUA\r\nC4FQhW1nsplV7gP5hfyQ9jnX51+sUYUwNgSjliRc0FYfGlwq9zeAGIENaSzN\r\nRNxzgm7nn3xcj92L98FPwFoho5WzGaOxfN/GA8CNABeAIl+VrDcznogU6Zwp\r\n7OopY8rTtTxQIh923e+2rNkKvKbcmjmdKVPrRD6nsads3jQKsbbnf0GKXghv\r\nUC35biZUQ3IqoBbhBuwWCvC4SQoQhxj4Y2cOj0JumU7mZxmDoqiwSHkQnlwe\r\nyXaIgaFlWIIuoqhPfVIAhlW4qTpfGM7q8w6OZUikiRlkxPK3GZPEJZ1gTGbv\r\nrIaVMfDXR3Jqg4srXENfnUKm14Zk5wlwY10O4RumQzuXBj276hWpg02ytP9U\r\n+k9miEExBVNXXo0mv8Exjv9J5nYnKCmzojgn75nqba+io0V71wDdmDEfgDza\r\n7i7enzyu92d3uPwQSWZ0a3Qs5JkIsdm2QLPu6sPhwKWQMinE3WgMt4/SN1UC\r\nmDpvA5D74pT+uO+BV2W5KMezH0tR5bhi4us=\r\n=p9Bm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6862afb00307b52f32eedee977a9b3041355f184", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@jest/types": "^29.0.0-alpha.0", "@jest/expect": "^29.0.0-alpha.0", "@jest/environment": "^29.0.0-alpha.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_29.0.0-alpha.0_1658095634609_0.5670301478431268", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.1": {"name": "@jest/globals", "version": "29.0.0-alpha.1", "license": "MIT", "_id": "@jest/globals@29.0.0-alpha.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "789ff0e6408b8b005237f6ffe36884ecd976b981", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-29.0.0-alpha.1.tgz", "fileCount": 4, "integrity": "sha512-yopV2xn5QzDrTEpZjmbVJ/sMC6Wsm1Srae5XUjGQO7AiQXVQAjfuC1mfnRevpyj8tnMJdRejER2ip6zr6F/b4A==", "signatures": [{"sig": "MEUCIAvIV6tLzpW4fZSqmYHsHgW7E/srGoCud3vCKXxIi/+pAiEAzAHv/Z1+I3bLBy6KfxuxqHGAR+Dy7JD0KAw7shIXEj8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3340, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi64IIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrPBRAAo6j0vRZv/mrbgCQwyzfFE0bL0gUdDhRnTd4T/7O8JxQvAqDK\r\n+BLKzSZrcWjCuGzLwdBeCePtXCunvcVezU6BfhQRCDW6XKgv49HTUGVA7Amu\r\nPqVTY4cQ1AOcxkLJyORHxwg7I4h5jJzdvE+q+z+glLhaFT8PgGzirqw1uj6m\r\n9k1I6gAzAu4Q2SbetmGwNuONLuZ6Kl2bTH5K1oG6WRouHVMgM7KAwt8hUN2M\r\nyoEO1VOjaI4CHNoZQmw/9A3Xmhqe++GI6qHMN1HBSGRJFDKI9Mue7evBlUqG\r\nCtzlWbNdEotN7TG48TNIojDZrjH73h74eAnLpN2EpyzFVUGCODMXptNuL+Ij\r\np9m2m0SFPt2U3DZHMqzwMMex/SnNgPrRHVZvPhsR6Tq63bGf6lun8t8XB5Tx\r\nYhR4kc80e33qpPVZ6EdTWyuLvxadhQyxLnaIyiyig2pJuBf87EmyhbxjkIAF\r\nGXZ6hixYCY6dU0J/T+isrgU8nnW8HMmf0Df1Opqm/eBz+jjoCVU8TT/ww+Dw\r\nsk6cX9+RoWjH7h4eB+OnXPP+CcxlrvvxY0cKi/YW7ySaKARgo/DlRsNXVIxh\r\nc3SdIBlrDtk99id0qXdH62uWCIKfjnt7Koi8Lp69JAaguG3JTiVxzTaOZeWH\r\nd6ayPbndLryt6tE2booP05t0vYyM42HxW14=\r\n=UmY1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "10f1e7f52d9f876e6fb7f20c1903fdcddd8db8b1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@jest/types": "^29.0.0-alpha.0", "@jest/expect": "^29.0.0-alpha.1", "@jest/environment": "^29.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_29.0.0-alpha.1_1659601416388_0.7689989885112019", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.3": {"name": "@jest/globals", "version": "29.0.0-alpha.3", "license": "MIT", "_id": "@jest/globals@29.0.0-alpha.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1af4b8926ffbed209a289ecbe9a5865a2d2f9fa0", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-29.0.0-alpha.3.tgz", "fileCount": 4, "integrity": "sha512-QXGA39Erv3DqiFBs0jFmNDoko+4cK+Rhijw+F4vtLaMJAh1k6jZnVvw4HQejLYrQiqAuHpZSLcY3tmFbDZMh3w==", "signatures": [{"sig": "MEUCIAfhdnFNWmhWso7fYf8IbelF4ephI2ercmu5hOkMI2xEAiEA7vue9sZarE0UNUCLChK3tF1Fu6PKc8arMPdFcYNYFlc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3340, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi78EaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmocwRAAndNrDojAAG6nuLIDe4MbRx86vIBfphmkI/vkkemOvD002Pqd\r\n0onxgIyUs2xSzrVOrhkvOwEGUe4u1yXW1z8xMaxGkMbbVSPAmnbAXgxtihSW\r\nB4arcqFdLPqWhcbagpBQg6rJ/b4Ta+DW9/NQGPfz6g3k+g3LngGS2m6NfnFO\r\nRRq9egG42TKa1Mu51pNDYgZdcbcjfMyc4DrV8y42fMAhVmMaES/JhWYvJ20o\r\nZbnnNN6n1ZoAkjVh/bqzlcRDoE4cTJmKuJ4wzY6tLWAo+zSkl6z7FlyXUF8x\r\nKkOiw7Zvb191vLizHVGmk13Sg+jl2Ns3kEADnZtZAPz4JyDRjdLj0nfC5zuS\r\nZ1dAgzjjjnpewDKT1bu+4lGMVeSsQiXeERBCwDZm0iKi6YgpXJtgtPQyzc7I\r\nw6tNyanIGvw2r9TlzZ84AByQBLWOA1Fl4VGrgWEKkgiml2yquxl3dlMnAYzh\r\nWFbbvVVa15xZho2HOYLdsUd2oB9aYuFZ4jnX/F1Isc2QDd9WyMbwD4Hk/lav\r\nMY8RevfSMLszojThy24cJsRZtsyZ/oKA+Koyq3/3SorTUuFbDreM1wvl1rke\r\nPK6h0QM7I66/WI5Ool660PMTxtu7hyWBOUJoeYUDrFAPvhfkSGkjbYvQc1Sf\r\nWmUtDDyMglsWRzffbgvJ0tosOuNZYuYYojM=\r\n=U1+V\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "09981873c55442e5e494d42012f518b7d3d41fbd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@jest/types": "^29.0.0-alpha.3", "@jest/expect": "^29.0.0-alpha.3", "@jest/environment": "^29.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_29.0.0-alpha.3_1659879705754_0.6286883864361263", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.4": {"name": "@jest/globals", "version": "29.0.0-alpha.4", "license": "MIT", "_id": "@jest/globals@29.0.0-alpha.4", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5b3cdcafe2ddf6f3fb831fcb7af6039f554eb98e", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-29.0.0-alpha.4.tgz", "fileCount": 4, "integrity": "sha512-kgqadbIsLaQGHXxVQy7joHxi/KsJ4ADxQ56NuC9DsXfNNKUbqON1Ig/4P8eBtFlvwu7mMnS1RaUWmNUNugmtAQ==", "signatures": [{"sig": "MEYCIQDs0C9NnEfl9jw88JrPDLzPr8dV/dUq6Zk0Jg3p/gFlBQIhAKCW0jMxPiWPUiMxF6NSw+6Sog2wfWCwkfyOI8SEZoOa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3340, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi8QorACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoQMg//SJyfot83MXDZgB7yo/l0seNJlAZbDncgYVzsV8mL6jMt6Rm8\r\nifRX378zRbRBuVjfesQN9yYqj5ybQ6Hj171HmJ7bNT/urrBfOEGwERIRBRu3\r\nKjFmu44+CO7C3J+QoRdEEt3CKFWaWuiLbee3pdIZ6arfjHNugCdP4gLFAOJD\r\nBzIut2fnZo5nc5he6Vjrc/Ml/mmDhgesH9jBCu3DiOmiRRuHrWEZTJD3eeyg\r\nlhM0RCSQvbzljT66Z9BLvTeADFWxXvB0D5bfNngFi0ovv1ig8NAnLt77GejC\r\nF/8IbVTbbWUaKUGBeaSdRGI0ZipkPW9REOOPPIc8qc7RSmX9U8Zl+W+vvd69\r\n42ZvL+2lOeA5VeMnmjAYDDDBrWuFMInzbTjOSY/lQc5ly4t17uNE6MN1zg8z\r\nMRv9Yv+dZKPhY0lSQ5/gvzUZOEorzWLk5nHFrzt9DA5mISAYHZM0IwScCvfh\r\nLnoSfT1t922OtwDsvsshP8Wr64VtkdjxcaMJXiOCF7QLin+InV1DVD2517V1\r\nwzw3hczohypUnDLKzTsZMelf6ZSm8HRE33aV4iBAQ7mKL8SP9LXkBDuggWqe\r\nrDkaJdkQm33bFi17tpWkPAQmvAc9Yjkra3xbjUw7qWrUFWEkR6nq0pEX3Q6i\r\nJsrn2FRAMf0XzNBaCmzPHJ5ELMeEA46utIU=\r\n=BidB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "98a833bd4bc0bdcfcee5d4f04c2833400c4e2933", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@jest/types": "^29.0.0-alpha.4", "@jest/expect": "^29.0.0-alpha.4", "@jest/environment": "^29.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_29.0.0-alpha.4_1659963946982_0.8021780961872802", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.5": {"name": "@jest/globals", "version": "29.0.0-alpha.5", "license": "MIT", "_id": "@jest/globals@29.0.0-alpha.5", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0d5d304a3b628bf08ee6a7290723a49dab68dfff", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-29.0.0-alpha.5.tgz", "fileCount": 4, "integrity": "sha512-yVKcxiJ1LrzgAApTCVI2htkfZmOwax6mW9FON+DH5vhrD08OtSNpn97tl3jbdt3evevKKPakwQJ1XSnVT5K2tw==", "signatures": [{"sig": "MEYCIQCbp8cpr2LrWPSJcXposntB565PJL3VqtckBcMmwrPy8AIhANeExIKkEF//YkxpQbvoI1Ck1wJageP49OHz62Nz/vEG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3340, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi9QbwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqj1Q/+OO0JDJKL5Cew/Uw1VCq8UJAhxmjZp46PxviuLX04F1JQuwR3\r\nIBVauKsuzTICqrkyrZpnFwQ4tvLvrOKiMq62e9VlanPT7QWHbkH/YDdlftnF\r\n0f0jkvbQovSJfgxhu7+GSLuazHbD0pmcN+5vi4Gkdp10J9guQex5l47fq/Uo\r\n2CZzB5eBmU+mUIVMFHO0pWk4ysh8+hz1FwTXdFueH6dQX0wU+aS2hkoAtJE5\r\nUin4RXg4jLJEmRAPyPovFMCTfzGOg04VRurOjeoJA0Wa9oUR7hQSTITsfH/U\r\nMOSvP9rEoBWsXyGeNKjGHNlvAPqTOP3ReWOjWGCZeWllj1ThJvJyuAHZTke9\r\nFrKG7tCID7W9f0Gq1qLjH5PyBcK4A5NW88ClG9QTIoFKdRsHvjL8e3Whtuai\r\nfwmqr533yFhhmLSEJEsYrnAuh8CM2d/Uk2V7qQv1mUt37GYnVvhcoRolhFXt\r\n5EzrbIVh8vZ1prUP+JJWaKGQx7f/55u5i6sly1asr8k2yoY7u6+5lTiFeLeH\r\nCkaKDbeht1g3tEOxKT308oOsMfRZRcfiTetoqjv9/zsCQH2x2AslLYfQ/Klw\r\ne+DqUagqL73yeSwJUSqpMMnQW5UTI1bqzqF/kuekgMffj29gL9iu2A2/CcW7\r\nZwPUmrc3+7SP2ptVvBJyvTxHYahYDACZGDY=\r\n=XjO8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "63e506b5d1558a9132a8fa65151407b0a40be3a5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@jest/types": "^29.0.0-alpha.4", "@jest/expect": "^29.0.0-alpha.5", "@jest/environment": "^29.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_29.0.0-alpha.5_1660225263860_0.24343813325748553", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.6": {"name": "@jest/globals", "version": "29.0.0-alpha.6", "license": "MIT", "_id": "@jest/globals@29.0.0-alpha.6", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1611827c93f9e123c5fff1229058948b2f9dd0fc", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-29.0.0-alpha.6.tgz", "fileCount": 4, "integrity": "sha512-6nBLumMG4PyHxFUSG6+V+dUsY3fduGbA6nD+/TgGScQpanzT8V1TBubXDoDi7/Njeu2MSPt/8EYcyV1a77wDNw==", "signatures": [{"sig": "MEUCIQCOzfJPBe0Te/NNqfHQ4MLdmNou7SYwc/vhm4OL900G1AIgFDoncTcEUf2G3oJ0XyPfDoBW8wlNY3z+SLravm/Dzsc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4233, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/5bpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo9IA//bjTAUBwc0g291goR1XdduVZNSfdFDDyUnEYIkHcUwNjhiiT7\r\nbSrYobe9DDfchT8stYklT3aqiAiO/hpAMdnWLBlKzatrp93BXSMFfV2Yih6t\r\nCWhWNPdsPbO4wcC0jknHG7fjmGuNWILabjZdg98GdleFJpByO2nvnCvMkAYm\r\nh/2erSrKUa61s2FQgFDTsjMcV6WgViN/GvI0Kiopf+GUPiFl8KWUAvTE/oCp\r\nNlRjMUWNekI7LtN294SyMTIMYUEb7uxNdNCB+ijlo/oM8Qwnbx91rxiSzRWy\r\nt/TKgvHODuvGayLmKJVSFRzjUx1gzPoG7nvVn773fbVp1xv3RXXzLkyurRDS\r\nWR7rHYDr94rrQj9IftPGRr2ZLbzSJIPARkutSsBUg/JQf4ewhw+ez4xfgv/W\r\nIlWP7AvrysFZa/dM/QrGXfK1k5WYxi7QPQHncIMAYSBwj4IMG+e2LTkbFi72\r\n6XkayKHKa14x3r6AqWXR+SyT8Ym3Glv70a2QnuluDu2P7UYIHSy2NsABsKOO\r\nCkXrjcZIf48RA5wIQ3YWQwSJNgaOPpBOmZYuYuoS4qwtChjv/mUbFxsTu0sr\r\n1tAZCwUyunU6qjU1ygsI/lgt2vl/m3mULjeTqLUi501h0nioDeclY3LA/ft+\r\nyabVXsQpJl+Lr/egMSuhlLm6/hCgEfhJFt4=\r\n=zzG5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4def94b073cad300e99de378ba900e6ba9b7032f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"jest-mock": "^29.0.0-alpha.6", "@jest/types": "^29.0.0-alpha.6", "@jest/expect": "^29.0.0-alpha.6", "@jest/environment": "^29.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_29.0.0-alpha.6_1660917481654_0.4074189980391967", "host": "s3://npm-registry-packages"}}, "29.0.0": {"name": "@jest/globals", "version": "29.0.0", "license": "MIT", "_id": "@jest/globals@29.0.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8a65b224e2eb1458d3780652d4ad832611d16066", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-29.0.0.tgz", "fileCount": 4, "integrity": "sha512-ZHQMh6BZtabbikh9wkuPpVQmPHEpc4EgOaY/UJNM6hHHA5HRmiP5rH54M8267nkGscuqM5KpWP+zAZ4XEOXZag==", "signatures": [{"sig": "MEUCIQDEnV5qoJ5R78korOPgiiij4FTq1MR3gKji0agzLlbnNwIgLGu96LE6x9UZQ84/BuFIiA6GTVz67JK7r16HZuLWrt4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4193, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjB2whACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmobIBAAmmRfOKm9VG1dud/LVSzUh8/k4ytNi6Qbw91m6HO2sOIFlgmW\r\nsd+p2fSiTw7NXNNye7+aEcJiQTk2ds9Sco6x4/r0B2ZTIIalndNmjLXpRljf\r\ndvo2fgu1AEmdVCuEwCXF6fQ1/U4ibN6eZC7q4/8O0Mjah8D9QilmNipFFYAK\r\n3xPNRawAIe6qRIfkQh9UGbowl+rhqqaYYzjVfWYO/Jxprq0ZGl+Fipw4jj98\r\npGT7QzI/r8a5clAUcjDANPDA/owhnc7xaVL0rcQIlS3rjGMfDAq+R8Z2DF43\r\nEsNqRBoqk1U37k2UycuMaSEuqWpNz2wF9IDV0iAzpH7uIcMbjsX/D2O+aI4o\r\nLpVQdxb3UbNYOvXYceW6qBGHCwifC9Rs8AAb6W0RUwQl9963HOirk6dOlJjD\r\ngw9CYIjleE/weKE3xKnpw4m+o6E5agdG6P6j2mnq+x6cub0lXF77xSlcrCZ/\r\nYdLjkgS/Ze9OV43uqzS+uBnXL+6OHPHAgHZL/LODMb8CQboyU/F1sK0sxvil\r\n7unAIYJ1HIgVcH/MXgfn1v8r3MWkvX1Mnj1e5NQWVjkKtZoiO5PZ788cGYCN\r\nCIrLw37L5cEQklXA4hrjMS5MuhogFCGw/q2lGbMx4Jpls8CMDdLBSYj+bCss\r\nGYB7nuxcKUsyvB6pOfD9E40a9o6eh2ZHNVA=\r\n=8HN6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "75006e46c76f6fda14bbc0548f86edb2ba087cd2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.0.0", "@jest/types": "^29.0.0", "@jest/expect": "^29.0.0", "@jest/environment": "^29.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_29.0.0_1661430817628_0.9422088800586801", "host": "s3://npm-registry-packages"}}, "29.0.1": {"name": "@jest/globals", "version": "29.0.1", "license": "MIT", "_id": "@jest/globals@29.0.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "764135ad31408fb632b3126793ab3aaed933095f", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-29.0.1.tgz", "fileCount": 4, "integrity": "sha512-BtZWrVrKRKNUt7T1H2S8Mz31PN7ItROCmH+V5pn10hJDUfjOCTIUwb0WtLZzm0f1tJ3Uvx+5lVZrF/VTKqNaFg==", "signatures": [{"sig": "MEUCIQCae5BrVxiKFuZ5lFaHOeU9mgkJEVni5kxMl/R8bDnp5wIgGZjTP3n9KlM8mvxipuqGiI+bF5s9wL7Z2TIRNTj8J84=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4193, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjCMv7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpGiA//bmy0y1qbmLX6jtFI7y6i/0ni+EapJUOigi7B0RyRedr6WJ7s\r\njG3NX9mBUGsZXx2eetGtuB1N6eMZv5xSviTbyXfBKlaYNt+F1KhgmCVf58ng\r\n1mRBhucXzW3lLVM+ARaB2mRL0MgdD23Qyso9RBWPfXWyefWamMER6Hafqzdl\r\nkuAcorwnS0GGcFkDWMoGZ64EHRoIkL/0TueJdNFvplRtjXkl3BrV6uW08Ge7\r\ndUWF0s1WXLyiINcWoxcVIjlSSvWryQOvcuAqAwfmYp98Rt59FEoZYBkUvh08\r\nY5gn+VU+lvKqtOUiXDviR9FRYaW38l5jTDgGBgwPhaz9uRGyYhr7sVJh38Eo\r\n15p/MjBfRzQfSHDWfYc090YemJeESePM/pYEs6q147cXjowQMNcsyiKTljJn\r\nVDAa7d2sNllEOZ95BFKzjux0BENzPIjvteqwbZ0Vt2hrC5pOg4Ok0l2/nbUY\r\nEOuu3wEgvkgxJjpb+q0Wr3KyTvqeOeBifwx8cfL/kXkk2xoKjEK1sEHzPt8f\r\nGihGeuR3E3EX6DK2yNSHokG8mjJYwgO2X5Yk642sNUzNJf2sR4649yVl6Cio\r\nRbDTRjem1zIX+k+WYKyOhyHUSZhCtpnOw1fV8DSYB2XiICCfiWF0h/hLGS76\r\nmvCWF1PQm5zGEU9bdZwFATpp2bZnyM6eX4c=\r\n=Tt1v\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "b959a3d3bdf324ed1c7358f76ab238a8b0b0cf93", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.0.1", "@jest/types": "^29.0.1", "@jest/expect": "^29.0.1", "@jest/environment": "^29.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_29.0.1_1661520891330_0.8062920733333843", "host": "s3://npm-registry-packages"}}, "29.0.2": {"name": "@jest/globals", "version": "29.0.2", "license": "MIT", "_id": "@jest/globals@29.0.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "605d3389ad0c6bfe17ad3e1359b5bc39aefd8b65", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-29.0.2.tgz", "fileCount": 4, "integrity": "sha512-4hcooSNJCVXuTu07/VJwCWW6HTnjLtQdqlcGisK6JST7z2ixa8emw4SkYsOk7j36WRc2ZUEydlUePnOIOTCNXg==", "signatures": [{"sig": "MEQCIBFbbDydWl/p6MpRD7JVk9lRmD1Sd+hJIyXpGopJSUhNAiAfdqfVuRaUpftmfyOHc7mZz8syCNlKYzG0wL7dMH3LaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4193, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjEzD9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoZRhAAj5CMfqoU4w8ZQRbZJFvapPj3/mHiBylIcQ/R4CXGStwJJNF4\r\n0GFEMIPRUFzitECtKtHlB66NHEyRP2oT8FLZAoHDxHgD1AaiwlZWjDdcvJt0\r\nYfRZmU3nGp/PySztT3wzimnKLWhI1fB+eFfkBupU+PeEngJfuyuvGFoWfWLu\r\nWLfjT5FQxgyPzMtpNEe8IkRWFWdg2A53YXWLAfCD16imh19cKtC4RajY1607\r\nlxW0d4cI2FAtfY+PqUlnqHCg8U+ImBhvzWMekZjs3b0FOqPK3QEYLdP3SWjL\r\ncIMp0Puqi5svQiw1cuV/by8dzYQzs8egeRDUgJST9mDB1zofXEFdZEbmBh73\r\nb2w+08YvPHDB5RS2S7mvowt47+dtUkw4b4jJ5952xtHx39IxLboQ1MipiMNP\r\naHCsLR/9JaD4oIjFpFrRNX65UNH716C0okw37JIj5mN7IdUJS0xsM2X4EOyg\r\nOl0QE+kHVHKnV11+j8kSIFSoQK5Ejb9W+RDL/isclBl6DTbyAzczeQM1TZba\r\nXTENJ10LqLxptK4m8t0QnkB1nXetcdyAw02e/cq//V8SEY1doPUfVNo/amcE\r\nPjb9Kmzn7IP74NzDdtC4uD3/TiKLgGUjhw9YVofJNIJSxBvqyQeRzxN0Y5Pd\r\nQyAihSMsaCq4qkWxy4OeQ3v6S9xpRGmpxjw=\r\n=6bfy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "616fcf56bb8481d29ba29cc34be32a92b1cf85e5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.0.2", "@jest/types": "^29.0.2", "@jest/expect": "^29.0.2", "@jest/environment": "^29.0.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_29.0.2_1662202108985_0.9036972235121234", "host": "s3://npm-registry-packages"}}, "29.0.3": {"name": "@jest/globals", "version": "29.0.3", "license": "MIT", "_id": "@jest/globals@29.0.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "681950c430fdc13ff9aa89b2d8d572ac0e4a1bf5", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-29.0.3.tgz", "fileCount": 4, "integrity": "sha512-YqGHT65rFY2siPIHHFjuCGUsbzRjdqkwbat+Of6DmYRg5shIXXrLdZoVE/+TJ9O1dsKsFmYhU58JvIbZRU1Z9w==", "signatures": [{"sig": "MEUCIGYdW4Pv8Ut+8WTvA+dDWhFKIejwU5LKGhDYuX6n4FN/AiEA5gpke7y6hRHWJV473/stZ4lq5qKnUqW5sPeqBiO/O1Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4399, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjHKI7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmooIw//UeAqpnGQatWyLYi8qMepXC85q9Ic1qE+1+gCZZizlvK4ZXCh\r\nhR5iceqqKCSCOPxswHOUW5zyq5gpx7pK6YwbFXeSiXoATQyQUjhns41lJ1SM\r\n10vNHBaLg52MCTzi1OV35lzaWpnneWyHuBbit4KMUpcU3DqsdNORmKRLSvz5\r\n8DdfP46suuBHxk+UnVL8ZDII2jsQR0Ui1QfDifDUndffPiGcffC/M/O7KPYe\r\nZY9ilXn38h/ypaw8pAIRNhwvwoIIfZyc5RqRBMChUmT2TOOKYh1EcRgX9sBV\r\nbboWomzSCqHEJcW++j5ze78J8q7znr8ExWpRQwZVu/DdoNoSJ643GbVkd/Wh\r\nc5cXNwXEtfWTgl2Cs2fjVbE/VhHHX/j4BOpJGE+GPRx8WiJ0g1YX6lR9dg1W\r\n+hDLsZmzACgFPkq7KBHC15li4DDmcHsjBA1WvynKELmaTAUCzefzYxDxmoOE\r\nD6fKM91Yl7MPx2E/I+kmcbkjL02MCRtphXqYIZjSa9w4bt8b5TXZ5wLP47UG\r\nry0YufZKXWajzL/BL+hw4Dnn7eqbZkipQfOUnD/Z4p+jI6rGOP1iAxLLBhl1\r\nvcuLWDFQVD0/BtUe20eoGHeA7nldFdQAd9DBaY6QfXQB+R4IDF2D57dagecA\r\ndY838VZN8Q7ArsWR2BZCRERd1IVzd43tHLU=\r\n=ps8z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "77f865da39af5b3e1c114dc347e49257eb3dcfd1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.0.3", "@jest/types": "^29.0.3", "@jest/expect": "^29.0.3", "@jest/environment": "^29.0.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_29.0.3_1662820923413_0.700126278975101", "host": "s3://npm-registry-packages"}}, "29.1.0": {"name": "@jest/globals", "version": "29.1.0", "license": "MIT", "_id": "@jest/globals@29.1.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "936e1255f0fb0e94685477be752671138102840c", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-29.1.0.tgz", "fileCount": 4, "integrity": "sha512-GqGT5Uw3R5TWjSYroQk20nywXo0tPttFI+SPoCgR34q08JU4UPFZAiM3Mbn/MZIRMYv18H7QwpICDRnB+KBOhg==", "signatures": [{"sig": "MEYCIQCk3A+50dtv5Yfz8QI8PKtMjygqYXMKGCE5I6c9Ht/IhwIhAKH7RhJWyYD4xhyRpexjRPTYNzlhQbf1OiwDLI7v4mmF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4399, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjM/nOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrqeA//abEryOTI9L5Ity0caSLgYG2ayNTLtIuiDQISyewWycKP3sRv\r\nDjogrHIUDtGYsQrWXNLD7FvOPaox/Frhiq6CJbE0DYRLcM6Bf6kBsvNt76NJ\r\nFTx/G50dpHopPYDlDVVitrwY8zB63v3/i2Ci+uuUKotsJEdrXvS5Afa7gO2r\r\nAKFhTQ5aY2wRCp0C0ruFfMdnuGPWsIq5pZrsjDOObPXCav5aGK6a8WCpnDKC\r\nQh9tmOrUVkA+lZ+wWCXz94UxiKIRsjC0yoHx56qJexdWocbGhrS+OT8vCs51\r\nw/dneW3t2lqAlh8UILreTFqSyavXIgnP8uimG3tUcBevgFuuB9rFNB60NdfX\r\n3NI+kooRBApWb0Wsa65ILhOOlVK4InX0OCLLgCspTgR6Mp3wrxcnNCJwlcRa\r\nbRG7LS2Yj8OAK+xq6H6vFcbt/YK1KAoa7Eie/ZtQTABY+jsAU4unPzL4ld2l\r\n7IlPIrAdQFQxy3SQOEjg9exV/EWNtoIMawjFozgNfVeIsO6fZmFXrP9RwgfA\r\ni6vSysdK5RKpx3a/wyWYTy1rj6SGjzLyXeBls2xX+un2R7q+VLj1jnnIxhWm\r\neQi9nc6wu8e7JbOee0HaA9PTXMVHg4gCUWD21Yi2L0PVD+L71awc0lysROyd\r\nunzsH7CUucALwiByd9CbYVv7oC0rM28ZU7w=\r\n=pN+Z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "51f10300daf90db003a1749ceaed1084c4f74811", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.1.0", "@jest/types": "^29.1.0", "@jest/expect": "^29.1.0", "@jest/environment": "^29.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_29.1.0_1664350670246_0.7675819010213287", "host": "s3://npm-registry-packages"}}, "29.1.1": {"name": "@jest/globals", "version": "29.1.1", "license": "MIT", "_id": "@jest/globals@29.1.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "017cffa0e79b35e7197b229d852c9ad35ed4be5c", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-29.1.1.tgz", "fileCount": 4, "integrity": "sha512-yTiusxeEHjXwmo3guWlN31a1harU8zekLBMlZpOZ+84rfO3HDrkNZLTfd/YaHF8CrwlNCFpDGNSQCH8WkklH/Q==", "signatures": [{"sig": "MEUCIAq0Q9VEWlLlFqX01kdybFPeQO6pe7jJTaXoR9wy72UdAiEAoeHz/AcL14TSQwIKKqf4gRtIzMpHFugINx45vSjLp50=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4399, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNABSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoV8w/+MssldqLDJcg4DUOXUyPZ/FBRomeJ1H1rTgFgfPj3lLsTcL67\r\nIIfMRAXhWFKplARgPMA5ha2F/jh0YdBBZZvfbKQN8eaNO3Qm4dsQR7M53+yR\r\nAiaa2dVoSOX9CGY2ghBG2qRC0/OfoFlcsh3m6OZ4dput4pQNeSY5iKdZBAzU\r\nEG2l1pVEFwyhL60CoYlpg3X1Uzp4omNX2hJXBL+mV0Id/LRM5Be/qN6/qlQP\r\ncJWFWzFl0BgINb3jE54TtRw3XU80SJOKR5E4yyCeUZQidXxLhyX15yv3mEbu\r\nkFBMVrltoiXAWErFcKIHiEuSt5LPu4z54ROIz6nO7GEQCUGXID/ZMoXiCgeF\r\ngv2T6xvp02G+lLoY0bkrLVbfcRRazkX1uZn+GJamp5l+EtCxsFY3bzWa7Kvo\r\nU6g+FgZ3GfHrf6uuYuE2WH9OwfaKfiK+V55Z1Y2fYc85pw9obX9qzI7zaQMg\r\nlAbXzquEypW1whZX9PfK4iYZBLUD6lgTwze6dpb33bzXGfpqAifF6XXPXzmg\r\n+be0vvvVq92hoqctWrtCx4ObfcXqDOhNq2wccvWY+hcHbVy5QVVHelUjjGJt\r\nExAo6yYlrOZU/D/yqTm/1OhR0FP4+IbIgPa7KwJyuaxiMMSyKQ9j5+gJ13ta\r\nmSXNZ+88t5ReD5GsEhjUoGtMsukE4VwUt4Y=\r\n=skXt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fd9cd900ad0904421a3d97661fdc3337194da1f9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.1.1", "@jest/types": "^29.1.0", "@jest/expect": "^29.1.0", "@jest/environment": "^29.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_29.1.1_1664352338510_0.19740820885594257", "host": "s3://npm-registry-packages"}}, "29.1.2": {"name": "@jest/globals", "version": "29.1.2", "license": "MIT", "_id": "@jest/globals@29.1.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "826ede84bc280ae7f789cb72d325c48cd048b9d3", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-29.1.2.tgz", "fileCount": 4, "integrity": "sha512-uMgfERpJYoQmykAd0ffyMq8wignN4SvLUG6orJQRe9WAlTRc9cdpCaE/29qurXixYJVZWUqIBXhSk8v5xN1V9g==", "signatures": [{"sig": "MEYCIQCRKNMXDiBiKgiVZA6zHqaEZJHR8oCGtGQLyM7mFiEGdwIhAN/qDstmXNN3gkH3nZJ8L27mnInpS2Rflm8LecdPMfVT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4399, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNplRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr9dA/+P6mU0uKgJtDrvjw7xkfTGTXVQoSSLWgiKevmTh5jiMltzukI\r\npVderzoBU0xI+Bxb7bRd6kaiXXqZ1+jL+mkTEDB/7MeTaD1VCz/msfTIWPkz\r\nUZhtXRYKAATAW/3L9uW4et35xm+enmotmNeJZ4btfh31LfG++FQ5foAy3qxv\r\nRdK3pwmRwO0bPIMpRZyEZI+uPvODSpKNTWKrR8xGPDsZVCKKVB0zO2ozboOu\r\ngIpnOBZ//ozNiI0xk5H7fwuP+XOh4qVdwR5WwdVXWDBVXRCqlthBPXq4258W\r\n5aalpCPdBNXXdOOfnyC/oIS8idak967hI0d65jdxrpp+TYw+Cx+23Ilg+WUB\r\n5NU7FWRjJfd3aPBKsVYK17vSMYkacmebViQ/0fCqO4c9twmnJrUPpw2MXupe\r\nBWEIw5gt8ONzSiAGIzBFdYAUGqSNgkvDt5/UPGLe/MlFvo4FYs0lyggnDTDX\r\ncgGmIGfxYPIOGt7rtmeumYxlAiwHcCmClpxmefIiBlGP37dbuTf50rJyiIxp\r\nglu28nYGwhjsBZCWH8r4fl0MzBWgZlRDrqr4gpidS6Zi2MMt8oowJdGiULC9\r\nWxhaG3O7e7RI+5U3lbMh6OtOkXQ8c1ToP9DbUtDM61t713SMlmURQ034La8l\r\nqZkiL+Kgm1EGJ1Os8CtHBs6oZrQnZHvPJko=\r\n=vjkd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "3c31dd619e8c022cde53f40fa12ea2a67f4752ce", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.1.2", "@jest/types": "^29.1.2", "@jest/expect": "^29.1.2", "@jest/environment": "^29.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_29.1.2_1664522577546_0.05244295111365593", "host": "s3://npm-registry-packages"}}, "29.2.0": {"name": "@jest/globals", "version": "29.2.0", "license": "MIT", "_id": "@jest/globals@29.2.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5cfc41c028efaf511624ba086d64113d5a8a92b3", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-29.2.0.tgz", "fileCount": 4, "integrity": "sha512-JQxtEVNWiai1p3PIzAJZSyEqQdAJGvNKvinZDPfu0mhiYEVx6E+PiBuDWj1sVUW8hzu+R3DVqaWC9K2xcLRIAA==", "signatures": [{"sig": "MEUCIHzkgvFfp+3EeH/52xOv7bGDg1paiOI8CWNEqMy9TxW9AiEAkFhBl0fG3APmzAiEDN9L5gA1l/IUX9Gpb1WrdypV8Zo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4401, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSSheACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqV3A/+OJrMhykjd705OpJeCpz+gSG+mnMuP3Ltkq4Ky41Of5+8wLg0\r\nlaQshvSwPdvNYtbbq1vAh85H0YIcod6b8CYqPchmBu6fLnsV6GAzChKUA2Hy\r\nnA0FvEUCPeXo0S73GwRFhsRIRSC8igvUl1G+c+qHVoMlObH18ujvNMr4DuYF\r\nqt7wBbu5E1Jc2Xo+sKOTkEYQBL5qiv41YG1afpxWOUYQ6QIzPVb1BMJ8V8sC\r\n+Vha85S9b9UFgWtd6aIPXq/+vO+gDToUeNxuxuI8ElepvmLWd6o9AJei7/tG\r\nRNY2gnFK6qYvGMYDYPygDNOGIs8bfV+rYLOkvth8i85uHijxRpD2Hb8fsdpN\r\nhpsvC5JC3bSlj0QZ5Mx2UV9XBgHwKz8zJfe/rzhMXHJ0lH7ZjyVUnPU2d+ks\r\nlj8EFsLZhANgbduKLZZ9+L3uvHHgqPyvIUAHmHqr2P1Kf+a7jYTX9WvXy6rB\r\ng3+NoTPXR1tekV4pgN0oIbdjFko9oyr5loof25CkLhakyfG5VYRTiJqxPNGA\r\nf27P5sEa11tdPfRj4K3DQSD8wsp/GQCehf4yD7SDPH307aKgkCvIq7cFHmGz\r\nhrvjYRWilRq2pfjB7VgPA0dL06GfeM4bElYttP8cCq8kudnPbEVKjklsq2UL\r\nS1Ko1Ikw2hgxbXraHBEa/D89CAjzxyj3hXM=\r\n=sfPs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ee5b37a4f4433afcfffb0356cea47739d8092287", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.2.0", "@jest/types": "^29.2.0", "@jest/expect": "^29.2.0", "@jest/environment": "^29.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_29.2.0_1665738845984_0.18282392948415982", "host": "s3://npm-registry-packages"}}, "29.2.1": {"name": "@jest/globals", "version": "29.2.1", "license": "MIT", "_id": "@jest/globals@29.2.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6933beb8b4e43b990409a19c462fde7b71210e63", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-29.2.1.tgz", "fileCount": 4, "integrity": "sha512-Z4EejYPP1OPVq2abk1+9urAwJqkgw5jB2UJGlPjb5ZwzPQF8WLMcigKEfFzZb2OHhEVPP0RZD0/DbVTY1R6iQA==", "signatures": [{"sig": "MEYCIQDgWW/ZkGwpVyQU6elwm2N+6fEU2GYaehAmBU/Q/qRDQAIhAMbnc4pattUrKrs9VDGdvvok6wqnQJ5qMaf8x3mszxs4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5165, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTs2WACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqf1A//bJOau6v9qXRI8lK9Z56PHIJ8psiIAMQUdCVPv+X16IKpHAMQ\r\nA8y9tVx0ay5bC3uVKugUJXnih/c8iLObWtqymWjWE9J8oZVHplXOrHYlVOGd\r\n0MDlBFLUyL5KVsAp8adhkZbpjoLKQiQjhxAPq6oDB9iSvU2xKggFib2wfxQj\r\n7Sn4MoBzwGwesDfC4QxMg80sQ2IBPtfKDq8sn5aRNErXrypO2bMrO0OFMSqe\r\ncy7HN32GePPvNmMqSr0DJJ1fI0ulBAj1BS4IRLWCMXnAL/UIpbV6vkcHinp4\r\nIluR2cKdi7jDXJkOBj2n9aa/255yoOe+WMeJ3WLAqPAQ9xasEfGJ0ZbLPm1B\r\nOJpbX0SbmDpGOCHiqXuMnahqq+PNSHRSBg/Or+Ev2VaRecua+HJbNcBVjuGR\r\nCvmKkxqVMC7RApHjSr/K7nQ6gCp+XG/upcrnsU6hMNHQJvLjQqtIT/fKBSm9\r\nA++w7ebyTBDmuLRXa5A5Z7pnRzOVD7A7ED5HtHXL9s2RN4JadyBoTfjdG7y2\r\nJMtxKeLIGbuUIVAfkHPph9/d0mdNo54FM7HeiV7ojhRp7zjE0J7uLSJlnCL3\r\neSn3MKHxVB3ZE0YgHGEathCpSADsIriorfK9Zus7do6T5HY486sa3r8cc+Ks\r\n1RXFzg+amO/yTj0gQWdLNdkeo5njEZUsIe4=\r\n=Mp8h\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4551c0fdd4d25b7206824957c7bcc6baf61e63bf", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.2.1", "@jest/types": "^29.2.1", "@jest/expect": "^29.2.1", "@jest/environment": "^29.2.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_29.2.1_1666108822407_0.12672796776799378", "host": "s3://npm-registry-packages"}}, "29.2.2": {"name": "@jest/globals", "version": "29.2.2", "license": "MIT", "_id": "@jest/globals@29.2.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "205ff1e795aa774301c2c0ba0be182558471b845", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-29.2.2.tgz", "fileCount": 4, "integrity": "sha512-/nt+5YMh65kYcfBhj38B3Hm0Trk4IsuMXNDGKE/swp36yydBWfz3OXkLqkSvoAtPW8IJMSJDFCbTM2oj5SNprw==", "signatures": [{"sig": "MEUCIQCqTWc12yW5IN78Ww1JYY2vH6uBSJ3Q+YBmVwT2aPUs7gIgWrNbZmqS0wzLkWs5qIE6HFQqLpFeCqMY8jRutFdv+ms=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5165, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjVvRrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp+vA/9HRixF26D1JbX2nSbSUheixnQkTea1+786yzKz0BzIJP5GlWZ\r\nHUuCXPfJVpWsoPCkEW0RQyshJohr7iHIucanGuHi670ZiD+cfQGG9tPzofGN\r\nHf0u8vtZA/Jz6K0OPUzSqjMpFrvy5cxLVK8NVvuquzAHEmHJqEzE0nnLSYYj\r\nV707zIO80GJK9F1MFRRTWUcvX6l2F43t9eftkjEW0NmbY2tSW4nKMmC/RBtR\r\n6L+9L5FQBVJGhrezt33hIrOHfLHO0K6JUisz2soZ5ZTpZyk3BgFTe51kQMmT\r\n6QxuQKEAI0QRAjQivZnuqjEvXFSia7DATj6U46Q7C6SIBIpAO3deMfj2vQxI\r\nnQvXdMObyX7snff3bTR4vqVC3sMFGsrEHNOb4ARaLO+IFEJF2GY26raLurpr\r\n/BB+XqXefTHm5bWGC1+3b2VDXMF9x5oI8Ipaz+juKDsHJ9M0knuETAiqaAy0\r\nx0SC0bRTm98vY5S3icgg8q/okrdS627LS2yFAGsI7G4rLEtMR/EOgIOuXcVA\r\nW9N1tA7girUXpGPGHeNyyZTvf25SOLzojmQKhRP4TvYqdV/Jn2EvUzo4Q7KK\r\nZdqIx9ZFh1+MNt5nZvouayUOK+jvcaqFf8vB4aJuSUfzwBHuOPC81QRz8Lzg\r\nubtgHleHSnko685XtTV4d4HYNpcEipuaLVA=\r\n=+bHy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0a8edbe0ac434394a16cc173a03ff54a9cc50e41", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.2.2", "@jest/types": "^29.2.1", "@jest/expect": "^29.2.2", "@jest/environment": "^29.2.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_29.2.2_1666643051712_0.7512795629612685", "host": "s3://npm-registry-packages"}}, "29.3.0": {"name": "@jest/globals", "version": "29.3.0", "license": "MIT", "_id": "@jest/globals@29.3.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f58c14d727fd7d02d7851bc03fc0445eefa2dbe2", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-29.3.0.tgz", "fileCount": 4, "integrity": "sha512-okYDVzYNrt/4ysR8XnX6u0I1bGG4kmfdXtUu7kwWHZ9OP13RCjmphgve0tfOrNluwksWvOPYS1f/HOrFTHLygQ==", "signatures": [{"sig": "MEYCIQDXQNgX9pfqbF/JpSq77l5IV2agmLrs1nKGhsr9Sa2FlQIhAILl1j9L3rd/+b9pGIQ16N4C9fqO2Wc7eLsUBJRV2Kv/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5165, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjaUarACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoNgA//fwA+58x853vXrINOaEtNbCKQOCjC0gpw0alSnmjRkrrbxFmC\r\nabfbxirhgwmo3XtNQgny4A8kfhrSK8ZZuGf6fwfxfOz/5UXD03x3nKZ20dQC\r\nwcOioJn8Ms3dbYULNRiZtcCzSiWeCmWD4iTqmuM2onslcjhCb5WWYwHbjgFN\r\nz4p2puUzefeMTDNS4G7Ed19VQWUQyplYp6G+AHVow42FMvc2mai9Fdc331Ox\r\nv/8Nj1NfAOzSnKhg2CMrq3MIuS5zV/hrVQ/qK2bs/ODA3lH0xWZb1cUj48+6\r\nku/FEnrHy2TUa9QjkvuFRzB8Ugjf9KsmnpE8feCm3/Hd0KpyowUBzvmzPK22\r\nMWs39bjWynieqLx7tFFsFAMTlikOGMAVh8ZcJdfyKoySbqHTmWl2oFHr0Jfb\r\n1l0ggP4PwV9ribIS4md4ZKF2tqaqLIXGazgCrnXuRLmHWfuo9sLg3MwmSWKm\r\nNGgRAXgDhFTi7VJKD+2xoEhTwBr7WpuhON194WXUAu1D7q0+9FOuRCQY+mcM\r\nWYskjH7WjeOtGrsh5ukjsAh2+a2wIsHO5dthI6CQXoJsi4wCNMD4SOWcz6qz\r\nqXc8b3M3XwqBTgeVQht/Tjrjsj5eNolLWOb4z6gIqgzp/QkXGY5IxqJqltc0\r\nxJ+tGpppP4EFAcpwJegbd9HJgdQhYhyo9Vg=\r\n=i7In\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "84b8de987b33e2da20dc833aeb65f23d72a673cd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.3.0", "@jest/types": "^29.2.1", "@jest/expect": "^29.3.0", "@jest/environment": "^29.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_29.3.0_1667843755682_0.12843309796269708", "host": "s3://npm-registry-packages"}}, "29.3.1": {"name": "@jest/globals", "version": "29.3.1", "license": "MIT", "_id": "@jest/globals@29.3.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "92be078228e82d629df40c3656d45328f134a0c6", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-29.3.1.tgz", "fileCount": 4, "integrity": "sha512-cTicd134vOcwO59OPaB6AmdHQMCtWOe+/DitpTZVxWgMJ+YvXL1HNAmPyiGbSHmF/mXVBkvlm8YYtQhyHPnV6Q==", "signatures": [{"sig": "MEYCIQDxNOyXo0NHG1cxcKx3f5GdC/GZz6bCTp28iohi3rfjrgIhAKZFzxxZKV5F4K4Vqdki7BT2pkBeglvDPbVPJ9Gm7hDV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5165, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjat6eACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmohiA//Rm58GxaxRh0PhadKxlzBASXNWp8XIKCWrfmZeVugldJlmfZo\r\nPciXH/uEESOEYGRKcEf1LYNyRwfeKghKNLar3hYXBEyNAuj3EvesELwF3Drw\r\n61M48A4W9dQMZtZfmSfrYbl9kIpT8R7dDOFl8hb3kFuxlkrjzYV6L9wW7Tvs\r\nr3jDnwA7np3eLcpb4q1cfkfzWOmtBIdkomyXgbM57tQroEkkbL0B31sLzBM1\r\ngeR9sGhThMA1xTSdj+VKAQmMwDsW6Wy02MjYK5d/Dl3L1OUQQEgy9JTHDS56\r\n8d7CDORMmFxR57OMrdK8fUIFK2iIEBqSo2IewDwxnBhGi0vevHFB//Em4qfe\r\nmVSLob+2AcvwI20M4e3WCL2iCH6aaR2Fht2KrqovGDyvzPXjcLWsg0+a+xKn\r\nqEQj5y51mGDlSg838cr2DJeol8TIThj+5xwwuXOhqaV4BSVqr3YmKUHlQigG\r\nDRcI0L5JxLLeQNtQK8eGYTAUN8pl1LtRDIEJ1H5XduLivaM9UM0cDGPvk7ry\r\nBywwv2hUfxX+kRnUoEy7bLNK9/b8PTXrArnzkGoiDXH8WXQ+GsPNHYsvjsSN\r\niGpGngO/036UazumR99UP70p1T9D8RdOToEBSLFIqQGRzeF221v/u4+g6hnR\r\nxN6S+mGyrIMLa7Fm2GhxaEsTUI7QrG/Hlfs=\r\n=9P2N\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "05deb8393c4ad71e19be2567b704dfd3a2ab5fc9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-mock": "^29.3.1", "@jest/types": "^29.3.1", "@jest/expect": "^29.3.1", "@jest/environment": "^29.3.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_29.3.1_1667948190453_0.6601058341732347", "host": "s3://npm-registry-packages"}}, "29.4.0": {"name": "@jest/globals", "version": "29.4.0", "license": "MIT", "_id": "@jest/globals@29.4.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b3dd853af92bb6b6156e246475f46fb8f3831785", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-29.4.0.tgz", "fileCount": 5, "integrity": "sha512-Q64ZRgGMVL40RcYTfD2GvyjK7vJLPSIvi8Yp3usGPNPQ3SCW+UCY9KEH6+sVtBo8LzhcjtCXuZEd7avnj/T0mQ==", "signatures": [{"sig": "MEUCIDfOnHn195lRoqYm+nMnnf4qj48Jr1FS/qI1/fPfxwdOAiEAoJeFpHSl/0wk6ESFY65NV/FFmuTEsQ3xNBUwiv0UP3A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjz7lHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmptfw//eYRM23NBCmLzepLdXaGvBqRlcthxdgIwWucUbkoCixqTbPOo\r\nBnzrRBhHTy2Kpn6XB4Vc/sMV0BBGNKof/nQahiGL2s3G7WZHEiJdDojtvtE1\r\nTcu51TJVtiUEtlqFoaBBejXpu1g2YdoJ3SHHOR/7Hg+UznlZ4yFI+NQZLr+W\r\nnE5/B7MzZGEg6L8Yio/zxZOFk7V5r+GXHa7yJszHc/mBG51+NcmyQJTYJx2I\r\nuiNsIbNVDQeQu8zV/EoP6BJLH6Q9m8X+88bXgyO63ZJScGOM2lrfoTTrc8pg\r\neN4hGdwcyN8KbSnmHH1GnfbI2bqaVY01qy7vyM0S4As1mk8n2gjeAVsnefTp\r\nADkAx0Y5rtukttx8JV3qtFT/ukfY2RTy37kbqUteFlHo6GDosfmHvBUz+xTS\r\nL+RUnQ0Xr6gvpThPf5zKTkXkul/hPOUCNqGEeauB1+rHsEiP9aZGkJCggJZY\r\nL8SG5ajYwZ1I9TbAX4xef7K7TJj6m+awvXLQVC8mUJIb3MzWWTCRaOVld6yb\r\nkzA4WrOqKUa14CEhUaqFx+GDoweEO7IvWfE3W7U0h3NgaznvUWv+YK+6h+/Q\r\nPu2YvyKGJytZtMqVJiJNBywIboEJ2O6nc1Z98ErFpfubXhmBlRXvqsdsIEcP\r\n7QVUGFklgHduuAwSdjWZcrHxoIkuvP2WmzM=\r\n=1KKK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4bc0e8acaf990e6618a7bed1dca67760c20bb12a", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"jest-mock": "^29.4.0", "@jest/types": "^29.4.0", "@jest/expect": "^29.4.0", "@jest/environment": "^29.4.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_29.4.0_1674557767274_0.9288007011429806", "host": "s3://npm-registry-packages"}}, "29.4.1": {"name": "@jest/globals", "version": "29.4.1", "license": "MIT", "_id": "@jest/globals@29.4.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3cd78c5567ab0249f09fbd81bf9f37a7328f4713", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-29.4.1.tgz", "fileCount": 5, "integrity": "sha512-znoK2EuFytbHH0ZSf2mQK2K1xtIgmaw4Da21R2C/NE/+NnItm5mPEFQmn8gmF3f0rfOlmZ3Y3bIf7bFj7DHxAA==", "signatures": [{"sig": "MEUCIEu56xNqb1/Vad2L6YQDPkQaiaglTXNJnCqrjv3vitPpAiEAkhPly5QF1QS78rxIQWW4P0BYsV0lLq+bDFR0PSHX0ck=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0peEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrQgQ/+LNz6uCdZtW/I4I3lAqNJL4wEIpzpl8WxN6xyKR1z3durXREA\r\nt9tQe7ikbKINfXCOdgQlVq5OORnU5IVKpZG4Sw5IlPnjYiv3rlENaX4C7JJy\r\nKaHP8dx07WX38LP8BsIIczWcvQPS9skXrx9j+KQWI7TxK/PJ2M3R1cAhY60g\r\nbVvzkvWWb5irYYAZPu2W+5whSSXIEVvCWA3/qd7Ig0v6fCFTunIqnCijQ0dg\r\nh49fc6WrqQ1tdaDe9bssJ9EffMwOqMRzMwvvr02dqdyaOQztllP11y2qYIoK\r\n3AjE1zv5FCUXeHADrOrNY3Pp9RphjPl/RZSLwqYXS4Wb3or16F+y6QW9P3U8\r\ngG6ArUuqSa1JNJ0EuhfNkMvS3GzjOSVIfPylT5J8Bg/XkBvVKjZHh72xwy5n\r\nnDeGjW1B5O0GV9HTL6dTZBciUiJ4sLToH+XXHYvgn6wiFWGm4LYuFqWu1NY6\r\nqJZG4GD7+phMPjypZoqt8jJ3zm+PaXYnIOcL3Ub9wQmjyovDIg31b+sJKwks\r\n88U2U+gZ4O8k32aEz2lIn/ydMdyBX3wzs/bUUEumTygd4YcDhbHNu7jhuJN2\r\nSnMZ6zL3zFDow3HX7dfiYDHT21Qv3wobRFEMuAkmSpGuu14oS8YuDl/rA0xb\r\nRKgcVx8gSiGaRyL8UtQgR4jgFF+kE2lCJ/U=\r\n=sD6j\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "bc84c8a15649aaaefdd624dc83824518c17467ed", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"jest-mock": "^29.4.1", "@jest/types": "^29.4.1", "@jest/expect": "^29.4.1", "@jest/environment": "^29.4.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_29.4.1_1674745731821_0.19984416567988483", "host": "s3://npm-registry-packages"}}, "29.4.2": {"name": "@jest/globals", "version": "29.4.2", "license": "MIT", "_id": "@jest/globals@29.4.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "73f85f5db0e17642258b25fd0b9fc89ddedb50eb", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-29.4.2.tgz", "fileCount": 4, "integrity": "sha512-zCk70YGPzKnz/I9BNFDPlK+EuJLk21ur/NozVh6JVM86/YYZtZHqxFFQ62O9MWq7uf3vIZnvNA0BzzrtxD9iyg==", "signatures": [{"sig": "MEQCIGKyvfF15W+DnFr2s7QLodqET0PMy9KAdFpWiXqyfsQUAiBMdE/4PwR6Ff9TT0bACY2KpCYQHr2eYGVFTBe/pwRwog==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5298, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4lYKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqGYw/+LsBrpSvjjdLSrHbjjGDnCNd6uZWE+mSW+62wG1B0XbgBHYXZ\r\nckFjPc0S6BPzuHiO1nJwqtY+x1YCavETVfoh6vTR9bGKXHtirResVe0OtOzp\r\nr3VcWcbgPaJ+0wm4aKDe2JEu3wdAYisHRYjE+tjKj7Ixg4EXJ42k6LZlJyET\r\nnbRtDtcNqEqrbGTCfT4APoado7WW1wAPLhaksBZZ9iPL8Pmsw0QYfSRPhzNg\r\n3jtI+J/o1LUZLgJ6leAwhVlDuosBHmjbmJeBl6ixVqx9QTQ3pPYo9VeCeTvc\r\neBCKi12Jcf25VYoL2NiDSS/VH51d3A2es2HA+CpuEw2KBtSp6DXrWsr4ArQq\r\nx7824S/WRpOWyAqpPjmzFMN9SF8/vxl8nTAvUNwC9ouH4uNEurErJ2CwHU2j\r\n1VlMgevGXiMGf6ypXFmRbVjAxSu8dgmn7RDKzjhKtIaKOv/IryfYM7QClVAf\r\npa16kOrMZHiR4CMc60L5JPkjvmRfaZtU6JuOjYIli432ZiaItpBDxDDiPUiZ\r\nTA9zCc9AdF1lSmQZhd/rp94Wo3IDR/b73RB4J17XT/gEcZU6Hh5jLN+Rnl5B\r\nCNCul+OhpRX3Y9IYoqHKHpEZZ6Hp5S47PZyNEGwNLbxvSeU+Rrjjf8xtAmzo\r\ndSgPnW+1iyw7PYNdIFdw63EScGfTpambztU=\r\n=78Nb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f0fc92e8443f09546c7ec0472bf9bce44fe5898f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"jest-mock": "^29.4.2", "@jest/types": "^29.4.2", "@jest/expect": "^29.4.2", "@jest/environment": "^29.4.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_29.4.2_1675777545863_0.9603158356168982", "host": "s3://npm-registry-packages"}}, "29.4.3": {"name": "@jest/globals", "version": "29.4.3", "license": "MIT", "_id": "@jest/globals@29.4.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "63a2c4200d11bc6d46f12bbe25b07f771fce9279", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-29.4.3.tgz", "fileCount": 4, "integrity": "sha512-8BQ/5EzfOLG7AaMcDh7yFCbfRLtsc+09E1RQmRBI4D6QQk4m6NSK/MXo+3bJrBN0yU8A2/VIcqhvsOLFmziioA==", "signatures": [{"sig": "MEUCIBum+3I5S2ftKvAtD5eJVx75xzTbSJOm6LHLHlE4x+vVAiEAgoG4onEjBq+WKPQWFcMmcHZ9aQEvnvtit3V35G7KitI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7MiuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqtwRAAoLbkAlIE3FlA9sFVHRkacM94E+FxDy5IUyD9K0KXSowcEkU6\r\n+cU6M36WbcbWQdsbfRCgiXScS2tB/TIqgMPP0hjtxnzPn/ZfTR9F/CGKtZIc\r\nLQRteXaY4JZCHBHUHzTxP/dQrx07VXaWayCOR1Zsqv79wlI5Pl4AHSCpY3b+\r\nFkswRXIitN7FOUpWhHrXj/nr3Byaud74RApx3k8LdFPMT3C00QIZI2M26alh\r\n4034nnUrcekYd0qy4XoeI/b1N7+Ie2X+++UCFQhmswON/90jclYTwLtV8Rh6\r\nMUX5x6ZU6mwQYMOVQ6Bmq0m8/Jgg+pvXjmSqjgXeeZbW3ovmuCpTxLhBRhqs\r\nTQtGuJ9PcnULRCZIrS5lo/WasxuIbSjdSQPqVG+MnIwdLPCYfHY1P6+bH1D/\r\ndWSzVjge82jfUKVzcAKBjL5i9OVqnYSzo7ckP+VshuwLHKalJxE1kggVk9mK\r\nHUKawMPrdcxPF8AsqP0Yjg2PRps1aAWVAHSsimxtRg5whUZmVJO5uA87XLWK\r\nxXOpOJe3/ZCLyxdX7BBsXk84UOvJVET0UyQNXZL4X6O8MBzVieFiktI0rNZl\r\ny8a3Z35VbQMpjcwxm9WLzHFmlZpnCAXXObD4GwY4/R3C2vlUBi9b+MaO3mDl\r\nucHpxhmxdd1XW1AHdJ4ZtRN90PF728SndvY=\r\n=rYdi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a49c88610e49a3242576160740a32a2fe11161e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/1.13.0/node@v18.14.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"jest-mock": "^29.4.3", "@jest/types": "^29.4.3", "@jest/expect": "^29.4.3", "@jest/environment": "^29.4.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_29.4.3_1676462254026_0.16529842939081862", "host": "s3://npm-registry-packages"}}, "29.5.0": {"name": "@jest/globals", "version": "29.5.0", "license": "MIT", "_id": "@jest/globals@29.5.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6166c0bfc374c58268677539d0c181f9c1833298", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-29.5.0.tgz", "fileCount": 4, "integrity": "sha512-S02y0qMWGihdzNbUiqSAiKSpSozSuHX5UYc7QbnHP+D9Lyw8DgGGCinrN9uSuHPeKgSSzvPom2q1nAtBvUsvPQ==", "signatures": [{"sig": "MEYCIQD/Hjv/SYZf3rrurOmZ3v7gUQK8S1jj6xejKhJL/Nw8WAIhANT5Elr8yODL5rmIScFRmsCnb1lXrKcOmmd1PxZOJFEb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5262, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBeu4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrTcw/+KSBphVhGI3pC28kiBYKhzDH95fU0RLe97ojMpxhJ4DMQrPmt\r\n+K5K66UdeUrrea/nyighTynqzk5dS/e3rrFyJXyVRSozm0bTQk13IrnD9QDR\r\nQCqFlG/fjyp3tXfYta9EcH9GlRfdWpgFX2uhQuvjB++sLoKmRJp3hlJj5Thy\r\ngk+xlFM7dIo3BjVNzNwVy+uaSHZXGv2l7wg97bF9BEkZ92trg8Lug4XwFG1m\r\nUrup/0kx3CjJgItt1H8hdpJvrWDz1IFqFIBNgWeUW/uoSf2xa2LLeynD7Rdo\r\nmWs8STdxmJEYAatekbcGPLvHCXBMrURV1b3FB+tOTmZvmQd0XufiIh+v+KHH\r\nyzOLc2eBzUNWk0GWdJ3diU7AfUHfU+/NAHIR28/EqySI9x38mLb/JcgfpVx6\r\nIwWw4JNNXWUQ5hPhnM76Sgc9g6qqzzsgit0z+dHgSIu1MTCjeKseuNrqflcq\r\n6Z70MN/xsw5s3/qvpgRiTZeEevYRlpSGFZBweVWZeXOZW8YhbysGFiNolJ4Y\r\nboa39rYmIf7NfyqsdVoRr0jSs9mWp8YlNfaIZwOWBzS8ms9GvlnofC3XYgS7\r\nf4TOq3XRAN0cWFVVpqqL5wvH2gEBDNaH3A86GFI0Rw2gbUHAbrDkmayWTow6\r\nc8Ymq0rJgm66asVcCX8vbTP2rRBkdNbRIUk=\r\n=Nfn0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "39f3beda6b396665bebffab94e8d7c45be30454c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/1.13.0/node@v18.14.2+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.14.2", "dependencies": {"jest-mock": "^29.5.0", "@jest/types": "^29.5.0", "@jest/expect": "^29.5.0", "@jest/environment": "^29.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_29.5.0_1678109624443_0.5212582664891532", "host": "s3://npm-registry-packages"}}, "29.6.0": {"name": "@jest/globals", "version": "29.6.0", "license": "MIT", "_id": "@jest/globals@29.6.0", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e1603da83f69ed1a75e272d15da34a6a2fca1e24", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-29.6.0.tgz", "fileCount": 4, "integrity": "sha512-IQQ3hZ2D/hwEwXSMv5GbfhzdH0nTQR3KPYxnuW6gYWbd6+7/zgMz7Okn6EgBbNtJNONq03k5EKA6HqGyzRbpeg==", "signatures": [{"sig": "MEQCIEadfUjRZmN81jrZ3mXHciNAikBERX1INDfaHPk8T5a9AiBjVDq4XGf7ZpZlu8L3x7GnyRTquSobNSCJrn4/y35uxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5262}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c1e5b8a38ef54bb138409f89831942ebf6a7a67e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"jest-mock": "^29.6.0", "@jest/types": "^29.6.0", "@jest/expect": "^29.6.0", "@jest/environment": "^29.6.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_29.6.0_1688484355888_0.8303312561486862", "host": "s3://npm-registry-packages"}}, "29.6.1": {"name": "@jest/globals", "version": "29.6.1", "license": "MIT", "_id": "@jest/globals@29.6.1", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c8a8923e05efd757308082cc22893d82b8aa138f", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-29.6.1.tgz", "fileCount": 4, "integrity": "sha512-2VjpaGy78JY9n9370H8zGRCFbYVWwjY6RdDMhoJHa1sYfwe6XM/azGN0SjY8kk7BOZApIejQ1BFPyH7FPG0w3A==", "signatures": [{"sig": "MEQCIFz7x5jwCwKc0HG93LjJI4UM7Mz2YGGOXzGTUOJ/mzmxAiBKb+6Qm2PXjlFvJkffGC9ZCFDU7wj6cXRBUbJLtu4ndA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5262}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "1f019afdcdfc54a6664908bb45f343db4e3d0848", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"jest-mock": "^29.6.1", "@jest/types": "^29.6.1", "@jest/expect": "^29.6.1", "@jest/environment": "^29.6.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_29.6.1_1688653117368_0.46120933625297167", "host": "s3://npm-registry-packages"}}, "29.6.2": {"name": "@jest/globals", "version": "29.6.2", "license": "MIT", "_id": "@jest/globals@29.6.2", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "74af81b9249122cc46f1eb25793617eec69bf21a", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-29.6.2.tgz", "fileCount": 4, "integrity": "sha512-cjuJmNDjs6aMijCmSa1g2TNG4Lby/AeU7/02VtpW+SLcZXzOLK2GpN2nLqcFjmhy3B3AoPeQVx7BnyOf681bAw==", "signatures": [{"sig": "MEUCIQCDYw362Y3UJTk9jYjDgO9VrJKzY6K+yhqwuxHh4zoSjAIgcFjRh+EWFbOGzpT6qHqHbFiRbAK7AGA0R3gCStpCN3M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5262}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0fd5b1c37555f485c56a6ad2d6b010a72204f9f6", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"jest-mock": "^29.6.2", "@jest/types": "^29.6.1", "@jest/expect": "^29.6.2", "@jest/environment": "^29.6.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_29.6.2_1690449700859_0.9871594454251693", "host": "s3://npm-registry-packages"}}, "29.6.3": {"name": "@jest/globals", "version": "29.6.3", "license": "MIT", "_id": "@jest/globals@29.6.3", "maintainers": [{"name": "rubennorte", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "fe9e302bc20683ba8feb683b8804e38a9913b783", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-29.6.3.tgz", "fileCount": 4, "integrity": "sha512-RB+uI+CZMHntzlnOPlll5x/jgRff3LEPl/td/jzMXiIgR0iIhKq9qm1HLU+EC52NuoVy/1swit/sDGjVn4bc6A==", "signatures": [{"sig": "MEUCICSVS21rcMw6bWUTZbUWXrKVegTJnRHhpZfgYrFWHku4AiEAwvQfvAH526q/Axeo4XKcp0sdZetZrN4THoAc3q/WCIQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5260}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fb7d95c8af6e0d65a8b65348433d8a0ea0725b5b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"jest-mock": "^29.6.3", "@jest/types": "^29.6.3", "@jest/expect": "^29.6.3", "@jest/environment": "^29.6.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_29.6.3_1692621589512_0.24203301281871026", "host": "s3://npm-registry-packages"}}, "29.6.4": {"name": "@jest/globals", "version": "29.6.4", "license": "MIT", "_id": "@jest/globals@29.6.4", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "4f04f58731b062b44ef23036b79bdb31f40c7f63", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-29.6.4.tgz", "fileCount": 4, "integrity": "sha512-wVIn5bdtjlChhXAzVXavcY/*******************************************************/iCWrWsA==", "signatures": [{"sig": "MEUCIC57I8WOBRLjCS90dU9qOI7Ay1HC2/xuzbTcBX5IMUWRAiEApkuU9yPdQufWTSgisL7r6S2LBvn5wa2dT2C71gsgKbs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5260}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "55cd6a0aaf6f9178199dfa7af7a00fcaa7c421fd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/1.13.0/node@v20.5.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.5.1", "dependencies": {"jest-mock": "^29.6.3", "@jest/types": "^29.6.3", "@jest/expect": "^29.6.4", "@jest/environment": "^29.6.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_29.6.4_1692875476179_0.04844520555139531", "host": "s3://npm-registry-packages"}}, "29.7.0": {"name": "@jest/globals", "version": "29.7.0", "license": "MIT", "_id": "@jest/globals@29.7.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "8d9290f9ec47ff772607fa864ca1d5a2efae1d4d", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-29.7.0.tgz", "fileCount": 4, "integrity": "sha512-mpiz3dutLbkW2MNFubUGUEVLkTGiqW6yLVTA+JbP6fI6J5iL9Y0Nlg8k95pcF8ctKwCS7WVxteBs29hhfAotzQ==", "signatures": [{"sig": "MEYCIQDcriifhi1Eje7WJ51HN7LZsVrFEWj8+e3OXTNBW8R1FgIhAMYaKEQnTtm8uXnpvK6QVWUdBu8ixolLlPiTtbZPrIYl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5260}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4e56991693da7cd4c3730dc3579a1dd1403ee630", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"jest-mock": "^29.7.0", "@jest/types": "^29.6.3", "@jest/expect": "^29.7.0", "@jest/environment": "^29.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_29.7.0_1694501033930_0.3596892424782203", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.1": {"name": "@jest/globals", "version": "30.0.0-alpha.1", "license": "MIT", "_id": "@jest/globals@30.0.0-alpha.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "4abdd0040b377f8e0834650a741cb8fc43f5dc71", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-30.0.0-alpha.1.tgz", "fileCount": 4, "integrity": "sha512-HQkUwQbsB4mWoaaJ4DTmY89V7IO+xdRh511t88374gf0dShetCHvnFJn8ovdEOchtdnD0sJ7xWuc20/fnx/FZg==", "signatures": [{"sig": "MEYCIQDbRJ0Gd8r6N5ip3CwH+DrUKUBYKEqDOM/+AQsPu5egZwIhALTUT96n7ucJ9SHp8Yr1LAzncvvUFkqIXnMMAHAGsAN7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5642}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d005cb2505c041583e0c5636d006e08666a54b63", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/1.13.0/node@v20.9.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"jest-mock": "30.0.0-alpha.1", "@jest/types": "30.0.0-alpha.1", "@jest/expect": "30.0.0-alpha.1", "@jest/environment": "30.0.0-alpha.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_30.0.0-alpha.1_1698672803356_0.874727755033393", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.2": {"name": "@jest/globals", "version": "30.0.0-alpha.2", "license": "MIT", "_id": "@jest/globals@30.0.0-alpha.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "76a3cca5bed149160bf72d967abf6668267be1c0", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-30.0.0-alpha.2.tgz", "fileCount": 4, "integrity": "sha512-5t2CFIcaDuHGodxTenI7g8MWjLPE00s9IuckpOnFDhHH2Vui0vXNTgU1ExwXffsFZPnj+9GDo1wbjHCNUYK7KQ==", "signatures": [{"sig": "MEUCIQCPT8pz0YJlfdlIn1yIfT5YqeIEptzJc9O/oqYBSjWvWwIgD9dtVJOUaHDs1pPcekmn7h1piVpevlG1jNl72BcaSV4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5642}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c04d13d7abd22e47b0997f6027886aed225c9ce4", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/2.7.0/node@v20.9.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"jest-mock": "30.0.0-alpha.2", "@jest/types": "30.0.0-alpha.2", "@jest/expect": "30.0.0-alpha.2", "@jest/environment": "30.0.0-alpha.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_30.0.0-alpha.2_1700126921752_0.7633312667231482", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.3": {"name": "@jest/globals", "version": "30.0.0-alpha.3", "license": "MIT", "_id": "@jest/globals@30.0.0-alpha.3", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "434ef36317b46bb0f50bd2feea3cf98b03bb73c6", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-30.0.0-alpha.3.tgz", "fileCount": 4, "integrity": "sha512-moTQi5Iq/DM8kr0rMbpvLuejonVakL7498+LORlcW8ZSpTwUSO1SIiJa81AU1PWbLR6MtrUNACk9KS2DbetqNQ==", "signatures": [{"sig": "MEUCIQD5nOOB66XMWogkPNaJHK+sE1b4xZEXJWHb1aAdcU3irAIgSFKxRrxDivAwWwg2zB3vacpRohdiPhJl7GbyC6ZHXcM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5642}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "e267aff33d105399f2134bad7c8f82285104f3da", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/3.2.1/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"jest-mock": "30.0.0-alpha.3", "@jest/types": "30.0.0-alpha.3", "@jest/expect": "30.0.0-alpha.3", "@jest/environment": "30.0.0-alpha.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_30.0.0-alpha.3_1708427369972_0.4730370574480389", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.4": {"name": "@jest/globals", "version": "30.0.0-alpha.4", "license": "MIT", "_id": "@jest/globals@30.0.0-alpha.4", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "40f1fd22927fd34645e0e4f5237c059426bb0082", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-30.0.0-alpha.4.tgz", "fileCount": 4, "integrity": "sha512-m6e8GKdLSXf1RPAHx2bL2szUSL8Oo4z28s/CyIVGVFjDcJ4xWZ+Y8kpaP+Xd4sIpiV/FfH/lfUVmspuxKPg+IA==", "signatures": [{"sig": "MEYCIQD1UxtcfZmN+K1gyTQK7sIq0GMAETSTSAEDxbE43ff7EgIhAJ9AbPlFx1w6HavrpHHe57o0rMpV2TO99kcIGBm7akpx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5686}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "32b966f988d47a7673d2ef4b92e834dab7d66f07", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"jest-mock": "30.0.0-alpha.4", "@jest/types": "30.0.0-alpha.4", "@jest/expect": "30.0.0-alpha.4", "@jest/environment": "30.0.0-alpha.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_30.0.0-alpha.4_1715550224417_0.5219427878699707", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.5": {"name": "@jest/globals", "version": "30.0.0-alpha.5", "license": "MIT", "_id": "@jest/globals@30.0.0-alpha.5", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "3eb58e80a332de08d49be9292c117713e2bf259d", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-30.0.0-alpha.5.tgz", "fileCount": 4, "integrity": "sha512-4J+6X5IUmrzN3MNLK8BEJxOiA1iDCi7E3gmaIiTxc2w+E+wApxM7XZW7gvJFwqBJLCeo6uSPQ3vr2kcIABxzNQ==", "signatures": [{"sig": "MEQCID9mvMSjL4X8q1FrZZQyubivm/00R45wzT17rnzEI70qAiAx9yL5AmWTB1aY8EqA0yymeObr3iVWUK3PApEw8zx12A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5686}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fa24a3bdd6682978d76799265016fb9d5bff135e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"jest-mock": "30.0.0-alpha.5", "@jest/types": "30.0.0-alpha.5", "@jest/expect": "30.0.0-alpha.5", "@jest/environment": "30.0.0-alpha.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_30.0.0-alpha.5_1717073060174_0.9843060803626869", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.6": {"name": "@jest/globals", "version": "30.0.0-alpha.6", "license": "MIT", "_id": "@jest/globals@30.0.0-alpha.6", "maintainers": [{"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "dist": {"shasum": "56c0104f7fe0909472524bd096a4d2ca6050ac21", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-30.0.0-alpha.6.tgz", "fileCount": 4, "integrity": "sha512-+uJMoPUos9RH6+52iNgKJBbx1Hd2QsCZjExi5XpVvTjJ/gE4eJ1X7irUMt+14sH0QkeZ3GnjeTJFopyjOCsu+Q==", "signatures": [{"sig": "MEQCIA7nNfmXTCGEPMs/3b1C0VFSeT1MKfi7C4S9cLg5zyEoAiBPZgIeQo9VbWwA1Nu56EYNsa3QVJ1M+cCzBW7jV0Y5lQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5686}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ba74b7de1b9cca88daf33f9d1b46bfe2b7f485a5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/3.7.1/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"jest-mock": "30.0.0-alpha.6", "@jest/types": "30.0.0-alpha.6", "@jest/expect": "30.0.0-alpha.6", "@jest/environment": "30.0.0-alpha.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_30.0.0-alpha.6_1723102996996_0.9629606600753222", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.7": {"name": "@jest/globals", "version": "30.0.0-alpha.7", "license": "MIT", "_id": "@jest/globals@30.0.0-alpha.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "5d439cc0fb2cef6b6d98504ef76043fc5c0ae227", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-30.0.0-alpha.7.tgz", "fileCount": 4, "integrity": "sha512-y5CKO76wPQfoqMVN+8DlYCU08mqhvfMDGV58j0XYYQIRWYOeIqvvveVBaq47ePknubsOBQeS0Z0zflQccZUnCg==", "signatures": [{"sig": "MEUCIHBuiggKsTcLOBXRj2pkYCumfxUFxJy4qUdD1+/iwW0LAiEAm2yyyeBhJfmZwIxA6K5ldQ7lXN5DOxTpoEIil6grWbc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5686}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "bacb7de30d053cd87181294b0c8a8576632a8b02", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/3.11.0/node@v20.18.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"jest-mock": "30.0.0-alpha.7", "@jest/types": "30.0.0-alpha.7", "@jest/expect": "30.0.0-alpha.7", "@jest/environment": "30.0.0-alpha.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_30.0.0-alpha.7_1738225725181_0.6008598564202356", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.2": {"name": "@jest/globals", "version": "30.0.0-beta.2", "license": "MIT", "_id": "@jest/globals@30.0.0-beta.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "c5f7f410491622c2f9665dd9b2cdaa8934cb8854", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-30.0.0-beta.2.tgz", "fileCount": 4, "integrity": "sha512-xNu6FlDeBgt/wpHRbwp6W6dZjikRea2X1l7R17YFZup/CwhFrODmiapBi/rAx8Y2r2tQuZb1pP5IrWyVQb0faA==", "signatures": [{"sig": "MEQCIEAUtYw7Oh7hNq8wPpUcUdFIFVsHXK7xBNVHXucQAzsOAiAa3DzGkPNRRvNeD76Omb8y76iMWgnkcEuniU2SiamkkQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5680}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "53a5635ac9a43099033f6103e179b13a5465e017", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"jest-mock": "30.0.0-beta.1", "@jest/types": "30.0.0-beta.1", "@jest/expect": "30.0.0-beta.2", "@jest/environment": "30.0.0-beta.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_30.0.0-beta.2_1748309010253_0.29039535029440255", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.3": {"name": "@jest/globals", "version": "30.0.0-beta.3", "license": "MIT", "_id": "@jest/globals@30.0.0-beta.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "cd5ccd293c5e392ebd1fda2064f1f902884f4fe2", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-30.0.0-beta.3.tgz", "fileCount": 4, "integrity": "sha512-VHeYkAz/hhj5cSNsdVEjJw0qSpred2LR7Q/UNgDasopVb7ue3DcAtwHx3f/lYsn8iRTyHQZFTZmx3rCMVlS7Vw==", "signatures": [{"sig": "MEYCIQCmnLX90XpQhn0fqfo+CrCGxtYGkEceCDOrsg80yTiDJgIhAJbDFkOtE+3DETxOajehSYYwqiKOQteQ060lCW/jA5nU", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5680}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a123a3b667a178fb988662aaa1bc6308af759017", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"jest-mock": "30.0.0-beta.3", "@jest/types": "30.0.0-beta.3", "@jest/expect": "30.0.0-beta.3", "@jest/environment": "30.0.0-beta.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_30.0.0-beta.3_1748309278856_0.7141356505051648", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.4": {"name": "@jest/globals", "version": "30.0.0-beta.4", "license": "MIT", "_id": "@jest/globals@30.0.0-beta.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "d8969a170f5f14e75140e11359cc9f4718ddc691", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-30.0.0-beta.4.tgz", "fileCount": 4, "integrity": "sha512-cYn9pAdjyVA3GXqabVnv1wg//QSQ1kXF6OLKTKXMM40adkdTSfAZ8C+E+KtkfREOrOr+TMDLGBd1FlN4xr7GvA==", "signatures": [{"sig": "MEUCIB5xPi3cXvzUfne5+Zveb6GHO+2HtIRhL1IICylt67gNAiEAkQPdEw+OI/GMc2m5hAZgsHCuX4U05W5ymaf1kd0umlc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5680}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "69f0c890c804e6e6b0822adb592cd00372a7c297", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"jest-mock": "30.0.0-beta.3", "@jest/types": "30.0.0-beta.3", "@jest/expect": "30.0.0-beta.4", "@jest/environment": "30.0.0-beta.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_30.0.0-beta.4_1748329476573_0.6312148060240848", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.5": {"name": "@jest/globals", "version": "30.0.0-beta.5", "license": "MIT", "_id": "@jest/globals@30.0.0-beta.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "3f76329c8cd0bfb203d8f9ba07a4078e00b36830", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-30.0.0-beta.5.tgz", "fileCount": 4, "integrity": "sha512-lpmW65k4EvQ0na2xyrHd61k9y7GMctxJ/A/9WmrOy5j8V5pS8Al01FdvmtYo3nv7w5XhG/vxR7qncFdGvcqXug==", "signatures": [{"sig": "MEQCIGPGpvfnW7rkucmv0ejy6L1QMDxQLaNjAFfMAY65NhN/AiAFQlv3OAJNDWje4SPWtgjBlENvOONZm1QMhDdxdCfpcQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5680}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f2171bb4c6836d74ad2b32a48151d9e0fdfa20a2", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"jest-mock": "30.0.0-beta.3", "@jest/types": "30.0.0-beta.3", "@jest/expect": "30.0.0-beta.5", "@jest/environment": "30.0.0-beta.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_30.0.0-beta.5_1748478624070_0.6220306711417771", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.6": {"name": "@jest/globals", "version": "30.0.0-beta.6", "license": "MIT", "_id": "@jest/globals@30.0.0-beta.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "0b6540377fbeacba1e60e25f082e0915a341e5a1", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-30.0.0-beta.6.tgz", "fileCount": 4, "integrity": "sha512-WwRm6UnyRgT8rj7nRIRlnmwLlXsfQIM8cQrttGQltHad2kf5w+FOWyCJUsA1rDapG8dgJNLFqIyCZ2EEHpntSg==", "signatures": [{"sig": "MEQCIGwqvALJqCN2DwgekwHYD/VNAamUsvgfvFMFzhyODuhNAiAirqBg1kKIXMLSxr6nXrUvjFXzF6kUbZLvCrtm7H8t7g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5691}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4f964497dc21c06ce4d54f1349e299a9f6773d52", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/3.12.3/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"jest-mock": "30.0.0-beta.6", "@jest/types": "30.0.0-beta.6", "@jest/expect": "30.0.0-beta.6", "@jest/environment": "30.0.0-beta.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_30.0.0-beta.6_1748994661893_0.7166275013585466", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.7": {"name": "@jest/globals", "version": "30.0.0-beta.7", "license": "MIT", "_id": "@jest/globals@30.0.0-beta.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "78130cf1dedaecaee8ced927a3c5b208717e4984", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-30.0.0-beta.7.tgz", "fileCount": 4, "integrity": "sha512-b0Ce3fNCSFHSv83UwOolEkdoqcQTaVJIrPNs16NRme0XbX7+1nPJ8tt18+7oocHfdCJ7zXiqvVaS86Qg0IntgQ==", "signatures": [{"sig": "MEUCIQDTyI/BOhUjZsv8kRnJQmEmiYBOjSZ+YyV6EspFNgtdbwIgE4VjtXb1iBNgPLUA09qijV6MZuprnN2zkq5bOT8/6GY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5691}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "48de6a91368727d853d491df16e7d00c1f323676", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"jest-mock": "30.0.0-beta.7", "@jest/types": "30.0.0-beta.7", "@jest/expect": "30.0.0-beta.7", "@jest/environment": "30.0.0-beta.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_30.0.0-beta.7_1749008154440_0.33696890442545047", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.8": {"name": "@jest/globals", "version": "30.0.0-beta.8", "license": "MIT", "_id": "@jest/globals@30.0.0-beta.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "5362fea6bd46c3e7494cb853495c683167bc1a86", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-30.0.0-beta.8.tgz", "fileCount": 4, "integrity": "sha512-eLhEc9m8QQwMtRMk87G4licNehGo47ZRSv4x2oCieZnNKtRDUP7z+XiFIl0lZLFLO9TtkUYHZNKj6ifR7c7hFw==", "signatures": [{"sig": "MEQCIFyxtP442FmRe+t4oq8jhK8oNbW3MLRwULvuQ2A7cKLdAiADV++gS4J0gF1UzPIu9vCFtjaAs6QPkSXHpxRhaMlHvw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5691}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ac334c0cdf04ead9999f0964567d81672d116d42", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"jest-mock": "30.0.0-beta.8", "@jest/types": "30.0.0-beta.8", "@jest/expect": "30.0.0-beta.8", "@jest/environment": "30.0.0-beta.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_30.0.0-beta.8_1749023602605_0.11510397576195208", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-rc.1": {"name": "@jest/globals", "version": "30.0.0-rc.1", "license": "MIT", "_id": "@jest/globals@30.0.0-rc.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "63cd8aa12fec372e8776d08ee0066bec31ff2383", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-30.0.0-rc.1.tgz", "fileCount": 4, "integrity": "sha512-Ch7TeBVtr/3D+h1tfkkR4/ngJHJyG1blnTBTqgGMnVQHzD2ITCxaA2EsJ5Cea9XESRVpY4AkL9XV+jX/EMPKfA==", "signatures": [{"sig": "MEQCIEp+qD60KnVscGy7zKr28gI49x6ESLO1AklhrlG/VBtAAiAheZPNbJ6ir0xjSbuUi63XZ8ALySIUkGBVLtZ1kaB9CQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5683}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ce14203d9156f830a8e24a6e3e8205f670a72a40", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"jest-mock": "30.0.0-rc.1", "@jest/types": "30.0.0-beta.8", "@jest/expect": "30.0.0-rc.1", "@jest/environment": "30.0.0-rc.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_30.0.0-rc.1_1749430978279_0.6222986831832718", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0": {"name": "@jest/globals", "version": "30.0.0", "license": "MIT", "_id": "@jest/globals@30.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "b80a488ec3fc99637455def038e53cfcd562a18f", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-30.0.0.tgz", "fileCount": 4, "integrity": "sha512-OEzYes5A1xwBJVMPqFRa8NCao8Vr42nsUZuf/SpaJWoLE+4kyl6nCQZ1zqfipmCrIXQVALC5qJwKy/7NQQLPhw==", "signatures": [{"sig": "MEUCIQDeXqcV+2g4Y5ROlUpCNz6qCYaHgZ7HdLCEqYBILY3pfwIgAWJJUf+woNIAy52RUonAdvGAcytufuKMYLSJdhjNwN8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5656}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a383155cd5af4539b3c447cfa7184462ee32f418", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"jest-mock": "30.0.0", "@jest/types": "30.0.0", "@jest/expect": "30.0.0", "@jest/environment": "30.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_30.0.0_1749521764466_0.4434477687399556", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.1": {"name": "@jest/globals", "version": "30.0.1", "license": "MIT", "_id": "@jest/globals@30.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "34e902432d5058e20aabda056d6e249c71b030ff", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-30.0.1.tgz", "fileCount": 4, "integrity": "sha512-5IdHDqKVayXzBL8sKM5AvPaAnrfO9GXphDLwOg6VWjUiqSrGcj/Hd518QpfDWOeu1aWjBblst3rxeRgbtOEJ8Q==", "signatures": [{"sig": "MEQCIFCwuioeCd5zuDpNG0+jWoxCMjFUgqtheEi1Og3N0gyBAiAr83DrOTtjd4ow2ZvbfNCLAAX276Bd8iZ68OxsTLmzHQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5656}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "5ce865b4060189fe74cd486544816c079194a0f7", "_npmUser": {"name": "cpojer", "actor": {"name": "cpojer", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-globals"}, "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.2.0", "dependencies": {"jest-mock": "30.0.1", "@jest/types": "30.0.1", "@jest/expect": "30.0.1", "@jest/environment": "30.0.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/globals_30.0.1_1750285901396_0.8555698474779263", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.2": {"name": "@jest/globals", "version": "30.0.2", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-globals"}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@jest/environment": "30.0.2", "@jest/expect": "30.0.2", "@jest/types": "30.0.1", "jest-mock": "30.0.2"}, "publishConfig": {"access": "public"}, "gitHead": "393acbfac31f64bb38dff23c89224797caded83c", "_nodeVersion": "24.2.0", "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "_id": "@jest/globals@30.0.2", "dist": {"integrity": "sha512-DwTtus9jjbG7b6jUdkcVdptf0wtD1v153A+PVwWB/zFwXhqu6hhtSd+uq88jofMhmYPtkmPmVGUBRNCZEKXn+w==", "shasum": "3b401bb7cb8cc0a00476630298747a38e40a6fc1", "tarball": "https://registry.npmjs.org/@jest/globals/-/globals-30.0.2.tgz", "fileCount": 4, "unpackedSize": 5656, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIFTcKw39JO5WmQR80z4VOQkKI3NUf56NTOqKU3w/H/y6AiBROoLKvdvAxzSd2Vg65NTbtLgFqN2/jMiaA7gHwUUi1Q=="}]}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>", "actor": {"name": "cpojer", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/globals_30.0.2_1750329990800_0.6388191415593445"}, "_hasShrinkwrap": false}}, "time": {"created": "2020-04-28T19:45:46.879Z", "modified": "2025-06-19T10:46:31.203Z", "25.5.0": "2020-04-28T19:45:47.235Z", "25.5.2": "2020-04-29T21:50:03.536Z", "26.0.0-alpha.0": "2020-05-02T12:13:22.650Z", "26.0.0-alpha.1": "2020-05-03T18:48:29.271Z", "26.0.0-alpha.2": "2020-05-04T16:05:53.768Z", "26.0.0": "2020-05-04T17:53:29.908Z", "26.0.1-alpha.0": "2020-05-04T22:16:19.665Z", "26.0.1": "2020-05-05T10:41:21.349Z", "26.1.0": "2020-06-23T15:15:44.288Z", "26.2.0": "2020-07-30T10:12:07.276Z", "26.3.0": "2020-08-10T11:32:09.919Z", "26.4.0": "2020-08-12T21:00:39.116Z", "26.4.1": "2020-08-20T08:31:43.788Z", "26.4.2": "2020-08-22T12:10:12.048Z", "26.5.0": "2020-10-05T09:28:34.347Z", "26.5.2": "2020-10-06T10:53:11.967Z", "26.5.3": "2020-10-11T17:49:22.158Z", "26.6.0": "2020-10-19T11:59:05.044Z", "26.6.1": "2020-10-23T09:06:57.150Z", "26.6.2": "2020-11-02T12:51:54.094Z", "27.0.0-next.0": "2020-12-05T17:25:40.518Z", "27.0.0-next.1": "2020-12-07T12:43:43.871Z", "27.0.0-next.3": "2021-02-18T22:10:17.991Z", "27.0.0-next.4": "2021-03-08T13:45:14.004Z", "27.0.0-next.5": "2021-03-15T13:03:39.154Z", "27.0.0-next.6": "2021-03-25T19:40:19.505Z", "27.0.0-next.7": "2021-04-02T13:48:18.272Z", "27.0.0-next.8": "2021-04-12T22:42:45.437Z", "27.0.0-next.9": "2021-05-04T06:25:24.946Z", "27.0.0-next.10": "2021-05-20T14:11:35.927Z", "27.0.0-next.11": "2021-05-20T22:29:03.837Z", "27.0.0": "2021-05-25T08:15:35.073Z", "27.0.1": "2021-05-25T10:06:52.125Z", "27.0.2": "2021-05-29T12:07:39.544Z", "27.0.3": "2021-05-29T17:47:45.271Z", "27.0.5": "2021-06-22T11:10:49.351Z", "27.0.6": "2021-06-28T17:05:56.899Z", "27.1.0": "2021-08-27T09:59:55.624Z", "27.1.1": "2021-09-08T10:12:26.845Z", "27.2.0": "2021-09-13T08:06:57.292Z", "27.2.1": "2021-09-20T13:28:03.468Z", "27.2.2": "2021-09-25T13:35:11.869Z", "27.2.3": "2021-09-28T10:11:29.255Z", "27.2.4": "2021-09-29T14:04:57.406Z", "27.2.5": "2021-10-08T13:39:30.383Z", "27.3.0": "2021-10-17T18:34:50.916Z", "27.3.1": "2021-10-19T06:57:38.098Z", "27.4.0": "2021-11-29T13:37:41.392Z", "27.4.1": "2021-11-30T08:37:21.314Z", "27.4.2": "2021-11-30T11:53:58.968Z", "27.4.4": "2021-12-10T04:43:10.013Z", "27.4.6": "2022-01-04T23:03:50.132Z", "27.5.0": "2022-02-05T09:59:33.668Z", "27.5.1": "2022-02-08T10:52:31.069Z", "28.0.0-alpha.0": "2022-02-10T18:17:44.837Z", "28.0.0-alpha.1": "2022-02-15T21:27:09.526Z", "28.0.0-alpha.2": "2022-02-16T18:12:26.659Z", "28.0.0-alpha.3": "2022-02-17T15:42:30.960Z", "28.0.0-alpha.4": "2022-02-22T12:14:02.617Z", "28.0.0-alpha.5": "2022-02-24T20:57:31.354Z", "28.0.0-alpha.6": "2022-03-01T08:32:32.640Z", "28.0.0-alpha.7": "2022-03-06T10:02:50.366Z", "28.0.0-alpha.8": "2022-04-05T15:00:18.108Z", "28.0.0-alpha.9": "2022-04-19T10:59:21.626Z", "28.0.0-alpha.10": "2022-04-20T07:37:34.532Z", "28.0.0-alpha.11": "2022-04-20T13:31:05.839Z", "28.0.0": "2022-04-25T12:08:16.989Z", "28.0.1": "2022-04-26T10:02:45.902Z", "28.0.2": "2022-04-27T07:44:11.785Z", "28.0.3": "2022-04-29T10:44:28.781Z", "28.1.0": "2022-05-06T10:49:01.241Z", "28.1.1": "2022-06-07T06:09:43.028Z", "28.1.2": "2022-06-29T10:34:02.198Z", "28.1.3": "2022-07-13T14:12:37.405Z", "29.0.0-alpha.0": "2022-07-17T22:07:14.784Z", "29.0.0-alpha.1": "2022-08-04T08:23:36.563Z", "29.0.0-alpha.3": "2022-08-07T13:41:45.983Z", "29.0.0-alpha.4": "2022-08-08T13:05:47.180Z", "29.0.0-alpha.5": "2022-08-11T13:41:04.056Z", "29.0.0-alpha.6": "2022-08-19T13:58:01.819Z", "29.0.0": "2022-08-25T12:33:37.802Z", "29.0.1": "2022-08-26T13:34:51.542Z", "29.0.2": "2022-09-03T10:48:29.159Z", "29.0.3": "2022-09-10T14:42:03.564Z", "29.1.0": "2022-09-28T07:37:50.382Z", "29.1.1": "2022-09-28T08:05:38.656Z", "29.1.2": "2022-09-30T07:22:57.715Z", "29.2.0": "2022-10-14T09:14:06.147Z", "29.2.1": "2022-10-18T16:00:22.571Z", "29.2.2": "2022-10-24T20:24:11.890Z", "29.3.0": "2022-11-07T17:55:55.863Z", "29.3.1": "2022-11-08T22:56:30.606Z", "29.4.0": "2023-01-24T10:56:07.407Z", "29.4.1": "2023-01-26T15:08:52.004Z", "29.4.2": "2023-02-07T13:45:46.049Z", "29.4.3": "2023-02-15T11:57:34.196Z", "29.5.0": "2023-03-06T13:33:44.604Z", "29.6.0": "2023-07-04T15:25:56.037Z", "29.6.1": "2023-07-06T14:18:37.556Z", "29.6.2": "2023-07-27T09:21:41.054Z", "29.6.3": "2023-08-21T12:39:49.734Z", "29.6.4": "2023-08-24T11:11:16.349Z", "29.7.0": "2023-09-12T06:43:54.076Z", "30.0.0-alpha.1": "2023-10-30T13:33:23.524Z", "30.0.0-alpha.2": "2023-11-16T09:28:42.012Z", "30.0.0-alpha.3": "2024-02-20T11:09:30.115Z", "30.0.0-alpha.4": "2024-05-12T21:43:44.615Z", "30.0.0-alpha.5": "2024-05-30T12:44:20.326Z", "30.0.0-alpha.6": "2024-08-08T07:43:17.176Z", "30.0.0-alpha.7": "2025-01-30T08:28:45.378Z", "30.0.0-beta.2": "2025-05-27T01:23:30.422Z", "30.0.0-beta.3": "2025-05-27T01:27:59.029Z", "30.0.0-beta.4": "2025-05-27T07:04:36.744Z", "30.0.0-beta.5": "2025-05-29T00:30:24.308Z", "30.0.0-beta.6": "2025-06-03T23:51:02.063Z", "30.0.0-beta.7": "2025-06-04T03:35:54.661Z", "30.0.0-beta.8": "2025-06-04T07:53:22.772Z", "30.0.0-rc.1": "2025-06-09T01:02:58.510Z", "30.0.0": "2025-06-10T02:16:04.652Z", "30.0.1": "2025-06-18T22:31:41.593Z", "30.0.2": "2025-06-19T10:46:30.972Z"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-globals"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}