{"_id": "@babel/generator", "_rev": "242-c6d0238b4859d28697261fdb7b9ff667", "name": "@babel/generator", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.5", "next": "8.0.0-beta.0"}, "versions": {"7.0.0-beta.4": {"name": "@babel/generator", "version": "7.0.0-beta.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.0.0-beta.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "99606c425fc6614e6386f9330efb48370f200fc3", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.0.0-beta.4.tgz", "integrity": "sha512-aLpZzf79oGT1bxnsadapfUWErDTcxVKrhvR5F8G27JFgH37+/ATrODMJ0/1D2CgQ/WStDX5B5znnWRv0NzW2JQ==", "signatures": [{"sig": "MEQCHzyN9iv4oOey7d7agbFCSFpgyp23m/VY1dOMFRzIaYkCIQCMrK4SpyhYn8LM62e4HozFQ0qGU2HxM3v1gAgwwQhaVg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "files": ["lib"], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.2.0", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "7.0.0-beta.4"}, "devDependencies": {"babylon": "^7.0.0-beta.30", "@babel/helper-fixtures": "7.0.0-beta.4"}, "_npmOperationalInternal": {"tmp": "tmp/generator-7.0.0-beta.4.tgz_1509388500974_0.10766214807517827", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.5": {"name": "@babel/generator", "version": "7.0.0-beta.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.0.0-beta.5", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "ad90d90b521f82ce5763be644903003f6da0685c", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.0.0-beta.5.tgz", "integrity": "sha512-ykIosz5pjYZ2jAbc4keVXqFnQkIv7MpnOAwNGsPZC2Y8V/uD3ga9/e1hrFvrBJMGgHQpMhc6jM+xjfyL2GjZmg==", "signatures": [{"sig": "MEUCIQClK9wLAtuPOlavfpR37CTfVy9P3RUyiNgfIHNIAWuuiwIgfZAQPgMgCfWtdzADRPWT9lT+r9v9Lw1M/OCo7RPsIQ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "files": ["lib"], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.2.0", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "7.0.0-beta.5"}, "devDependencies": {"babylon": "^7.0.0-beta.30", "@babel/helper-fixtures": "7.0.0-beta.5"}, "_npmOperationalInternal": {"tmp": "tmp/generator-7.0.0-beta.5.tgz_1509397000542_0.6743148185778409", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.31": {"name": "@babel/generator", "version": "7.0.0-beta.31", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.0.0-beta.31", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "c9be74c20ab21ce8c623246c64c2d8f9905b26fd", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.0.0-beta.31.tgz", "integrity": "sha512-Y4waeBHwWySZWt1O7sm4Ym+4aGa1I7bLCQb3Bw6xEgvRxkJzACP3mDVVzW8T1U9p//JkkwCO5bvjUBhcG36F8Q==", "signatures": [{"sig": "MEQCIETZSU5owHeaFFy74DZhVO24YMB5ban9J4lzgpxU0pC6AiBTdeN3Pt5jd6+rXAG6z9O9bvz2IUlWXQ92U5DPDacExw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "files": ["lib"], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "8.1.4", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.2.0", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "7.0.0-beta.31"}, "devDependencies": {"babylon": "7.0.0-beta.31", "@babel/helper-fixtures": "7.0.0-beta.31"}, "_npmOperationalInternal": {"tmp": "tmp/generator-7.0.0-beta.31.tgz_1509739419330_0.08306208555586636", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.32": {"name": "@babel/generator", "version": "7.0.0-beta.32", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.0.0-beta.32", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "37d8124ea7770b4555da28be0917b47f365aca97", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.0.0-beta.32.tgz", "integrity": "sha512-JQrG+5JQmaDwyj+VL01eE9dX6PSVBfvDyRbA1AXwzk0+BcQ71++M64K2HzbVyD3sti/yjh0EUJAg0Yw9Fn7ZuQ==", "signatures": [{"sig": "MEYCIQDZmsQT1Lct/qXHT7xqeVWWRWThPHuiJtvYy8u5wbZ2tgIhAPuBsduNz2KTWqtJYuQAqYpcEQ6p4jQoSVEyi2z3MIFv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "files": ["lib"], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.2.0", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "7.0.0-beta.32"}, "devDependencies": {"babylon": "7.0.0-beta.32", "@babel/helper-fixtures": "7.0.0-beta.32"}, "_npmOperationalInternal": {"tmp": "tmp/generator-7.0.0-beta.32.tgz_1510493610903_0.16813884885050356", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.33": {"name": "@babel/generator", "version": "7.0.0-beta.33", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.0.0-beta.33", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "412662df70948c7087b5128a713acefe84395a20", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.0.0-beta.33.tgz", "integrity": "sha512-BcnfFgfJ4shTYBKoY6IG2JlbBix0c4JiGiQtcrqm3DEeAPPOhwmgltIAQjHFNTCn7nIyHjPTLRye/zCGgRn1vA==", "signatures": [{"sig": "MEYCIQCQgCklH+/Grr7Eg5nsHynPnjJ0teEUmq2Mz/jUO3ZqQwIhAMH1Q8F53fouYh9laGv819Mb9aIAe6Dl6oyfGL1LmuTu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "files": ["lib"], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.2.0", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "7.0.0-beta.33"}, "devDependencies": {"babylon": "7.0.0-beta.33", "@babel/helper-fixtures": "7.0.0-beta.33"}, "_npmOperationalInternal": {"tmp": "tmp/generator-7.0.0-beta.33.tgz_1512138517157_0.9602218335494399", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.34": {"name": "@babel/generator", "version": "7.0.0-beta.34", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.0.0-beta.34", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "1a98992f3ef9e61a6007830883dfa2dbb26c622d", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.0.0-beta.34.tgz", "integrity": "sha512-2ymX/4tB4JzuLIpv2iYzWlMGz55Ixs/YjMVjtZ49qXaKvVhkJ0+a2p3rg0Nq98/1ngI1mTRtOzGnR6Or01Qc8A==", "signatures": [{"sig": "MEUCIQCqaFE1fTl1ro/QGtZrBtCnin7EdWRj+3Wbh9q01+a37gIgOXYXdncVHPE4XhfLVL4+RuoWsyUTKGjtGQD3C1u6nwg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "files": ["lib"], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.2.0", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "7.0.0-beta.34"}, "devDependencies": {"babylon": "7.0.0-beta.34", "@babel/helper-fixtures": "7.0.0-beta.34"}, "_npmOperationalInternal": {"tmp": "tmp/generator-7.0.0-beta.34.tgz_1512225577898_0.4069888168014586", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.35": {"name": "@babel/generator", "version": "7.0.0-beta.35", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.0.0-beta.35", "maintainers": [{"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "09798d7e714ef8a3cd6ac27afb140e21c2794cef", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.0.0-beta.35.tgz", "integrity": "sha512-5QO6oYlc5xpbZEBFcxdUfzNvtjTwKfttfqkkZZ8FCs7CQwmtqzZMzFvYc+pa8QmMxUF5D8c1+XI/mOy3lYjK7Q==", "signatures": [{"sig": "MEYCIQCM9aKHw89LWc8n4HTJMwN8lkpORf8A49sb2nBxyFLHKAIhAPnidJA6du4AZzEJD/MIfmpZ6sAxR0GTxX1ocng6/5Wg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "files": ["lib"], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.2.0", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "7.0.0-beta.35"}, "devDependencies": {"babylon": "7.0.0-beta.35", "@babel/helper-fixtures": "7.0.0-beta.35"}, "_npmOperationalInternal": {"tmp": "tmp/generator-7.0.0-beta.35.tgz_1513288080357_0.6554676676169038", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.36": {"name": "@babel/generator", "version": "7.0.0-beta.36", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.0.0-beta.36", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "kentcdodds", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "not-an-a<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "mysticatea", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "669c39ffdeb75521ad43b5c627743658a3f1dc5b", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.0.0-beta.36.tgz", "integrity": "sha512-lsFehk83UlzT+5ZTC+4p8WI16zkWYGgGQGqQFgdhCdcvt9yLHZ3bUORHC0H9+Wyp/7cJ68xjlYMDsTf48EcbDQ==", "signatures": [{"sig": "MEUCIQD4Ywc1nfjG5VRo01dACCDiiN16MUW/sUf3bUf4lU7GBgIgItPE+LNXdNSzNN+TsAr861O4dzRv0sUnZcNVi1RGP70=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "files": ["lib"], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.2.0", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "7.0.0-beta.36"}, "devDependencies": {"babylon": "7.0.0-beta.36", "@babel/helper-fixtures": "7.0.0-beta.36"}, "_npmOperationalInternal": {"tmp": "tmp/generator-7.0.0-beta.36.tgz_1514228697622_0.9167013845872134", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.37": {"name": "@babel/generator", "version": "7.0.0-beta.37", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.0.0-beta.37", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "df5183995f20af4afe3bd5499a4c1336355fc163", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.0.0-beta.37.tgz", "integrity": "sha512-IXxBvA+df/CXcvpvlRpi6eWx7B/rXrTcqcs0QraCauhBH488YZoAU75ZNpvJldlwLGdPQrsTB0LKA2VN+yWyYg==", "signatures": [{"sig": "MEYCIQDoHYpjTx3C8W/M1U/ZoPdfsOE8Jum8KeoSaunsEgQWPwIhAJlSM2QPyDmOftXp9dgoAnAdteZZLaC1I15J29bnuOUc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "files": ["lib"], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.2.0", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "7.0.0-beta.37"}, "devDependencies": {"babylon": "7.0.0-beta.37", "@babel/helper-fixtures": "7.0.0-beta.37"}, "_npmOperationalInternal": {"tmp": "tmp/generator-7.0.0-beta.37.tgz_1515427361574_0.8515464782249182", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.38": {"name": "@babel/generator", "version": "7.0.0-beta.38", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.0.0-beta.38", "maintainers": [{"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "6115a66663e3adfd1d6844029ffb2354680182eb", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.0.0-beta.38.tgz", "integrity": "sha512-aOHQPhsEyaB6p2n+AK981+onHoc+Ork9rcAQVSUJR33wUkGiWRpu6/C685knRyIZVsKeSdG5Q4xMiYeFUhuLzA==", "signatures": [{"sig": "MEYCIQCxDbpLJu44TtLua60vz9v/T3Kc29d26OFgsiJILQqCHAIhAIjVMlx9eT8GYTzq+3RZIDNInZjwCYOajTd/RJagndEX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "files": ["lib"], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "5.5.1", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.2.0", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "7.0.0-beta.38"}, "devDependencies": {"babylon": "7.0.0-beta.38", "@babel/helper-fixtures": "7.0.0-beta.38"}, "_npmOperationalInternal": {"tmp": "tmp/generator-7.0.0-beta.38.tgz_1516206729802_0.8417271552607417", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.39": {"name": "@babel/generator", "version": "7.0.0-beta.39", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.0.0-beta.39", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "xtuc", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "d2c9f0a9c47d5ff288f0306aedd0cf89983cb6ed", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.0.0-beta.39.tgz", "integrity": "sha512-fjKbJxdvzmTxpZPDrXxSMrzzJ+qUEjVNXmJuXwMTupPVBAIr13EGE+VauHXp/14AcXAXqX1/XvFK9cf/tB3OMA==", "signatures": [{"sig": "MEUCIQC4GXybvrcT8/qge85FAdVrq882v8uMzvIwGFmK4FFiIQIgUvtAOBn4NTbjQ6uV0kfuyN8xOZ/Xa0VkN64cekm7zQs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "files": ["lib"], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.2.0", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "7.0.0-beta.39"}, "devDependencies": {"babylon": "7.0.0-beta.39", "@babel/helper-fixtures": "7.0.0-beta.39"}, "_npmOperationalInternal": {"tmp": "tmp/generator-7.0.0-beta.39.tgz_1517344063550_0.17312948335893452", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.40": {"name": "@babel/generator", "version": "7.0.0-beta.40", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.0.0-beta.40", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "ab61f9556f4f71dbd1138949c795bb9a21e302ea", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.0.0-beta.40.tgz", "fileCount": 21, "integrity": "sha512-c91BQcXyTq/5aFV4afgOionxZS1dxWt8OghEx5Q52SKssdGRFSiMKnk9tGkev1pYULPJBqjSDZU2Pcuc58ffZw==", "signatures": [{"sig": "MEUCIDcp/v2u13Otb8rjBVRDdUCId/uE6Wv8KKWvBr+2TrTCAiEA/ofXRQHOzOptoNMbNmfppdlQxRCfW+irSZAVyTDR3vo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106590}, "main": "lib/index.js", "files": ["lib"], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.2.0", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "7.0.0-beta.40"}, "_hasShrinkwrap": false, "devDependencies": {"babylon": "7.0.0-beta.40", "@babel/helper-fixtures": "7.0.0-beta.40"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.0.0-beta.40_1518453713937_0.9614778915644844", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.41": {"name": "@babel/generator", "version": "7.0.0-beta.41", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.0.0-beta.41", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "5fce0676cea5acc1d493480d9fb7317ea2164d3f", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.0.0-beta.41.tgz", "fileCount": 21, "integrity": "sha512-j1hBM7K53RqAwPzPkS3GK9ggxJUBaUHevkpnt9iKBPHVlTrO3owMMlT8wH/GmOjr0HyBGFlL8cSlygx8RLdD7A==", "signatures": [{"sig": "MEQCIGbTNZX5v3ipC2YxaNyN4pTz788jcuRFF50S54BCOa0sAiAuC3hTxfkEGF/0xYYgwg+OdF2OsOJGHfhH4rMROY1xcg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106958}, "main": "lib/index.js", "files": ["lib"], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.2.0", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "7.0.0-beta.41"}, "_hasShrinkwrap": false, "devDependencies": {"babylon": "7.0.0-beta.41", "@babel/helper-fixtures": "7.0.0-beta.41"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.0.0-beta.41_1521044742103_0.7138530712331883", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.42": {"name": "@babel/generator", "version": "7.0.0-beta.42", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.0.0-beta.42", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "777bb50f39c94a7e57f73202d833141f8159af33", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.0.0-beta.42.tgz", "fileCount": 21, "integrity": "sha512-9x3zS4nG/6GAvJWB8fAK+5g/Di36xdubB43dMNSucNJTwPvmyfCippir/0I8zyG+ID66hLCLi8V9bomlWRYaHA==", "signatures": [{"sig": "MEQCIBzgEV0tkKUHVaaiucZNFdYNn3XBP6bTtnegQ2OvGZ7yAiBcu4EiYPfmdMq+n/CPbQ9WzMOFowMWQOc5yuC2kq1iCg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106958}, "main": "lib/index.js", "files": ["lib"], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.2.0", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "7.0.0-beta.42"}, "_hasShrinkwrap": false, "devDependencies": {"babylon": "7.0.0-beta.42", "@babel/helper-fixtures": "7.0.0-beta.42"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.0.0-beta.42_1521147016388_0.7140943323862943", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.43": {"name": "@babel/generator", "version": "7.0.0-beta.43", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.0.0-beta.43", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "9f32baf9fe6a4a79872d1825bb7541ed992573ca", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.0.0-beta.43.tgz", "fileCount": 21, "integrity": "sha512-ZzwakIcwsWEL9DpKgUg6SxTbObME+U8TvEqWlKvdRbrTp/Z/kNUnNHbSmWbPNEq0Min8I3mMdh16DE7rcpui7g==", "signatures": [{"sig": "MEUCIQCGQZtJLfyPxaZ4eNotB7FapAj8nv9umu2sYkGsbcDaaQIgTqEMVstP4n8FA4czXPCAOPeUmkR5jTO+A8ahqFF1bvk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104766}, "main": "lib/index.js", "files": ["lib"], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.2.0", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "7.0.0-beta.43"}, "_hasShrinkwrap": false, "devDependencies": {"babylon": "7.0.0-beta.43", "@babel/helper-fixtures": "7.0.0-beta.43"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.0.0-beta.43_1522687686797_0.778636609205325", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.44": {"name": "@babel/generator", "version": "7.0.0-beta.44", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.0.0-beta.44", "maintainers": [{"name": "andarist", "email": "mate<PERSON>zburzyns<PERSON>@gmail.com"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "c7e67b9b5284afcf69b309b50d7d37f3e5033d42", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.0.0-beta.44.tgz", "fileCount": 21, "integrity": "sha512-5xVb7hlhjGcdkKpMXgicAVgx8syK5VJz193k0i/0sLP6DzE6lRrU1K3B/rFefgdo9LPGMAOOOAWW4jycj07ShQ==", "signatures": [{"sig": "MEQCIBVdZ/RaG8VNw7R6/UaC4W0ZsZMRLqC+4GibHRrQi7vZAiA4pj36V1w9jKQi6XpwmqBFrBaNnKkjabtFuJe1TI9dbw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 110677}, "main": "lib/index.js", "files": ["lib"], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.2.0", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "7.0.0-beta.44"}, "_hasShrinkwrap": false, "devDependencies": {"babylon": "7.0.0-beta.44", "@babel/helper-fixtures": "7.0.0-beta.44"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.0.0-beta.44_1522707589079_0.5922532035430448", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.45": {"name": "@babel/generator", "version": "7.0.0-beta.45", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.0.0-beta.45", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "a4008808303f80e8f46c1bca2fd44f557ce246d1", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.0.0-beta.45.tgz", "fileCount": 21, "integrity": "sha512-/XXnZFGSf1fJtFxZrTGKjxYcRNHWoO6880xuWRPj3mgy8PpT6SIglTJ41aeP6IWdNHBSVl9SOos/hcVSlyAzHQ==", "signatures": [{"sig": "MEYCIQDyIYRmLenhUARgh3nr7Mzii6wfORto3chbhBkAv3kVrwIhALe/natZnaOsJq0NsHNoHNeo527NQdKd7fJ834G7Fni8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111311, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T0hCRA9TVsSAnZWagAA5qkP+wTiTXoo31leuGzsxAiZ\nVGDfN71skQsQjoiNyX3mzdX89xcuqP5UQVP0Ridhpow+Z4qyJV4nA3rnlpw2\nBVzXuzCWOL0clwWiVz+S+4WFiLZKRqcietf6gZh1Boc8Vzc0To6O/NAE73e4\nm3sbqnXzmotVN8i9s53srhebjaCU39IvGf+dMQzPVVTBZCm4Ym+c3Qq/NSTM\n9YPHaNXSs4ElwsGF5DO4r8shQAEZfciPu9Tdk6ddR0Jzl+KCE62OfliWIapO\ngmpCDS9MooBZqDIhhAFtjTvN8HC6V+UUNvRUaCVVfKZJ/6n/h/HofZw7IHsO\nooJJQ54GXWFsPaCK6VCFx8VcdVnwkYU5UU/9TdJD6uQQviqegj0GD7y4Dcqg\nmsmF0yEzo6+bIpCR94QoB+tCd699kF15irJ/c+M7sdRUvEL8dkOSrky4pB+R\nKiHyASwemJs/helg/PaL7AOmEhGpvgZmjAeB5lb/29C159W6IdRR+sUPbdZV\na8m9fMybCJcHlCN1RuqrhVzVRxAzLkNbnod/v+t7zgtKdOuND2GElzE2VoiN\ngPgvPF5FyUbZyVqmPluCP0AFAggnjPnAxrMBEr3ognMq3kKDKaznQZEYadiC\nqpOeeUIfk8LFiBC+Tqsxct56NK/aEMMpy4hig1bhPQS1RJQoviZdQSGGDjNJ\nyc1S\r\n=pyr4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "files": ["lib"], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.2.0", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "7.0.0-beta.45"}, "_hasShrinkwrap": false, "devDependencies": {"babylon": "7.0.0-beta.45", "@babel/helper-fixtures": "7.0.0-beta.45"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.0.0-beta.45_1524448542869_0.8888351862944004", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.46": {"name": "@babel/generator", "version": "7.0.0-beta.46", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.0.0-beta.46", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "6f57159bcc28bf8c3ed6b549789355cebfa3faa7", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.0.0-beta.46.tgz", "fileCount": 21, "integrity": "sha512-5VfaEVkPG0gpNSTcf70jvV+MjbMoNn4g2iluwM7MhciedkolEtmG7PcdoUj5W1EmMfngz5cF65V7UMZXJO6y8Q==", "signatures": [{"sig": "MEQCIHMSbhoMOhh0nL9pFNiFGJ6gVfpVMhLFsYzVwKCvT5/NAiAn8YYdKYBbEuEPS1l/AzD5DocN19aa1QXfRNWtCPnDoA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111311, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WFQCRA9TVsSAnZWagAAhLwP/i8u0EGOc87VxDvxMNRS\nRshwtTgX8kG1Knrc3EGbOkFYqYO68YaFCwTxHBAalKzSKVgEHP9qVOOxUhS+\nwoC8txSwPNqw0cV2DYc6KQyEBYPDWPd8MD1JOKUaV4r3x9S0t2UYFEMxm0GQ\nRxrFO3CNVT3RV6Ko2/1GcsJ20Ld0HL2DPzDi0aZpAMpiTwMk3IDH6AbrFid5\nvtEgbSWhNw0ifo/thBz0oIZGJLq0+Voj0yUQUPNKkY9bzUknr1O36X8JgaQN\nCIU6Qoqlh9tTcAUmWOsC/9/C1gy6DCz1UNhl8Y9Q6jLuiqAzxRoi0pLYij4F\nmA3ckaKyP+ZHJDLDV/xDg0QOoZGp2NNJebvXPgO7/DIAzhORfJmUOkLYb7ae\neic/XeSiRSDNVHBlZ9uChL/Ow3sfKoBzdp6trTpf3VYSEaeU4VJb3OP36DpU\nWcfXnj7u+3hOpK03AAA3RuDag2q9fMDKTvvDRi2LZqrW3zLtOy7rAvwexi81\nv/dzOJcOLEZdjgh2bqe3rXY7NWqxvCLivKwFzZU7GJfQXN2A0RDB/hD61nYj\nFcmP4+UjyptOLZHYNpJJCBURBdvQWI9Xsxi31JGxIzMx2ZFechju2lKmG1cb\n68cARuH8NUCzS1YBwNBd0p/sWyRFX4qJ6rv+5ogHdx4QGda7fALSbjQPKEO+\nOC3q\r\n=PW0B\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "files": ["lib"], "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.2.0", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "7.0.0-beta.46"}, "_hasShrinkwrap": false, "devDependencies": {"babylon": "7.0.0-beta.46", "@babel/helper-fixtures": "7.0.0-beta.46"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.0.0-beta.46_1524457808362_0.4803428707099102", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.47": {"name": "@babel/generator", "version": "7.0.0-beta.47", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.0.0-beta.47", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "1835709f377cc4d2a4affee6d9258a10bbf3b9d1", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.0.0-beta.47.tgz", "fileCount": 21, "integrity": "sha512-fJP+9X+gqgTTZzTqrKJHwebPwt6S/e/4YuyRyKyWHAIirGgUwjRoZgbFci24wwGYMJW7nlkCSwWG7QvCVsG0eg==", "signatures": [{"sig": "MEYCIQDaxu5M/pF5InPmVcVof/jE2DSKNWkIasRapnc3oz6DoAIhANNPqJKXDCp7ZhFSv4aQDf27Nyo5lw9FTFJPMEAJFSLk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107628, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iTICRA9TVsSAnZWagAARFoP/RNEQIuMg/d/Bt7wcr6V\njuUfibj7ZHRh7qEHfqBBkAHr0hVq0X//NhBAfzowN4R553+SX/IdWkleu9Kd\nacA1KjSDzUp6PGOSwSH1YH4fqAb/aookK7VBpuTY5kOMGilVt4USz3lIXV0k\nKOtfajGsHEHAtQfu0it2GXmi6iTQbsEgHFnNRNGkV+SrQLjrb9TObPWubuPH\nd/W4GKSnsD9jY/x9Q0A3kBnv3xfdWcfL13rKBAgVOLvyugmAogqkztX+FOfI\n/1dL3xW124YsZ5jFq6tLOCwCj/FT+m0+V2zFSd2wAAfMGz9wIGJX9n1ul3aa\nljV6VccsBxPLbt715zR3OOpFJLp9Isr0PDS1Fl8dHVVQDE15hi4xc1DUrW6I\n/afZ1feWFu6qgy69U7Eiode80T7CclAdPBJb41140S1lf9zJ0Y/QCI8tfJpw\nge+5XEC+Zetq/K2A5VV9Djj9Z4CH4d8ZW/G8L7z9gBDhNg+7tzvAue1B85ft\nEMu89etKGyp6wi0jhqpm7pvuWzV+NeVR8FRvqkp93KpdhVBMTKRcw5ayKwqC\nlWLWhhVvm/S4qeebra/9tRtElvpMPd23vJeDBl/23rzi29iEt//Lh6zFkCG9\nTM+f4AG6Ady0rCruTq4YTfdKJr5ydhbjFSCAhDeOT7jcvYGE08WZSga8Oeq1\nQJTX\r\n=CeY+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "files": ["lib"], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.5", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "7.0.0-beta.47"}, "_hasShrinkwrap": false, "devDependencies": {"babylon": "7.0.0-beta.47", "@babel/helper-fixtures": "7.0.0-beta.47"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.0.0-beta.47_1526342854452_0.24764683815435862", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.48": {"name": "@babel/generator", "version": "7.0.0-beta.48", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.0.0-beta.48", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "53d1c359f66a25b8c1e82bc6642fe0a62d22d1ce", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.0.0-beta.48.tgz", "fileCount": 21, "integrity": "sha512-RPS4dHg3wVmivvMIHW9N/ZHPZ5vc7runskURDqz3xamcFlUXSVxoziaS9VOJeivKXGzFPqr/wY03u9wDc+4A1g==", "signatures": [{"sig": "MEQCICkGu/R3IfYBWKxoekmq3622GZ9LSWsZGusN8MOMBf8aAiB63A0iBH2exwL5R8c4NKK/W1gPlqKBzMkZ7cBG+BA+DA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107048, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxCXCRA9TVsSAnZWagAAS5QP/3Cu3Pj/b7Ann9RBhVzf\nrx1Yr1zsO4sjqZhi1vdALCDspF9P5woMMfBMYwY6Kl8Rlk9iLwTlo84giG9+\nXmKDp8lCtgkTjWbKAlgZwZ3u8OqoSs8aHbGjn40EA/CbKwawzziWA/80lQYP\nkFwwCB6XAKPFkwdcR6ERsBzZo3riDvomwfhE4ix4CiBs+4QQ2Cj3lE7XbYGK\nivLy8SKkDneb0p6r+c5ZcF7ftO+1thxGp2Vu+Ae++5887Z13vkQQH53rSvfs\nVo/L/auZpFearJ6kB9KE3yaZtp6bJip4QfyWDpVAqZBTMcJHlptBsoiA+Mcq\nWFiBvsKxtQB2nMicmb9OQg+FMS6PoeeHd3zAAw3zJm8SdGqHM0Ov56pvzLRj\n4/TeTHe40XksWlR+ZgI2iWCSMSSBcz9K5AZA9sjva6piq/s+SI+tOWyEZ1/B\nfqgs7Ra5YZV1WF2D9DOf/PCLwFI0hL7yp0vPsOzMXehXv4wI6pBeL34cbcEe\nDIPce6tmILWxe9RQgh9M51sn3yqr/r3DQF7nQU/0xuvGb6WywYzFmbdEx3D6\nUISfY5TwQhV1MCx3aXTB5yxEdfFTRI1eRMpEIcDmKsEWa41X5Qs3bUDqD/pB\n2FD5HWUuCfIMS5ZeaaP4lPNTOHmKfpkakTCpR/jSdzVkxvAzKjYXiee1kyMz\nGWAq\r\n=JSaH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "files": ["lib"], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "5.6.0", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.5", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "7.0.0-beta.48"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.0.0-beta.48", "@babel/helper-fixtures": "7.0.0-beta.48"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.0.0-beta.48_1527189654612_0.01624314860981646", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.49": {"name": "@babel/generator", "version": "7.0.0-beta.49", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.0.0-beta.49", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "e9cffda913996accec793bbc25ab91bc19d0bf7a", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.0.0-beta.49.tgz", "fileCount": 21, "integrity": "sha512-MNPBltWd881VT2tUUU0MbpwW4d38gF8jSv+yvqMJOe+32dmu3BTuSPNujBZ+3F1w9uHtcziqqZrcf6YGETw/hA==", "signatures": [{"sig": "MEYCIQCDo0uCmb9bj/FnTshDr+ilWTm1//Mzr8T9bOdxeNCIywIhAIm+nvjMdL7C8pvFGT+5chw7xaIGkNIG9psH4VQTfu3T", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107048, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDMmCRA9TVsSAnZWagAAmAkP/1EYSoaF5nRcd7P5u3x5\nQ/LK0XmbUazfmyezS5dNIGeomqSTXju7qWnYb+XCRGEemZqsBqzeTkzirV2V\n+RhewRMbQaRd4tk1rq7QM6+Yptdly6BkfK0nP7XPU+L1c8Wu0hT/mBKPqrES\nwa0JvH+Cixzzb5Am1Mbap8XjKfIM3DLj+nsbEsbZbQDf0O5fgqieOdOHApjR\nECi1mN4ukisMa27raOFSVVz9hLeeaBVKAQQ1a2SOk5tfOgafDySQVRTkpxbC\nygv4gdtdz08HYtMLbOJ39xEWe6NEajDc9lOkbRv8Fex4P6bTMrMNc1HYkFOK\n3ndAPSLy9e3xl/H9uWjyGmezTt7HNsfWO6+jnPck50gu6wDJxx74kZpaUwmU\nhYJiJBfqF1auqrGEaJEJ4MIeUCYzi70Y5Jl+ykTuREBoLSOd1Es16WXkr9ck\n8wqvJQGfwLKl+kXTcpc+NqvkqBRbaUsJfKIwpvixpqRRDg8mJKECtEl5Twvs\noh1LsJMeSifCfIbIbZ8RuiUFvaGYDSaJ0tIB78g7MwW8mXCS3cgwI3nxumAE\nFsPW11JnnrDG2wPviszn3mnBqqPjJ1PcI2xYjFuNXko3PK8fvaypIEINuy8M\nIerVF6rw0fY2G1xAFws4YmlFh4xexPB+uvYtslUqqy7B9/DU/5iXqpfc7VtG\nTbyA\r\n=zjsE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "e9cffda913996accec793bbc25ab91bc19d0bf7a", "scripts": {}, "_npmUser": {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "3.10.10", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "6.12.3", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.5", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "7.0.0-beta.49"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.0.0-beta.49", "@babel/helper-fixtures": "7.0.0-beta.49"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.0.0-beta.49_1527264037194_0.9901878290925281", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.50": {"name": "@babel/generator", "version": "7.0.0-beta.50", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.0.0-beta.50", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "0c5cda176aeb0beb41ff270e9741aa60c4fdba25", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.0.0-beta.50.tgz", "fileCount": 25, "integrity": "sha512-d34cW2uX9t4D0QAM5RVO5HFIrvrXTQ9OzapwfAg0oin4dirOZ10Shq8wRSdB3Efic0YWlLn4hLJyWD4/+n3wGw==", "signatures": [{"sig": "MEUCIQCzHyDBcr8u1b1c41LMtlBaV4VTqBpgx7bnbR/1AuHrLAIgUeUA/5GamIM7Cl6WJTntpeu0rLKoQTRNGxQUcd5eDlw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104116}, "main": "lib/index.js", "files": ["lib"], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.5", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "7.0.0-beta.50"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.0.0-beta.50", "@babel/helper-fixtures": "7.0.0-beta.50"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.0.0-beta.50_1528832809623_0.06543150101596829", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.51": {"name": "@babel/generator", "version": "7.0.0-beta.51", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.0.0-beta.51", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "6c7575ffde761d07485e04baedc0392c6d9e30f6", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.0.0-beta.51.tgz", "fileCount": 25, "integrity": "sha512-bQ7sVUNMcz9WRQEajWT0VGllxDSx0tx0UGY8b3gByzgwzE5DHHRfJz1J49p9Aypx/kMCPchYSxinuinOVQADkQ==", "signatures": [{"sig": "MEQCIFeYpZhw8SwaxgUDrFi8Meo2vMlMXbgm30MlB6ypYcaAAiAvho7rw5Ass+dvg41diwVZm3InDcav/LcUt01pVRHaMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104116}, "main": "lib/index.js", "files": ["lib"], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.5", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "7.0.0-beta.51"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.0.0-beta.51", "@babel/helper-fixtures": "7.0.0-beta.51"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.0.0-beta.51_1528838355980_0.861397094776162", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.52": {"name": "@babel/generator", "version": "7.0.0-beta.52", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.0.0-beta.52", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "26968f12fad818cd974c849b286b437e1e8ccd91", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.0.0-beta.52.tgz", "fileCount": 25, "integrity": "sha512-NfaayecL+hD34UseoxgrSOUs0rV0rmgBpNgjp1yGaAx6lzI6oT5FvrLMM4qaMww0j5nR96HhWnFXFce7PTylUQ==", "signatures": [{"sig": "MEUCIHRjsdmKb/m2wTu9C16k5tiIPblJ+qVgxsOYPGpbBoOkAiEApum9kGUmp3+LGpyfkjrIdsdiRRW9ktoTUXPkev9tcbU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104115}, "main": "lib/index.js", "files": ["lib"], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.5", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "7.0.0-beta.52"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.0.0-beta.52", "@babel/helper-fixtures": "7.0.0-beta.52"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.0.0-beta.52_1530838752224_0.4148148998920935", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.53": {"name": "@babel/generator", "version": "7.0.0-beta.53", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.0.0-beta.53", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "b8cad72c572be3234affde22be6dacc4250e034b", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.0.0-beta.53.tgz", "fileCount": 25, "integrity": "sha512-XnfdZ6oFVC4cE4+7jbEa1MLFSXrGY/SfSE6onUyyPSrRbjYs9sdrYKi/JgKGSJX65A8GFswHwWcBPCynfVEr5g==", "signatures": [{"sig": "MEUCICOfbS2DnNtHxcnA0T/JvzOMzmtuZCqJKv85ZFq3tMy/AiEAqvs337we55En6BSVJs4B1k0GV4O9mXGoc/MYLb0pzII=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104156}, "main": "lib/index.js", "files": ["lib"], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.5", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "7.0.0-beta.53"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.0.0-beta.53", "@babel/helper-fixtures": "7.0.0-beta.53"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.0.0-beta.53_1531316401675_0.08928619579570873", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.54": {"name": "@babel/generator", "version": "7.0.0-beta.54", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.0.0-beta.54", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "c043c7eebeebfd7e665d95c281a4aafc83d4e1c9", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.0.0-beta.54.tgz", "fileCount": 25, "integrity": "sha512-gGL7yj60AaebeO350HGiPu4XO2mtt9+n7y5/EjqDmka2ekLKRtIiqKtfqrs3r+0z/1rygjz/JmVsj6QmHX2xhA==", "signatures": [{"sig": "MEYCIQD/a93cDpPiAE/kfSRtKFKzGsDTFcllWdVAxfABfbhGoAIhAMzbcJpwlzHWGLwfj4y1Bdi7DveOzAChwL9MMur6s/pH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 104156}, "main": "lib/index.js", "files": ["lib"], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.5", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "7.0.0-beta.54"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.0.0-beta.54", "@babel/helper-fixtures": "7.0.0-beta.54"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.0.0-beta.54_1531763991878_0.698656668452778", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.55": {"name": "@babel/generator", "version": "7.0.0-beta.55", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.0.0-beta.55", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "8ec11152dcc398bae35dd181122704415c383a01", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.0.0-beta.55.tgz", "fileCount": 25, "integrity": "sha512-tUg1YiUTzx3g9gxpcax3oS9jSUx1luOtGBhmJhCcG/bcAuo2xtTVuJ5W23hAPbeBZMuWkWVXbpRwGRm4u7/vdA==", "signatures": [{"sig": "MEUCIBye6NfQqzYRaNdzbdL8NeJTGe9IbfX2YLIOOjecR+WTAiEAwpDawZ7/IRtIfW2gnD0yB16tTPRcK54oi9QkVrAfXpY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105775}, "main": "lib/index.js", "files": ["lib"], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.10", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "7.0.0-beta.55"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.0.0-beta.55", "@babel/helper-fixtures": "7.0.0-beta.55"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.0.0-beta.55_1532815615833_0.1486913975511639", "host": "s3://npm-registry-packages"}}, "7.0.0-beta.56": {"name": "@babel/generator", "version": "7.0.0-beta.56", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.0.0-beta.56", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "07d9c2f45990c453130e080eddcd252a9cbd8d66", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.0.0-beta.56.tgz", "fileCount": 25, "integrity": "sha512-d+Ls/Vr5OU5FBDYQToXSqAluI3r2UaSoNZ41zD3sxdoVoaT8K5Bdh4So4eG4o//INGM7actValXGfb+5J1+r8w==", "signatures": [{"sig": "MEUCIHUEjgY7sF2bqb6AlLOkC4j90qmzz7387uynwrcp2iioAiEAq6oKw19LT2seY/1QGpPWdEIVZe+1EleCiioYAZfH7N8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105816, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPtjCRA9TVsSAnZWagAAvrYP/AjxDBh6HWmt9p9K19Wh\nRUzq0bkXAXUUYxVuNExujj58R8+L+iapL33+hbwe/0n9P7HN9PU+OG+LGZ9w\nBF5lGGwC7MxAgqx+DylEVln1vKkbfuLumBGEqZDcwYe7S78yoM/s+zBAQw3m\nX/0mlepMIhAqZI1En65P9Ko/G3iq/qXp0TdDDsnZDNOMbpXzxMfRMpWcp0ai\nty7xu6L2QdAU0eDPGEUXnw7CZypZ1i+8/khuzUVQwWo4YI+28tl/VfwL33cD\ni3dEve8ae5AVI2F1VDwKorpDi58IOnTZZCRBfeTsMo4chvhKNtrrMq1XZva7\n9OaM+iy1XN7SWQj1VlbNsbkpcet2n1SF74+ptwI3gKmeHGdeQgsYKX4Mwo9A\nez8GnpbVngB3K6AtBa3VuJl53US3ORC+10giULFitB0Xf15S5TywyxD1TIqS\ne/eebB0jviogb7vWcTzg6kE2Qeyatu8IHOzZYi0pqxRb0FmZzzqV7jx80uHo\npgzXsLjVuhOlfIki7SdgY5UVrMusH+F8Owf9OrCBSyQKOyAud1SmOUWRMXI9\nxKWkavRqF4rfRp3/48c2Wm4DzRCZNWjUJ7L+UcyTmlesHo0oGjG7MEgsHKq6\nVkoC4+h7Av1+IkRHvSXu1bUtW2cCS+B9E+bXBg3w2/bQnwzjt3lQK/44KBCZ\nUE1E\r\n=FudI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "files": ["lib"], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.10", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "7.0.0-beta.56"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.0.0-beta.56", "@babel/helper-fixtures": "7.0.0-beta.56"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.0.0-beta.56_1533344611140_0.19306091735857955", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.0": {"name": "@babel/generator", "version": "7.0.0-rc.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.0.0-rc.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "79c3a248ae3a1ae2ea959ed59496b75d68fcf07d", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.0.0-rc.0.tgz", "fileCount": 25, "integrity": "sha512-sV1q//NXax7OxIJSnD8TR0yT9/IIsyHQ5OrKSyhS2S4IgaKm/tw80JduIgiN0I+Ggo4uNdQQjJpeXj/Q3TzriQ==", "signatures": [{"sig": "MEUCIF+dCnHpfYsdOf9r7EJQGurY+5hOnH7BBCyO+DI5PjPjAiEAyWt7BKtR4Dn8ZHp8qLNQKLrzOvk6L9KXuf9NXGhBQKU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105804, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGRBCRA9TVsSAnZWagAAtqIP/2di3035X5mbVsqHeO5v\nkEER1e2vjJAvO4E3nVeClVJ9ycJbiaQJxed+Ni8gkMCwFOzKbCXkHwDRt/+M\n3GV5VIzSLaEhYZ6g8KOoeLv8L1Gd0UGMaw0HhSafe33h+fG5V8E/YmtFMmr6\n44cQAmr7l0mjbmvoMl/GLcYqM+tiZtvz58Jpc6I5o7MyW8RRW3wEd2ChvSC4\nxkkawkubMByunSF1+ajHYdI2PlORHdo5KiaPus2FdX0hMYRxwajg7R9heiXo\nhTBARlqv42odGRGZgCUkLJoZt7WOAIh5irAtz8hzNlZY7x4yh0HrJdN/l4ac\nf+obeTMYELw2S1zUbRLHRBS7gXU+MBmtkNzshBOSvgvoBDF8u2qVZOFLtsl3\nE6hW+9M1vKeUoMZJKSqPAeINHQ5RL8XOxguxPqV2Pr9dW3jdRDq2lmCO/a5l\n3vIDUKhJkKEqhMXgvsuHrzN1YAm7Us/4oGbvA1E0x/rhKiMyViLwXDvqF50S\nqG6tra4wz2QSaxTjR6DqpK1j0bAgawO64mRhd9GG0kRCeycGyYViyfWN8qXy\nrlNTmkqj1p2IY2RV80jKZ5mvA2FpO2tN+cJ1QQuQX/p0kEpEvK0G3kwQlUC9\nZaUem8leCXfVIklqd83b1GdOqqpupTR14fl3O0XmklMI/FR0Jqr+zl55H+k6\nFUsQ\r\n=uL2F\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "files": ["lib"], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.10", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "7.0.0-rc.0"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.0.0-rc.0", "@babel/helper-fixtures": "7.0.0-rc.0"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.0.0-rc.0_1533830209093_0.3099786279688308", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.1": {"name": "@babel/generator", "version": "7.0.0-rc.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.0.0-rc.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "739c87d70b31aeed802bd6bc9fd51480065c45e8", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.0.0-rc.1.tgz", "fileCount": 25, "integrity": "sha512-Ak4n780/coo+L9GZUS7V/IGJilP11t4UoWl0J9cG3jso4KkDGQcqdx4Y6gJAiXng+sDfvzUmvWfM1hZwH82J0A==", "signatures": [{"sig": "MEUCIQC8OS4g0LxckaturFe1DPPEOYBKbGbX5F4e2zdWwihx7gIgLkaEt0fzofnRyHbraVxIGDpviaI6OKTJmLs8vSKs37s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105804, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ7QCRA9TVsSAnZWagAA2VoP/2tEehq7SGhbgtbjqDKV\noo1Jx2ikBUiZPQqE5/U1j4yyxden0rzg5u/NLne1L8p0fvwKroU2+Zr6dLoe\nNOL4VNF4SV/kZshpQrS8K4O8NPdOfOAXWdIQQekNJu8DYddPg9iZc7sE7Mi7\nmXFpQa9bMMVu2B3DKRYaWeS8/SGJzJYmSVbCiQvURzt+t2JOQb9UKnQYpaMn\njnlnd76ZUD2EL4Z6gxHxvXOG4Xr3gi8DGDMOqNjxA/3YHuvujR1Wpip68kju\n4tHHDja7SLLH7PxGA6PYpLm0kzSRZ0emM3BNOkXo1+9wL2rVHgi442gm30gS\naN5vwohoIpk5SubBqzvH7nPHRMEqifKTN7uoShk+ZgnKsdjdpSrr+6O35QAi\n8w4L+cH4wqOYZyFDR9FNcSRwT7vgTYW8UBRrjx/HfRw3FFZrTbIjes3BeCws\n/f7vKwE0Gbeu9sn24ynWJrc1jUde1b35Npa2NiNSZEA4oKmIHOa0iuLmc/iR\nSkE3kRvJpwXKB6UlP89hWCMKJoQ8Gzl34lZx9atGY0/a7TbdLS2+Qkf41tqI\nXFwSZZFtNgNqVUUm98lPEofBdQRQPg99UhkEx/8J6WnH1qSVvbW0VIpkF2i4\nryoW9xFfkbyzLlwloDVzxQDTqJneSG9n29soRv+rLc1HtNu35So6VPge9BIf\nihPd\r\n=hyyg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "files": ["lib"], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.10", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "7.0.0-rc.1"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.0.0-rc.1", "@babel/helper-fixtures": "7.0.0-rc.1"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.0.0-rc.1_1533845199683_0.26418838104773656", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.2": {"name": "@babel/generator", "version": "7.0.0-rc.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.0.0-rc.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "7aed8fb4ef1bdcc168225096b5b431744ba76bf8", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.0.0-rc.2.tgz", "fileCount": 25, "integrity": "sha512-kD6hlprDaBy17V8qd9uXJbYC5ZYyCggieT+tiGzCwayA7oyT5ynPec3MNkWQHkLyhB7IP2n3c/Ep329jOPQY/g==", "signatures": [{"sig": "MEUCIHM5dhvFuOMl8U5E1ADYLGLg+20wKEGyTdX4pCKKDh7lAiEA4ugfL9AH6PliWuSb282TJPmVB54qS1MvSADCM1h09JU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 105804, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGaDCRA9TVsSAnZWagAAZ28P/0fxRASkOTw60JZTzAH8\nwroiQA60cA0RIH1mdlMZk4cmNQEtaEtivg7C663QHqvqeRRifaDs9+pKHZVS\naD+rZJcLDTnhPBZq/0MYRlWIjN9RaJZVB+EYEk/o7acXZWPq2MwiwCFGmcNi\nchZJt5oTjNm8pwUGcdWgoR3GYudw7YSlCH0D/0lftgTuvDkxpaM3NllH/4wn\nrdPvsBSkEz5UbWsonNAK0ifNyzKx7gzuU4LEAGrSuCPg7SmgB6wkGtxMkYQ2\nc74W2P2YRoq0ADt4wewA91Iw+EegDLU04w0nhlhEhHEBWDMyzmhBaBmpnHPR\ngsREhbEodmnVbWmJsKvieKmf7M7XbCBaQAhWV1uVsLCByYgmgZT3rX0bFwXB\nqC3xWxSHeyt3XVcqdglulx+5/DLVHpE2gfTvCzNWuEeqSx2xYa16ajV+y7Bi\nJekycKYVrHZmwDWyy03bZjl2UDBor/h3H/2vpSiXDcFzHr4g36aY4h5KvqBn\njHU573wa1ryM3//RJh+anGLr+zPvDA+cqJ6tS2Z9o1cN1k8At8BysE/1b/cB\nzpm6sx62VUdzMaEkEkqSSV5X6OE9wrw7kgwmXRlOPcc/vTOd6qGDkvac13MP\nkFDDs7af39f4rMmVh9t2qn/alGc6T9J3VlEuhwVSwppe2B/h6bqK0C2+SsgU\nF9I6\r\n=hNAM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "files": ["lib"], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.10", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "7.0.0-rc.2"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.0.0-rc.2", "@babel/helper-fixtures": "7.0.0-rc.2"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.0.0-rc.2_1534879363137_0.6858596988258816", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.3": {"name": "@babel/generator", "version": "7.0.0-rc.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.0.0-rc.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "3267085de2d9b8779bde79052ee5f7070d99a5ab", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.0.0-rc.3.tgz", "fileCount": 26, "integrity": "sha512-vTpLY/3fV5ML2DxvrvU1/n6cV9+yEj9Do9zrb8UopKSa5WVyjo4tzH0Gp9Ka7zThn5kp4XB3DH385eHOVbwN9g==", "signatures": [{"sig": "MEYCIQD1pCo7BlF1eV7LefxFG+XhBLxQTAK3hz3/D4YvInmv8gIhAO3zCpuruyKqi+dNGF/1M9pMfvGZabtk7L0thGCLOtJ7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106903, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEkvCRA9TVsSAnZWagAAy/EP/jlFFoap61yAmBzfnzSh\nDtu5VBkVp9mO1EjbK/GAmFuO9aGDX99eMLsoGB8xWoRt3wJ72N1EqB5Sxn1n\nVLjr4X0UZjoHlIxYJxS3DToeR9TYs3+sJFah14GU9YXntQte9wrP9nY7/W7H\nNYCjYuWY4lcJ85MPuiBX3RNUR4hvs2sIEm3LajvzHP42FeDfdwDLJ8ddwflh\nTUtuBsEzKzzG8Aj96ywJMG3cshPfkeikJ+9wtbSHzKeOy2A3ISPMXu05zg51\n/jq4SONeCNe4lPCq9UJqSKPZ58dniUYFKg71byrI5AqNzOUWtUa8JpVXUD3V\n8XQsgNod7Z3QLIPdKJLEsZ1YQeVfrmICV/e/JS1PUn2rBwKsuKdrhyM0YFzl\ntjQn+U0JoZrjNAwi6lOMp54MuISOINg5IwO/MOK6NCB8FBS4r2wifBJxbc9M\nkpGZq+sUXCLcotHm1sqTZUd/qm9S4om2rXBYR8KwAZMgH75RYtf3nKCUYHdz\n9wN7ANqM5IibgHkpKw8gmavDFdqe7FM18eSHAWNxbsV1GUVyxAObZigzTbm0\nmqXJ6QyaimKhG16yVszIQSMHS7qYFQoFqJj/Y3wYoP/gkaIKzR0ZePJBBjKL\n4kEU4Sy4NCV2sWhGBlhdcpnNKpnRAiNcoyHkt6oxI+ROZXr9lnjY1SmFxzHB\nZ6Qy\r\n=Fdgf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "files": ["lib"], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "description": "Turns an AST into code.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.10", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "7.0.0-rc.3"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.0.0-rc.3", "@babel/helper-fixtures": "7.0.0-rc.3"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.0.0-rc.3_1535133999213_0.07120459454114925", "host": "s3://npm-registry-packages"}}, "7.0.0-rc.4": {"name": "@babel/generator", "version": "7.0.0-rc.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.0.0-rc.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "8f9ee78db28ae3225f5c143266dd211262febc5f", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.0.0-rc.4.tgz", "fileCount": 26, "integrity": "sha512-xyfV9AVUVaacmJ2nuA+HzwxIE9fK9DKH7dVTKdf9IcXGwCVnaicbvqPXyJinJWIp3d9txHJaPU+jUosjRPtihg==", "signatures": [{"sig": "MEQCIASJvPKK6T3qWb6ce7UZ8wYW+1rC2XHNCXdu3mvmr/eYAiA6P1zUcj5GKSDDC4BWfb8jYrltZXJ7Eu5WAXAqJo0keQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106906, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCoKCRA9TVsSAnZWagAAzXkQAIbaj7EwzBof5mUw1/hm\nZ2Z/YunV4sFuK7H1yGTy6cybyH1mhG3gtV8H9q9CWFnJ1kk1f/GPNg6CqUUw\nNM+xzTuzyfDqGazmOX98BxscsNy/0Dm7g8fZW8nHQuCWXmd87u5yOuy4Z7kv\nBYL0U+VAJazQzekAqgQYyxLHITcIvzVxcG0cgmQZn7LWhHSmsTbJJoQSJMi9\nSubarboEaPfkv7uC7UliVlc22xmOrT1R6HYuL9BN3eEARhSNsCeJNSYB0m9u\nZ0H6bp5YYVFgIj4PUfOZpKuGAF3Oy5qj5WXaYI/8rXqnx9Gqox42fZ4+A3tV\nAv9qxUJMUD0HLpaqWbSonlF2AkFuN2ueMARftWHHry6mL5qp77bSNll40zY6\n23MebO0TDsIoHPxk6q/vxZZi6jEPl9my2oeUngJ0eb/HiHAy5u67bo4BBgyy\nmVz/eezRp7YlURpiCwWn3QYGKLGW7BXtGOcmLZIuNul6y5rebSqzTTi4+Mh0\nuH0KoLC2588ICtCvjhq+xF2KWN6FfjoXvTZicd8IJ65X5AL0AQN0c7pTR7qE\nfj0mSDZUmA4fbgi6iFdZTtsSeljt5teu2ws05AhKqVS4sh2/u7A8rMeycoAG\n291c6YqD0L6N2qsyY9wjrwnmY4KH5YgtMWnsGoOJIPjAG/XkZ7YeTAZ9wcK9\nOv6Z\r\n=n313\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "files": ["lib"], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "description": "Turns an AST into code.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.10", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "^7.0.0-rc.4"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.0.0-rc.4", "@babel/helper-fixtures": "^7.0.0-rc.4"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.0.0-rc.4_1535388169706_0.8862834208359984", "host": "s3://npm-registry-packages"}}, "7.0.0": {"name": "@babel/generator", "version": "7.0.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.0.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "xtuc", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "1efd58bffa951dc846449e58ce3a1d7f02d393aa", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.0.0.tgz", "fileCount": 26, "integrity": "sha512-/BM2vupkpbZXq22l1ALO7MqXJZH2k8bKVv8Y+pABFnzWdztDB/ZLveP5At21vLz5c2YtSE6p7j2FZEsqafMz5Q==", "signatures": [{"sig": "MEUCIQD3wmMakp+JMYCMdfUd6hYml5bmKH1F/EUPZ0te8BAsDAIgNpUvKAaDBTHtbeUKXm+3qDFkO7h7kCgHkJqdXMr4YsA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 106886, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHApCRA9TVsSAnZWagAAWPoQAKBDwM9sDvea9ffYv3xq\n0osE3r1yZVR8hLHFyAVyCYv/D6XK5sGwf8YC5ESjEyZlQxdmf0xmyuEFC6qH\n5cmykROszDII1rSfKwNv2tCcE/iKjpxcfTxqHA3dbjfXQ1HxQW8wkRkovAVQ\nl4/3xLVJdweM8Tre+gO/+LRGxALH/IUBpP4QI5QNU1swOWbUSjLUW1fyo6ZE\nKfPnC6Ekeg8+6DBTop6cLOM+xefFczUm3GHla4yw7i50vDal5xj91l9TKRyP\n8b0jru+A362mQUehnTv7wbQwkD4uoFIrK9kOBNLmqyS66e1Ez4c1XA0jadTz\nx3kdeSO8ULzF1vPWzCSyG10oBVjlH41VTNoEJ7yZie3dQZzCZLFIbNnUjCYC\nMDNASIwaRvIa2gauQUO+woyzOzLNiIAzfcNeV2dC1T8nWHPD3mbBebddMEZV\npF4ID4OelvKmhAlj0shRp8Q7f8Irq/0vVYvMwA3Pr7umthWJiIs3Td9d1C5S\nAiX0V72+C3RgrphtSL9/YIBAeLMFD+eMo8oQMbiF89pDnCD7/r53+B8av9Z9\nj5ogR7WdijfnTBLmE1fnxEBSm8yz1NRHCaya89sdLjBtdap7zfsTKHeS1/Br\np53rA0Sb8mUBkZAVI6TRjZKlx2S7j+DFqDeDlJsWB5/PowYfcHTykkZbJn9q\nttIz\r\n=jurB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "files": ["lib"], "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "description": "Turns an AST into code.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> <<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.10", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "^7.0.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"@babel/parser": "^7.0.0", "@babel/helper-fixtures": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.0.0_1535406120460_0.1487502983427058", "host": "s3://npm-registry-packages"}}, "7.1.1": {"name": "@babel/generator", "version": "7.1.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.1.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "4a5d2b7ab9b7c75a5aa56bf0750224f41e4eae41", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.1.1.tgz", "fileCount": 26, "integrity": "sha512-Lz4XtS67TiePhGdjf+mXW6HgvrZT06+cKArd0+o7JrDo9pEKzRUehLpSsArbIg7I83ZGmduWHVN/m/OqXTb0bw==", "signatures": [{"sig": "MEQCIDDV6fS+he4ogWVlTUZoHw0tmvtkQdVLfDJVglDvi7ywAiAbxNUalBrQTZprT/VwOcIy1t49anj/NFHpog2Z46qeWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107299, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbroj3CRA9TVsSAnZWagAAtykQAJHLdwUqEpBia/c+hXOh\nxb6ZahEtGK7bBJI5WO39foYgXWZk847+KnHQEqmpi+4SD7VxTrvMViI5CHxn\ntjllYShyncnQNKbFK6C8Cvey16zhTLUYY95mMFH+F6LxVL8+6/y9Tw+otwT1\nXDmIXoyM2O5OVNDJmbncQcWlhln4lR4HgNM3vZprhzU0QhxzwUmDO36fam/U\ncgPU5Go5zvRUFrBoeni749UfpFTiDp0HQF/HNI3HWllDy4+I9DNqT4LAVbxf\nu7jkjdcXbJsjt06nlVA2h+J/lEMFt+OZ4iuyaFpWAZEAzOLdMlQkmCexhzKK\n+xQepaxGM+yNdAnPsEXJ4Wulrn/MJmvPcPgI3EK/raHDmn7TmCOOQdPp3BTq\nYkoB8GY+J5DXa6DYPBVHjjd/QDuNOJwTuitOWVoB4KgpuBVr4Gs/ppzIEgP7\nBKLsoYny4rS7lAoHn2P+hVUZRG7wetR8sfys7tR0Q8ggS7BCONhNL6CGMq+w\n3rZaQK7vfXgdbj3+Oqgv/DgX5caqPBWCdVynFDzwk1eVSOV2LvNo7BCuu1Mq\nv8Bcj06nai+F2sMbUmr2lPjGkH7DhiebDoioIOh7h5fIALjXfw25dzSpF2WW\nHxGPzRC3CewaWpi7hAHZCBe0oT+okioQIgO2JgPvdBCsDQRcoRMM4shxmrXh\nsijX\r\n=9+rH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "description": "Turns an AST into code.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.10", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "^7.1.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.0.0", "@babel/helper-fixtures": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.1.1_1538164982595_0.3029699561135104", "host": "s3://npm-registry-packages"}}, "7.1.2": {"name": "@babel/generator", "version": "7.1.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.1.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "fde75c072575ce7abbd97322e8fef5bae67e4630", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.1.2.tgz", "fileCount": 26, "integrity": "sha512-70A9HWLS/1RHk3Ck8tNHKxOoKQuSKocYgwDN85Pyl/RBduss6AKxUR7RIZ/lzduQMSYfWEM4DDBu6A+XGbkFig==", "signatures": [{"sig": "MEUCIQCvXWP6PJKa5UednwFCQmD/hfBXnL6yE+I7qjN8p2ARZgIganxbnHZn6vh1jx0rMyEXMR4gB0LbAuhD1JNXxqq4bfo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107299, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbrqkOCRA9TVsSAnZWagAATrAP/2VuuXZE3IF+MLa/oroz\n1oDzvb4W3nQxThZBgEU8MfkLxx4IDMCml2llEe2/iJmyrlDyLXp8wY9/ulq5\nEorDVnQ0qaSH3FXDfrs48XZDjx3P36DCnWk4FX9e3FjghIAr3n1cd0XrmknM\nFxWwYM58gwjV/IX9Qg5VXXEx/M/8rfp3/kl5IEhsB9DLGjX22upWz0L84UzT\nNqGzGh+7JOCfpBKrouem4+C8rBWJsbs3vnYwB4i7qZCwFGpfv2XtrFG2q7GE\nd2xS0wSIPW2b0pM6pEj/3aI8dxcEN0PBeEL5oPzRH6h/wnOW6EN5w8BAzWvN\nzkf2m3f2U/F7N2H2K59Rnj/tmd40ixsn66QZqPpdqMFM/o4TlqYpOnL97kRW\nkBNcCPm44WZazaXtpsogH7oXcFdUfvpifUv8x2TikgD2UfibwlSPDr6ousIL\nJkoSHAWwCtKem7yR98zCSq0edaIfRbwJjMo1t3eUr/jgDkWZ47/DsiQX0vuq\n58rgw/kpWNxFY7k7jpR6cAko6g4k8r6gM8lWVzi0YSyrZWbN1oNr3y0e0FQx\nJ1b/4+YJ23F6PrBjglofNhtWPVNi5wd+opTIW7VRlo6SzQF7yhjWXkjJqIs3\nGcC40IEkcCrjUOKAp8LUz5vdHwM5LfLx81YhaETufszpIVTNI84W9zkh2DZJ\n5Kqc\r\n=sLwG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "description": "Turns an AST into code.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.10", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "^7.1.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.0.0", "@babel/helper-fixtures": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.1.2_1538173197736_0.0831364457311039", "host": "s3://npm-registry-packages"}}, "7.1.3": {"name": "@babel/generator", "version": "7.1.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.1.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "2103ec9c42d9bdad9190a6ad5ff2d456fd7b8673", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.1.3.tgz", "fileCount": 26, "integrity": "sha512-ZoCZGcfIJFJuZBqxcY9OjC1KW2lWK64qrX1o4UYL3yshVhwKFYgzpWZ0vvtGMNJdTlvkw0W+HR1VnYN8q3QPFQ==", "signatures": [{"sig": "MEUCIHzqAswJ1+tGtAo+OZHndI6pPssGch0rbRKbJgBKVn3RAiEA3dyPNvEctiLvj+Al8ORccnOVdf50cI0aX/Nl+MGwswE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107561, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbv3HCCRA9TVsSAnZWagAAwPEP/RazeNDHY1ZY6X8LC4or\nkvPkb95ZGW1MkEe2qMLgMcAgiScUuPeFqORtTIxQiP9RgNp69XIuXqx/5dT8\nIiRBMok1HqwfHQuCZxp+NETx2ApQp+tqMkpVrmaj5rWmHM08a/yFiROW3WkY\n0LUGBNQqE70SDSEvHf18qQJxY5I9kktMDIdtznJy2N8TsQCQBjA7GF7EQPl9\nSAQuBYUkpkQOwy4N/D2EeVDPHk7l8dXlSFa7Cn4VqjkLI+FbJAY2Mp3x5WE+\nwO/RBTTz+kihi++vDzSyRflSP0/5b37aealwW5RUPyVtRGALs+1pxs2lzbRD\njV18/tWHoUeGOLOsBet10Q5RljnegIGNMk0eiIVfMUXbzqNOhPpLyyJRLotY\nh2x6H2xPQgjSidh1V8WpDTkFXCMa73nN0AjKozdhzJNHclUHRhRYMCd0Lch3\nTmfXCYJEMYY/h4XZUlwFZMzyWlXNXfXX+lVXw02vXaUZn4OApb1KwmS3yiil\nGDMCM3OpgD+l87f8MWBDXeYiyhUWFN0bPHDiK33wrYnvL6HmReZn4uVkGEIr\ncLRAqctGNRzQwxDBKKxi7oVMe0EVtEFkfyjSU80nnzjukEZ1VarY5/rd0YyR\nUOwTmobZPhT/Zr78YVJnZhT3ZvruLRr1Mo2ZQOdsA45406d9gMj6fnn49wh6\n8WAZ\r\n=4hKE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "description": "Turns an AST into code.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.10", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "^7.1.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.0.0", "@babel/helper-fixtures": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.1.3_1539273154108_0.7972881623988144", "host": "s3://npm-registry-packages"}}, "7.1.5": {"name": "@babel/generator", "version": "7.1.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.1.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "615f064d13d95f8f9157c7261f68eddf32ec15b3", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.1.5.tgz", "fileCount": 26, "integrity": "sha512-IO31r62xfMI+wBJVmgx0JR9ZOHty8HkoYpQAjRWUGG9vykBTlGHdArZ8zoFtpUu2gs17K7qTl/TtPpiSi6t+MA==", "signatures": [{"sig": "MEQCICzLsbBhA/Dlyyw/MGcm9+H4J5dp7ZjRCq+v6tBG9S73AiAQvg9fVM4dMdAThSKBK/PnCxvilx/i4OvxvzsjTEn49Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 107969, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb4hPzCRA9TVsSAnZWagAAFDcP/Ru9CaWIN/30xiWHsRvS\n9s27HQ4oTM6pcnL2XcDvXcGIZN0OHq0p5zCKqq5EZttMi5FZLEK19EuGpj1L\ndrIvPih59pYsh+QbsQh/LM3P3oSZ4ZTNBWsnOSbLih4mbd1o9PKdhXZeCEMY\nLn+395GgiBZlLr8GB9Ex1ymvKqfJCBBjmb9fMx+0IIpmaRZDnautJu1iCjhH\n6100soDMYMq8SkIfH91NUwHjVcWLDJZBpIYPmcC5JyaWe6rXlbjn4Tl1FVb0\n0RieIfD2lHAecr8qy+P9K5p7dWkcu1Bgz9ZOhlExI1eNB3e6hYgFOBgmvOfI\nRLPMhXy4aBmnfPxSY79P/BsQNU8kSHQcgJrfe2dlKZheaQp0Zn0a4dnhPQnk\n/FD4iRIJIozzxbbQD0AWd0Uz6poMn12RxUNKuCDXlDlFePty+TdVDKsDhIzm\nLQxWmcA/KcUn+q7KLiVXehmkN1Wrj3/7r/D/YxuMaXD9TIfyb+5sBEtFKTSU\nf2aw7lULvNkiAicFRThu3sGd6OSgVUgBoLeN5JKvxLmQy5+2NJl2fsNu1fis\nz01K0CvPmdd7eRGqH+tg2ZyxVdhVfPD6NIQpowipFzmMUBRmJQvvk0A/zaW9\nW1rxvvT68gzRpvl8STVNXtcMjott1Z1dFY8lEd77bOMf9xzd7zjRGY06oPHD\nG4bQ\r\n=HC0v\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "hzoo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "description": "Turns an AST into code.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.10", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "^7.1.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.1.5", "@babel/helper-fixtures": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.1.5_1541542899250_0.06026721234728449", "host": "s3://npm-registry-packages"}}, "7.1.6": {"name": "@babel/generator", "version": "7.1.6", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.1.6", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "001303cf87a5b9d093494a4bf251d7b5d03d3999", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.1.6.tgz", "fileCount": 26, "integrity": "sha512-brwPBtVvdYdGxtenbQgfCdDPmtkmUBZPjUoK5SXJEBuHaA5BCubh9ly65fzXz7R6o5rA76Rs22ES8Z+HCc0YIQ==", "signatures": [{"sig": "MEYCIQDAd9pnvwQiCRBnY9QqscLWeOZWOdShTsyFkDqR78PWzgIhAL83zpsaQVAm9lMsZxVII4i52i/xUmHmXs9lh9Qerj3J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108016, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb6z3WCRA9TVsSAnZWagAAgAgP/RnsufQ8+wMmrNNguFRd\nWpRUaFXbMpIDwXZb/jJPrVrpa/CNko4zvDaAP3eSFzz5ICmjac76GtNnKP7W\nWruNv5JZ3QMe/LZI4QCC2GuxpMeDLDjrCRrzFObwyqbT6sRG0fcftH9InZC2\nqaAlgDJHAu2MuA1nqQqAMOW3u7Qag3eC3+d8uQfLv0VjVPrPFzlSs6Y3xz1J\nZyTFlNl+RP+H1tHGO8xFLHHlumEgS40HnIqQCFEpDUzREYDHuJbstJslUldb\nqvlr+QoO1KRtgMdcx67x3pwfwqIhsCWrXwgk2cNu+WGLJsyoRK2blNyQiJtE\nBRVkkdxYesW0EHc69t058XMbnnzBxh62HdpIw9VR4QQAWj2ZmGAPhFedrGeu\nvhfafqQ6bUwH5TQg/KrZpQoe3DWWcv8AjC+izzUXs6mWEx8qP9qhJjcIBK9Z\nboRpr//L7do9HO66cH+c6Aryhqq7mtOPlVbEVmi6JybLYPWCz35fWu2yzlrH\nIecdOGuH9DsMUBB0+l8T9XQRcz+mkC06o4TKRo80YwhOOj9erHtlDaz+ruVU\nlNKB0YCesz7R7rh1F+OOmaNVzqIQC6LoKiWVvmvORJv+vVU21XSyZjYTdtwL\nK2yBsQAbpdI31DwCUwWGMS1N6d6Px1UwhRe0cR9702bMZ6J3XsqCN+75v3+3\nZxLx\r\n=K/aV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "description": "Turns an AST into code.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.10", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "^7.1.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.1.6", "@babel/helper-fixtures": "^7.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.1.6_1542143445601_0.1643575964382602", "host": "s3://npm-registry-packages"}}, "7.2.0": {"name": "@babel/generator", "version": "7.2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.2.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "eaf3821fa0301d9d4aef88e63d4bcc19b73ba16c", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.2.0.tgz", "fileCount": 26, "integrity": "sha512-BA75MVfRlFQG2EZgFYIwyT1r6xSkwfP2bdkY/kLZusEYWiJs4xCowab/alaEaT0wSvmVuXGqiefeBlP+7V1yKg==", "signatures": [{"sig": "MEUCIQDrR3wUSZbpx+TRKYloT2pox36ZAGTi5Wc+IN8xODNslwIgeybZD11HGl6xcjG3NlmeqcB+b4UtHQfI5gKJf1cisDk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108199, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX25CRA9TVsSAnZWagAAuNYP/2XjXMuWZfBJIzvGRxyE\nxCbpi82fZBgFQrqF/cxJPic46L9LPSUqpYV/zNUUH72RphhIef+yBjejHuST\ntt08IKEVvXPjtoPVUC97h4Rzs2xTq9bIPENAAW8jqkCJ9hGlIUUb6F+DFwv5\nYbETHLKfBV/yXfjjb70uf2tJHhOokV08FkXMYpfgR5IB//sIOaS1ULRRRFnX\nODn+UBT4KhzYhfkk3t0chNA50S94oo3fJ+iePqphXBO0hIjZL6RfEg3Dx5XS\ntXPXjm7lrCmhLGdJA7wZ46XcIWnGxFJ/wShXOyDNW53o0mlGs0I3DtYO/MNU\nYyUhpV3Oxf5SovaqIxu7NYRhyaYLYqBGckBC9lTyNGjIhUBlNS373OSlsNK8\nWpW/y5ih5OIrNJjnDWMsRp0m0KUAbPZD/e7z5MAzLCFWRK8Rmk5FiOcN4p8C\nM/7+95sO78A+OdmPe0ma6C25gLVyKsztIMc0txHqNWyIz1mLOaTAe9GLyl5O\nJBtqCim8lDiKzhsKnrrW71JyhaIL+VwYeOE+0F5kboBVMawiO7PCCG/PGivd\nX10muqrz1Ayi7916DgJ709s+u24ipdBBNP/NAgNKizUa4/igcsELFghjyy7D\nO9OQqbIFvJPpBSOagkVeaV6DAt4iFXGdiCqOa7LETu0hTDHNk/HhSH210AU8\nshVj\r\n=x+vL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "description": "Turns an AST into code.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.10", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "^7.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.2.0", "@babel/helper-fixtures": "^7.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.2.0_1543863736913_0.06988590841406284", "host": "s3://npm-registry-packages"}}, "7.2.2": {"name": "@babel/generator", "version": "7.2.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.2.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "18c816c70962640eab42fe8cae5f3947a5c65ccc", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.2.2.tgz", "fileCount": 26, "integrity": "sha512-I4o675J/iS8k+P38dvJ3IBGqObLXyQLTxtrR4u9cSUJOURvafeEWb/pFMOTwtNrmq73mJzyF6ueTbO1BtN0Zeg==", "signatures": [{"sig": "MEQCIFCFI4Ts1lG3nCpUOL0I4lTYA6thIIf787AKGZxmvfioAiAq9GCzT61tAutAd8kEqRlH9sInGeYLzgR51N0GetFSHA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108602, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcFNHzCRA9TVsSAnZWagAA/RIP/A1ut4JAXRtVylg2pQNa\nqjZTDvmvOtzHj4aRiUnTtkbJEwDDOVA3XDdk4m2eLDXvHxxgEIyhFn8vEM0G\nIPjmfyCpbXXzHYN+PxJMQ0nTxCy2yfP0SbgBzOxd2TjEAvVariWLNxoP3Knw\nzQ8GUq5ZtjEd0GX3uibfv/7VuzDsC23+3JV6lgHMcc9gRtRCGuzrnYAkueRv\nIB3nkzmCarRuWzOwfUhOj387qDrZvM2Xv4S0yv0iEfz/rM8F/BbWMHZRwFUI\n+2PD05gU/ii5xw6ytvB7sZLqi+/yRwjWGDQF8vHZphp1OurlQi9eOsJCenvH\nldOXZ7mysRnndUBjE7nJcKhoWJo/xSzG9fE1CbtpIs2N2bz3ADRvgYp8TEHk\nrU4RiEeCrUk67b8Vg+WCVEuj5JL97zBbprYgsFQLKGUL7ebWn5vz8L1rI/7m\nzOWthgnk0pC92LmJf0HWvuHrgpf98nyiHBd/ufzqaIXhNLkjeBlDiafuHxE8\n1A0WYSkGTRBI5ImO+96JF3ZlRddTvv4fDcJGSwrhfuFQetx4e/vIaCaHrx3E\nc+BDv+BmbFt7QCxlraAq6EBHj2m99CjeL6Zg6tXEMA21wNxelHi8amnzjH+G\nhyKaetlHFZol5x9ljRSYi+5HToUb2oTcpr0f9lkvpMSgcl8jh/lysaLF1sTQ\nvfni\r\n=B45O\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "description": "Turns an AST into code.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-2018 <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.10", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "^7.2.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.2.2", "@babel/helper-fixtures": "^7.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.2.2_1544868339217_0.32264632127016046", "host": "s3://npm-registry-packages"}}, "7.3.0": {"name": "@babel/generator", "version": "7.3.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.3.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "f663838cd7b542366de3aa608a657b8ccb2a99eb", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.3.0.tgz", "fileCount": 22, "integrity": "sha512-dZTwMvTgWfhmibq4V9X+LMf6Bgl7zAodRn9PvcPdhlzFMbvUutx74dbEv7Atz3ToeEpevYEJtAwfxq/bDCzHWg==", "signatures": [{"sig": "MEQCIDXPlBtYS3fwUeq3J44x7g61Zkum4TLkrvo6yk/WJzCZAiB5ohrgeNqBWke1CkFVLW9aHUwFxWjMMPWVkSfvPjDcdA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109038, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcRjtmCRA9TVsSAnZWagAAYmMP/1A1iSzxB8y5kTxg48I+\nT57rpAQu6BxJYx5BfSWKVaUoCzl7IIV51o8DtjfKSqNq7tsZq6dc7seWbgyH\n2GdPhiOP7rNh9/pMiI+XI6l/qHMe5FivFK+szdo3w89963RwLqhKKwBl8n/5\n7nYBlkNRXYWr1hfDoPxoXX+nnXLc8VUILt1auPknVJsqdkjQg7b82nwlJniC\ngq3MiveP3Lt+O2PdW1W2/+JsFpN5jLiXFbE/89ZJEs2U1ye9B/ljDkcApkXv\nFkqG9WOX3u5OdFsOOKoC9WIRqi+rKet1yxPoVVVD6V0xR3RibjXW56abesf7\nSbUDRmsn/t/jvo99TYZe59lGN7eVyFfJGnlqKwANnuzrIzBGXxs5KQxfgWKt\ndgmzVtvknJsql6zdaTTExzXYCk+OHpPXbvyy2Xrvun3DPzHemQsvtKeb5WHk\nBJi4FIoi7BHZCm0iZbJdq87kPRfgq1529+NA717nIbyNf+S9bb8MUmJt+VAj\nasBXKiS1Z1mrCzdbxi0HM4071YTltTNJ2eR3uAPxl8zmSkzhLdgrN2InBaBu\n3WmozTl5XWKF/3Vmbv6wo/sZG+27jG+iFBNPLBkmN5DiiDBGZadaXoPneZJO\nmouoGjVpAJjdgU1cGTBh+Tg8BT02WuDHgNbxbUFDTHYBCE/L9kQUzP/EBiLq\nLva8\r\n=8j2y\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "f6ee26c3da4c903818fd61fd9e5e8e1970185f77", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "6.4.1", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "10.14.2", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.10", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "^7.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.3.0", "@babel/helper-fixtures": "^7.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.3.0_1548106597624_0.7758453964286762", "host": "s3://npm-registry-packages"}}, "7.3.2": {"name": "@babel/generator", "version": "7.3.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.3.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "fff31a7b2f2f3dad23ef8e01be45b0d5c2fc0132", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.3.2.tgz", "fileCount": 22, "integrity": "sha512-f3QCuPppXxtZOEm5GWPra/uYUjmNQlu9pbAD8D/9jze4pTY83rTtB1igTBSwvkeNlC5gR24zFFkz+2WHLFQhqQ==", "signatures": [{"sig": "MEUCIEZy4UbmSrAfFWlUIg1wJEpFN+QswjOZNGBt4xXRWiOEAiEAnJ2XaxMhPKy7gArc1nUMxACSeqdMAlHVn59vbJitZEo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109038, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcWLtWCRA9TVsSAnZWagAAoeMP/3Y1k5s8ndxbASS43KeI\nverRhGh1u46xsOriqJhHgSgqEuSuNCBdYgw/UN+A9Mdo2F6nGU0pXtYET6CT\noWVbI1/RfvZ4ywKgdhhsyeKdTeN4iqNA0JdKXQ/bvjHVzRiIa2/38ul5MiLd\nD/5c7/tYnth2PqzYXKkrySPmVECWF9wY42UY1rwMSFsPWeyQnWaJ0ak7EPnC\n0uRYg8QRYGD0qlQLcaj1Jz1VQlGsqgnt1xJEfIMuNOwJpjJIMNZApApHIYIx\nazOfhnfw8/qoBJfyiBAQw1lbLfC4TyRpHTLQDNdQfxdPXIZaEVwo990m6z3w\njvuZZxXNsDyqsYREsoPNFutXvjqQtjuTcHizBgU+OCPKr+cxIjEApySEmUMZ\nqBBTCC3Pecxxw2vnPLL0D2LsMndiwg3zC4a17VSvfbVfCgpEznpQesuwn0bN\nGs1wcvxC4qFArXMJCfts4f2BOBQFv+2CBhcmz/C/1PBb6TqnkIdY0sBSH955\nI5ngVWVIH3q6MQ9PdvbUt/JcDZqth80VFgHw26gpCDiWhZwzLXmWl4/RHe8w\nNpSfSXZAI8tThIu8TjXlY+MK5UiyQahdRyq/qQjafrs6mr/ETNAR1mS69pyF\nk6KOxCnl0vU4iXQATYLIBpap1LA0hTeUc6ISNxk2F7ZeALDfEwUvLArl0hW1\nMgeL\r\n=r0um\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "d896ce2b53f64742feeea27dd33ee45934cd041a", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "description": "Turns an AST into code.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.10", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "^7.3.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.3.2", "@babel/helper-fixtures": "^7.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.3.2_1549318998375_0.2570812834877463", "host": "s3://npm-registry-packages"}}, "7.3.3": {"name": "@babel/generator", "version": "7.3.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.3.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "185962ade59a52e00ca2bdfcfd1d58e528d4e39e", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.3.3.tgz", "fileCount": 22, "integrity": "sha512-aEADYwRRZjJyMnKN7llGIlircxTCofm3dtV5pmY6ob18MSIuipHpA2yZWkPlycwu5HJcx/pADS3zssd8eY7/6A==", "signatures": [{"sig": "MEYCIQC+nKZCc7GBx8eH1i5D/4L/NLuHCrQzAmTP+Mvlqus0qAIhAMY5GCC1QJKt4UgUAIaSAQVBGk9f4E3R0Qre9odbm6Td", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109506, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZyvLCRA9TVsSAnZWagAAAW4P/31LmfpY7zPAZvjIULMk\nUxI3GBe9/67Rec66rk3UEbzARTq60yb0EwGb0liuOY3ZUMEYcuul/S+iaOCS\nXEPOoJV9CDiqb9hJW/p2tumgUjFeSvaN//2u2XHVnOJMH8/D9bsTkRTTK3BJ\n4ngmyMCjdAtI1OOw+E4Hkh/SoFbnyOI+tlYTjXa2t8pKbSG7lQZHfn3iOZMw\nLTqVT+f3QlEdpRRmKxSexbSPB9ekEfGWnPKgmbbdzJPzs/mGmVp7BqFrnhCS\nyJbViLXLnOmKfqQjSeGjT9ZBtTffQejjm9sPh6YogTkU7zPfvcgbfhn5YWg5\nOV7ZAQ4iKr6PjjhJJw3NxInTWWh2IQnI6AF7GfTVaHEHzA2R5xbXYHRIKFJ0\nVyx9e3vwNhQv3HbRyTl3qnv3FQl7qPUPLQqlAjwy9ywgTwX7F42zkEWHefnK\nUeDucEtzr1MsNyKw3354HxF/VO2oXS/j9LLJHwLkLyTNTbZ9ZwvZ/0IylSUZ\norKqFvJgBtXIMTzgbKRCvvsS0orts767BlqbCZs1Pc8eci6skrHUiD6J28uC\n049bI0Zi1HEo+tXsv78dY/MVxvKghZjnJJ4+OX7TIZd1w2DQoTOB09+b02xj\nKyq0IFy+7HOoMTP4jS69DkvPzxQrxc7+IZ7vLKTX6belPKyeVfknDeXJ/a7x\nR0Tz\r\n=2Qv8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "d1fe2d05f4c468640facf40565e30f7110757f2d", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "description": "Turns an AST into code.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.11", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "^7.3.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.3.3", "@babel/helper-fixtures": "^7.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.3.3_1550265290558_0.9709474912455571", "host": "s3://npm-registry-packages"}}, "7.3.4": {"name": "@babel/generator", "version": "7.3.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.3.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "9aa48c1989257877a9d971296e5b73bfe72e446e", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.3.4.tgz", "fileCount": 22, "integrity": "sha512-8EXhHRFqlVVWXPezBW5keTiQi/rJMQTg/Y9uVCEZ0CAF3PKtCCaVRnp64Ii1ujhkoDhhF1fVsImoN4yJ2uz4Wg==", "signatures": [{"sig": "MEYCIQD34g8rQvktcu2ev1JVDMvDnwmvxdPYborukFtcmKPevQIhALi2z/Jc8wRhP7l2Uwi22GHEPQgS4E5iCAfXWQt0l/1X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 109977, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcdDV8CRA9TVsSAnZWagAAAegP/1GFs3/9cjI3onmNz/A5\nICWflOqzho81b400U2obu4f77+7Zqh9VHbglbJpUV2QswPaYqs8+AviJhrE3\nae8hWb6shhhItVz8Zu6EIC3PmLdAdx03+evLbjdBhheE/ngBUpZ+0nPFkUoM\nvPIIkhUekBqJZQR2U1S29HiBY0uhDM7fG9hzPnzJpXygiS9tU7EmV4XinNTb\nC+4l5q7AemjCHD3Yg+hiqoucsVbsoTrdgI1d9g7Qlnvgfli6jBLvrL4uwS2f\n70QoM0JqnafXRp7HcJRkWOgQBR1ylnOD09h1Fw/+BZJZxBziA3Z7EVFlOBQH\nkFrsW08OUXCbFGFuV521Cn9lQ1A3oTzw50zBsJY+KpMvDNcJogtLelOelQGV\nAWh1RlbzR0eqX+/enojZrKoiMNb3UG9bx7dQcVE1qdYUXPtgqTSd8t7pivMR\nW8RYkXb5f2OI4FNSZ/poNJpfGZO91lsPEMGdr3I0OPz/uVUEVRwpa2lkgzox\n2VolIZ6W8tdWgtpx0JHGXW66e7SgI+kWxk4uRf2PB/Cd/sm1EdiSe1tT3h/v\nRVhmKcT7z5qIaqHXg96WpkbmhpH6YO6pRG40Pvc/6YQVSiTuorJR5uO7/Fg3\nLaZ33V+sm66rp9SS6pRJgc7PY3dHaxb/op2vvlvLRNEDxbV9bpUz8u0q6biT\n6Ues\r\n=Vyl3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "1f6454cc90fe33e0a32260871212e2f719f35741", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "description": "Turns an AST into code.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.11", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "^7.3.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.3.4", "@babel/helper-fixtures": "^7.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.3.4_1551119740128_0.12650970273691264", "host": "s3://npm-registry-packages"}}, "7.4.0": {"name": "@babel/generator", "version": "7.4.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.4.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "c230e79589ae7a729fd4631b9ded4dc220418196", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.4.0.tgz", "fileCount": 22, "integrity": "sha512-/v5I+a1jhGSKLgZDcmAUZ4K/VePi43eRkUs3yePW1HB1iANOD5tqJXwGSG4BZhSksP8J9ejSlwGeTiiOFZOrXQ==", "signatures": [{"sig": "MEUCIDERMoTV3+onv/fw2J7x17ZBmOjZqHHYJDd5NR5RDkZ+AiEArHs13WPWgmtmuZch7bCC902vUFRqX4pSI7AntAZmOFs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 110292, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJckVTLCRA9TVsSAnZWagAAj3MQAKCAJLqO5fTLn0U9zHiL\nCM8tkjJpO1jZfuktouycHwqFjydMBuTMSSjaE/1KcY/lMmw2lJKTu6RjrhpB\nSHNw5Kc4KDgPcll4V32e+A16cvW9s+I8Ku6f5ErvLCazw4yO4q4ry+BwLc1V\nH/eXLRvXt+8sQCX0bG4QqjmWS0vxvnrJq9ozrpU41J98C0XwbzXCEBfghb5I\n2vjvPAqyMCVb8Vu+YFfMwVqWSASUR6AbYE9XPQ/pV9c09qfN5qI9q9pvuf1b\n/V4+XIBpkH0eN1KrJ1TjKNpzDA0GpRbNxYGLiYKB+jcFa9xUJx3GfezM0L5u\nYcEkO1xdz5z+70TI+xiVeBAuC+OYpsl/OAHCedM/WFnqJ2uIrdw5035bJN+4\nJe6L3Bg0Nh76LrwGaFmxeO5QG6i+DX5pMDjzzRO2zhkkfSdknG+caNtgbW1M\ndXFJ3azNd0HP+4bnOexdzRroUnGqlUkUmf9Jgf7h9X3eZEfbTWdLWfpn5Dfk\njxk1PYHNlEBww3WyIT6m1sAvkJZnIQeGvo8R5YgjTXwVoLyiwvt6BFhPjnHe\n1/KU4WP+vKCbyK5Pdm9M1V2v58h2ZgAl4FtkIr9dTaAUNj+OU20soUyvea29\nwF0T//rIcbSD0jNGNIvt/Tivuj8OupnfrRQ0rr78Z/bVUH0ooVwuhdGK6uv4\nz3E0\r\n=Qf5b\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "f1328fb913b5a93d54dfc6e3728b1f56c8f4a804", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "description": "Turns an AST into code.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.11", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "^7.4.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.4.0", "@babel/helper-fixtures": "^7.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.4.0_1553028299068_0.7557125822895621", "host": "s3://npm-registry-packages"}}, "7.4.4": {"name": "@babel/generator", "version": "7.4.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.4.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "174a215eb843fc392c7edcaabeaa873de6e8f041", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.4.4.tgz", "fileCount": 22, "integrity": "sha512-53UOLK6TVNqKxf7RUh8NE851EHRxOOeVXKbK2bivdb+iziMyk03Sr4eaE9OELCbyZAAafAKPDwF2TPUES5QbxQ==", "signatures": [{"sig": "MEYCIQCtxBL05BUaQBOGy1wVUdRa5B+0UefXAa0Q1IroKdiWngIhAJE8d1gpz83hf4WY0MSVQUu1PoQ2MQaEHlLMikRc1t2k", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 110292, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcw3JdCRA9TVsSAnZWagAA97cP/0c30Ct3H8Q4p37Irc0t\nOnvcE5RIO4yNOrccpOkZPPUp09l21Cl/9I/oOJtU2G8TQ9j0ZvYBd7FehgQV\n5ZmRNgS7arp5I2bkW0rrzLuVSNkxEE8PE0b94dp00IMi8V1gY5hKZu1iPrOS\np65WGtlmi0HIjOx4gvPkgx3ufQvtPOI87BCeQP5X4FPvTyiOKbLhk6W3EYIU\nBVMH78scVqvobPK0A5HWyQJkJuAyqSEuljY2eioSo6aUW8eBh3xqBhFNQDgw\n/mdiIS08fMUQZLmRlEECyDuKEqoyY+/H+2OsdKJzmV8bYfp/qGXl4F+WUXGp\nwpXlldMz8f32Q88fnpmv+V+RSTiG+EBWuxWuoIzPJPqETfmWiuX2NmG1MUk0\nn8BZYrbrXua7RMBapte4US9FmAtkVkBtKN63dczGu3BZXSnlnWhgyC9ftSju\nIW4bm97AQ1anDEpoBTU9peApTsr/J3UxUPm0iYUPtw5bCXL5gjsvCM5hpa25\nxxhmiS/bMsLNV9fRQS+aIHG8F4st4WgdXSc+LPis9K1LawHsj81b7FUv8QZ7\nLyFz9KaNW+TObrWL8Wcr/DghLpnpUuXEpkABWKgxWycwND0cGxamOLjSOqpw\nCAI8xejjmJ1VCYhXUzCVDI4MA4SaIIBnzWLMk4BgO3SnCxQqjX4DtmzgeeOY\nwyEQ\r\n=SjMz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2c88694388831b1e5b88e4bbed6781eb2be1edba", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "description": "Turns an AST into code.", "directories": {}, "licenseText": "MIT License\n\nCopyright (c) 2014-present <PERSON> and other contributors\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.11", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "^7.4.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.4.4", "@babel/helper-fixtures": "^7.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.4.4_1556312668511_0.05590685410629748", "host": "s3://npm-registry-packages"}}, "7.5.0": {"name": "@babel/generator", "version": "7.5.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.5.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "f20e4b7a91750ee8b63656073d843d2a736dca4a", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.5.0.tgz", "fileCount": 22, "integrity": "sha512-1TTVrt7J9rcG5PMjvO7VEG3FrEoEJNHxumRq66GemPmzboLWtIjjcJgk8rokuAS7IiRSpgVSu5Vb9lc99iJkOA==", "signatures": [{"sig": "MEUCIQDSDBkYBTfc3o8ZF2BGiYk1+tX6pwc5YhnkdpzBth6+yAIgfH6qNezLyQWb1qVi/+33txlLUNiLfKG4eeeGxaD8uDY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 110450, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdHffoCRA9TVsSAnZWagAAl00P/jP6X5GDYvy0pR4A5PNh\n8UmSvHUeue+ighBqO07XENXMBZkenTjgPYTREQOdWuJHLFPHRYqnqH+CYDv7\n9f2MgmYCdwIQ58ZvQPtQlrrQwdXuagd4FGw2VSrlo5LiinABpimyDsVg9sLv\nATXy6259q86BpDUI41gnPQ3sBVNS15Zc8u0x/Zy6V1H/IjksC27m6tWkPE5Y\nn3zhXBCBbj8bLJP1BIpOa5U77gadR3vf/Snj8WIPwVDQCKXdEVD232MhCW/m\na4q/GwH1EqJZBGPg0Txyef4yM1MvwCu2FzPQ8CcK8t3G2sO+sCBcraxwZUZn\n2ABusgb/Tp17RTH5mjMhPx2qrMJKbk0/47OzZkOkBvZTCLoqRcvMn7aO+8h3\n3k3L8IcILoXPFpHa6O5RWswutilrvGH3xniCqOKMcWVgVTyPwH9ow78nCxOC\nmfJfcjCMGunkafemTk1Bf9YN1zhWkC4fCppW6dFEl+jwJ0ABMlLwCQPZ6EzN\ntCGCGSbMsD/9er7RQwk5PilgY+bzncwM+VNXuFtyXmEyrF/xlCDPAFOqpn57\n0jeqwET3/6V0oabXOw8eCh2d+gW6Ss4RqJTJBWYA9leoqFyZGrAABsi9BVcJ\n1HbFywrLWYUlnb/5NLFy0VldXFy/93l1G2/RO79iv9v9VDoUCleb1biqjQxT\nphzG\r\n=AAiH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "49da9a07c81156e997e60146eb001ea77b7044c4", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "lerna/3.14.0/node@v12.6.0+x64 (linux)", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "12.6.0", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.11", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "^7.5.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.5.0", "@babel/helper-fixtures": "^7.4.4"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.5.0_1562245095561_0.9250804971130204", "host": "s3://npm-registry-packages"}}, "7.5.5": {"name": "@babel/generator", "version": "7.5.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.5.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "873a7f936a3c89491b43536d12245b626664e3cf", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.5.5.tgz", "fileCount": 22, "integrity": "sha512-ETI/4vyTSxTzGnU2c49XHv2zhExkv9JHLTwDAFz85kmcwuShvYG2H08FwgIguQf4JC75CBnXAUM5PqeF4fj0nQ==", "signatures": [{"sig": "MEUCIQDVDqzci/cQPQMuM8cg4TmlaYrH22FwPzXO25YJQum3TwIgcjM5nidW3M7DI546L32rSaVYXSWH8lswMmITS1P/dkk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 110450, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdL5FlCRA9TVsSAnZWagAA2QEP/2teCMY7TtNh9fWim4Cy\noZPJXkHtGlfQWGk18FaeB3h1Gln6QYzqRRhyI0VMkKOSamC/nzFvs/teSemn\nXvcFYxSKUoVZXjmd+i8Mc4vb77a1lr8DFDpE6j68TcnFq/DXEMM5HzP65b/U\nWdDnw7B0slw7AXRN3VkhI6jOyyLSGf2AsE8YlO+5I/5pSTTvOXIhAcNQKyay\n5DjZhpaRj11b4JD6ZFa6cV4xgWpKy6cVTGfe4AejO40x9pWM61VYVt0cFhu0\ngYOEJDJr8vIpQ4Jm1OB6tMBAAC9aP4b6Yo6lneBPz4n7VXIPRmIt6cms8pqg\ntRLWZXekIjqMWjcNcoHKaI5bqembF4ZgnORa4+CJZLaBN89top48yfyfSJWg\nIB6d8wcLHZZEgaYuA1tfz6yfsnN9eNM0IkqBQX7nBz203sV+gfa9jsBshEIN\nGDasoJ/rFGxvQ3EHs4SvSN0MKgDfs1SjEG34ph1MFjRkUMUMuiaSxGb2iii2\nP6WZ32UJzfELeqF0p4iDaOHyn+HsKgB3irvBc/1a85LFE41OeGwb7KdihSW3\nRnzN0G/imnrkt9jhwKalJJrS0rhIG0L8L+cE/BjgIz/zFOJoYilf0b6whUBK\n+RwFHPQHAFMSyA3q+PiD/UPW2eML82PMNBrSv4AE+TtxpNsJat7hIeCfUMw6\nkal+\r\n=VoyK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "0407f034f09381b95e9cabefbf6b176c76485a43", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "lerna/3.15.0/node@v11.14.0+x64 (linux)", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.13", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "^7.5.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.5.5", "@babel/helper-fixtures": "^7.5.5"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.5.5_1563398501008_0.09000875904427086", "host": "s3://npm-registry-packages"}}, "7.6.0": {"name": "@babel/generator", "version": "7.6.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.6.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "e2c21efbfd3293ad819a2359b448f002bfdfda56", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.6.0.tgz", "fileCount": 22, "integrity": "sha512-Ms8Mo7YBdMMn1BYuNtKuP/z0TgEIhbcyB8HVR6PPNYp4P61lMsABiS4A3VG1qznjXVCf3r+fVHhm4efTYVsySA==", "signatures": [{"sig": "MEUCIBztqKhO4y6bnQKBssq8PsJzmKDNa3bzAl9bqaNx/AT4AiEAqd7/lNSYAnDSWJCV+2ORcf5rzxpPTpi0gNYwtGumE50=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 110804, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdcpiBCRA9TVsSAnZWagAAUrkP/09qFXDdM0tgAOTWrEjh\naAJEojmO2u8Y18JFptRdiFlCCju2e7eT+H7K6DbxOkoZMYa0SZCfG+vkYz4t\n8UN/RLavdtPwSuknHRTrZKfUvJhxnClWmEh96wHKVVkORNkESYK4/YcM+wnD\n/Eg+oJbySBpqsI6gcli8i8ui7JWOLDKJ5tU/t8oz/IAoJbKk3tWnzhUSpPT3\nf8kxQYnP/uNXhsZ5Z+j0BGNX6ntxbXV0DSX2He0nvSuQImf3krF+8mBGQ2d+\nxUTTNFrQ1MnBY+5lQN4x5AEZpKq4rQKF1ML80hqeK8inT7zJZyQUI3JfHttL\ni6oVq6AxqzMZdM7MRGatSWAxbYSEzbUqUc8a1eVMXLwnktxNuZjx+095qiFX\n0Nv/dQoromv9CCCZYOXEbF02a8lILpsfYe2VA5H9C3EVP3r2aUXFJHlTRPTn\nM4k7brm66ruqEH5UGYurgbnLHteFXN41LwhGQ/xKMHM7ekDz8uNrCC3/NUhm\nA549Fa2fHwPyVfbc5nMD4TnuFJxvT5pVrjU+tngPtTR4nKPVj+bPJ/FKCE04\n1ttxjSUm4bmtxZaPtuWSRzBkirTzVktAeOZmo9jZczCtjPbq00ere1wdO0YW\nZ582l3d4Yl9WjQICPxxiVqXbYA3DiQDg3Ytntcn72HUqX/AQDR9hANBLHcnn\nd611\r\n=KemB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "cbd5a26e57758e3f748174ff84aa570e8780e85d", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v11.14.0+x64 (linux)", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.13", "source-map": "^0.5.0", "trim-right": "^1.0.1", "@babel/types": "^7.6.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.6.0", "@babel/helper-fixtures": "^7.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.6.0_1567791232609_0.2814134080275843", "host": "s3://npm-registry-packages"}}, "7.6.2": {"name": "@babel/generator", "version": "7.6.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.6.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "dac8a3c2df118334c2a29ff3446da1636a8f8c03", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.6.2.tgz", "fileCount": 22, "integrity": "sha512-j8iHaIW4gGPnViaIHI7e9t/Hl8qLjERI6DcV9kEpAIDJsAOrcnXqRS7t+QbhL76pwbtqP+QCQLL0z1CyVmtjjQ==", "signatures": [{"sig": "MEUCIFZkcMuThSZbLLgKoCwf4R9HzjheA+WNV5Cwol13mmFjAiEAzimLOIuupo5KfWQgpAKcObAumP5kmn4JztTcyubQbn0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114653, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdiTdgCRA9TVsSAnZWagAAwKAP/1C2++f05S4WXEIyfJUf\nD1wOtCQdqLsT47AUp6WRxMJbBFJ0g/mW/E/PdwS3z3AtW7Zqr04CMPvbKpYf\nSFP1EM277n4fs3Bp+LAm4Vt8mrjCHw2rPYZ2Txm92P+QQ4IY1z5WKP75gyZj\nbToXNFNxN8tHbP4upf188Smgmh6OfkkGrjEgoJlzbFxCHxj7mnjN7MB3SQPO\nzAEOZJw+0EwDUrvKnAFA6k51HI+BTNrPlx+BLSGtwyXlge9PKmYSSacGbypF\ncVwq0CPkWpH+sFIc+5fMv9Zp4IQ0nYJ1cAGGND1AAtxLxBJBk/3Wx4q5HotW\nF2qamOClkZ24HKMQMEqBuAXLaYUe1LvuV7n5Z4c+xt5EjkgG1v8R2qK6+rg3\nSrb6XQFdFhjojMarJRX0YXxZwlGraJ5Ln3cVSEw6rLUoignmNqixOahzWRpM\n1n05CQiyLgu/xSxWPA7ZvLFt8LhBJi2CixdppE9YUtRFNBdRtSbC8bzMyyN/\n1qckaeKlXrq2oeyub9LKHFHoIgkH4z/+rfaA5Q8WwkeBE9EZpK6uxNtmRIjt\nWP+CRGTMwApyZlYNM8ENtixzw2X0J+mIaODTYCpwx5qL4jA/QYFL1YBuArDW\nXVsir3oiIW51TTJl1bfDK6BLLphU+ZAZiAQQnChKHTerAfu2Lse1TV7B4JCZ\nxGeM\r\n=oX+C\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "b9cb4af953afb1a5aeed9b18526192ab15bb45c1", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v11.14.0+x64 (linux)", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.13", "source-map": "^0.5.0", "@babel/types": "^7.6.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.6.2", "@babel/helper-fixtures": "^7.6.2"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.6.2_1569273695483_0.8315668111296024", "host": "s3://npm-registry-packages"}}, "7.6.3": {"name": "@babel/generator", "version": "7.6.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.6.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "71d5375264f93ec7bac7d9f35a67067733f5578e", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.6.3.tgz", "fileCount": 22, "integrity": "sha512-hLhYbAb3pHwxjlijC4AQ7mqZdcoujiNaW7izCT04CIowHK8psN0IN8QjDv0iyFtycF5FowUOTwDloIheI25aMw==", "signatures": [{"sig": "MEUCIQDm1YgqeP6vE2eGcdYeSpqTV0VoxmXbgIpXZ9BttntfYgIgT+qqxkwtm7ZDRBVewC+Sb+gGTznnTYJtvKxI5VEbUn0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113187, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdnOhZCRA9TVsSAnZWagAADJIQAJ30JmKc/DeL99O2ZpZ/\ng5MZtOLFj4d1QPBofvGjZfruqyO8+iqfvgeElRKq8sDFfgfUIrkmPTw3zHv7\n9hYNEAqg5Yih1oqlRw1bxd4rpeUDb1DLLgGNCTSIDHmvwBLrllFCztw47lYc\nKtRaODnaz0p8jjAlbXfy4kjeIlm/QTYZ5kCqZQijKoMv3KkNqNqSJ3Ccw3hI\nSvDuL/pq4twp8zTnM8FPBsv8C2g7D5Xm/+2p5RtpM2y/8Toz4xb0I8CK/cGA\nYRgyMicUnvHoWEuBNvFVHhYYzp1tQFfHaau4TuupGpg40TQqG4q12Lr4a4FH\n3QANNJqKjxqCwhmwRPryAwA0SP0byqag7TTZ/dd28uXesB0MMmtK1KQXuhlJ\niAWdH0fonQ7dax495YiMtpIQxpc07OBBGHOZLxc9x62nwA5go7Dj6imFeP87\n9hixRfh+1hB7KE79oRAeyzotMV4FJ+RONP7uyDvhxRsPwBaYBxo8THmeSxZx\nBvbp0qCB4N27jvrZEzrSWTMfJcqqrkwwVFIhFh7JICR6i7XSQx6Inmk7l9F9\neS3sPpUw7qB7VSOjOuPnL2fOwQaYSR16ZpGTGGNXqV29+/i3AuUkqNI/mZ49\nJRRbjz9pG2GzL56HEudw8naEn6R6GAaKNCiuEMeGjRPWVKtq6eHLo9I1BJtk\n6W+a\r\n=nEeq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "d329156ebc17da01382acb83e212cb4328534ebc", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v11.14.0+x64 (linux)", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.13", "source-map": "^0.6.1", "@babel/types": "^7.6.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.6.3", "@babel/helper-fixtures": "^7.6.3"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.6.3_1570564184976_0.24541815505666498", "host": "s3://npm-registry-packages"}}, "7.6.4": {"name": "@babel/generator", "version": "7.6.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.6.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "a4f8437287bf9671b07f483b76e3bb731bc97671", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.6.4.tgz", "fileCount": 22, "integrity": "sha512-jsBuXkFoZxk0yWLyGI9llT9oiQ2FeTASmRFE32U+aaDTfoE92t78eroO7PTpU/OrYq38hlcDM6vbfLDaOLy+7w==", "signatures": [{"sig": "MEUCIDnwVFli3QyslHd5spa+OoAOxYBzu45U1rexuwGNZa1GAiEAykawpsKepWv+xB4q2g7iglpghtvU52g6/dtKtIAJlms=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113187, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdn0A9CRA9TVsSAnZWagAAhKMQAKAoDmK+Rdll2GTuFjs2\n8zuETLWt5FT7h7CgtekXkY9Pj3ltQjSf6tZPLLq7di5rD7M4iJZmqZbFAurd\nglDpWZnD49yLqn90qBXW2MYoac/MlVYeITQfjZxP2epgpUalBSwTrbUqOHfM\nlf0vhq6y2B1mfwWXkLeaGw28wNAfMM31K7HoGWqPvTcXOwNN+k1UEVgj2+Qo\nU2LPsI1Ru3LKrF5WDBtG7JS7uBCvuPtQArhCAoio3JNHkU6gafbK7MQni9dO\nujK+/wSL7kQMUwO6CCjMlMECb12jF2orWGQn8AJvC+29vqZwQOHgLyec7hi0\nz5bi4kpPBb1Jc7UwSyHdNo6LPwuNCw+0yS2m2MdRj/DlKqfX0WrLQbwC/V0E\nRnhjI3XPoxSPoxAyK5Vlsc38O9e76tUB0Vgcc745jk0o4YPS03yJyvGEZP5U\nbBxN+8WMFE7F1cjYscopciAtZfiqaM4ZxdUM71R507pc7eUdev0iL7ptVRzk\nxUxwGFbXF/KzR3bx8KxV3ZsEEPt4EJNyg7JOBAyvyMYC/XzJzwLiXstCYajU\n9CXgm7Hw41woNjuDpzACSojJ/Xp5rDdhCIq0NxANci4tnrdUQwX7hqC58++2\nkYdSqVnpOnFVxVyDgbntHQMTgXiZSJn3Pvl3rHY00skqVz3I4XWp3Dhmwmqm\nBa1N\r\n=4Sm4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a422ea64ee2208a55dda33f990a422e14b917f5b", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v11.14.0+x64 (linux)", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "11.14.0", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.13", "source-map": "^0.5.0", "@babel/types": "^7.6.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.6.4", "@babel/helper-fixtures": "^7.6.3"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.6.4_1570717756161_0.5767853165618524", "host": "s3://npm-registry-packages"}}, "7.7.0": {"name": "@babel/generator", "version": "7.7.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.7.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "c6d4d1f7a0d6e139cbd01aca73170b0bff5425b4", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.7.0.tgz", "fileCount": 22, "integrity": "sha512-1wdJ6UxHyL1XoJQ119JmvuRX27LRih7iYStMPZOWAjQqeAabFg3dYXKMpgihma+to+0ADsTVVt6oRyUxWZw6Mw==", "signatures": [{"sig": "MEUCICmBmBMffxtRBEDfJytphDLZpXYwXVEBHbWQN3iafnYYAiEAqwaOK8xbBg7maxeMHJmfe3P1Y/hqUiq9sB6wYHRB19Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 115637, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwVSqCRA9TVsSAnZWagAAz58P/jUUFWSnie5FhDCuzNY9\nk+2oG25V8sAa4eR99d1iYBsRe/GlqWm902eOqipj1QA3NwNp7YjyX3HYvTrm\nkxvr9EWkFSP/ZihoXNissGNekAOmhvOAS2TZddEz87xTFOgeYcDEY+b+v7xY\nkHGiptTfYBbkDY2/S1JfUvys1WrMi1BiRg3769R2S6nkwiyoGLP4Jdf4pzD1\nJWDxSoUPOG5LlQWqcMNZ5beUl+9tzoerounhDAxok2xVxic4zs5kEmsi+57w\nbaTn2qvabmpa+XrnTpQqi5ZYWgrE1a0mpKbNXU0togreJtlgVZOiu8+gPDiE\nHH/Ef9QaHQtRUfoFH7ZxtWbP0FRQiZ+2K+jClBHDko5KrJ/crTXZw5efTJgf\n4s0Uy9SlYNhj8BSeYVAFX2oQ+YNIIgqqd/DqkrqIdHjPwIWVmYLzQKmzQK1M\nmSQ7yq7bXH2LcHaCbIngHTH1aeCZQpDnr4hpUYzA5n5dUdc4Gep4FEHN8cov\nOJ/8nGj/r4DN9lAM32RNDoJfhco0R0oiLSmEZ2CGvHpyh01dLH8eg6Ta313B\nJntxFNiiJrauTeur+Hta75Qzf1ow82ZjUSR3IDJHCmxPyfoG1VzKJcZ9wpzL\nF8WGqraJTRGJILgg8dM571sAC4UdProaDVMjsHQkTLVSWFg1CL1zyIVFotOM\nhS61\r\n=Xq9e\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "97faa83953cb87e332554fa559a4956d202343ea", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.13", "source-map": "^0.5.0", "@babel/types": "^7.7.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.7.0", "@babel/helper-fixtures": "^7.6.3"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.7.0_1572951209414_0.0774122104917776", "host": "s3://npm-registry-packages"}}, "7.7.2": {"name": "@babel/generator", "version": "7.7.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.7.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "2f4852d04131a5e17ea4f6645488b5da66ebf3af", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.7.2.tgz", "fileCount": 22, "integrity": "sha512-WthSArvAjYLz4TcbKOi88me+KmDJdKSlfwwN8CnUYn9jBkzhq0ZEPuBfkAWIvjJ3AdEV1Cf/+eSQTnp3IDJKlQ==", "signatures": [{"sig": "MEQCIE6XTKUKEUETMWA3wztTYzEjXPsgMszrEjztPDyVoGV2AiAnE+QFHL/dGVouCVxjWt5OXcdo/XRMjOaBHE4hHb+uUw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 115637, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdw1bkCRA9TVsSAnZWagAA50EP/20JlclDSY2X69z5dST/\nY2N/JZCKHRxAInLmKtNINhXLISlu8Glcu2fvcHsgWC4j7Sx4Jv6UoedbQW+y\n92CkhMna+Tzha7FDEghIoCnd/y8AuqXukP3tu6thkDHvzYEfL+3wJKVCCpM+\n9CVsWQW0JKcrs8ewJPXGdmGobl5iAIQBx5G6/+9z+/lGBmWRQylh1aE/n9DS\n+WmR4kSBMOIaDS3EAZUjtxdgq1fSqT9P9ejLweyBqDKSFeqmPY2JqY3n3kZm\ndz6n0ery2T7cz7RJ01h342I2CJ1cJhTmaB6W1EenM7Y1vrTq23RXTYq2v2hU\nrzrzZ9XgWLJXDpG59QR4vD+PHcUzG64uQ8dyjekUO4BO27NVx8xGcA7sQ5/O\n9/5JlsLfoCewMif+B+ezWWXCCdGiqAeSZ3y/YlrSgu8cllQmgMs7Nj1yrYTS\nQ7RUpRaFCvXlYSp8E00PMDHH1vCmdNyraz4ikyWi9RDdZ3f9VQGdoqBQSoq7\nFrIshfu1YHzUsqqMazp+d07qefKmh/roCPvoqiahCbEAuGERjZjd+E2BbyjY\nBy466k0HkBBlu1F4JK62hLakcvzyvVLh1iGlKf1gjFj2uEh0PI4yEbwTGcLi\n6rGR8PSvOLlU4ydvFfWuHpynQRIal0V36QygJZ6qj8rbmSC2c0B20DOF0WWj\nxoXu\r\n=pLwt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "35f4d1276310bac6fede4a6f86a5c76f951e179e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "lerna/3.16.4/node@v13.0.1+x64 (linux)", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "13.0.1", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.13", "source-map": "^0.5.0", "@babel/types": "^7.7.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.7.2", "@babel/helper-fixtures": "^7.6.3"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.7.2_1573082852420_0.6643442277480396", "host": "s3://npm-registry-packages"}}, "7.7.4": {"name": "@babel/generator", "version": "7.7.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.7.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "db651e2840ca9aa66f327dcec1dc5f5fa9611369", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.7.4.tgz", "fileCount": 22, "integrity": "sha512-m5qo2WgdOJeyYngKImbkyQrnUN1mPceaG5BV+G0E3gWsa4l/jCSryWJdM2x8OuGAOyh+3d5pVYfZWCiNFtynxg==", "signatures": [{"sig": "MEYCIQC7tmNlUut3T1fRjGVjtbbVJMcJ6w0PSsP+LGvFe+FiqAIhAP6Gs3IqBFwvrap9ngIFTRMzKIxzFE4XoX55qdUSpyGz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 116539, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HA6CRA9TVsSAnZWagAAxk8P/2CNvUgjxdWnM4yrjqHW\nFa2vx9BkSPGz+oFboqhpjkCOoknN3+qRqZl0S3pJk6ODTyTGtBU+G7r2/cJw\nHYyjkoemFINyxTaQRVmxdQIPSxp7plgE6JgOIVtaK00XrsJAJmZ4Sjl2NI3l\ny6VVt0uG9b5KAQ/ooLyM0gHvfSi7cx0LxtIcGdPlKMtUwOr1nkkKdWgRSyQ2\nvGt5rmtUuUxOLCFbm45yFokgNTbtkREkemBC1KYX/VOb+kkexee71bnisLbp\nNQxmIxPQ00K6VNwhHm1QT+yNSg10z/5eB95VXH39877um0SHw3L9wD9PD36p\nXKC6SwzlD8TBjLRkGsPAfCJf6mC2oum/4ayk/tr8JXatpCgSJk8KdrEEb5qL\nt5Psmw7qoseiLzw1ZUYgrk71tqThOCRMBHClvmp2U+4smjOW7NEnqXScL6jt\nwod0jaeiydkSIPqSuXDuCS9xzqtaITY73CgHi7U7FY9A0zDQkVfq69oCkuqd\nbD/R69dp3HyK1ifTSFgP/D65Y65Pq+ouIjlQP2jlzeRXEqhFWuEkApoO74OK\nW/0iW/x31j6ZJkN5bm5EhqcBb2/ZRid80tOvFf392Jvqe3HGNfwb3vrYJOIs\nbGTjE40bckQoYj50re7TXjX5HK4j00ApcpyEQWlKP7EJi2lrvzoZGZWu05UD\nSpI9\r\n=7/bP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "75767d87cb147709b9bd9b99bf44daa6688874a9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.1.0+x64 (linux)", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "13.1.0", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.13", "source-map": "^0.5.0", "@babel/types": "^7.7.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.7.4", "@babel/helper-fixtures": "^7.6.3"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.7.4_1574465594455_0.38453075795568115", "host": "s3://npm-registry-packages"}}, "7.7.7": {"name": "@babel/generator", "version": "7.7.7", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.7.7", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "859ac733c44c74148e1a72980a64ec84b85f4f45", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.7.7.tgz", "fileCount": 22, "integrity": "sha512-/AOIBpHh/JU1l0ZFS4kiRCBnLi6OTHzh0RPk3h9isBxkkqELtQNFi1Vr/tiG9p1yfoUdKVwISuXWQR+hwwM4VQ==", "signatures": [{"sig": "MEUCIQDCSCmtiFsb+wWQjBpWn0gHbL5SWfK4Q1/LYEWeEoleHwIgFxH0pqopeyPf9QUdSRlAQ4+RGSIrZp4X2GMzJTfPK3k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 116539, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd+sn6CRA9TVsSAnZWagAACLEQAJoFNcw5LU24GvESmu0u\niM8UBS52E36afMkUBs5GDtUrrrPj2YZzvcccRgkUX5QdF2U34Uxb304RBnyV\ndNAuHHVaWuknqy5qNvt2pXtMYZDrZ7IC5zFOC4EhpFBQ7EfU08yD7Sv6caOi\nySyHbLp80E0WqYfmMzwdBsPYFVYfpzOqMKftnkRB+UKG4BtuWxOHwP1bFNmh\nnxDP1rUu6XIpJ3LHqdh/ClchlSZpSgRYTt8rmlvtj9tDyxN0SWQ9Dxd70y7j\nFEozYPktcZNEost/FYobrEuZeqtqDjFkwfs7InKLCmrkGOypPBbFpHg8KRvk\nR2vxoEQlZw7sm4OJWPDvq9JrZE6TTw5b7EgHcuVTucyyosxnFtkxtKYfDpRV\nxbWGcTxLsejpSr8rsJO0W5+l2jnTa72bO+Ut6yctVwpms3buOvugvExYX4z/\nsMcE5f90YSBjcWvjwEOFVELo6AyGjMJNTuTIY44FNlk8QHv2i8Kaw6HMAOvs\nRjgRMfyGWOWEtijzd+WtUrmY76BCLGnoeD7RROtPxyaIO1pXICXFMhcTn3U1\nBaRV0awvnthv+Jg5SFvwvARKtLWtseMTnWCZOOzTIW+r56HPX/hf5D00WEXs\nNgQQMq6hK5sjM4+6+4TmMKTTzsC22pCU44ryVWAmMKHPlIOd/cVDTwgXkh/G\n/Pgb\r\n=FF8u\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "12da0941c898987ae30045a9da90ed5bf58ecaf9", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.4.0+x64 (linux)", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "13.4.0", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.13", "source-map": "^0.5.0", "@babel/types": "^7.7.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.7.7", "@babel/helper-fixtures": "^7.6.3"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.7.7_1576716793334_0.582107696040916", "host": "s3://npm-registry-packages"}}, "7.8.0": {"name": "@babel/generator", "version": "7.8.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.8.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "40a1244677be58ffdc5cd01e22634cd1d5b29edf", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.8.0.tgz", "fileCount": 22, "integrity": "sha512-2Lp2e02CV2C7j/H4n4D9YvsvdhPVVg9GDIamr6Tu4tU35mL3mzOrzl1lZ8ZJtysfZXh+y+AGORc2rPS7yHxBUg==", "signatures": [{"sig": "MEQCIDc8DovjfwUMfxwGhoWDv91HL1M6oVk29OjLKTNnoXdeAiBqfig5w8B2b05Jpt6T5148/zhvyT+sIxjGta4ciGYjHg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 116561, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmVzCRA9TVsSAnZWagAArwgP/i/8bzS9jbLvP0ndUznH\nzxarkC+VLB16ZySN5zSkZX0FDsoDnw3miU/+iqSUMoeTAIPqR1JOs7eFIzTu\nVWXd8H/7Lhx7wZL2dfl9LgWPE0QufkQydk6TQpWI5N9baWVOFCbmEwfPA1rK\nnBZNoImGE4pvUM7jfPjV5bQ9gQruGqZW72stOl2JKql+fEgG5F5SDODKeaOH\nhAYaW+5c1ySesJZ8OTp+t+aKLw+4E9SBEr+N/jc2YMk58rOPwbj7Kd3N0D/f\nsV4JySBjv8B30Oer93HeBU/x89lrCKMXYVmYbUu8e/ggbqsTUInDKo0CQDer\nQJVmnd0cEIQYxx/Jm0uqE9neB9sZNGwmPkP+lA2s1hQcH1fdJCYtILM3yWju\nBb8nZTF57cip8GAMOpM7OY87zAwuc+BeL9xR9Ri9LUXlvIKFLRgWPoH2ECGt\nyr7Sv55NLPeF59GS4L8ayM/lxgCmsjV5JT+jsR5MqGBg9QZlO79hrVOHwYqE\n+vwtO/hHjkKmv3+ha70cBKf1UU6RiHiV1X9BUopC052lX5C1AvX4CUOPSgqf\nLlv6EqAtOFdRTfIOLLDLWvvR57ZBfbB0l1L3QuHIgNRWu+YtABZ3sW8Z5RLw\nN8tOXXCOlypIyHfrbFADz/FkUt9v0y+Our/55WdUndUpIwqleSd+gHGat9Bv\nNp7q\r\n=2Ny2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "type": "commonjs", "gitHead": "2486615a74580283c49475d66067bd7fcab3330e", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.13", "source-map": "^0.5.0", "@babel/types": "^7.8.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.8.0", "@babel/helper-fixtures": "^7.8.0"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.8.0_1578788210839_0.9416187011851644", "host": "s3://npm-registry-packages"}}, "7.8.3": {"name": "@babel/generator", "version": "7.8.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.8.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "0e22c005b0a94c1c74eafe19ef78ce53a4d45c03", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.8.3.tgz", "fileCount": 22, "integrity": "sha512-WjoPk8hRpDRqqzRpvaR8/gDUPkrnOOeuT2m8cNICJtZH6mwaCo3v0OKMI7Y6SM1pBtyijnLtAL0HDi41pf41ug==", "signatures": [{"sig": "MEYCIQD2P8fMFF7gf937QZapgD5yQAce+UoFSFwtR7fc4sZ4UgIhAJV1Jlf+aQPbIMFwBma34Vomyh2bdWzSbBpUCweUukFB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 116539, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQZCRA9TVsSAnZWagAAZbAQAKOTWVNBOfUSKfW4UG6o\nd+AQxsQmawkUsb5yoMNYoJ1sO3RtIitvMzgm9pEGbLXUBVS8ah2BYbbv/Wdy\ntJg2UBr2srwUwM/IjEaZ9QYHALUW57oYAsdMYrZoQ/PyGE5HfchiplcJzQtp\nIYfNUDMVycXlLNkVFmTdqA6/tiP6Usp8RtEKhvgbb2o+VGEiAoJiWHBM/aWk\n/EeA8KLu2v20WduEEXn7OF5CTadfdFnfasKL08jdx8tWqOJH212gUREqYoAc\nvscspLtKmM/Up9DxgyJ+Qpp9eAkD0fTmvzTMG3xRg8aMjMP+3Dn0t9kLBszR\njD9yhNaRpnLkaixlenXxPXfTehDDD++4hqbxi0Ng3BsZJZkgxTUHLxjMRNzv\njEORygiKsMatmALOUqEe+PU+6BMUu16a/X9ZUkwUXYIk1TKV0USELjksD4Ed\nUYhppjQKFVuYS1X8lqDfJxDEpXINaGl321X8I8EhmmriwreEJhd1OGyq2MTh\nmSj2kMDhYNqdmNScaZDs9Pz++ON884dlimYzoJSQd7yxRzgXHvgamHIKsrn2\nzEN9dqzQlI9z/8jXJpMWA1DcmG5sgVe1xPHNIrUAi+pZ1K7L0XaV9mwiuPrE\narY4m9Vk8Lf1UKpGF8ZpW3x2ctnKup01tT9E4Muz04zZQSK0tFHGWD8oaIry\n/5oi\r\n=Ty+u\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "a7620bd266ae1345975767bbc7abf09034437017", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.6.0+x64 (linux)", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "13.6.0", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.13", "source-map": "^0.5.0", "@babel/types": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.8.3", "@babel/helper-fixtures": "^7.8.3"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.8.3_1578951705076_0.12159272717685732", "host": "s3://npm-registry-packages"}}, "7.8.4": {"name": "@babel/generator", "version": "7.8.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.8.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "35bbc74486956fe4251829f9f6c48330e8d0985e", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.8.4.tgz", "fileCount": 22, "integrity": "sha512-PwhclGdRpNAf3IxZb0YVuITPZmmrXz9zf6fH8lT4XbrmfQKr6ryBzhv593P5C6poJRciFCL/eHGW2NuGrgEyxA==", "signatures": [{"sig": "MEQCIBSIdSUZ4wR4W2T7rxJ3L7srV+h7qrX5Z4+SADYvi7LwAiAM1bmui29f82ZHWUNnklrk2gWRSSJDCmXIdQ3wpxAcHw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 116550, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeMs31CRA9TVsSAnZWagAAVWIP/i0DXMFMDkTOjtXl3sRk\nVeP/e9cKudiXToJo3JBfschvAlBVrBo3oDW3s/UGJvC3YAfUGTRRF+cM2ExD\nrUMVs/WiArNGKjgkg4CfdBVcfyYrpcu5bQonvZ8KmoAz8vMMzYZL8ZRykZwC\nQivyHqWPi9I5mXcazXUS+jAP1/daahKWgaRLSGVGurmf3J8ZlSnrzL7iNZ6k\nr6MUTQfgKE5oUPMoa8GY5WPOMVmUk7EUekB5OUfqTvDgq2vh5IuDwSI5p0W5\nLFfRXEm+S8cKyLzl793FDi/fsoN/Rq73lL+WjQHCIXbunPc3OGei5eOqtQJm\nJzZE28gYoI4hx4V0skqR6H/ieOg1on5E4vWeUh4NqP2in9EjQJbUnmrCwC+u\nljzoaRh0Zs6OJPPezOuErVy6TqxoR7Q6bz84OTvGrZL79e+I5Nd5aJLGPKXT\nvaMVt1ozNs7YcNQ+Qu0+rKxWLuy73PMcMjU5BDRuYqeUNEGqYZWRunVprpFa\nIQk6Lv2n7CcZinF4YE4LnBCnBxFP78DfMxZR0AIznCyYVrjZiLDE6HhnwmaT\neWDRRorBtMiKMmnTXu8FXo/rYzbQAWCX4y2QWt3e0Dr46BMIelBcWEOQk01y\nT19jTFtsO3sKZ2hslRp/ZE5aSJZ3+YFLM/qMK9SnxEgOJTuRhD3LnY/60EMK\nmY3v\r\n=QRlF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "5c2e6bc07fed3d28801d93168622c99ae622653a", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.7.0+x64 (linux)", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.13", "source-map": "^0.5.0", "@babel/types": "^7.8.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.8.4", "@babel/helper-fixtures": "^7.8.3"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.8.4_1580387828823_0.6685986910894628", "host": "s3://npm-registry-packages"}}, "7.8.6": {"name": "@babel/generator", "version": "7.8.6", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.8.6", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "57adf96d370c9a63c241cd719f9111468578537a", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.8.6.tgz", "fileCount": 22, "integrity": "sha512-4bpOR5ZBz+wWcMeVtcf7FbjcFzCp+817z2/gHNncIRcM9MmKzUhtWCYAq27RAfUrAFwb+OCG1s9WEaVxfi6cjg==", "signatures": [{"sig": "MEUCIQCGlYQvN0DJlVU/+W/4+YUEJZQMg/iBiDrDaCsEBR+oFQIgAKe0t9AKp8YpZ/N9aQQoZkHcoxbPGCnLcpIiMcUegO8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 116878, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeV7RKCRA9TVsSAnZWagAAProP/Rz5l4Y7kiwpHISC1VUG\n50MVDlNLb/MECB46uz4nr/6HjMepm4nwSuNxZ8Rr6IisRDOj3RBpOVzgcnol\n8u+/Edchlr+PkXv/aLPHTRGVEXMMjsY6QjPjJanF5WSygo1YFAdkD6Qdc8uL\nB70AaT461px50YdHfSe5CIbHiuJR9+I/Ej1g09VMpCGeyWht97KemHCUFqof\nHyNN1aN+EJSSpjRh2qfLh/YUUamc+tG6fjIW8jdsBfxwHQ13cxVkkr6+8+/Q\nRLJLilhx73Au8F0KSvyClDJaEiGE11EKg3t+DpW3sk82MR9BCJso059RqZck\ncjcVzngtID7koEpEd+OWwGrNAsMAxmhL3ofOFeKMBJKZlYpovsvGn2nFnAWN\nfI9VtEUKPmBvsVUl05BopdOmrAp8/xSof8Hnh48rsUTem4SvyaQ3ev75NEnp\n4pOmBL7dOHUJ3zTmES3Fo1bZNRMAaaJxVuneZ4ulYYhBvVzHjqCGhGWWdjxn\n8VIzRbBtqq5QhUhbsgs0BMuXWoNDnvg2LVbHlFhowc2KAYNjW7GM3+7k2/5E\nvfPGamuRE0cBfdZK662GhPEgJiFf/BvjvznhPcCM88sgczlEvDXB5OOvz/Wl\n4tgb9dys/EM9C76yqspSaKc/sb4a/PNPXargqPiyXn1fUwxjXHUO1N8awL+c\njB5g\r\n=MW6q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "750d3dde3bd2d390819820fd22c05441da78751b", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.7.0+x64 (linux)", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.13", "source-map": "^0.5.0", "@babel/types": "^7.8.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.8.6", "@babel/helper-fixtures": "^7.8.6"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.8.6_1582806089964_0.29129231362131325", "host": "s3://npm-registry-packages"}}, "7.8.7": {"name": "@babel/generator", "version": "7.8.7", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.8.7", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "870b3cf7984f5297998152af625c4f3e341400f7", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.8.7.tgz", "fileCount": 22, "integrity": "sha512-DQwjiKJqH4C3qGiyQCAExJHoZssn49JTMJgZ8SANGgVFdkupcUhLOdkAeoC6kmHZCPfoDG5M0b6cFlSN5wW7Ew==", "signatures": [{"sig": "MEYCIQCYCRczw2tBQ0AmPYvMZr0iVMGkdCaPTNob1/fitKASXAIhAKFrrbcaYENCMF4Y1lF1vV9t7SVfE2V7isR7Gsmh8CUo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 117151, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeYFw6CRA9TVsSAnZWagAAHnYP/AqE75jtMjVMWG2z2YXd\nUc7hVYQLT7DK2Y6ZTXbxP26ZboILLZ3QaKfQx7jb4TyWs899ofPDFGe+5MqE\n2dRZSLDt3t90x/fGZvs0VJ0iOidEMJq+T6hE7BuiBuJB4V9gSalOYiNIiyVn\nKxMaNkpnwCaqMUA9aUmz9bjeHdTvbvt1JjB0sODbZC+W5iRrK0UsZWthYnx6\n3q2DGPEO6+LTMx9GqOV7QMZdYHFlD6o0oIkKkoenopdMFsfUNhkJyM7zyC4G\nSgnS20KPQCv56xxBL3qPg0lAVYwCqXFMS10Wb4GApeH5aTqJaUiVIGxmjh3u\ntfomJ8k96z2LT1aysUcN4Ogf+w+0W+zz2jpmy+qp//rS4cKQo20ofggorgxp\n3KYW6WAxh3UgDrm+ufB4fDFzmREcHT3Y+Q86nJRoFMH8Uvlyg6W9g2aC0qBn\nbasvpd6FkJVDnfwj+sKZwMxhJNNiCadhQXA90UZd1Ol7al1VdnY2mChKVfEj\n/GpTzZs166WccDHFI1pOqK9epJA09p4UR+Qv2zT/jxAWhnv2HHoQn+WfVw+L\ngha5iq+nptp/iJQiXF22bTMNqiMpMqbeM7uYovJ9KW9SrtoWHICpFLCXT+6D\n3CQKbNglaPvFahxwlj8HMt4Wl8LOIZOt5m541Ks3k6jCQKbmI6R9T7g097fG\n7jP2\r\n=niU4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "595f65f33b8e948e34d12be83f700cf8d070c790", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.7.0+x64 (linux)", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "13.7.0", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.13", "source-map": "^0.5.0", "@babel/types": "^7.8.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.8.7", "@babel/helper-fixtures": "^7.8.6"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.8.7_1583373370138_0.03131588957420983", "host": "s3://npm-registry-packages"}}, "7.8.8": {"name": "@babel/generator", "version": "7.8.8", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.8.8", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "cdcd58caab730834cee9eeadb729e833b625da3e", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.8.8.tgz", "fileCount": 22, "integrity": "sha512-HKyUVu69cZoclptr8t8U5b6sx6zoWjh8jiUhnuj3MpZuKT2dJ8zPTuiy31luq32swhI0SpwItCIlU8XW7BZeJg==", "signatures": [{"sig": "MEYCIQCX8KsklWUwChQd4Sh/d0J2k0tnhWa07Z5qPxH7+nsRIwIhAKN+d4uKFuJlyD2Q8kDwRL/nRseIGmq+nJwBVC5prBSE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 117290, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeaoQbCRA9TVsSAnZWagAAMPsP/i05H64xEYl1kJi496la\n6EOF/cGELMEeWBK5fLo0T36gsfiMzaedYrWXP2VzDyHd36rHqeI8j3n1ZVZA\nMUCThrhdDHznDo7v2VltEqZ1ywBMMokSGe4s+e/nUqoNnUQWMYZZicsmBCm3\nbalfYe7197Gsd7HZEAlgo6C2ZboHIWLVARuW+iiYZkSrtNBjOb1UNmMZyiF+\nR2Yobz8JFlCOln/FU+XKaDjLNYdx88tWIqdptOsxYZirdZR5yFz8Qpl0hhVS\nNH96Gt+yL+4Zrc5okIINDpfy3+zDVjhLxPL8VFbS8uwORZB2rIZzCufb80y2\ndrttq64WdIUj8PzcrCOBKTwr77vaygXFJ4Fs9iPmEvhr023zycsHAEfctr8d\nFVAvt6hJAFU/rUdzIIv2X28rVD6+EcLtgwft5JwqNiPIMNHlbZyJ1xvkY1h+\nknI4lskUpMh8OxfzCRltCkoXZ8y9z+Hid1gyXgw5Sc7jX2BbAYVmieNjdX1r\nxyauus8YU2J1kI+GB3iaKlD3Maf3Y6R1mmynuqr93nt/AZu1YLfBxdrYZkUp\nlUJNsLInTw5+n9UmrgtxDoZqBrqeC2MaGt/eL3MV9wCSD6RBZMwgCgM41uWD\nmggFZ0zdOYtV+rXw60YSy6jCDo3ZQDkccw/ALXZIoBCMEgDjj1VJpwq5gmKq\ndC3m\r\n=EOGx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "c831a2450dbf252c75750a455c63e1016c2f2244", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.9.0+x64 (linux)", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "13.9.0", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.13", "source-map": "^0.5.0", "@babel/types": "^7.8.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.8.8", "@babel/helper-fixtures": "^7.8.6"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.8.8_1584038938856_0.2998252849246681", "host": "s3://npm-registry-packages"}}, "7.9.0": {"name": "@babel/generator", "version": "7.9.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.9.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "0f67adea4ec39dad6e63345f70eec33014d78c89", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.9.0.tgz", "fileCount": 22, "integrity": "sha512-onl4Oy46oGCzymOXtKMQpI7VXtCbTSHK1kqBydZ6AmzuNcacEVqGk9tZtAS+48IA9IstZcDCgIg8hQKnb7suRw==", "signatures": [{"sig": "MEUCIQDkDEL6NUj9BByqmcnRnaV17w0L0ymIiEdXf67ZW5frEwIgH5LlUB8+UR31zPbeU6gdL2iRm2THjF0ppDevsQGg6JI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119463, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJedOPDCRA9TVsSAnZWagAAQM4P/1ZGwChN/JE136vGAJDW\nGd/zrc+mmSxk4p+d9SBDkJVp51yK78jBtbM+KMMwNQqIATu7T6FuKvG/DOFD\n9AtenP8GDy0Q0U7+pV0HONXrufXml/Prj6vdtDJvlGThusGsidp4UU6MuuuZ\nAQP43rzCcNO+dCmizmfV5SceOCsRkjevqtltp7NB4zdQIkJzLP9HvftGZrkw\nmcYeR0G7eB4sw/jsjNawVHMiX9Zw6zYgqJkgQgmnedK7Bh74TjjlG38QG2Rm\nvvDNLh2kr4Y4sTWwGZXsj+mX6cDoolXaFzsxTrO5zZBuh5w5xRy5wzN5hZox\n24gZ2C4hznWzLly8iwYHCkkuZhh1mU2W10mvEKsmh5NB5UWzx73G1PE7JgZJ\nOAy5MAZas7MMNIdu6NS/WnVMfh7OzVj6Wlta6nOerij54KWgWurKA8jE/FEt\nWr78Vf3YDyQIlKRbxDC69qomX/ps4MmESKwzjLSLXob7TXfTPLc4yBvZf6S7\nQhVU3avlmT6EnzN0Af684xy5Lbwj2kYWU6uFxaq61ryVq0mtCx2phGTtIgq4\n+CzdDHvGIEexu/aolFEIsXqLUzZuZkw4bFjDBzJjW+yocNAD+PIQ8xrtJG5s\nfowLabhwk1VLMp/ba9T+rgw6rOnvkObu3lV6XUZ2UtS/sFzDimFeKioTfhT8\nvxup\r\n=WjFX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "8d5e422be27251cfaadf8dd2536b31b4a5024b02", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.11.0+x64 (linux)", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "13.11.0", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.13", "source-map": "^0.5.0", "@babel/types": "^7.9.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.9.0", "@babel/helper-fixtures": "^7.8.6"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.9.0_1584718786840_0.5932056454021772", "host": "s3://npm-registry-packages"}}, "7.9.3": {"name": "@babel/generator", "version": "7.9.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.9.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "7c8b2956c6f68b3ab732bd16305916fbba521d94", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.9.3.tgz", "fileCount": 22, "integrity": "sha512-RpxM252EYsz9qLUIq6F7YJyK1sv0wWDBFuztfDGWaQKzHjqDHysxSiRUpA/X9jmfqo+WzkAVKFaUily5h+gDCQ==", "signatures": [{"sig": "MEUCIQCQh6d/Pt+S9IAOsPWmha9ecSBjgM1Dbs1qGiOI2YIJXAIgXV7agzBTCMD5TVo4yVJTM20Xr2uRShkA7e5mdwH0l7o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119589, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJed0XZCRA9TVsSAnZWagAAGLgQAJy18uSJX9E1Ap71KjGM\npiOoP8LD3+dn8/wAPo+Bc2EApHpkhXCmsz+ytxsJTMIPz0p42TRveTip2+V8\nB0zx6p89ShX3B/OuwIHKr2eidFfP7EKz48SKcxR6c5b77vLFmBQtyCEa5eV8\n7tQpVkUAOAP3eL1zHXdeTC29YtCPLdxMs/jydW1gE4lv3xZ5QdIJSdWxYjDy\nWRIZmoQyHSQNRelZpwrh6TWtC0S/9SpE9pTfgAamQUx5vF6O54FJHT1b8HNq\nU4AXqMb/6Uo1P/HzyZBvGDXJNuRXupXqR7K9O+dXYW5jzH9ZWykgB2aiIG7r\nwfBw0o9SZ5cefoxww5cBJhwIFjkYQXWqXZbm6tngrATZd/Y2AYNbzP3pnAzv\nf8nGRrlhuCECv2UrURhOkk74phCOGuN6Oy2DtGRKbeTSoG73C4oOEmntt2UZ\n8cueEihg9F8gG7t76tiHTm02NOQtkBF3cv+cN0Pz+gHjZ35Uf2gRYYOwqjj9\nDoluucrSpVaLCqDFK0e/UWDfERtQ1fnGigjVxDH5+OXBn0kbI/x3K5kE17xR\ndp76aG/SV1sVB8/8Ia40DVmXG00Qi3LNzZ1MCdMNUIjF37DQgX8jibqYCFxC\nkQ/LBSSvBthC7tfDh+obOXlMS1PSlCCIZObm1jlzw95/82Km7TYcW3pS/n/V\nDrax\r\n=HfQB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "1ae85560a7e1c24e80aadd04a44e0dfa3e2699fc", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.11.0+x64 (linux)", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "13.11.0", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.13", "source-map": "^0.5.0", "@babel/types": "^7.9.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.9.3", "@babel/helper-fixtures": "^7.8.6"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.9.3_1584874969078_0.5614831890509511", "host": "s3://npm-registry-packages"}}, "7.9.4": {"name": "@babel/generator", "version": "7.9.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.9.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "12441e90c3b3c4159cdecf312075bf1a8ce2dbce", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.9.4.tgz", "fileCount": 22, "integrity": "sha512-rjP8ahaDy/ouhrvCoU1E5mqaitWrxwuNGU+dy1EpaoK48jZay4MdkskKGIMHLZNewg8sAsqpGSREJwP0zH3YQA==", "signatures": [{"sig": "MEQCIBCWkNVhbyiEAsdovfPBkwO9x/s3okmik4rjFOtewtOQAiBi25+erv7c3nymWCrHDfbpZnAILRWJbUlF240QtrtlQQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120013, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeecVWCRA9TVsSAnZWagAAwmoP/1Go6u1PcHZHg/kEOhuL\nkpKojmsPZuSJxsalQQXB3jjcIDJx35WkiHoZL3UPJoiYOTgV2wG8Js4h8qpc\n9Yw0GsFenxZ+xiBgiZWAIXnDHegC/+A0aBwwMecTAJQehax/ngGfFm42CGmF\nckBfnyHqQVUqY68yz+SQmg5/65wpCAjLz+f0KT+8QWZOkIfOrpO/zU6wOv6b\n8cypbF4OAAvfJvc76pWvaSfNDGDuy2vdplFWO7i8Ezk+Wo57sV8LsDKHnoMV\ni/UJh+9HKQwpu4qFxJfe3neF+0ApkvyKDSZmFs8At/aaNalLcM/xhcOMWbBh\nPYPdFtNcqn4DMj1ghFahs5QPeQmmOG4O9L90ney06sBrgUeqDVelH6uQm1MI\nsLWYpA28pOQ+nHwVjc7YZybbx9g6G+kpiNbGXxfoUjfSigoez6ya27lQbdGP\nNy6lPyQmh9YJJreqJTwasK3B8Z4LAwrhMM5LWXOsSgYZQxVifng3C1nYOXaQ\nehsJ/xQZvkx6eb1u/bk6EJDvSwF7Qs3OIpwHuajXfI9XN6cRvqsDEn8dUW09\nBdvPHlppjFmNFsd9oxD3r3FSDhL41zHwmjYfRQI5FnAWUXT4U26NJQBWFYHk\n0AcnGadv9KgYpLVaVDdzufOvkF+VypVMqkQxKqRQLI1ijl5VGu6Fb1o5ABpc\nuRcm\r\n=UZyD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "d3cf5fb5f4b46a1f7e9e33f2d24a466e3ea1e50f", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v13.11.0+x64 (linux)", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "13.11.0", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.13", "source-map": "^0.5.0", "@babel/types": "^7.9.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.9.4", "@babel/helper-fixtures": "^7.8.6"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.9.4_1585038678107_0.21067757783689922", "host": "s3://npm-registry-packages"}}, "7.9.5": {"name": "@babel/generator", "version": "7.9.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.9.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "developit", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "27f0917741acc41e6eaaced6d68f96c3fa9afaf9", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.9.5.tgz", "fileCount": 22, "integrity": "sha512-GbNIxVB3ZJe3tLeDm1HSn2AhuD/mVcyLDpgtLXa5tplmWrJdF/elxB56XNqCuD6szyNkDi6wuoKXln3QeBmCHQ==", "signatures": [{"sig": "MEYCIQDLUmMc+JtomyBx4zCX1vLsNeMY6nUShwN4XzWnsLCvUwIhALiq/C0KsLOD0j8LfJaR9JOpA86nHMpy4cbwrzQQT3BJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120235, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJejNOnCRA9TVsSAnZWagAAW8kP+QGluZjvs4oJrrFX0RFk\nDKtHMexN5Sn04rXjWEFst6+/EEpibzegpwEvQyp4gUgjaeCNbZVvH8/lbpcP\nhiZdjhEPrpABO9MtwBzcFNzZx+amUst5Vo8gddAX2Y8xoRXcPqJcR69G5plC\ndJc+AaB9NllzCcevrPSYgFK9VMnQnHyR/rgGDd0RFVxBnQhlRfPUD421zvf7\nSo3S9nfs/2S9BdG5ei8wdGHQRFEaE+f/QH5FogNGqupKh0Mq8NotnXUymcRZ\n6mgEii2f5TtBtDY69xRKvO/mvBIduRRu11vowTXkwdy61AuiZ12Jy7ntpGm6\nGzxEoEXU5Qs3tF/vq9acLqK9bcxZPkBIYlJGI8o+ke1Ki4kkpF+OOXpdNr17\n+LGOQ66vB4kX2r4JzGJPHmnMdo3ppsW2A3lpBwzoLUzlHT8Kci2UDIO5zO5v\n+dAsdZggnHV4NHP7MFWJRQuvm47UM8mawUTCG+n3nV4X8f54LvZRGWCQ4ts7\nTiA81uifsG0ClV8RirY0NryRbYJh9kfUXzyTvtLaaCR4E1S0AbKhebPcBS8F\nLep+qKfetJTJ15L7qBENMZTpkmE+ey/yguwjgKdFqVkHb+szvl88s7x0Lp3w\nhfwlbnOOGY6q9v3duyhDR6r3npPUoHTm4uY7cTFmea5Ftzmj6hvdzlEphicW\nK/ev\r\n=xo46\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "5b97e77e030cf3853a147fdff81844ea4026219d", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v10.19.0+x64 (linux)", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "10.19.0", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.13", "source-map": "^0.5.0", "@babel/types": "^7.9.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.9.4", "@babel/helper-fixtures": "^7.8.6"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.9.5_1586287526558_0.013770238398856272", "host": "s3://npm-registry-packages"}}, "7.9.6": {"name": "@babel/generator", "version": "7.9.6", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.9.6", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "5408c82ac5de98cda0d77d8124e99fa1f2170a43", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.9.6.tgz", "fileCount": 22, "integrity": "sha512-+htwWKJbH2bL72HRluF8zumBxzuX0ZZUFl3JLNyoUjM/Ho8wnVpPXM6aUz8cfKDqQ/h7zHqKt4xzJteUosckqQ==", "signatures": [{"sig": "MEUCIFQlRv+o7IeT9/5gZqHWDbMNfIsA6upHALQ85weVc0J7AiEAk7F2/vvyJEDeN+8oaHpKSWx9fXTP6Z4yTpKsNIkkerQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119286, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqcmUCRA9TVsSAnZWagAA1PEP/0mLJp2VPKAmnfX0l2lv\nKBikOdf0BMNrrrZuguI3UbO6ScSHKIsXgj7SelBI8DeKmL2laugP8lB6dC7r\ncJwJ/NMj56IhqWpdQR10ntDE6AIaTw2Gji+676BP/2Dn2Dwb/YjcjsKhOZPF\nmi7EEb3ikUni0kxG88ExmpGobl+oZGdbmkDzj66Ivl2u6i0zp0BpGtQ+wHWN\n47pkKSNK1tGkjjqWp/jBKr1HS7YbZ2nAJNEUzCv4+V/yq/Or3ZOYN4pbaj7b\nolaYMbmkAeYPzCzlGXoCAdyaNdcyg2ou2CXyzuHzKwA0Ni6IuJEmP+Nf75Yx\nKn3WqiL8IhZHZay+BFiki/bIiIBKaOu2t8MC7VlvPmUlXvu1kteOg93Tk5dG\nuXiOBL7r4zFL2CbouXZ9YRqhYvS+Wn4XseqnjxAvQVO/hA7mSJu+Y6mWb31o\nlCcIHeIIHOJAdXQ8FyQ+ooW63R5bH7IC0wYGN/x3zjnIDmdnGsOWiU+ZNguN\neEWiLHVFN7H5Wm4LU+bumqB5g8+5BhzJXhcJSjCVjkpzoX77GXiF7DFc9lEB\nQFkoH3sLEP0ZDrxOR2AXAQqmfhTrVrV+hE5MaGtD7yZaX6c+4UWJHN0f7C/p\nf22/Sbu6mAScgiZlJ/wu57vJJ891GieEyG+nniVv+U6c2/BDl9duNDbQGthL\nBT0F\r\n=/6yC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "9c2846bcacc75aa931ea9d556950c2113765d43d", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v14.0.0+x64 (linux)", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "14.0.0", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.13", "source-map": "^0.5.0", "@babel/types": "^7.9.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.9.6", "@babel/helper-fixtures": "^7.8.6"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.9.6_1588185492151_0.1521591870516663", "host": "s3://npm-registry-packages"}}, "7.10.0": {"name": "@babel/generator", "version": "7.10.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.10.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "a238837896edf35ee5fbfb074548d3256b4bc55d", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.10.0.tgz", "fileCount": 22, "integrity": "sha512-ThoWCJHlgukbtCP79nAK4oLqZt5fVo70AHUni/y8Jotyg5rtJiG2FVl+iJjRNKIyl4hppqztLyAoEWcCvqyOFQ==", "signatures": [{"sig": "MEUCIQDL66T4dcYrWYPPwa7H1C3aLwFWQ7esK/GjZ5jdoO2u8gIgEHt3EcwnlCt5qFDEpILcB7DNXtJqFV9wSiullDE3KbY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120135, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezY2JCRA9TVsSAnZWagAAeVYP+gJO/YxfI01MzdskygaU\ni1QOVesMeakxO3+SnDKCdeMPenhI7zV8a70Uk8ejNhOb71lTI49INLw6m5EX\nStxUoZe9NRbJc0BBM4LDAl537bVDxaaIE1nfFlCTke2q9+7zVs4jT4ZLvx4X\n8nXAAjQnX79ZxecebEmF1PWSClCjeDf8BO2yLtcu+O0bYhakZK0JL7daUC0H\nqy0wiM36upUJig4qRhIFFjbEFXCptFgU5pH9C5Bq4s9Lr7XPmJWFFDJIUK2S\nW2XLF7/C9v35tHg4uitkPkkDJ0IlyQ92WpBqZPt+0r9Xbfpt/a8fAZZM1pgS\nFE0jLOiHbAq9gRLHRADTkWa/H3b7IGUaPor+Q0GupGJp8UR1Og/RtySqtSpg\nwHWS5Xyew3KbCRQL+1LWd0gc29MYl5nxavEFFC6xjG1pvS8nChkQ40gWpKne\n9GF3j1N/zTNVLy6m4v6At1OivTdWYti5Ba9fgEufML3UwYWuOedMHmkpl0mC\nf2IElLD8XkhSmHJgDwbreNrWirRpgETeJ57aProTsuT6uLkYIJ/dBUBUOV83\njC/08FUqKx3sc+SfXy4txnzO8F0F2mm/NdBgxXtt+j3bQeOsUGMwtz8fpJQ0\nxSX0ZJ2M2TgIKwLE/cO6mw1vzuCHQCc8+00vPLv7e5qEsGWBWHJTfd9zcHjv\nNL8+\r\n=fFE+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "5da2440adff6f25579fb6e9a018062291c89416f", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel/tree/master/packages/babel-generator", "type": "git"}, "_npmVersion": "lerna/3.19.0/node@v14.3.0+x64 (linux)", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "14.3.0", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.13", "source-map": "^0.5.0", "@babel/types": "^7.10.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.10.0", "@babel/helper-fixtures": "^7.8.6"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.10.0_1590529411541_0.20222048971555906", "host": "s3://npm-registry-packages"}}, "7.10.1": {"name": "@babel/generator", "version": "7.10.1", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.10.1", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "4d14458e539bcb04ffe34124143f5c489f2dbca9", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.10.1.tgz", "fileCount": 22, "integrity": "sha512-AT0YPLQw9DI21tliuJIdplVfLHya6mcGa8ctkv7n4Qv+hYacJrKmNWIteAK1P9iyLikFIAkwqJ7HAOqIDLFfgA==", "signatures": [{"sig": "MEUCIQDJQCrmyaWHgJbhQH+dQsuS3T40AiurskQTpvbxcz5c+wIgCE0KS8i1Jr2LS/pZRgVHzkYS5cGj59eU3Lyy2QoXjXM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120184, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuS5CRA9TVsSAnZWagAAwWEP/2RmIlAWX92+cWDnGuqe\nih8OcVD3UXqP2dZKEnxnexzl/LvM1YDdGVE0ZYqtvEbXdOrSglLT8hl7WJRF\nfMIBhFdKqkWrIhKET/2+jw0i56nFwTHsGblaj4fn8AJOpvbXoFZuLuoCmlWR\nyEB6iPvMPwGzi4FKzgLRu7Hkg2wJ7N7TqdRFjZQ3+XI5mzbtdeDAZsOjAag1\nsQt24YqrtTKJB5t1sliq64VCEnrNtJ5h1isKgKjGg33KWtrM+/Eucn3CSPu6\niXBteg03pd9R21Z+DW1XGHVTnhqTubKnQ4l/mC2sDO7Fqiisc8aQvTVqOOi9\n0sgBxP54DSHUMo/Zx9PHaCtJfDyXuU5wnVf03eg18hmSnTF8qiMx0eNx2Vwq\nQMAds+ZmXWCLUTKJ7ZbiajSTdHtljbaXxSna37Ho3HSQ4OTSzRzr6NBK7L25\nRZP/yyzj2EtHVPdG2n/yVtpHXhYPrqukPCmd2dWRHyaqd7YBMv+twhQp/jEw\nz9ZP33YckRUDxMw3/JExHH65FyqRoGcG9OrbOQLuktS2JGMDi1DZSCN4+XBF\nbnpjp+pHNN5Pgdw7LLe1WexLeLSuYTrkxa2nOXRr7JVkF8Gtz5mYl+pwr5UF\n0mwFmyd+EXR2xYnKn+cp/1aX4l1wtiB7xe+eoUPeFWpVVigI39P5SgutWsgB\nf59x\r\n=wIAt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "88f57a7ea659d25232bf62de1efceb5d6299b8cf", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "_npmVersion": "lerna/3.19.0/node@v12.16.3+x64 (linux)", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.13", "source-map": "^0.5.0", "@babel/types": "^7.10.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.10.1", "@babel/helper-fixtures": "^7.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.10.1_1590617273189_0.5074202945725941", "host": "s3://npm-registry-packages"}}, "7.10.2": {"name": "@babel/generator", "version": "7.10.2", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.10.2", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "0fa5b5b2389db8bfdfcc3492b551ee20f5dd69a9", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.10.2.tgz", "fileCount": 22, "integrity": "sha512-AxfBNHNu99DTMvlUPlt1h2+Hn7knPpH5ayJ8OqDWSeLld+Fi2AYBTC/IejWDM9Edcii4UzZRCsbUt0WlSDsDsA==", "signatures": [{"sig": "MEUCICtr++BVfHEqArbnivtk1fzgC40cwZdy7BO8aC6Iw5nCAiEAk+8Bu/aUnQPkGWFLqc0FqAuWd/RQpBzZ2VP5PUld3jo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe0rMgCRA9TVsSAnZWagAAf6oP/i+hFomYhzEfVGlo9mLJ\n3kFKwXoK7Vuc6uNtEfbOlVBZi65J8fUKxHhfgYqnuc4yPdkMCG1GxWVSLeK3\nvo0VFOAyr0QsepsvICMwxebZYsfHfOh+d4yE1jEPUXha2u9lNAJ1ixs373QG\nG4d0YPOUH2tpwfX2LVsc3ur9RqyJOXbhqCByxrkZ7bT6mpxLv8nvHSvCXRSt\nWk3Pgvv+AtLnVAok8RbfwiI/uO4JK2UDPGjJBPiskPtOlUI8DG/os7b9pr64\nxgJqHEpscBs1tqvQF08IjESr9c6PoDi7U6VVcBG4+lWVWHuBWkpYW5SRsr2h\nj01Ai+kIY4E1/TVCfHY20CpcKG+NqVXv0aWaDUnY6qHzFYHj2vEemHjC4bGM\npJVJo2uXzajX575RcfYHvoAGmKAWzxSVJLDl/VeBIMkGjP8ie442vNZW6XOx\nEb+9Mq63fspmEVPKn/D/r/1NOyAeBlDUpIX5Wi3M5mPa+4WgbUf9uWx/ao4M\niW6LqUZJjQZ5pOQHogCIcE5isLQIzIDVLeVOr43qczS011+MPk6vjFqOcSNU\ntR4/uK6IcaJmKq2jazTm6wx44FB+dS5u8lSZbYo7l2m4duTGejOjkU2Q4q26\nmPP2tzwbMgeM6FMEkXl07N5/beo328nZFU0yl7CyOTlbe8a+IxNOlgCh+fQJ\noaD7\r\n=1Is0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "b0350e5b1e86bd2d53b4a25705e39eb380ec65a2", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "_npmVersion": "lerna/3.19.0/node@v12.17.0+x64 (linux)", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "12.17.0", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.13", "source-map": "^0.5.0", "@babel/types": "^7.10.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.10.2", "@babel/helper-fixtures": "^7.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.10.2_1590866719774_0.5046610767245261", "host": "s3://npm-registry-packages"}}, "7.10.3": {"name": "@babel/generator", "version": "7.10.3", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.10.3", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "32b9a0d963a71d7a54f5f6c15659c3dbc2a523a5", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.10.3.tgz", "fileCount": 22, "integrity": "sha512-drt8MUHbEqRzNR0xnF8nMehbY11b1SDkRw03PSNH/3Rb2Z35oxkddVSi3rcaak0YJQ86PCuE7Qx1jSFhbLNBMA==", "signatures": [{"sig": "MEUCIBsHLaWptAcP3ivNW67wHyrfCiYmfv/PRGymiQYei/2lAiEA42RSlv7Ke6K8yOnCTWdVSfidnlnpQoDaoWZNHJYjwHk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7SYCCRA9TVsSAnZWagAAQ0MP/ipXYwdRs1D/eakUAkE1\nbAZReFRZGWs6bxmUqYwv5urJLtT4dYLEaAB71fP6610Ru55PDbh+oQKNGc+F\nOYZhyu7EyYx4Ov9FG5OongjilUzyoP0YodQR+/fqKhsgtQ/XXydjbI5FI2xY\n5QLn5ZiKUJ5kfiDbY+fWhZFqI40YnyvuuaSeBwYOAgFlK03yTbQLruuRg7xG\nDU0mgzAQRoqrjPClzvMq5VNdT6gsiEiPtjftn4DrLJw0maP9cEH0S90aEaSc\n4cNiq9UHQm5WYwiPPo/2o+ODDoIRcbR6SDdqcR2nsAoltj1/ZLxZMvd/c3Vj\nAZCrTFyBZF3CAoR9LsMkYa5tMf1rILXoXJrtoQPIUKsokg20npGSRk31WRy6\nMuQ44kTEmIGvVJNVaPOu3izXlI71/t158ubm9HvoIesI210q+WBfpzgZB/vL\n2ShBsgThfMc5Uci5Xc4tQd52EGGMyF3ruve3a66ENMvZRIlobI+qeRLk26iM\nCC/mT8Dmoap2Whc1BHoYOAtwTUzjlg/JG/nCgpf8fN+XCRkLHweSI4rPcoG+\n0W3Ii4xIBxgIagwjT4Ix9uP1x98/x7Yrwd9DkOLAz8OAZUAISP7fG78bN+dg\ntvZ3A/twnGYx1IhqLlzLqJEHV5A7URqeW4ufy+7jXfPKdt1QWM7gyWtPmoNB\nNHtn\r\n=l7XD\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "2787ee2f967b6d8e1121fca00a8d578d75449a53", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.13", "source-map": "^0.5.0", "@babel/types": "^7.10.3"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.10.3", "@babel/helper-fixtures": "^7.10.1"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.10.3_1592600066276_0.5194185811410033", "host": "s3://npm-registry-packages"}}, "7.10.4": {"name": "@babel/generator", "version": "7.10.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.10.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "e49eeed9fe114b62fa5b181856a43a5e32f5f243", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.10.4.tgz", "fileCount": 22, "integrity": "sha512-toLIHUIAgcQygFZRAQcsLQV3CBuX6yOIru1kJk/qqqvcRmZrYe6WavZTSG+bB8MxhnL9YPf+pKQfuiP161q7ng==", "signatures": [{"sig": "MEYCIQC/8lZnvwg4uBMozYvFUrD2jlEZjkp0nteIgFMc0bIN+wIhAPtySqYj3tJvmtcsRxgYrbwNutChUP2j+Jmep3NGFCiN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zovCRA9TVsSAnZWagAAPScP/3DF958qU5XCVollCrOL\ntK1eyGwSs6mU8ouCd2CvLlSCgDERUsCOFIPt4cjNxR+17C2RemCpT2qY9EOk\n6nK8q0B9XPJ0j2wglH2jAIvfuM7d2anvgO1q8cqopWpaDrFxXwA8HTfpvIi/\ndbksKKNjvCFsmdWRj9Fc2gZ5Dxqc303fpHZ7hI1oqQki0fX2AcfFwt8sTMGb\nlivnwEDyySWrEgzkPbBVcCPzGhcPqEOX89r5EptN5zXl9aejpwnzwi+4aFCd\niX+2auhQYVSjCPvTQfmHs9ogDvLHVGKtsnqyNAByPBH4BFzVUcnk5Si70JrH\no4M8nwcMAtEc7PIoiI+jiB0rSlUJzlbgNeiufGkk8w6aBOG68uesB9GP658r\nOtTYpxAlNROzfGjl+m64cmxfszECqOaGeFspUXsMPc+AsIgOU8zdcmBqlScL\nKxDUfuV64qIB6gLN32hWvvCzP2fBkeLJ17bM2gFow/DaOZt+pL2JZI0B9vl/\nSIFfBKrICFE0JipA8wa5hKlfMsrbSoAA6JxtfIqiqFi3SE6L7bP2xwonkOV3\nuXMx2bMRajZuIKxf+q/vQGePvsbd9BZG5vcBJiAUqkh6Y+EvmpO8VMJHGy5G\n8D8YJitUJKjgqfK7DLkzigdmfAZrcAobGSesudX+VMOWV+iH7nyj5QXTKBdN\n3aJl\r\n=jU6S\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "7fd40d86a0d03ff0e9c3ea16b29689945433d4df", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "_npmVersion": "lerna/3.19.0/node@v14.4.0+x64 (darwin)", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "14.4.0", "dependencies": {"jsesc": "^2.5.1", "lodash": "^4.17.13", "source-map": "^0.5.0", "@babel/types": "^7.10.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.10.4", "@babel/helper-fixtures": "^7.10.4"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.10.4_1593522734694_0.1742150239289566", "host": "s3://npm-registry-packages"}}, "7.10.5": {"name": "@babel/generator", "version": "7.10.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.10.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "1b903554bc8c583ee8d25f1e8969732e6b829a69", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.10.5.tgz", "fileCount": 22, "integrity": "sha512-3vXxr3FEW7E7lJZiWQ3bM4+v/Vyr9C+hpolQ8BGFr9Y8Ri2tFLWTixmwKBafDujO1WVah4fhZBeU1bieKdghig==", "signatures": [{"sig": "MEQCIEUwNTEOWnWGYEXGdK0uec7jVx5Hs5TRXAWKKaP8WE1UAiBbAa1g7e2VwZ9Xm96Irdq0NAS5jQzEFxkxwivEnrN8nw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120377, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDfbdCRA9TVsSAnZWagAAUP0QAI/HtATYxK8dFtOXUc/1\nLG9YChXRsWrYv4b3cIF77lgclPzXFtmz7l6d8u3Fcmijt7QyfPe67kjUX/0c\ncFUNZLq4dhsYr49FzVU2QFdrGDzodk19MFj+9a2xhnD0jj4Ljt7NhHmtd/la\nBSQm7WRIecK4tWErHKO6WqvPYC2HttLlnCp45uzHpQwlpD+1NJWte65i1r92\n5DtVSi/pQ+sAFKawo2LH0a8Ry8/Vt7PQ8AeQ+lO6DaBSeljL8wLWA1v0j84V\nOtrUFBuojy6BGuS/YoLze494QyceZgNkumqqRWZPuBIl/qRksrYAUUkQXKNK\nZ1bmezOP4TO5jjN7diC0wXABBpNaf8gI6JC/KnW+tTdVVQLOlxY+kMmfrB43\n3OobYMetBeCxMgABOHLoIN3XGin9Ppv+IJVG7vHlX7Yuojowm63tWa642NE3\nNJO0uPKKMQuEJkLMv/3a/LsIHy3gmA7cyBikA+wJVoVHsScWGuQN7i/F10SR\nI6/r/JS/Cbywt5ufGh16pVciPbGv37C/BfR56MO5pGPY45p8C/1r5elKBa7N\n+R8etItMerZs6uuLke7swIZ9h+CLMqvlpArSFgrXUzWnaO4uUdZ1TxTHCrO8\nZHFu6EzZb66ROw+9JKLYVIWQlgxb5iNlgIfI6TVnQIVjDk9kbAtYhf2jVADU\n1C1+\r\n=0Ht+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "f7964a9ac51356f7df6404a25b27ba1cffba1ba7", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "_npmVersion": "lerna/3.19.0/node@v14.5.0+x64 (darwin)", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "14.5.0", "dependencies": {"jsesc": "^2.5.1", "source-map": "^0.5.0", "@babel/types": "^7.10.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.10.5", "@babel/helper-fixtures": "^7.10.5"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.10.5_1594750684779_0.10941349804913814", "host": "s3://npm-registry-packages"}}, "7.11.0": {"name": "@babel/generator", "version": "7.11.0", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.11.0", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "4b90c78d8c12825024568cbe83ee6c9af193585c", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.11.0.tgz", "fileCount": 22, "integrity": "sha512-fEm3Uzw7Mc9Xi//qU20cBKatTfs2aOtKqmvy/Vm7RkJEGFQ4xc9myCfbXxqK//ZS8MR/ciOHw6meGASJuKmDfQ==", "signatures": [{"sig": "MEUCIAMZ7ksOZCR+5m5iuhqeD73GRlKvr1kuG1xf5SVlA/gfAiEAu5iI92WFulpCn7GFvkGuUvswofsn+md0pk3CR2gHzwY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120850, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIzrmCRA9TVsSAnZWagAA12oP/3O+x/5w+6E3cczLvg5O\nJHX2Y72uE2eq13UMpCUtRo8PCtbfZFbfjkWcejJRRsIo/6huukl1JR0SHJBm\nf48eMRxsuLyXqGvujqrBgx08Qjyh24FVyybogHymobZN2LQ988QZ9lTYCqVI\nrdBI1hX3bU96KF3C/0V6IZpgYGv5lVkARRf5AmD7+5dG/1qoeuxzSB76Z4T5\nsimcyySjMS1Pw1X+tgvVZX6V1yV6qqWRR9rw8H/+cs/7bsNJ8ZVeHYVVVH15\nRR4SEt0hS9aGWHvE/oF50ukWW21FiVK46gHJnfVa7uJuxLIvESFq4rvTNzwu\nBmaSJRnYAtTrJncoTvedvP59qT2bFH0lxi6hOSQbJ7ZD/1l6nxhh7rueonlH\nQwAuyo9dV+nW5p+K28Z7C1R49briSOhGbhh/5PgKvNjczjHrjXWz3s8lhAu4\nuvUL1EIXdg7+QJKHJU/OvZxwG7sH+8kzrbO5mdUVvBO2gzIKyehnSrwVrMgx\nLxm8mk2F7QZrH4jJppmlkhKUpG5OWfZcxFOM+/JjpYxByqquWV9OOpn6tEcS\n6TxPQmiuLaYOOIxVOsrfD1QyPwv5Xy0kbiU1YaJ2FpdXTWblugpaTfx6hA2I\naEApRhR4rs9Bw1CjBttn89SPpdnWvRrIS3C/i6fSEBU8xL3hxK4+nlyTwQaD\nrNBY\r\n=Gxwi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "_npmVersion": "6.14.7", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "14.6.0", "dependencies": {"jsesc": "^2.5.1", "source-map": "^0.5.0", "@babel/types": "^7.11.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.11.0", "@babel/helper-fixtures": "^7.10.5"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.11.0_1596144358041_0.535782336467185", "host": "s3://npm-registry-packages"}}, "7.11.4": {"name": "@babel/generator", "version": "7.11.4", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.11.4", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "1ec7eec00defba5d6f83e50e3ee72ae2fee482be", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.11.4.tgz", "fileCount": 22, "integrity": "sha512-Rn26vueFx0eOoz7iifCN2UHT6rGtnkSGWSoDRIy8jZN3B91PzeSULbswfLoOWuTuAcNwpG/mxy+uCTDnZ9Mp1g==", "signatures": [{"sig": "MEYCIQCrqmPXzaoRhHieATcJpUhVonPAQYxzGjCFRDNPg+5LiAIhALKoxopK0zcTnr5xSPkLQ+V4osop7F2Ff0FvbojEBC5A", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120945, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfPsgjCRA9TVsSAnZWagAAWSsP/Ah+u8U/VcfsFQb+Qq3z\nxBCqbBNo2qqyKJG6DNckDmSDYjlJ74GWVzfzC3lw4h4SJfL7K+0Cz6/fAg3L\nvtTNYXIn3M9GPKld2YzSOPYlZ8RDZylTYcu8LgO6JbO/Sng3HuPgGsRklQfN\nK5bGISPFNn6lxjWW1lZXEUUzcZ/VZor01bvmBRYl4T5ne+gO2kMHc0d+X1zD\n3AilTu/DjV38NsrevwIYiITkRhWvGfqKd7Jf7IKo4DDEzdUoYZm964Akclpf\nyiiv//fknAVFh0eHgrhjEl0Mpkv6iiTJTgAamLlUwVBjOA0RccKhy/HpSUVj\nbhCqSr7DloWU8ylBTNDtWa3y/eN75FokdWD27ZTpBbH5JCxS+SpFg+VK/VJ+\nEutigS7dB5g49srq8IUYj3Jq070QkkGrNOCGHSmEg1SyCmX8V90+cvdx6JPa\n2pFyoVP8XdmRxAaUPtQXSNZsnYpk4EsmbhVdmMJC0eeGeRL8JIuJ2rfafzXz\np7VdwYeoM91TMVwu3ean3sbK21Sha99+2DpeslwVpYHP0+4l0Fj/BIh0lFPi\nBBFSLKnrDVr1JO1cuvpPm/3JROowxd+nXNpMKBmGoSWNrEsI2sS94zjnHq4n\nsk2F1ydApfDD6telPKKEDRlrLn74WwVu8dBE3d5j/avV1U4EwWmocVpz9a+3\n682n\r\n=als4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "90b198956995195ea00e7ac9912c2260e44d8746", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "_npmVersion": "lerna/3.19.0/node@v14.7.0+x64 (darwin)", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "14.7.0", "dependencies": {"jsesc": "^2.5.1", "source-map": "^0.5.0", "@babel/types": "^7.11.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.11.4", "@babel/helper-fixtures": "^7.10.5"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.11.4_1597949987284_0.7568463606179425", "host": "s3://npm-registry-packages"}}, "7.11.5": {"name": "@babel/generator", "version": "7.11.5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.11.5", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "a5582773425a468e4ba269d9a1f701fbca6a7a82", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.11.5.tgz", "fileCount": 22, "integrity": "sha512-9UqHWJ4IwRTy4l0o8gq2ef8ws8UPzvtMkVKjTLAiRmza9p9V6Z+OfuNd9fB1j5Q67F+dVJtPC2sZXI8NM9br4g==", "signatures": [{"sig": "MEYCIQCkp3D8gadmlWYUWgPMvI2lUSRU1ZghKvMYYr0tzsYycQIhAPQKyDkWExvOOxjfEFL3IAl0lC31KeNCskp0Jw8Qzmod", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120944, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfTVdOCRA9TVsSAnZWagAARooP/jDd6jl6287XvUeHOKDv\nbUSedSt4hKCH2G/1EQsQM/vyLNlD4x9SanaEaah95hn7y39RdkBAEDQLWzCz\nZnfAeS9GPb4xgP2QGWX0p98qduOwFiyU9UX7rfPBd9a0nT0/UGY3kekh+UD8\n3d+x1CY/0GdqHA+ZHwMEdyQ/+KrydwXGaiJbN2DnXgsKIIuCOYMkFOGaFuq3\ntgAFNyNj/uKqnmL4W9L5GmIBlUYi+zmpxhh7T2qvjMiYpBaKYTrocF/JdgB3\n3L+2HG1SaQRlTaiL8F4k8muCZu+5aPsWoNP1Ke1Bc901ijNWxfcCgRnjBhGc\nEMm1KxXNWR6Z94eC9FdUSr3zYHM/ep8okawF5/o0yIsuaPJQXU62YLOusdHx\nUEybxI1JNpsX2s9sbFrgkFuKjM3+Nz8t7vPSjW/6PduvtBoH3rQLIVqC4w5L\n+CNROlYGPIZjyZK8/BLlMP3EfkAocRlXBklJai+rOUtEyG+RzH07skZTwmv+\n1xDiqayGNKWijVC94K/Sjr9irDnmK7nxLJtLh/RxD3j7MLUVMtEPtIkatyzH\n3dDp3pzvr2aQ35ogfhK/fTQkZmZIvF3dm8g3T8psWBw3w0bLWg2b6Ll8wbEW\ny/x0eCRGGgrXJv+2+pVueUpP+CPsDT1fQ6t98X6i2UKZ1SM7mPnejWxfk6IX\nQTqd\r\n=pbki\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "af64ccb2b00bc7574943674996c2f0507cdbfb6f", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "_npmVersion": "lerna/3.19.0/node@v14.9.0+x64 (darwin)", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "14.9.0", "dependencies": {"jsesc": "^2.5.1", "source-map": "^0.6.1", "@babel/types": "^7.11.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.11.5", "@babel/helper-fixtures": "^7.10.5"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.11.5_1598904141650_0.3431494812523157", "host": "s3://npm-registry-packages"}}, "7.11.6": {"name": "@babel/generator", "version": "7.11.6", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "@babel/generator@7.11.6", "maintainers": [{"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "bugs": {"url": "https://github.com/babel/babel/issues"}, "dist": {"shasum": "b868900f81b163b4d464ea24545c61cbac4dc620", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.11.6.tgz", "fileCount": 22, "integrity": "sha512-DWtQ1PV3r+cLbySoHrwn9RWEgKMBLLma4OBQloPRyDYvc5msJM9kvTLo1YnlJd1P/ZuKbdli3ijr5q3FvAF3uA==", "signatures": [{"sig": "MEUCIQDxiZ7BW0Z439535mPQCKg/i8TD+Nyiu8/euEy+aOphwQIgGnfZIo/DCC7N2KCOOe3gfDLDMmJ1mWgK3CfjzGJOl3Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120944, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfURGsCRA9TVsSAnZWagAADcEQAKE2hbZmiL+KmD3L8w2S\nUA1FLdLqI+LpLkx5d/iFywZhvL7KsgqGTlALWFAsrIEJkxQUEHLosW3eHSnX\n4AMn1eaTeuAJtgVgobq4Az3bMJzXBj/HY7poeKxgZ4nJeemwFocCu3DIbJT1\n2zvK9L66Ab2IiGTcud79Qd6y4taBwj04dvcAJOXD8BVB7JMV/h7jZr3fJqz2\nMv867sryj7XpD4erHdbS83EyyYdG/3ROd+Ml+m0VskNKubvgijpqTa0XY+YG\nTnSiL2rrLobMvg1WrtSFkw9VCpGMy+zqEbj/TZY9rhX8uJ0bhN0Jp8jz3DAe\ndoT6hYspOs68dQ3vmq7irxz+SWdchBrTDYHyM8kGYvaDBB+Y/dx6eamFVQgS\nYWeJY8IdUy3d6h7Q29OCLAmnCYTZAmhFkKxZ4EI2ESivddrn+0r58KHEK+2Y\nFM2syVlxmmT3pU4u6jAkaiYZnK53sS9gbfWB05X9RJCD6SuMWmTbS/5T5j7h\n67e9MkS0UCpEXOTE4ww1V94ceZWmeeKZFiDBr78HcIwIant9Jjg6ZuncpVVh\n/k2iGV5xYgxsV1TDm+0gHBeujLj5useagYiDyDr8nRsLQQDsYX+AX5/JTg2D\nxwTWi1hqKZx6p4gtRQZaKXNSdJvWufZ+JjyHYMokm+zhSnAVdKRTwI7YspUZ\nsZrA\r\n=fnJy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "gitHead": "e51139d7fd850e7f5b8cd6aafb17cc88b7010218", "_npmUser": {"name": "jlhwung", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "_npmVersion": "lerna/3.19.0/node@v14.9.0+x64 (darwin)", "description": "Turns an AST into code.", "directories": {}, "_nodeVersion": "14.9.0", "dependencies": {"jsesc": "^2.5.1", "source-map": "^0.5.0", "@babel/types": "^7.11.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.11.5", "@babel/helper-fixtures": "^7.10.5"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.11.6_1599148459752_0.7203516458623516", "host": "s3://npm-registry-packages"}}, "7.12.0": {"name": "@babel/generator", "version": "7.12.0", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/generator@7.12.0", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "91a45f1c18ca8d895a35a04da1a4cf7ea3f37f98", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.12.0.tgz", "fileCount": 22, "integrity": "sha512-8lnf4QcyiQMf5XQp47BltuMTocsOh6P0z/vueEh8GzhmWWlDbdvOoI5Ziddg0XYhmnx35HyByUW51/9NprF8cA==", "signatures": [{"sig": "MEYCIQCgmzzBNhJfxy4DspaGZKmq2l2H51kqWLVxrm4aWvwlMAIhALqF0Q3eANbN5lVu0YtmbzldICPbOWQ3zZhd5DzShIUt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 122170, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfh1mBCRA9TVsSAnZWagAAJWMP/RuYkUlQK2tUldLDdCH2\nRiNdX5RAKuJcXtYe1uCjEfYP/eNiUvNcAjxoihSK1xEEg7SgyBrgisAKV2s0\nE0oaiNLswg/vhik8QNOeMCDdpuT2Rhr7lRF14jod42fXA6XjAvH/xYI1vn97\nbU5pZXmIr/OZfZ8LuRKGg28VuEJ1l5bRHsvoULspL66HJ/TQ0SfmSXotqiBK\nsj2SaZBABDskztlv2/lCiOmGRSshH9dtkR1o8af5E9or8xnFdgxKTEvjIq3X\ntnZU1xnSovzcfl5YizBCksoGGrZHte76ZhnXZh7AClamP8Qr7u20hb7blVg6\nz9flDARNPod3ij61LwnKw6ckZW4aSN9mhD3jYgdkg3oVnPyaX2srkfwL5K0P\nBUBueYZk7J2wRRW+Un4KyjMS1byt7aPtw81OzqpkfEKESqdCSETK81ReymYK\nfDGdqYYLr0iCwb1o0Z7nIZAeveWNTHtEANUmHZ4FJbXh/vuh84NQPvYyldsZ\nROxoFDvAB1vqAZWycvS6BA16015fqjf1wySHRWjrwZbA1gclrJA+u77H1yJ9\nBYSrGHhuXT+kN4bsg0bUhRAm/tALp2a6DaB+18uvIWOkjDzmZq70ugg6sFFj\njWCPuBOWZm/7+WO0HPH7LZf2s3oqVWNzaRG6SrGfhYAW4U2fOgjFgQq2YXHn\nTYr5\r\n=ikY0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "source-map": "^0.5.0", "@babel/types": "^7.12.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.12.0", "@babel/helper-fixtures": "^7.10.5"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.12.0_1602705792511_0.15715835847652992", "host": "s3://npm-registry-packages"}}, "7.12.1": {"name": "@babel/generator", "version": "7.12.1", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/generator@7.12.1", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "0d70be32bdaa03d7c51c8597dda76e0df1f15468", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.12.1.tgz", "fileCount": 22, "integrity": "sha512-DB+6rafIdc9o72Yc3/Ph5h+6hUjeOp66pF0naQBgUFFuPqzQwIlPTm3xZR7YNvduIMtkDIj2t21LSQwnbCrXvg==", "signatures": [{"sig": "MEYCIQDs6VZSAv2sFTLgasNhDtQ4qhzFbmk9sB7HgcaaTc6AKQIhAIl4arLvxqIZgrIzKyXAeC4FikvaH0QBQ+TmVj7GdS3j", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 122880, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM/zCRA9TVsSAnZWagAAdFEP/330trIb5Q1vvslqM2a2\n3KK7Iu/eG7BG5SxJMDH7wbCCCa5BiCDeHuKsR8CCCKB+WMbp59f0Mkv6AFIq\nXB7a9XGnbiVJ4/P/mr9mWEE/vRRqJG8z/M3LD0xWWItsjhKjmx5BNady/Lg1\nrLoeDznK6q27CHfya1C+qomccxIZCNhr9zuHFz9zcavVLPVqbWcTVyLj1FSQ\nNzjC3wFIM9F93b9wd9emisaj2hrGn8YJNDrWPEWhlNcUvrgxcxJBqeWa8etM\n8svjYkQJbBdM40oRuKCN8047h7vTFedvEx2WA9l9BtZMxI1s9k+Jd1MNHvRc\njd3wAFsG5cVBMUqhT3lpGePCPvwraOx4b62WrRa9yXbM4+UKblQNsbcZRyw/\n3TFijug6NxFIZ7ogBJWkD/clH2bjMKpmblz3V2+wIUPW4/vJY6A9dUZRZSWe\nOVPBW94bao41M/+DRl45lEjUBSrTKrqu4ehgYcgYVCr2phVJL9v4tcimfYEy\n1fEGbUNxKL/ubDj1IUKwWJ+RQc5ITEEOW+r+ne5k69QKS8xG/R7wpDA/vVd8\nULDXoKMVOdkl+9VpR0CLHFKYWTc8mDcPEqPULTz03AwzmWr7GI8PS0LhuUHB\ngz+muHX0QlKtxT+R+zQp7/mVYHIMNBrOF72e0tiSBk1MD6YyfcuitxP5VUpk\n4T0B\r\n=XWE3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "source-map": "^0.5.0", "@babel/types": "^7.12.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "^7.12.1", "@babel/helper-fixtures": "7.10.5"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.12.1_1602801650898_0.8086634030704363", "host": "s3://npm-registry-packages"}}, "7.12.5": {"name": "@babel/generator", "version": "7.12.5", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/generator@7.12.5", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "a2c50de5c8b6d708ab95be5e6053936c1884a4de", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.12.5.tgz", "fileCount": 22, "integrity": "sha512-m16TQQJ8hPt7E+OS/XVQg/7U184MLXtvuGbCdA7na61vha+ImkyyNM/9DDA0unYCVZn3ZOhng+qz48/KBOT96A==", "signatures": [{"sig": "MEUCIQD03Pcy+Cz1SgFxkkAuugD/3lMxUzqffkyoL4X+VaY2BAIgchgPM4aNW4TeUDu5MmOX2AB4eRV2KScXxK+TDvCpoDo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 122990, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfodr1CRA9TVsSAnZWagAA5oIQAKGzu4qoNYgbSEuGb1Ue\n2Q4KV1JM2xf5hjDpyYx3fEWnF/lS8p8xIi1VbSUGDc2NSRbiLggMisCH6Nqh\nqfIK2md4ryCkarRNJUOW3EDeH/Oj2CUc7RMYwJ2F9s9UiT6YeniSFG6NZ3Ut\nwYeS/tUX/FRXOcj69nSau0toAfZcUqBYhsrnBhDelTPVcRSAiMPfV4lod/ug\nKcVr3k5iNIspcX/G3y6YmoyLIwXy5RZja1aaiuXaQ/Jy4TyqWVLVcbGYdI9s\n7EmUsikWIkp3/0NimETmCeFERH7ZzFQK5JZxkScJ8IpeVA7WtKsyRUGseOti\nM1mfQI0EM3naybESO+qaAFGApLTlFiVXflqd3luHZQI6Z6xj6VDhhnnIK8FJ\niE8uVKguPjwzEzKiLuz/EPE1oiyPxqMRE8UFu9PmSLU0hDzuAey+T2K+oan3\nh8zsS8CcMUzW8d11Ye5O1LPMk4VtmIcI4ZViDzHIhu1rmyeq0d0jizgPgw4T\nUe3Z/8ZgZbgFiZo3Bw6qAECYNggvwIUZxwk0xde7Jr+C8fo4OrsaaII61cdm\njvQcKrDjAG5eY5EOlhGn15mOhYdEBW9gOlJEYBlJyb0OGgVSS9miMYXvRjZj\nTQ55FtDTMtrTHRIkg3rarNwbXg49EuNpaYvULrZuXCevVQC37xYDx1VhArBd\nN+zA\r\n=VJga\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "source-map": "^0.5.0", "@babel/types": "^7.12.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.12.5", "@babel/helper-fixtures": "7.10.5"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.12.5_1604442868854_0.2792847861942034", "host": "s3://npm-registry-packages"}}, "7.12.10": {"name": "@babel/generator", "version": "7.12.10", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/generator@7.12.10", "maintainers": [{"name": "jlhwung", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "2b188fc329fb8e4f762181703beffc0fe6df3460", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.12.10.tgz", "fileCount": 22, "integrity": "sha512-6mCdfhWgmqLdtTkhXjnIz0LcdVCd26wS2JXRtj2XY0u5klDsXBREA/pG5NVOuVnF2LUrBGNFtQkIqqTbblg0ww==", "signatures": [{"sig": "MEQCIGHdlTP7oYx1tNUFH7GizbKLFh9g6pJafRRGjtsyMBLIAiASvFVTkzS1903sdDC06V5iYxxO3+3M+0V8GUS1kYbCzQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 122990, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0VQjCRA9TVsSAnZWagAAp2QQAIEefzgRj/ME9SKlA79g\nS3Z3r3TjjetxeXBi7Lz5N8oeudOV5+Jq7h0mQzfV+EsM/WmY9EXiv4uYs258\n9EOMHnHE2I60zkSnByuui2Q+DuOcDeD+80soIsd6qqSr6elwvW5MeA/3sV0/\n1vynd75yd5Dyk1Whv8hUdABO1CoDSD4T+gO2zEHtyW87M5AFcKI87b4RvBwi\nk+Z4oKSV+HbKWJtkdAPF4iu5J3+FCC2EojWLT9TBAGdpq9Oaqe/gfVsI/T5Q\nc196HMCsD3lYAK7vrgI0Spb/xjFzGozzogvzEBeDw0/BXIgsPwkiiWgeRQAA\nlc3T5xpWMDilKE2DLHECRcTFimzqUsMHrOEoXQmaRxrOawE++Ajpw9YjV1J6\nsl0J+mXclG4/ta2zj6l6BqAuEFDNEYeb40BzuxjKppWwYkgLYT6vIQWnj/Sm\nfYyRG+GR0yIzDwZvHiun0UpsjIAwqFSMfi6DCmYEPTARndIS7NeMHOOH8LER\npcgS9Uhfq1yLhecbI+fwMyA0pygOcnPN+54UNErHaTvejJlI6rvDpd5oc292\n4UeoCTJoudzqMCSZEo6kn1lMkyTMFJhv6VJiRhQn37LDWGvb5d24u8GcoEqc\nQ0EVrl08Be6Zyln3nC1Hn8f5QwusPfB6nhks/gFgUEMRs2Uib+jocQOzX26M\nhCEi\r\n=4zOo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "source-map": "^0.5.0", "@babel/types": "^7.12.10"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.12.10", "@babel/helper-fixtures": "7.12.10"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.12.10_1607554083084_0.9860813810920381", "host": "s3://npm-registry-packages"}}, "7.12.11": {"name": "@babel/generator", "version": "7.12.11", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/generator@7.12.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babeljs.io/", "dist": {"shasum": "98a7df7b8c358c9a37ab07a24056853016aba3af", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.12.11.tgz", "fileCount": 22, "integrity": "sha512-Ggg6WPOJtSi8yYQvLVjG8F/TlpWDlKx0OpS4Kt+xMQPs5OaGYWy+v1A+1TvxI6sAMGZpKWWoAQ1DaeQbImlItA==", "signatures": [{"sig": "MEQCIBpDKQiDXQyKSjS6PjcIQ5uFXkiny6kN8fGDs61MJwklAiBulgfRwhxFgCiFAW+7VNZ7P3+qajTgB7Y1rwpuxdME/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 122988, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2U3jCRA9TVsSAnZWagAADX0QAIa9elsJWxLJOK8us3GG\nGXR4SDQ5rwKhlru7S4IrEL/seuNKGDzS2uP/RnJ1BCI2yBk/9ezsKuaxe2L4\nH1Di2RUx+mkQeAutC5ZjjPSPoVqvXhaiqAbeU8bl7/xy7GP+sLlQXPIuBSmr\nwIVDq9HXpLwUz7DxpFvJjpM0Z/7ReeS9vL0B62DkMunv2PRRmdHJEaBXzQVl\nOOIv9MdAAo0Z6UW+44C6hcPkC5dq9Jd/kNGq4+JrZHEfxAghK9HwUkYNuo1J\nOP7nL3vEZLy/x5XZPLxBdQSIljJAm12vhz6gdEFqhmvSqidiAWRcROj48Odr\n03RkzVMSZr+/vVFvKHFPzY/pyws9YSztnewjgtDYkhayi8F8f+VI0Lv8sdP/\nzxxPe76JGl/wBiBnzFQpDNibuq4rWQlHI0rICAsbUXygs/de46G8ks64PTkL\nLyvPuFvTc//EZd1/Pjhi3hhqro50cUkNijJH/H4ZfWfvMo8H/KmRJa3VN++Y\nV9LEafdPjgnG0AZBvWC158JH9nlajc2n+ggn8UDJm0sfgmbryQ/z8ey15Y3o\n/I7Y1cVJELsnhHUbr+Xvnc9fLd/6oTU52J9kSmzyu7VyTNTbRehV62RSMMem\nuUjT0EuMIZphdraFegYfQfIkl9boaFzAKuIr9DXXSoFvIJDzeGrLYXFCFXmM\nAKa9\r\n=YtOd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "source-map": "^0.5.0", "@babel/types": "^7.12.11"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@babel/parser": "7.12.11", "@babel/helper-fixtures": "7.12.10"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.12.11_1608076770981_0.8828075647967004", "host": "s3://npm-registry-packages"}}, "7.12.13": {"name": "@babel/generator", "version": "7.12.13", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/generator@7.12.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "5f6ebe6c85db99886db2d7b044409196f872a503", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.12.13.tgz", "fileCount": 22, "integrity": "sha512-9qQ8Fgo8HaSvHEt6A5+BATP7XktD/AdAnObUeTRz5/e2y3kbrxZgz32qUJJsdmwUvBJzF4AeV21nGTNwv05Mpw==", "signatures": [{"sig": "MEUCIQCRCp7qf0WDoTs10gBDr6ZV3uda7ykBnymjEFNb+YnXLgIgAg2hR0US/eKMOZpdm3KSjRtA1UTYWqR8BShMPU6UHaY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 127706, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGffsCRA9TVsSAnZWagAAW28P/0VGaZVMrHfA47CzYhVq\nAPUrkiD96BzS4+HDfpjnQjyUHnUSzNlGCr61rhScn0TyOR+SBMKbjmf6kblw\nqRnTtjs/lwrMNZ343TFYNwDFYjDzwtU9y6cs++AjCY/I9X8bNlONWb8NnWjc\naESvKpA1dwNrwkBJ0jmbU0mo0OwmiBWg36j2NuBBpOrUvvFT0CYQmvRR1yb4\nGpxVQhzVSZ2ZTHVvZuRrNxrPKaW3lMUN/D3XJz1qxb+KyA2n22oXqCRl8Ydy\nwgc/QMsmAYWLMGZf4RE4UFCDQJ0f3iT9Gr9zzleSngr4ey9BqHsotW33hJLL\n++FhWvuYq5gqR0/jN21Jx214uV59R4gvSpgHKPg+b3jb2mdOYDzdl1sipbuw\nBC7g2oReIUBuPIfbDPwznaQNqncl6N+9wUBCJ9pS9yXUp+AUbfvUk0hyKf+i\nz+1AgrjswMI6LN4KhlfJOJhNQxXqohAgB+qnU/juhoarGmGOrbBc+dSuN4dr\nickS8L8cU3yOY77uB6wuVV7ZxrKJTakmBzvGX6raO9BLNyFy+HJl5Y5xeU35\nehtgbnk5AKC0hTNm1KHkT0Wn9ApRukjDxq8H1PKhycqRZhKdHY/QuQyeSPO1\n5rAxW8nkR5LZ7oRARE1ieBwKaIPGXFWtkNJOcH5Shhh/FPG8RPVRttITc76S\nTCvU\r\n=nP0x\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "source-map": "^0.5.0", "@babel/types": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/jsesc": "^2.5.0", "@babel/parser": "7.12.13", "@types/lodash": "^4.14.150", "@types/source-map": "^0.5.0", "@babel/helper-fixtures": "7.12.13"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.12.13_1612314604031_0.6950931453616791", "host": "s3://npm-registry-packages"}}, "7.12.15": {"name": "@babel/generator", "version": "7.12.15", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/generator@7.12.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "4617b5d0b25cc572474cc1aafee1edeaf9b5368f", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.12.15.tgz", "fileCount": 21, "integrity": "sha512-6F2xHxBiFXWNSGb7vyCUTBF8RCLY66rS0zEPcP8t/nQyXjha5EuK4z7H5o7fWG8B4M7y6mqVWq1J+1PuwRhecQ==", "signatures": [{"sig": "MEMCIBN0VVJCEk3pXvOyxLN7vtRLvRoSHOiM07feGA3tKkigAh8f6sgA+T+p6iVThMbMHd5og1OJq+VSfKlg/Xv4JvJG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 126562, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgHGk5CRA9TVsSAnZWagAAoUIP/i7XIyJXKVJEYxzeEHH9\nHPbmezaMstx1RJ2Nh2cN3xqOYpv370QbZgRBRwQQ874HX5BTzKbq5Vhn1w8Y\ns8hbfldVbU1MHKaaG6AAaRokFC8igXbNaFpkgshqmP6OAECIgMw35uILPtEG\nfI1VDhqGrAxu/tEiEtwt7+1QN/SFb56zUj5uE6fiEdZrTRq1XlvroaXINpNA\nmt6WqkIzX09PeVNyjy5UOfWr+/C5r8mzWRTHtMluEzHXvW8q0TS646Hns56h\nKEHDJjcHqCxJluX9n3Va1Su3FBYMQjEPzzYrig6cF3GG2RFbfjCCaXbs5EVK\nMkIiod3PIuvcZM5BMEzHX5barZ5lk39EZJ2Uv3vf12LQ20NoRg6hqwc41TcY\neHdjOUv3NcH7/y8oRIV25SpVWxcSh2iPxZ33kUJz02hKqX1fIzdPtfPXzdQD\nFkLnTPqSALPGWkFHDm0V8rFkqOHNetwcdJQ2Kbos69Pf/rWjKNYr46vjKLh2\nPV5AMxEKubLs3TgYXRHfvYJH5lyh169m52+EVmgtbEzq4LzLjXfpa5FEd191\nvF+hKntw7HVlLmSJ0v8hF2CaoaIHCLWUcSH0RH8mXNngB8qg3XzbizQ2tXG/\nQTgM4YLxvBD5SiPrzpisq8lxOZsOJNLDYFKcM+SzcWlD9HVNBUj9Ipy5sn6k\n6QfW\r\n=Km+h\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "source-map": "^0.5.0", "@babel/types": "^7.12.13"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/jsesc": "^2.5.0", "@babel/parser": "7.12.15", "@types/lodash": "^4.14.150", "@types/source-map": "^0.5.0", "@babel/helper-fixtures": "7.12.13"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.12.15_1612474680599_0.2784143639013432", "host": "s3://npm-registry-packages"}}, "7.12.17": {"name": "@babel/generator", "version": "7.12.17", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/generator@7.12.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "9ef1dd792d778b32284411df63f4f668a9957287", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.12.17.tgz", "fileCount": 22, "integrity": "sha512-DSA7ruZrY4WI8VxuS1jWSRezFnghEoYEFrZcw9BizQRmOZiUsiHl59+qEARGPqPikwA/GPTyRCi7isuCK/oyqg==", "signatures": [{"sig": "MEYCIQDSQcuiWzYDTCN0MhLrWUm73NfGA20ZoQ81KTyw0r9QJQIhAJGKOJyBBm3xSoJR7vGRI05UqgmEAN8alngNHDGjtXx0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 127668, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLoP3CRA9TVsSAnZWagAAS5YP/1lfBGb6ezE59H+MQszf\n0SGdaQz8m7aRV8SdxGE+hVtug+yxNWhEC5F8A5Aw/wzwJzC69IQEvAKySOi/\nOHyGmW+RX5Pb2cBEuIGGumkeogg5biEXGoiwYTjY/tH15ZqL4NJ6NQ40xnDi\nFl1Ltk+aBT0zXMyHPCpGW405L6CvaSC+cdt3oMT4gy1Ev5PcSoJ5uADpWJjW\nY/A+9tWg8/sWSA5E5tvqJKgOMCQ+K3nU1U0qb5fCIMncwePua0ll8hT+fork\nOdok1k1vtfD170M4LPG+MDrtdLjoB2xB2UOLe/krLwZ7tGoMsPus00skj5gP\nuvRBbL6WgwCOvUyDRrQyEy4d63ZtNz0gpIdf3pP2+/WMpap/uP7kOppN0Ebg\nRyTYFL/nhbXgpuyBxNcmn4hBJdhwMfvb9F45K0RXT8mE0GCZZpqUmDVNQV+E\nsJ4xqNVxgvLlCMmE1i2hdQKkW1a9JL2p1FB592UhOvTZPv/qkUN7eyk5uq6v\nuQcbFjWe6doWf5Obfq7QpFGoCIl01LjMHR/GZVFwmitF4iq6vOmMlpjE5Uka\nCBsWXRgIGHnTN+0tbKNIx0H4Up0HEo6eIk/2w9iUo4Dg6nVuP2XowVLJnJiT\ncWau7iQ0yhOLdrA6Ate803YY8IHsNXoejaC8a79BlGGvQshTVojMqRTruDUK\nAekd\r\n=9PkJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "source-map": "^0.5.0", "@babel/types": "^7.12.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/jsesc": "^2.5.0", "@babel/parser": "7.12.17", "@types/lodash": "^4.14.150", "@types/source-map": "^0.5.0", "@babel/helper-fixtures": "7.12.13"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.12.17_1613661175094_0.2780171190947198", "host": "s3://npm-registry-packages"}}, "7.13.0": {"name": "@babel/generator", "version": "7.13.0", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/generator@7.13.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "bd00d4394ca22f220390c56a0b5b85568ec1ec0c", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.13.0.tgz", "fileCount": 22, "integrity": "sha512-zBZfgvBB/ywjx0Rgc2+BwoH/3H+lDtlgD4hBOpEv5LxRnYsm/753iRuLepqnYlynpjC3AdQxtxsoeHJoEEwOAw==", "signatures": [{"sig": "MEYCIQD+QChE2AqMk9n8ETJZjyDtSAdmdfP8nifMpm5jYqGsJQIhAIId97gHTT3+TrJECunBe/t6Vc59iCnesOewrjdzs1zD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 128392, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgNDUKCRA9TVsSAnZWagAAM4AQAJV1L5bYIXlXO55J1fC7\nptJvW7hnygRgABiHC+EVlX+BF3IcsJ4bPyGdRrJumOmGjFdPiCQ+kAFrN3L+\nIq/UAA9EbfA9ESOD46YyVDFEaL/MRGxOKPW1YdvnR2s3+kpBiN/7XJnwWhf4\nFxzdh6SDdItubLWu5koqhVQTFuIoN/F6berdnSNCLX0yEhIsckMIGcMtWPoO\nJEStwijDQilD46k6D+3cGTduXcM1wU1N40YlbN8VkdrxX2Ureeh6BAJW/z4R\nlWtTRlblT40ktbvJSlOuULSdhSgkzivRjY4heN2qWb8eS9Bc659NYORTQCGg\neyBubOe6veL3/owJcPAuzs/KRFUt2FUJ2rBbtvEtBIs6Y9vXfBXo4Iy05J8X\n0/evxqA32rf9uOtV8Pvoxl0vSi8gAfqniNUTmlZQbdUkaOFceLzhTjHF0o3S\n0h1i4ZhgASXaKhRVsPibKKRI1l847x9urec0VNkDRWoJBZ45KCNJZAIo5gw/\nM3dYY1LI3sx7xCjFIUPX/IigjCZZ6XmhnMwZHHm7xf3kQn0veVINuZ7cu2Co\no5XT/R9+wPaIqCsDZy9MUijEYh4kVd5fx/dsY3fs+vo20CDMupu84ohGwvBt\n+cV1udYI252uQT5Hh7ZWUEofzJNKnAwXu+SSqFN7lBq1EJ9WXgKnWAhG3CmH\nYaEw\r\n=qsdV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "source-map": "^0.5.0", "@babel/types": "^7.13.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/jsesc": "^2.5.0", "@babel/parser": "7.13.0", "@types/lodash": "^4.14.150", "@types/source-map": "^0.5.0", "@babel/helper-fixtures": "7.13.0"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.13.0_1614034186105_0.5470952671130489", "host": "s3://npm-registry-packages"}}, "7.13.9": {"name": "@babel/generator", "version": "7.13.9", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/generator@7.13.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "3a7aa96f9efb8e2be42d38d80e2ceb4c64d8de39", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.13.9.tgz", "fileCount": 22, "integrity": "sha512-mHOOmY0Axl/JCTkxTU6Lf5sWOg/v8nUa+Xkt4zMTftX0wqmb6Sh7J8gvcehBw7q0AhrhAR+FDacKjCZ2X8K+Sw==", "signatures": [{"sig": "MEUCIC/8LZ2Z6BxVSACPyjDdze5X9cBqakR+grssc3qI9D2dAiEAzbk8gBByHyGDlO4Gg8qXgGtRMpiU3DJI9QvJ8X0BVaM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 128379, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgPWAfCRA9TVsSAnZWagAA1TQQAJdMITc3DZ59+XFHjmn8\nw9fb5yJ1JC3aNB7wAdG/5Bi6oqqcToginYPmBSbxrqdnrGht1vz/eRohkUd2\ndvjfrzFZCp5ta+sst/pAt1FGEo74u8OIhD4FOfIJ4x66qXQvivCrrfHotJQp\ndAhxrKb8c3bzcxmryf7cK10piPhp8yiLzee4xB84q2kIEeIESGYV3u+2ynEd\n/EBkvHGRcPS0GqOO4745q0aFDbbeHYW+Ov/NqH6r9WJ8agI9Yi/q6A2kw9Ms\nAlcQB3WrHVpjmPTEMVZVLFVN8Yw7Nrc7iOZ1lu7numujUdvSrLnZ9Z4JiU3l\nNiZFu4jW2tMvCXGU+xHD4Gld+CgactdXPwuLW2ZPOr5k+b/CTk5ktLK07yb0\nIGrUoo8hB980shUIdEODC2B1+2XuogtmwL0MfbgRBsVfjaiJrrDkMT0K799N\ngBR+luMOleh7OJFmj29jzYBwh2fZbSAbR+Oqucwa5NdCei2hqf4wiRJ+eURB\n3tCSfEIkmHB5qHrS3lz5rsXEQoH6N/ABZaEypyOVeGlOJ96FoZznRzfiMH8j\n+vwIFHc5dOairyc9dV9N1l4PqgNl7Dw377fduEmhA5bPBLZkrkn3mPJKGbg0\nzApMl6cD3+iTrSqW2Y056L9gwbWpM7MN7AWxIX4wlYKB2eENzXVAl1v9l8hZ\nTCRa\r\n=DjHP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "source-map": "^0.5.0", "@babel/types": "^7.13.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/jsesc": "^2.5.0", "@babel/parser": "7.13.9", "@types/lodash": "^4.14.150", "@types/source-map": "^0.5.0", "@babel/helper-fixtures": "7.13.9"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.13.9_1614635039094_0.8650593259383528", "host": "s3://npm-registry-packages"}}, "7.13.16": {"name": "@babel/generator", "version": "7.13.16", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/generator@7.13.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "0befc287031a201d84cdfc173b46b320ae472d14", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.13.16.tgz", "fileCount": 22, "integrity": "sha512-grBBR75UnKOcUWMp8WoDxNsWCFl//XCK6HWTrBQKTr5SV9f5g0pNOjdyzi/DTBv12S9GnYPInIXQBTky7OXEMg==", "signatures": [{"sig": "MEYCIQCp9Y1E2BBgNxJYiAY7F/HRdlhiJtjNcmwlL4G0uKhKCAIhAOK9pRKpQ0LuDTSoe1FLoXz1p6TI6N9HW6EpSJpVeZhA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 128322, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgfrkvCRA9TVsSAnZWagAA5IMP/Rnvxsi272CXNyE6qOh5\nxmEMlfcX+r/uczUcEDeqYshMJall59bBkmw6Nw4ohRQxoG8BXTcKhhTsQXyI\nAc1Fd6vG++xqMV3F5QlATO6Y660t+y548AW8gPhNkqEqaDgT05wXJu1GtOiT\nM+gExzfMTTlZSRQt5qElFP4jrXK30rIj2wry8SvHwJesVS+rL6CxA6h9jnr9\nJp/QFe0/EY7JfMThwr6JNkp185ttE6kooRNNqFf+d5Q68YyFvcDsC9ybKqhC\n0g+rEXo3q5QDxTaTwKi9zm55VCnjkxu+WZhGlt71lArtQb0mEZ0pagQNHg66\nPc4UV2Y/030RNErtXHkjMSs9N18C9y1fTFF7gugZaPXMlEgiz6mlzSgGAh0d\nz0CqlYN2Arf8RBxhOIkQkTs7azq7rDGdYTD0+bp9CXo7XHT3PbZj/mCZiIE6\nUz1TlPaI0qnahpLGI5fIb/SNteN7Tkp0fqwbpkqBxXOwtluCmkJrSi+uOqkA\ncmtTtCQANV4z//0eI5yprUaEaPXbRlCfMupfa2LwlORyVTfeNFWNM1lasWY/\nu5CSxyJLnKPTlv6F8VJ9uJdULOxw75qK2iZd7RtzxgwFxp+y3R1zFs5ldtIP\n3Fv8DSY8yUCGhbrjZ5uBv5AjWgJ/lRt9YhAojWHZbbxNLr+7KCV0sizl4t6K\nnHHO\r\n=j8z/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "source-map": "^0.5.0", "@babel/types": "^7.13.16"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/jsesc": "^2.5.0", "@babel/parser": "7.13.16", "@types/source-map": "^0.5.0", "@babel/helper-fixtures": "7.13.13"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.13.16_1618917678990_0.5182040336099099", "host": "s3://npm-registry-packages"}}, "7.14.0": {"name": "@babel/generator", "version": "7.14.0", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/generator@7.14.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "0f35d663506c43e4f10898fbda0d752ec75494be", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.14.0.tgz", "fileCount": 22, "integrity": "sha512-C6u00HbmsrNPug6A+CiNl8rEys7TsdcXwg12BHi2ca5rUfAs3+UwZsuDQSXnc+wCElCXMB8gMaJ3YXDdh8fAlg==", "signatures": [{"sig": "MEUCIAaD7Tp3ZH5IADBFmacS8zTVNTCMql1NJJG8ok2eeTZdAiEAvJi9yRr0PEpbcu+ibVifejPWWZosbUxxGGqC7pqkh+A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 128932, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgixKhCRA9TVsSAnZWagAAk0YP/RmmA14RkiOKoxlvYWUi\nWXFOAaP/zIEr/814nxP8YvIbFwWD6kriAoBRPgvBSSz+4Vw05QX0sfcU4fEb\nghLqLlUjGscWnMYejoaJWtTLejyBgKuV1h6FATbNjnaoJLo7BcfkaGEDuJHe\nemF1Tab/H3HXgDCA76XIi1/kSWZqQqjGjeVPhzTC6KIkgHnHkh6V6A0PNWmM\nX6kSOkWfS6uSy6RIDMfvt4tSnCpau+mEcLF+kCReCniJNCd+m9zSiKvjMAVD\noAzPm7Sc8VG+PhH2Fi+viAJF9cLkLTwYHrFq3IjpMW9yv11FtlphyTGTEtnQ\nwAFYP6eKV4jkIZ8Wpd6n2rnqVo1nxps1/8Wf1oOLN/v5pI8iVN8Knmxb9hkU\nxLPIOh6Bv9swPtOTaufkMZdQTy5wIJ+JDtX8DPJonkTctakV6KPQ6ArjeEGM\nsqPGWIEnqW6J4RcRZTh/w9k5Fv2qP1/hbLEeTvy3ICX4reLU+txZ11QX0nxd\nCyNqdX7MnNBmwklbt8+WanAsOEMBN9I7itICd85aee2I61WUaZbjenpAjx7d\nkqNIw29KGxa9NcBQfcXTTLd0H+lLlwQ0gw2M51ox4MPSJM/PW5IGs2uG9hJ1\nl4i9ewrWX7fXwdi0geAATtIPaxRifo1Yx+qLaUryAM3juYexob2PqiyhOkgQ\n6EvT\r\n=E5OQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "source-map": "^0.5.0", "@babel/types": "^7.14.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/jsesc": "^2.5.0", "@babel/parser": "7.14.0", "@types/source-map": "^0.5.0", "@babel/helper-fixtures": "7.13.13"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.14.0_1619727009230_0.18533313921520045", "host": "s3://npm-registry-packages"}}, "7.14.1": {"name": "@babel/generator", "version": "7.14.1", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/generator@7.14.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "1f99331babd65700183628da186f36f63d615c93", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.14.1.tgz", "fileCount": 22, "integrity": "sha512-TMGhsXMXCP/O1WtQmZjpEYDhCYC9vFhayWZPJSZCGkPJgUqX0rF0wwtrYvnzVxIjcF80tkUertXVk5cwqi5cAQ==", "signatures": [{"sig": "MEQCIED1aRhVz6v5LCu8IDj6+W8f+yI52s8iaInj+Z5Y1ZTRAiBLBErNrWmN6SQsC/J6PII/CkIfCu1GZDEcgQMwA89teQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114173, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkKm3CRA9TVsSAnZWagAA6yIP/0x4wiunThTzjWVcNQpA\ndLTtrBtdXyg7TOYxVtCHhLBv14BpkexsdKubLdQe+0iuXQ1Cm6/I6BqjxqvQ\nW3SeYieTdGgBqPhASX5pDe2loYTFeJlOG6inTH0he5asZji3rmc8UvKCTVwZ\nG+GtsvLJ9caFeDtopjqSP/LT9LShUh3ARSUsVf0BM6//y0SL4eW7b27aWFMN\ncaKln6sS81YA9GWDPk6BvLntLUzSf62H7oCCnOCt6GPjPg/wY+dPKM9EgUYL\nUZCnKFBgLb1KbfCw9+dhMv9DPhUoCqvJvqgAVi0Q9P2KIxMPedvAVE/5xj4W\nkF/NrP2OjAWKS8INSZISi+aS4C5W6ZOFHJrezayqWAlzuOsDmJSaWlT05yy0\nNqQt6d7ozyVSTycNqblSd8bOv8XbzbdNLLAiEi2FsxQKUzLH5tEG/IhR7sR/\n2d9CuFNFYkWbmsw46v3TJ/Cs1sd4G0aptxq4vOGKtYDje/mPZE7lDB49bwDA\n3TBIvTypNao4sOC1v+sqfzfmzuXiKnejFHxUJIiD7Ygb+aVN6dq+/BlBhlWa\nkDpdH+emZBaENzwXZ9jVVNKT2zA2lBK0cs2hkdCp4y+tPdb7qybov3GIVERC\nj7PeoErKHJqGyKTriH117xFyYdRyO/Ax3ZNJu+RRa/8pag6W8OgTSVhNaPBL\nbdpQ\r\n=TYIE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "source-map": "^0.5.0", "@babel/types": "^7.14.1"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/jsesc": "^2.5.0", "@babel/parser": "7.14.1", "@types/source-map": "^0.5.0", "@babel/helper-fixtures": "7.13.13"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.14.1_1620093366884_0.37359213500769606", "host": "s3://npm-registry-packages"}}, "7.14.2": {"name": "@babel/generator", "version": "7.14.2", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/generator@7.14.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "d5773e8b557d421fd6ce0d5efa5fd7fc22567c30", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.14.2.tgz", "fileCount": 22, "integrity": "sha512-OnADYbKrffDVai5qcpkMxQ7caomHOoEwjkouqnN2QhydAjowFAZcsdecFIRUBdb+ZcruwYE4ythYmF1UBZU5xQ==", "signatures": [{"sig": "MEYCIQDZ1I8r1jbZjkERhRDrJmm6U1Igkmk2/WiS313MdqGulQIhAO98SLUWAck0YhbLoCQn3hhwNfY24Dv90+qbfU96Alcm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 115023, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgnAvNCRA9TVsSAnZWagAAuzgP/joEyc6lpFP1/7maUW6G\nQYMuP/bqYE4KFn+TaHWJ4HCtOK0aDoCwKxrhXYsMcYgSAG0qnUbqpvDSY4Jw\nKxqHorypf/z/hEUv4IAmpC1VtbS93eLQA02+4h6RhS9uR/n16ypGm3ENK7LO\nKVy3VS8X1YuX+sSfS/JW94wjAQi2wGaZijfONaaYKuS0+04FGeMTFQepTOBh\n7xGCvj0qEZk9Je4XNiLq09+iHr4VQgK104qOK1Gw4fyA5Ywe9kkhO+hw+PE4\nSsljU0d1lhuyXrjo4+I/COXzwtoKHRs/fXbXTh5ZMxd40I04SymLZQBWDkBc\nlua+2rmwn9Vov2tILPFY/32CM5SdKlzoYNTb46Zu56Y2ou+sRQbYOGcDOyuA\namwE/9Us8FzC6Ezv47E9JO+sKI6DoCjLbg0+GQm4Y8To+LMg5Hz+O19SZa+1\nWogXYf6ExJTCZ/uD9FNpimoDIlxoTgeAbJUh5UfMz1sHL72b3NfZF3ntd716\ns5UJw17yWC8/op7S3YAZIk4eBYiGgDgPZqMmGuxWyLSJ+ivty64oui2KOr9c\neL8DMYLDlm6kNpu1bhZg0XHHd3ym5TWkEt7lTTflyFsMYGR8dha7/1nG5vuq\nEmTGcyaf0h660h4zTmzz6TjZK4GdJMTcniSpge9jNsI40cOBSWU9AwnMdqLv\ndcq1\r\n=G5oZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "source-map": "^0.5.0", "@babel/types": "^7.14.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/jsesc": "^2.5.0", "@babel/parser": "7.14.2", "@types/source-map": "^0.5.0", "@babel/helper-fixtures": "7.13.13"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.14.2_1620839373011_0.16690640923647027", "host": "s3://npm-registry-packages"}}, "7.14.3": {"name": "@babel/generator", "version": "7.14.3", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "_id": "@babel/generator@7.14.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "0c2652d91f7bddab7cccc6ba8157e4f40dcedb91", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.14.3.tgz", "fileCount": 22, "integrity": "sha512-bn0S6flG/j0xtQdz3hsjJ624h3W0r3llttBMfyHX3YrZ/KtLYr15bjA0FXkgW7FpvrDuTuElXeVjiKlYRpnOFA==", "signatures": [{"sig": "MEQCIC7+P3xgimA5sIQpNw6VQdt8/qyZ2RlY7BI8rg5Ma55iAiB0hq3CBw0zadUpKX5vGl0g6p4pDty4oP+t5+3bl30g0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 115043, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgotWhCRA9TVsSAnZWagAADooP+gKKKHKDInxXFo3JUTUE\nVuD+PS65xVX3ZXMh4F6h9/A/ZcjRZ66wyUyQIBvPXDP29KPm4hXW7wQQy8bJ\nuea1iuSZr7ef2dxcq6qaVX9czM3le/okx/7oVZxpd+oLBPL7797c87TasEIL\n8o1T/T93wTFP4DSC4H4l0NBzGIC3xb6K3DyVBSPvr/R5ofwWnFqUuVgcqgYk\n+LkYE8N+CUDIlyh8xAQYsQLYXUfpE6OdXDVOYvxld/blIVbNXxJmjC2oifzK\n/Ml3cPg1LYPAOzii+qQr5IvPz9Sm4MC/aSE8mOhDvtzN081jqAGQ8C5sAru5\nudB09o55sTnGm6A2x7r/si0s7S1HW1v+XItS0wZk7OWPBDlL6EojZ+o20Zhb\nmFnz7rwUOd5yVdxblZCbdY0qc6+IcXGPZZkfM43jplJYlCJOqokxRaBV9+gs\nXH42sDZcouCHsp7vjzYAAeuC/DTfELz0aLrtmYzSARoV+VtWmkK4gBdOSZGZ\nSsiTkO6dVaf8ViByBGkMfsuNC5wAFhruQZvyyLo9eKGSxVrFx0WhQ5SR5MxB\nPxCZ3HenxQ5aTcA3rG9i+o0HOTChNRFQbgMo7ENXGFJ28uv88oEfWw7yM01P\nhOazegCfBB6YcZi92b4ZtBAhy52QFj4/bZvdH/rEYrCzGLTQR4dVE8tvZZJw\nqUe1\r\n=OocF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "lib/index.js", "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "source-map": "^0.5.0", "@babel/types": "^7.14.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/jsesc": "^2.5.0", "@babel/parser": "7.14.3", "@types/source-map": "^0.5.0", "@babel/helper-fixtures": "7.13.13"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.14.3_1621284257167_0.45526278067538417", "host": "s3://npm-registry-packages"}}, "7.14.5": {"name": "@babel/generator", "version": "7.14.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.14.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "848d7b9f031caca9d0cd0af01b063f226f52d785", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.14.5.tgz", "fileCount": 22, "integrity": "sha512-y3rlP+/G25OIX3mYKKIOlQRcqj7YgrvHxOLbVmyLJ9bPmi5ttvUmpydVjcFjZphOktWuA7ovbx91ECloWTfjIA==", "signatures": [{"sig": "MEUCIQC+n/+eBu+wlAvL+qkuwygPADlBQY01bZVU5KDVAFOzTQIgVYDOZkjTBr7FPTfB9p7etCHParD78KQvV2dd/avnLwo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 115088, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUrVCRA9TVsSAnZWagAAA/8P+wVvWJ/zzLuw/mWpA7HS\nOOgMMnq5RzyhIPZiwhVWvJn7cr9rgLvUewlQtuGYuhrdk1fF4kmXxqlv/QjW\nDWyXVZwPRdolZPtClZpgui1f9Yn909nQsXTJ0rP0SadpnjYAAMRxKF8NnTZx\n5PejCDcctxOeTPzQpVkx4GQCG8UOffyM8gRghJUFk6zM8YxP5/hUC33nLIGH\nPJL42ewP6+A7Aw4Y8o/Up1rrJ7eTeIP4UiS/NOeBUZEScIxeXgzjd3sl87S8\nVmTnwRwE+47QUmupuWaZQWHuumkjT7la2rCP4jkgTeRgL9Rqgnoej0c+umBe\nv4gu3twddXA06bsIwaUgfkcDf/YhOei8VyeTZtiYNP3foFcjmnYX3qKwZF3a\n+H/wr3yff9u/cF+0Ve3bZU9P7e8jPS5AfOjsVNbFPKL8TjothlFUGyM4CLa5\nTBG0ASCgSkQjwdeHEp2IYuKvyyw3NeznxHfs149ansL0vvLqg7n8MNUCvz1j\ndEAQk53yHx3wvC4CYNC4dh6w9MBIy5P9ce4d46Ax/fSYdU3WnVSFvHSJCWca\nCQDSiox/MQZUXBhvdtOU9e7ZJj69r/LtL5YWpPRlvNP9aJqR55P+OdYanNeD\nh27SZctAtrHO8dC9uOchgmq1vAe/pCj8cH2f7FjIt0PL1CS/xzpBsGlefRQQ\nze9V\r\n=xdTF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "source-map": "^0.5.0", "@babel/types": "^7.14.5"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/jsesc": "^2.5.0", "@babel/parser": "7.14.5", "@types/source-map": "^0.5.0", "@babel/helper-fixtures": "7.14.5"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.14.5_1623280341664_0.20157040944198723", "host": "s3://npm-registry-packages"}}, "7.14.8": {"name": "@babel/generator", "version": "7.14.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.14.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "bf86fd6af96cf3b74395a8ca409515f89423e070", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.14.8.tgz", "fileCount": 22, "integrity": "sha512-cYDUpvIzhBVnMzRoY1fkSEhK/HmwEVwlyULYgn/tMQYd6Obag3ylCjONle3gdErfXBW61SVTlR9QR7uWlgeIkg==", "signatures": [{"sig": "MEUCIQCdo0XCOCWXf9zRj0B+gk2qoPWz93oKiSkZTqw1QVhOnwIgYM1LJ3CA5fnHO9o9lfb63LDZzElUooA1hk91OpN15I8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 115088, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg9w/FCRA9TVsSAnZWagAAdnMP/2zVCgjQUTO/33LnDP6M\nEtWrnSP1PYpNv2R7qOAj1BA+HtfWq5Pfo1cCJTLBVGGk0NSrZjljRKyQQ/VL\nG07kv57ej/R5OJUQLOI3qIDVCf0VkT6KC5/MHlDaZpJIQyPZBbmmuC/ydkMQ\nMQzDhmWsJcfj+CTl4XCVMhWVRT9EP1lTlxLeb1HJdfdn784iCcGphjfp+RvD\nokqq9tR8KNBRUujA3lfXzNd6zi1z+tl0ODXyw4fiWDH7LR0vmrLgU7nOyatm\nQnkYUDIjEhqadMBqukDgFdEqwgkY5LOZfI/ZRmmakPOUdIJy7H/ZoWvfBF+E\nLE7Ta/p4BLfk073tUGfWpvSBhahrT2tCQQu4tIuFqLoa6SikWtX/u85UXxvc\nxsbZv0vYsmkm4kJGTQ775zXNForSQ0xQo4XFRY8tFSqS0hriR+TcwEvXGL2s\nw5zsTwyiLtlOIlZ0R25f3nMAQVZUpygNJOTPfnWe5KAWPk4LWz6DEqtZBN5e\ne7HoIWZYmyUmpBZ/8tNbXFsW/j2EuBsdK2MsLvSXw4z0DoQ4kP3nUsfgYMXb\n3e4CKf7+cv2f7v/IyjWtIL297DlrhHvy3e25adal+4zZf6W9DF6QFphYnpmd\nvFQyq9DjXbzjjO8AuMhSZzn13TyWRsROvmufi1oHLGTtX6GGxb9wFyh4lhVR\nfR8A\r\n=fnuv\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "source-map": "^0.5.0", "@babel/types": "^7.14.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"@types/jsesc": "^2.5.0", "@babel/parser": "7.14.8", "@types/source-map": "^0.5.0", "@babel/helper-fixtures": "7.14.5"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.14.8_1626804165698_0.6400832947892752", "host": "s3://npm-registry-packages"}}, "7.14.9": {"name": "@babel/generator", "version": "7.14.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.14.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "23b19c597d38b4f7dc2e3fe42a69c88d9ecfaa16", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.14.9.tgz", "fileCount": 22, "integrity": "sha512-4yoHbhDYzFa0GLfCzLp5GxH7vPPMAHdZjyE7M/OajM9037zhx0rf+iNsJwp4PT0MSFpwjG7BsHEbPkBQpZ6cYA==", "signatures": [{"sig": "MEUCIQC/zRoOf6IrnkuCzYT74pvwS1jsO9F9k9v+hwTUTRbNTgIgR8D3COlCwH7IByKUYAo4i/HufkFffuJdOokOo11unrU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 117500, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhBlLzCRA9TVsSAnZWagAAYgcP/0j2UO6CJge+IaOXik/b\nseE+dGne/Ja7oT7a99crTddQ/UjE3KO5j8ODCOLGDFOocm3dZ3KO3mtwO0PD\nJu54b8WZilwDhY9TDD3QEgFhUVhjREFcGqL6IK03p6sGXLyGb65v6+OAi6Z/\nIseQhyU/bSMt/Xw8iXuTW0Dj1aPfYL4kXlfFsk2pw9F1pruC5SbeaiVHSMZP\n1AVA14w/VtvF4kIRXShEuUakMhaldSrM3Iy87/3h69N6yh8VrVwwTz1mf8vL\nGwMuRgmp6zYlL4KGNnHiNPemnjR4JxuFVOSdDzWRoH29/0Uk/pkw5ADWXssP\n1y/bPaaKoC7kfkoDCGD+wSoxUWoZUDavgLbmTx94xC3dLZYYaoHVlzsJJmkk\nE/Ld3Z0Wx5DDN46Ft9FwyFznYoeMBzoPT6xafsD348OWPdPCEtB7im6ZTOzq\nbVdFzt3MSNQfwCu9Vign2x22RhkbwYupaNLV0rPr+SeIQ+VJ2qcrEglpx9r7\nB/6KjFofmvCgaIIzVV8A2ynE11vtucnsQ6CXKrkpP6fXkknMEe4vD51P+sZw\nFQgsNkvrWrrwnPUEZDHfU57dqGj19FFZmPfI9ZvZR9mq9HNmnnmkuKpol9tk\nDl6Xx+LpagH32mn/jqMzE7xiGtGlkDZbvqjeuL/Jct/AH92JmC8APvLqVEf2\nE/El\r\n=l3Px\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "source-map": "^0.5.0", "@babel/types": "^7.14.9"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "7.14.9", "@types/source-map": "^0.5.0", "@babel/helper-fixtures": "7.14.5"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.14.9_1627804403289_0.5420520157160975", "host": "s3://npm-registry-packages"}}, "7.15.0": {"name": "@babel/generator", "version": "7.15.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.15.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "a7d0c172e0d814974bad5aa77ace543b97917f15", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.15.0.tgz", "fileCount": 22, "integrity": "sha512-eKl4XdMrbpYvuB505KTta4AV9g+wWzmVBW69tX0H2NwKVKd2YJbKgyK6M8j/rgLbmHOYJn6rUklV677nOyJrEQ==", "signatures": [{"sig": "MEUCIQCsD2I5t7P/WOh2z+u/weknN1n5EKYaMaUZfn/qUxBi7QIgHPkSKoHZ+ejdnLpcHNiIV2D3FUFUofVCBTKMMwMDDeM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 117972, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCwLfCRA9TVsSAnZWagAACzYQAKTIi1qtZoewGevjPgEG\nb4+naX3lX2enpds8esFiOwERDOhCxZ4dYUiCeSnnxgg/puXqS03XAToKmXbg\ngD0C8ubfJMqhHBFPhH1G85ciMlFsP2erGSGovgL9riI9+3G24LWKFrgdnqzI\nQUoMmTP8CIOS2ZYXPNrml++BK6tdOxYiUYoMK/IC5zswJbVQcF0/0YuH0e71\nn45UT/pmFgyl9MXMvHnn0e0mrqU7zGDmhFhelGDI+ysY84H5I/ljC6Xijf4r\n3b4OhNPgYBEva9uLJU6qP64+fADu1jwwy2W95bFofjNPQSbuEi49sQnQkqg5\n7lCirzDNrUdZJva0REbnNpYYzESm80cVgTNXnONezJGMgbHPV1Ya4LA31qoF\nF4QuRyRBaD0nReTf/0ZCJui0EddJRTU1wJHVcFzjhcaj9yyoInLaRcPTcV0A\nssmuL9xWGwpH3DiRdIN7nhkl2WCGDf7KcwnOij2N/RpWMbNhvKVBlCNuqH3h\nOA4Myj0m+523jVwYvCqjnXKD4rSxJ/if0IYZrfWeS+kuViAtMvk4g/+m9/Pf\nEOp3C4zmh5K/kWNj8Z9vnFU2uFNa8jCvAlA+mWgR3VsIHpX8BTD8okM8SXW0\nyqBDH0aVEzWqIGmLtdLSmO31zI+SD2VWLQlD3EzEqX9ExMr91nPwTdrrEqhy\n1ZMx\r\n=irr1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "source-map": "^0.5.0", "@babel/types": "^7.15.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "7.15.0", "@types/source-map": "^0.5.0", "@babel/helper-fixtures": "7.14.5"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.15.0_1628111583829_0.2978559184364906", "host": "s3://npm-registry-packages"}}, "7.15.4": {"name": "@babel/generator", "version": "7.15.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.15.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "85acb159a267ca6324f9793986991ee2022a05b0", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.15.4.tgz", "fileCount": 22, "integrity": "sha512-d3itta0tu+UayjEORPNz6e1T3FtvWlP5N4V5M+lhp/CxT4oAA7/NcScnpRyspUMLK6tu9MNHmQHxRykuN2R7hw==", "signatures": [{"sig": "MEQCICk3J89QUJwCLoThQzyTem/+8hQPRbejXckv3oTCR2ObAiBWNUuo8m7ba0zHZT9EhX5gX4gscF83/urQcAeFcYVo4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 117898, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhMUSTCRA9TVsSAnZWagAAPU0P/3rN4fccp7nApUEXNmvT\neq+dhTcJ0yzBCkGXkPgJkIQ8S+15oLt4KnRw8gZMMHTkq6KBLNHAvW0q1ze5\nC1R4PPRcqi7vHptgFuCWrJ2ZxsUqrYkEHfMTB9+m2SqvIzyM33JwmW5TFcsG\nH0W328YPzb1G+0KVIrtjUUENsjCV3naGw6OLj9gXg1e3+M4FlGMaOa8J6dxF\nWOUdjy3u89aGzo1UEMyNcMl5qHne7b+0waICWi6eGZkxO7tAAtgMmQmu70Ke\n3FHIemeosh+EocG9v/0iCgRBuxpWUXT6dfmL6p8ZNy9eTQvTW9eXqpVFL2BW\nLVG9LHi/aYRsqfV8AfPnOR1VHIXGAdG/g1MlZlZhsTfhODgq5r8G8VproX8f\nA3kp2j1WNYfr2U93A/wsZdZIWfNrH2kKXq9m8BCHsL1dhjQZm8Ww2Px9Kdfe\n3vJEwUMnbGTUfG0kh2en1pLaxU6aoA/gZIXChcadtpYRcofYsqbRsYNVhAP5\nuFkGP5Eno6AZY7ER/z1UIZwb3EL73QzKmGf8TKgSXe1b4+UTWtX41w7tqEvG\njz72saQQvvuESQFXHmWIxdI4qTEV7Kbbyp+Vq3JITA9HjqC/7VtcNRGLqBtG\neLnqT9S1jqMfSRh1TriVydNcu24hNXaQos3ooMCxgxTMZubiH5ljwgfi9mOm\nLiTN\r\n=la00\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "source-map": "^0.5.0", "@babel/types": "^7.15.4"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "7.15.4", "@types/source-map": "^0.5.0", "@babel/helper-fixtures": "7.14.5"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.15.4_1630618771323_0.5349708081481439", "host": "s3://npm-registry-packages"}}, "7.15.8": {"name": "@babel/generator", "version": "7.15.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.15.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "fa56be6b596952ceb231048cf84ee499a19c0cd1", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.15.8.tgz", "fileCount": 22, "integrity": "sha512-ECmAKstXbp1cvpTTZciZCgfOt6iN64lR0d+euv3UZisU5awfRawOvg07Utn/qBGuH4bRIEZKrA/4LzZyXhZr8g==", "signatures": [{"sig": "MEUCIE1/8O6zBPBm69izmpXbZHI6+42+685djJlccs1DcaWcAiEA1Oy9HKde38BFzI2I0t7iMSrmXNvvmMofyL9Vjf1Srks=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 117931}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "source-map": "^0.5.0", "@babel/types": "^7.15.6"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "7.15.8", "@types/source-map": "^0.5.0", "@babel/helper-fixtures": "7.14.5"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.15.8_1633553695065_0.780599851891012", "host": "s3://npm-registry-packages"}}, "7.16.0": {"name": "@babel/generator", "version": "7.16.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.16.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "d40f3d1d5075e62d3500bccb67f3daa8a95265b2", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.16.0.tgz", "fileCount": 22, "integrity": "sha512-RR8hUCfRQn9j9RPKEVXo9LiwoxLPYn6hNZlvUOR8tSnaxlD0p0+la00ZP9/SnRt6HchKr+X0fO2r8vrETiJGew==", "signatures": [{"sig": "MEUCIQCSFIMDKUJe7EH6jht0D2YArOdSkZe12qvnVw65x8LgygIgTq5KLrvfsHEZNXX1J3qeHbs5OZu2Tgn5dNmLI3O6+0M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 118194}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "source-map": "^0.5.0", "@babel/types": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.16.0", "@types/source-map": "^0.5.0", "@babel/helper-fixtures": "^7.16.0"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.16.0_1635551260872_0.33490995260504186", "host": "s3://npm-registry-packages"}}, "7.16.5": {"name": "@babel/generator", "version": "7.16.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.16.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "26e1192eb8f78e0a3acaf3eede3c6fc96d22bedf", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.16.5.tgz", "fileCount": 22, "integrity": "sha512-kIvCdjZqcdKqoDbVVdt5R99icaRtrtYhYK/xux5qiWCBmfdvEYMFZ68QCrpE5cbFM1JsuArUNs1ZkuKtTtUcZA==", "signatures": [{"sig": "MEYCIQDrKgdBLUxZwvPIeWRhclJJUa1b47qb543WdouxjfpnjAIhAPt09BxhPfNWxGKnuT+SzKwFKRemkTHMThu7z9cA3xeO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 118206, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8jMCRA9TVsSAnZWagAA/NMP/2LKB2tLZQa5IHluSMJv\nAM0onjSJ4Ky/T3bWoe5pk3lBNZAe5T+NtckxqcgIjP84K2cKZ3XqHytbq8KV\nsCt/2Fsg1IElDXZe3bkLz3tvtKwSyCsnUJU7pBOHrIBPaui4cLutiRwVB+if\nsXT2T32sW1w53OnnVyC8AGhn3hrckTN1qSaFhTyAm4OsU9s0VagujXk4f/Lb\noPP22x3MGivGhmw1/k7dJwFKym907rKarQiZVVqsmqI5uruCTqcCiEt+Bvhc\nKjAifd1ZRhc7IHWsKRM1UaRcer6t3B3tD/f+AbRRrQh7Wi5eE8EZrfvkoGnF\nqDtTt96rGSy+wz1QY16fDtq8aU9rbsnfw3vpoBBBjIFSyPx6oo2vFuCK7YjI\n7Az3eKPYiWqDI4CsTY9X2REX3Ym+AYez6Baal+Z5wQ2T9zr4UBmLRjRW1Aql\np8F+y5BewgryMc/10i4CPm00O8z1sNdcWIrqvARfOpCAk8Bh/QWPF7wyGat7\nOp9DqtK3/rPqsezLBUhqNfHwEiEG7QJSboReLfg+sjGYrILQxODA8l4wc0Xl\n7WbflbsChY3CITujjaiHzzwsC4Dai/6hJFuP1w1BRqH6llCG5TbhbG82970Y\nPfF6Z3sJtlA1L1/Og96+x56OJtRZaHEVXvZRw/nJZVOAIBdHm4cvPvB+qDzg\nx6Dj\r\n=pfa3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "source-map": "^0.5.0", "@babel/types": "^7.16.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.16.5", "@types/source-map": "^0.5.0", "@babel/helper-fixtures": "^7.16.5"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.16.5_1639434444197_0.43674138439912613", "host": "s3://npm-registry-packages"}}, "7.16.7": {"name": "@babel/generator", "version": "7.16.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.16.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "b42bf46a3079fa65e1544135f32e7958f048adbb", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.16.7.tgz", "fileCount": 22, "integrity": "sha512-/ST3Sg8MLGY5HVYmrjOgL60ENux/HfO/CsUh7y4MalThufhE/Ff/6EibFDHi4jiDCaWfJKoqbE6oTh21c5hrRg==", "signatures": [{"sig": "MEUCIQD25S4NshIzEX6/wHtkEzAG/lYyf/iJ5Y472bpHZjFeEwIgQL76U1iizDIWOXHPVyJyDkpW2kXfMXIKT0ppdPni29g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 118206, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk05CRA9TVsSAnZWagAAGSoP+wR8cwgyITG/jMkDqkyv\n6/K3yWfOMbtiHwThZcK1h65VUzgpltHGrb8S1SGDCkLc6Xm/CvrqWxtfsIN8\nLY1EMnoPfGAM18nKxbZRQhAncgCqbygA4QNhPz9Q+WSEdjo5oIk0JsWPuj4X\n0OueiWEeV3ylxwjNzzF8aHwj3VCAmI6eZNzk/I3NOuPdhNFr8T6CSxdHgm86\n3iBeVSws4Gaios5UOs7ws9GX6fTG+qZqh2tXl92tdVefZGSpGG+RjxG2b+15\ne4kTijQ7lM9uVQt68fHpidmFSu0vs2DoUKt5sNSkrHqVXTGYa+u+ogQBfNXR\n/MYhaKf/Hej3kAYevdIuAFVAp+lvxQekP2ssh2kGXi1oOrF4DzfYSgvPZ3Of\nTfyUE7e5KGRWQc3SyhpR4WL+PZaUGsc55UXhZuOSy4rf2j4mS9TkT73XNiJn\nInwSTURYBuE7tVldz0Q9o7qt20zNEw7UwmdRd3jB9/5wh1Xvwj4uTeskDYVv\nN5R+J1Xbx7Ql6OChMmFypstRo55GcUIgCHTRkrL4yug/6hyvynMEVmroZp8K\nhnydYYaxW5XCGaUZkX/GbZBPMgQPMqrtKyiP9tLy0lYHQP8YpcglUdSrJg5I\n1q9RsoRa5WAqd+UXSuS39Egd5fGLAjH8vJi76AE/p/PPZ21YJr6UidkQE49i\nW8+i\r\n=3iu8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "source-map": "^0.5.0", "@babel/types": "^7.16.7"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.16.7", "@types/source-map": "^0.5.0", "@babel/helper-fixtures": "^7.16.7"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.16.7_1640910137548_0.3713144357475908", "host": "s3://npm-registry-packages"}}, "7.16.8": {"name": "@babel/generator", "version": "7.16.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.16.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "359d44d966b8cd059d543250ce79596f792f2ebe", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.16.8.tgz", "fileCount": 22, "integrity": "sha512-1ojZwE9+lOXzcWdWmO6TbUzDfqLD39CmEhN8+2cX9XkDo5yW1OpgfejfliysR2AWLpMamTiOiAp/mtroaymhpw==", "signatures": [{"sig": "MEQCIAqwtkWg2WPhTEGsb7GOHnlhUzVLX0lrrBTKY3ohNRqlAiBYW9eydPH6/SRzTOxzX3+tJ6CjKuTdfKzd1nLYqLGaww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 118455, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3KKoCRA9TVsSAnZWagAAwSkP/RhVzf0IMdkNxPa4rsGg\nvwPSUQYXExrTdT0S604k81QiFQDYCpodBi/QSY7B6gdJNVfKB7tjtx+XPhL8\nxUmPrm5WQIEZBlry2UR7hRe+tzR6uXTBADRz7UNwnfvayv3JFHAyCvt7KIqq\nlumNF/vBIHQHryCziOmyyaBKU7CcbmQzC3YMQaNFISkEiPz4ddl5SiDB8F0A\nXwLm1REW5eC6JFoi9oRtIEn8nagNlVFvA3ocWczYWE52/RQrb+V/yQ0dRS/a\ncgc9r3FCtn80n9mxcHkwsUBvVRorHBo6Cm7M15Ij5dZb19CyTjztCcZbk67b\neTrzxZYLBtrOJ+rcRMVOyRY/WJGjAkge0wcAwAKhgBVL140IbRyqIcswJXkY\nfrVX2Aht2wB7E/UIFZllHab9oZxg8r6Mw7Ahfzmjv2mZ4sgPx0GK48AbGb6F\nmfGIO23Ll27b0Gj5VO6qMG6d1Khqp7u4rrQ7rvWrlkeOQeslpTaC212DYE1U\n3b1pgM0hu5OD5J3xADrIQ1ioqdEXxGt9EIcRo10x+mgfZLZ4pqu/B2w4mkym\n5qH4vMDO4oerwChZx1wltib4SR/Q/OPHsr35fovYvuaWO96o1JoTJ0EbFqOr\nyJTSrXaKaGb9W+zc/jwFiR9L9z30vm02WyLS1GzNxwseEY+V7/fFxTSYgTq3\nac1X\r\n=RJvZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "source-map": "^0.5.0", "@babel/types": "^7.16.8"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.16.8", "@types/source-map": "^0.5.0", "@babel/helper-fixtures": "^7.16.8"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.16.8_1641849512323_0.6788737108781158", "host": "s3://npm-registry-packages"}}, "7.17.0": {"name": "@babel/generator", "version": "7.17.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.17.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "<PERSON>an<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "danez", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "7bd890ba706cd86d3e2f727322346ffdbf98f65e", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.17.0.tgz", "fileCount": 22, "integrity": "sha512-I3Omiv6FGOC29dtlZhkfXO6pgkmukJSlT26QjVvS1DGZe/NzSVCPG41X0tS21oZkJYlovfj9qDWgKP+Cn4bXxw==", "signatures": [{"sig": "MEUCIQDHX4iYq6fcJq8yPGHgr+3qUyEX/Lc4wFmyZFQ92f/IOQIgHZ2VJTda/xYhGps91t/vhurhheaEpRtXU86fMq5X9ag=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119212, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+w4YCRA9TVsSAnZWagAAmB8P/3au++63HPrWYi1YHqgl\nAbYFMOB0x2NUA2Nei52ApXC6GM7mm2+6pEuEshEmua55XubtsTcDhQKYQIuz\nO2/uE3dUDlT7NHfRq3nYEQbGIbS4A6Al6Ubp4LC7jrru6/ZLgv6XvURlf9DU\ngWsbqDbWpJL+gRYkOohNGs8mPqqw2fiRvDFi13eri5nEFgbyGn1CmOqWYQ/G\ngZthUON4yrdzgzgVohYHSrYkhTSa7vr7gsy83ZzRucneVdMydv+PuhImiu+a\naAOjLkFEy+G1HaNjAv9Zip2Edx1yU2MnYBALCaUEueHpn/mB2JfRdr2pRTmH\nzX2Z7cK24humC14aByzjjeOdnoBHCThpp0ZYEk88J4O9W3ePguKp8ui8Fh2K\ngZJgYXFUAfFZzrRdb/714Cb557ES4gZNngZBdjUZoqsUhiIfiuba04N9RmcG\nO63fldOiWEgoGdLDAibBdfmeyxNLrRmvCb2kE6BOpGBM74UjhTNaGdy97F3T\ns+9YoS/ZlEv78YUN8qrcB3UGbUfKy9Ow3MbEmsCEQr3C/vBORVWh4lnMUrvT\nHI+4s1Q0o897KIwOY3IlNNOh/EoDy82UEoJwARB7q47jg+86I7gml4LTEG6W\nXhGrN1JRNj0s17pOniPl11OcHj06OrIIqMTTHeHjnfx4DnA108o/EA+CLKKU\nQTyY\r\n=dzaF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "source-map": "^0.5.0", "@babel/types": "^7.17.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.17.0", "@types/source-map": "^0.5.0", "@babel/helper-fixtures": "^7.17.0"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.17.0_1643843096820_0.0017797194342044076", "host": "s3://npm-registry-packages"}}, "7.17.3": {"name": "@babel/generator", "version": "7.17.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.17.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "a2c30b0c4f89858cb87050c3ffdfd36bdf443200", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.17.3.tgz", "fileCount": 22, "integrity": "sha512-+R6Dctil/MgUsZsZAkYgK+ADNSZzJRRy0TvY65T71z/CR854xHQ1EweBYXdfT+HNeN7w0cSJJEzgxZMv40pxsg==", "signatures": [{"sig": "MEYCIQCDRJsXMrt9SHvzgv0h5iynqw0kSk8xx25GvqCRfHU6HwIhANfdRQQeUy3UnKFCU4i2Ytw+JKHGkojkR3JdlMN04u8K", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119255, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiC8pTCRA9TVsSAnZWagAANGIP/iMprdrgFaZBKBSc0KSr\nCT+ljamHXorB1DNwKopcMtZeLO57gvCAsMreu3EL80t4g0p5pJI7L2Cb8FbY\nSUJULtENkvFGvTwfWsNplMjojJEXrlcJTft15QutLw54Tza1f9td+c1S9YrQ\nenUifx9+2AmXUT7wDZjBVqJaCeOvKHBj0lzESmoOSbG4/4TW6eYIkc/fLsrv\nkQdRTyFHMLdUtKgAXa1EAe4ITJe65nNY5exar+fLVSuRJbGhi5WmM237847Y\nJNuctWAEPtT2T3XwxlUJsttfmhT/yWC2NyDa7iJgsjRy5+Uvl9xb2gP1Jrc+\ndFVB/gaM1nwaWrzh5RNqxv1hwQEEqQyUNrvotGNq2rwwFe7VO9Lc8R1ye6CB\neaq7MRjXO3ywgTPkQItNi3xMk5FtpFhd1969fvjudL9Ry8Bjtd8VzDl8ztAR\nnnLgsYRbQ4gt51d4IZE6SEtRiO69aOkFqGszuKlpalCIbm6sMP3g2WELvwsC\nybKdPsJ1kR1spZnhkFomOjvCNfOdsGYtfjNI//4U3mWl/Uy+YSPR3SlsMgK3\n8mOOG5YyFhXevbEW/0u+zySALz3TSmnyb9Go/DILOh2CrwSUI8b0pmmVufu/\nJO7jKLkkAJPyDkqQlZ5nUQRez/F15qarG9n+kiCSO2HJCiyOVsQVM0Y8WUMv\n3oTs\r\n=crAp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "source-map": "^0.5.0", "@babel/types": "^7.17.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.17.3", "@types/source-map": "^0.5.0", "@babel/helper-fixtures": "^7.17.0", "@jridgewell/trace-mapping": "^0.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.17.3_1644939859051_0.4916260745246681", "host": "s3://npm-registry-packages"}}, "7.17.7": {"name": "@babel/generator", "version": "7.17.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.17.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "8da2599beb4a86194a3b24df6c085931d9ee45ad", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.17.7.tgz", "fileCount": 22, "integrity": "sha512-oLcVCTeIFadUoArDTwpluncplrYBmTCCZZgXCbgNGvOBBiSDDK3eWO4b/+eOTli5tKv1lg+a5/NAXg+nTcei1w==", "signatures": [{"sig": "MEYCIQCAsN8xwbsIycA3weqZqwXPSOOxbK82BBPv7jM7P1Lu8QIhAL8w2xoX1zqyuw7M4AGQl7PgEWBjhawmtmHxarowAmlM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119383, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiL3Y7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqpMg/+Ll+2AA7YdeY0Wcja6bdNXq0nep1Mlyj9NZjH2tVpXTxfaz+G\r\n03VlUIRLC9nAx5RJb9CEOEw6c4N6AtWYyXi7B4AT5BwRCT9tzCJ+i8+O4P8N\r\nBO+eVjyDdfkcfdgnSEYo+kbHVMTMh1qskQHBDmVHNsOAxKAnXzrWUemxK2C0\r\n2+Oc+qz4Ju5kQshCMEJZXXhn+it0M2DTiHowh2PW0StsRMMshgV1zi2pYUMf\r\nec9k7/tc9t2Y7VDRhmPtKVwg0vFA0keBNmMic9iLTAZ3UiND9rdRJX0SN64r\r\nJtjrEETzJlqJkdXi4F3k3jnv9FekebgQrVCFAD/tMy2YQ946BhQViZx6Fcmh\r\nx5pnRwpjqkR2i9ra0K52cP7twfUhHb0TX4Ye8qWk9Zy+vdXYug7Z5CGqFvYE\r\nWtw5j95TsBFVu0hfJ8cJq6hqVaxHN8539vQdRYX3tH7V/yY3YcODE+ANoGWY\r\nE0d42TfvPqtvJfqt1TLATrn/OEmFbWyGT16sZloTzgcD9L2IYJ+kM5exKzyp\r\nKOIgQVdFmjUl3J6ODuhxVSjgw9UllRUmtrbu+5H2xMvc3PDaA3qRGxD4ssyv\r\nDR5YA5fgL8Ua2U8W2raSesGC/H89hk2RZN5teLlB68gVWU1rI+j3MSbfce8M\r\n2iD+wfPQU5jZuwPtgepX7feaGQMOuTz9pNE=\r\n=buYm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "source-map": "^0.5.0", "@babel/types": "^7.17.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.17.7", "@types/source-map": "^0.5.0", "@babel/helper-fixtures": "^7.17.0", "@jridgewell/trace-mapping": "^0.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.17.7_1647277627488_0.27048474484336293", "host": "s3://npm-registry-packages"}}, "7.17.9": {"name": "@babel/generator", "version": "7.17.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.17.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "f4af9fd38fa8de143c29fce3f71852406fc1e2fc", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.17.9.tgz", "fileCount": 22, "integrity": "sha512-rAdDousTwxbIxbz5I7GEQ3lUip+xVCXooZNbsydCWs3xA7ZsYOv+CFRdzGxRX78BmQHu9B1Eso59AOZQOJDEdQ==", "signatures": [{"sig": "MEQCIB7YCTULPzys30baWWyDWKLQ61nkf+aad+e8WYDOtzuvAiACMvIAt/NGAiTpokBIE6jdvqK/N6IWoX0n/VDRFJ0JLQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120106, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTbfoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrNWg//by8NE6qD2Yc+qnMxh0YdV8A91So0YXL8J49gOjmfkIwA7gBi\r\n2Q7C49CvbAPGNQlPHUvI4ghakMJawX2u/R6zGrYSlSR5m+8zq6KX62s8xIKF\r\nAX1h3tWjiFHM/01nRhvGDAww56dzeeeIsChNxZoUYlYz39QKY/ryHQDxshVy\r\nn/Qp5q88Q3hT92Cph7W6wlMTorJDwAx4uptOoFcgdH9qYIMAhx2oHoVSYneK\r\noXSg4uRlVttqPpnzMZMgNdgbyV2i2oXrXQpfqoqCtKJlqMxzMC5ppqKphhJ+\r\ntwedDmuvvTkM1JnqoiknwgL0GgpqSCkzvyp3+pYdhhIw3vVcWyGEaBU7W/4R\r\n6/fn2K/+Tzg3N9/O+RtGAhKIoqukrnn8+4+KnDqCJNGhKvC+GVOdQ4rc89b0\r\nR1xUSZpzpZRJXJ/aeDny3JGY8cE4L9v9ElFYuD1E9tYrA6oMRFmVPZvxU2hd\r\nbvwZT8PzXxBTEaefwFNATf7jIPu190/zz1Y28NUOZ8yGuqFho5N8frOvewfR\r\nkp7GADxApYir8UMMAFqkFdxP0dNjyVEsvGDeJgJJJieKw5M59sO9YcGccCGu\r\nXnv165oNQk4X9vzqREJHQGo7uRdVrUNct4FeYwJCIQ2gm0+9dwm7tASXKMoc\r\nxSAiFHWelgrFZR/tCk1TFco2pOHY8k5yePU=\r\n=OxX1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "source-map": "^0.5.0", "@babel/types": "^7.17.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.17.9", "@types/source-map": "^0.5.0", "@babel/helper-fixtures": "^7.17.0", "@jridgewell/trace-mapping": "^0.3.4"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.17.9_1649260520742_0.3562423425595549", "host": "s3://npm-registry-packages"}}, "7.17.10": {"name": "@babel/generator", "version": "7.17.10", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.17.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "c281fa35b0c349bbe9d02916f4ae08fc85ed7189", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.17.10.tgz", "fileCount": 22, "integrity": "sha512-46MJZZo9y3o4kmhBVc7zW7i8dtR1oIK/sdO5NcfcZRhTGYi+KKJRtHNgsU6c4VUcJmUNV/LQdebD/9Dlv4K+Tg==", "signatures": [{"sig": "MEQCIGlWjwkezk6Lk/VjQ9WtP2dXEhmyyEDX4EyyTNVBEZXcAiALSTac+Qm4EtwXa/PN+J7oP1NplVgAS5LVI17okdzjfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120244, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJibBRVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpucQ/+PBJ+sDCU35kn/900ktTDQ86TkYg4kWLXBL3gxZHbBGl6Wxth\r\nBTLKHsacTMltQAlgpiiw4nd5+JWQBqLwtAFukCdZSbP40awQ5nM3UJd4mMKU\r\n6+HhklwSaemcyo81zJIapg38CphQhN8XD5eNbQ1/VdhY+syTR5QgXVf9Ae65\r\ncbHhYUyvtTVJtpTwQpGjIzSDYv7j5JTYqMhtOKXFfZkCr+S634JM7PW0ks31\r\nbOJMpz5bTc6uyg9fsCEg4JupSDnlLVLzzqo7iWvQIzax0BKool59ZYIA9hXm\r\n1kWdxtUNNTQ1o3GciwICKRGIoLL3PXmjiN53VhD7GrJycjWaFvagUKPZpiEw\r\nb0rfgu35pfPX82DcZLSkWXJKBgaWNvkOvm89loZy4TmWjCQzHSDy3NYDfaAA\r\nga1hzZx1SoztplRmJ1vvyroTX3DU8omNoiF1aKbElviHVcdHdioosqcmfMel\r\n/vMcPcirSF69GnGYRLlfDXy+6mWq2mfxqS2GpKKeEHDZHJHUTqtMVNsFLoV4\r\n6qXp3w86u2pHrPsPQxXdtIopS5pOnZEX41D4fGjd7LzL/83XoAL6h6ibIIgP\r\n0fw85MiHrmroi9QhH08sr64gFaWp/JN4qCua4Ae6yfmVqmYYT4WGpLzm7w6E\r\nJKzHrz59PBl1IZnQPstPczh4li+39Lglw8g=\r\n=MCMb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.17.10", "@jridgewell/gen-mapping": "^0.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.17.10", "@babel/helper-fixtures": "^7.17.10", "@jridgewell/trace-mapping": "^0.3.8"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.17.10_1651250261499_0.13818729105149008", "host": "s3://npm-registry-packages"}}, "7.17.12": {"name": "@babel/generator", "version": "7.17.12", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.17.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "5970e6160e9be0428e02f4aba62d8551ec366cc8", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.17.12.tgz", "fileCount": 22, "integrity": "sha512-V49KtZiiiLjH/CnIW6OjJdrenrGoyh6AmKQ3k2AZFKozC1h846Q4NYlZ5nqAigPDUXfGzC88+LOUuG8yKd2kCw==", "signatures": [{"sig": "MEUCIBnKTFR5NTZwVESusHAQ0gXnhV2XhgDwsYqyz9M1PnBxAiEA69HoKYxa8VeS5T5rxkEDb4+JBwVxN5gJ94I6erLtAOo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119604, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJigqbBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq6+g/9GyILx8SDbFth4c+9BLgsnl1oalZ099Gm+ahof5a70wOGJYae\r\nSXzoW/tHhJkjPD4b+Qnt9YjvRuesb3p9NyJBkWE2HzHW0Kevt0tvrWpVLEND\r\naJ46qlLMGCtEDXOn1zuqb9fVlwgqkKe7Jj7gMWTO/ZZN5sS4gxA6exIeJqAO\r\nX1HMkw6qiJ3PyQdpyRjNd/VLC5p9OmsoHCHwDNj0z1zfUL03BD7+8D12naFi\r\n0KoBdkP5B7fNUVIqviy1lqRTvMDOrg83mnnalJSilAnnOl7Q2HEAMYmTGfVM\r\nksNzs63U46RmvNK8+E7X6g/AdCuuHSvc8RkPTbaPtAqPkOMCluZBBCxrBke8\r\nElJkPghEWC0l0BaXP1Kslbq68VeHnG/W8jOlx+m49XOlXlS5AD5SLB5HUID9\r\n4ekbIUZFdS1VzPbjL29WYlUrZPL9jbLopcnGj54bLZVVc1Uof9PTgwfFPkQA\r\nNlJYaKu3haEll7ISAy9o7CfEv4LgliZAjq8IhwHt4RM156u4uPxJvvGHTch6\r\nPhVTG7f3qX2zH5TsDdt7vGziG8BIGNL9XFf53nZKCsm74NCDw3DdOBaFv6y9\r\nzl3e8j1jWlBbwk9+ZZHRuYv+77TAvpSJkZG6Qsk5cQ2tI4OheVXjpAaSwEkS\r\nfGlYCO+J9Bf8HWuqUFL8DFTOfcXOFyRiBLE=\r\n=JKjH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.17.12", "@jridgewell/gen-mapping": "^0.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.17.12", "@babel/helper-fixtures": "^7.17.10", "@jridgewell/trace-mapping": "^0.3.8"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.17.12_1652729537284_0.9864461229286441", "host": "s3://npm-registry-packages"}}, "7.18.0": {"name": "@babel/generator", "version": "7.18.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.18.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "46d28e8a18fc737b028efb25ab105d74473af43f", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.18.0.tgz", "fileCount": 22, "integrity": "sha512-81YO9gGx6voPXlvYdZBliFXAZU8vZ9AZ6z+CjlmcnaeOcYSFbMTpdeDUO9xD9dh/68Vq03I8ZspfUTPfitcDHg==", "signatures": [{"sig": "MEYCIQDv26o77TOkyIp2a5QGq+qlHyhA9n8oeiX/zXWmgmTzHAIhAJZKKxk+xVDm1DHSzyfxXBc3zbTrs9N3oixe1Je9v732", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120303, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihomFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqGBg/+JM4hnMrmFF/BFakF3LHzt6neZ2unTiJtgcprctKUOu+5TKz7\r\nDfHQjjPUR0hY9PYsESqbkg7qZhB8BCGoTAUHbZ75PW3KjyMECvcV/CBCCf6P\r\nDZT2v8UHzy9e0Yj5iEQKi9/p0OFZqUPzNiLBfGqXYeT2Sq5QDPks3p4ZXJHb\r\nuqWsNlXZGELzs+nJKA1vdbdk3b1QfYFSEnwsTtaix8eyvqQ0zNqwU4siSsXL\r\nnyD9BjIxQ58/kNnxUXyekefy2KrALFji42uOEZjBz4mHfqYHynJTT/7Knncs\r\nsQnSiBNRcxttJrh/JdiBN60S1A2jOLlRYUE3VZK7Y7LOay7IkhKBorfiYE22\r\nRwSTsaqGTk4VV8gvgSTjv/KxC0XbMX8VQA5t3b7KHYoPtoxVvWrBAkAw+pRV\r\nuW0HRiSF6YzgJfBVxfpIRCzk3RCvkaWeTN7TSHU07oBkP8hzh0Q9dmDvTtVa\r\nHa43st638AZZMKf1KKXWl40+AvNgxsNj3NLGvwTYKqWJFf+yTS2tLDWHL8hr\r\nOO9NiYSC19mJ4fkWPJHaqrtmRN7WfmbdnhGKoMTeLsd3DjjKbcKJocyzWkDK\r\nj56/Vzg012hB8vPkYSjDnYnSNKJZiBjgS46m/C2SJBUBV9g98bSO7ARsqYUQ\r\nqYJmy45AeOidP2L1Gz29351QX3EYUJQeo8A=\r\n=JCQV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.18.0", "@jridgewell/gen-mapping": "^0.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.18.0", "@babel/helper-fixtures": "^7.17.10", "@jridgewell/trace-mapping": "^0.3.8"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.18.0_1652984196973_0.1927482891175203", "host": "s3://npm-registry-packages"}}, "7.18.2": {"name": "@babel/generator", "version": "7.18.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.18.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "33873d6f89b21efe2da63fe554460f3df1c5880d", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.18.2.tgz", "fileCount": 22, "integrity": "sha512-W1lG5vUwFvfMd8HVXqdfbuG7RuaSrTCCD8cl8fP8wOivdbtbIg2Db3IWUcgvfxKbbn6ZBGYRW/Zk1MIwK49mgw==", "signatures": [{"sig": "MEQCIGvuXYIGvxc5Lhkf2otTm5ebKMSwGDp5cIX91igITRoRAiBUlOAMJAD+TjRF+DlulxN6819lWO6JyLpAs+H9xSpB8g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120303, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijfPvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmplSw//Xruw+hLzk90EzmFIO6TurWaLPDmMZEQz3GQBYkCfyRp7EUL0\r\nO7a6orGdNMmGpuU4bTwPljozMdQWy1JB0ZnlaZ2bXaMa49eKVZ1pzhh91sZ0\r\n0Zap6PeJ4eSGW8z28IQXL/uo9rqomnFx+NfXseShhNDxGf+fiRepd7tfLX2a\r\nKj3LkavSCqVjXyqibqR9kew5KR9vZQRlCbocW1dmvp9GXzFTMlv0UYtLRVBQ\r\noQqH/ulEiUXdZN22Hgwt2IlAX7SL56P9xQjFUWlf3b8onJZZVfSecX/iNpOL\r\n+Qq3vkBiiWhLiwBmmeZYews+bnEXoVMZVeFUuuDOgsUVzCryHwEPCwaOX69O\r\nQI1wH71xvihD+EcxSV2xQW6Midk1D8WwHynxRBltjRgD4mSruafG+z5Uri65\r\nJk9HMVD7M1xbBPQ84ZJ9ELYAXgP6lfLXIk2BX1cl3iSHKA+UJbYkYljdArht\r\ns7sqcohgNO/gqEj/VdtcDj9QIzpjIECoGRnhrZBEAYGWlwNFCLA+OT/ZMeu1\r\nIrzAjDrOrqMVtcZIDDejfKso2mHnHBFzxxw0JcZMlGJ7/EQQXyEA3dGPk5l0\r\nLGuO/OoJzbgh5z39DRX7dNh9sSzeDqMKg6JnJYbFLzqfm47ZT0UGLzD+51UE\r\nuWPWs0h1Fo8mk0lf5+beTESu1OznOuwVBqE=\r\n=dJp5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.18.2", "@jridgewell/gen-mapping": "^0.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.18.0", "@babel/helper-fixtures": "^7.17.10", "@jridgewell/trace-mapping": "^0.3.8"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.18.2_1653470191716_0.1994487008827701", "host": "s3://npm-registry-packages"}}, "7.18.6": {"name": "@babel/generator", "version": "7.18.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.18.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "9ab2d46d3cbf631f0e80f72e72874a04c3fc12a9", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.18.6.tgz", "fileCount": 22, "integrity": "sha512-AIwwoOS8axIC5MZbhNHRLKi3D+DMpvDf9XUcu3pIVAfOHFT45f4AoDAltRbHIQomCipkCZxrNkfpOEHhJz/VKw==", "signatures": [{"sig": "MEQCIFhWN8awm93Ude325zAq8eM53ZRFSKnsLhmb+yn4eRgeAiAQl1Ckq1WWDFWuHsmLy1KL6bsvAcnNzAWXEO2cd+1h6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120240, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugnvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr1NQ/5ALrBaQF9zA67I86rIEdw+75JCKlzL1S9EZxSh5Vokt4wXnpL\r\n/rpZCHqeN88j5pONq1l5BcSMBY/fZ+sNkPLKVEFvSt0Z8l5HUmxWMKBKPaur\r\ndpEqTmBYnInpiiPPLSZaVLYv78uryy1Ka5MX4IvnhvQK49MjiZHvLy0xYiEn\r\n9r6S/5zWw4wwDr7/33E+5YZKc+O5Eeny7XapmV9hx3czsAuFGzlGwiAQxCU0\r\nfIlvC+kvDsjf/XMNmzNsi/ky9AYRW7ieJr25jFdEZhDV6TpTogcLQJWZRtUy\r\nxDwBkvi9Y7iuXxY+STphGzzkv9+pFhYBECMgEFKhn8J3LSJlQJ81S9KUjRkZ\r\nKOmZivAd6dBAbjru7562foLoludz7sExAjpR/GX8u64s80A5OWX4TV/IwuzY\r\nerFeelNA3qTItONSD1/OFoqeYSDx2xGGY2O4NWAU/NKmrK4lmXKP6g8lxI1s\r\nt4PJ3Nzv1BFFpfr2KUfTpHf54dtAtWm7h4sfKCPwi2Ik1COhmaPcYvDtoJN8\r\n/aC8QkpGWZJNSDmcjt2xrkZiLXCiJaSGoB/Xov6kZ4KvgfSrGfWOUIq9lNv/\r\nSAOr70cA1NuTCNRX9znDON5cNHxLBBnjWE3oLYchQjhnjvJHQwUiKcLVAVGc\r\nkS/vDeeTBSA94LiS0di9y51a2t3okTfke9M=\r\n=Tdu0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.18.6", "@jridgewell/gen-mapping": "^0.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.18.6", "@babel/helper-fixtures": "^7.18.6", "@jridgewell/trace-mapping": "^0.3.8"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.18.6_1656359407328_0.8975331586255479", "host": "s3://npm-registry-packages"}}, "7.18.7": {"name": "@babel/generator", "version": "7.18.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.18.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "2aa78da3c05aadfc82dbac16c99552fc802284bd", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.18.7.tgz", "fileCount": 22, "integrity": "sha512-shck+7VLlY72a2w9c3zYWuE1pwOKEiQHV7GTUbSnhyl5eu3i04t30tBY82ZRWrDfo3gkakCFtevExnxbkf2a3A==", "signatures": [{"sig": "MEUCIQDWFOv/o0imPyaQcBHHXe8JZL7gavxci+tj+bjBXH1RmQIgFiOFZ6f0MJc+WM29DX4I/AOB+wdXCEcRZBG4pDKfjMk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120240, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiu2TqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqCZw/+JowxhfjnPbEOjzcvOzLbgN6dkhTl0YOP3gVGcK5/JLuoF3HJ\r\nEtMFqEi6XncYZliRnDKPtktY8WkWX/Y+T36qNrA+XoiLIx3Bcc/H9+hcxWGp\r\nK50tUDckObVKutpKzEYv8zyc+VGgEqo1W0VO/KR2TH5/eFS+unBXtgJlyLi5\r\nJLb7wE+62C+M7ytDNwigUNlknj3YpHbONb40VRYn0Xi7/eV2+CpzdeeOPOmJ\r\n+ALOq4/yB2h3lHYptE0eKTzb3nZ4/TtQ6KZT2ZyCHMQ7SVSlqx5zv65WSilw\r\nXzk0xjPGX0JFfa6tFP96s4+aKVk79u6n/z2glRBtUrW63P3ehsP5eIsOwg8U\r\ndp8OsqDKMniuCZPqrr60hzr1Ekp/y2J5nEQ665csX/uLm1R34pIIrXFEIVOw\r\nMh4SLl7u6VqhdC7gSZs9rfa1MfbWMAHCB/NldHlrLH6adUbad3pwd8qcpL8F\r\nbjfB5sWthFGrd9HY+0kdoR7nl1tm3QsFN/491YZkHZ2DyDJeERXxWZiDbjT4\r\nhwjG1vxKVaVjg6bs0NnYRqyfrk5AJD9v3RTHlz89c34jxkPyuvAunAx37fvT\r\nbu3qGnBZmuK3pasnqH0+vYZUJF09Ss2goMtcG7RnSX7hhG/xl0z6vlsUalKA\r\ncvrq8dWCOpN6fqqn8waoDdTNK5K6zVNEWNA=\r\n=x0sr\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.18.7", "@jridgewell/gen-mapping": "^0.3.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.18.6", "@babel/helper-fixtures": "^7.18.6", "@jridgewell/trace-mapping": "^0.3.8"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.18.7_1656448233995_0.6664059252646937", "host": "s3://npm-registry-packages"}}, "7.18.9": {"name": "@babel/generator", "version": "7.18.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.18.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "68337e9ea8044d6ddc690fb29acae39359cca0a5", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.18.9.tgz", "fileCount": 22, "integrity": "sha512-wt5Naw6lJrL1/SGkipMiFxJjtyczUWTP38deiP1PO60HsBjDeKk08CGC3S8iVuvf0FmTdgKwU1KIXzSKL1G0Ug==", "signatures": [{"sig": "MEUCIQD+5ZfGUubNqKT0itJ1yju2RIEYasvOQDNPUN0qHDT9qgIgUJ/PaA+z9JxWAQwUfC9cavs43m/1e1LXHPv5g6CPuBg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 124573, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1SUnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmruJg/9GOizQ29QNId69eDr2Cp3rLKOIemRzmlvpe82MJ9m51qSvswV\r\n23T7gPeIe7ppK2dOE2/n8uI9uSb61XpooB6/YQr5OxHTJV19YudLAaUbDeub\r\nsl29sdSLkFM4IYx74kPP76/WkPSRT98Dl75hg2r5RHIRXpAdFCLEp1odFZOA\r\nQes4cOx+mrd/kulblEsvm4i2Fga/MJzqsgxeFYeH2SEMmXpf1JlQ/+AWLfp6\r\nCiDMDxLaQ3967adERemjCnE34nm8QC4AJ33E47r7M/VE7yMU7RF5MdRrpDNe\r\nGOtPd800oQN9+DXEagrZNMUAQQDmQPGNoj03p/22HHgKX3uCrX17bmKyEpEI\r\nu/pOu8Yqf4Gc44rLvQfarUc64p0kDbquJSi+Wg0dmVT5aVEaYGyQxqb2RnYU\r\naPumYVf/jJqzTFAOsbNqWREZQiVyaVcZ6oCkReHey91M38C4fyVSV56933Td\r\nXIPVdbXD/wcQCcqS6/BiWJm221wYd/bRxCAZTQbX4IYKZBTv/7f+gGCBg1vk\r\nnRoaMS2M4nYn2NzeJpM5UZd8I4WlgesE9LSZRxgMQSAHOsawZT8bTs5tIvuD\r\n/Z+pN4ykoVaZR3vFaSyZFImjUnlA0sCXgdM96eJB34B0FDZ7hE3mn6wh21qd\r\nfLABX4hn0tJskMYTo5geqy80OAsqyhfLl+Y=\r\n=oeHn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.18.9", "@jridgewell/gen-mapping": "^0.3.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.18.9", "@babel/helper-fixtures": "^7.18.6", "@jridgewell/trace-mapping": "^0.3.8"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.18.9_1658135847160_0.9795936031660857", "host": "s3://npm-registry-packages"}}, "7.18.10": {"name": "@babel/generator", "version": "7.18.10", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.18.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "794f328bfabdcbaf0ebf9bf91b5b57b61fa77a2a", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.18.10.tgz", "fileCount": 22, "integrity": "sha512-0+sW7e3HjQbiHbj1NeU/vN8ornohYlacAfZIaXhdoGweQqgcNy69COVciYYqEXJ/v+9OBA7Frxm4CVAuNqKeNA==", "signatures": [{"sig": "MEYCIQCYjFahv75YF1xas10EeF1T4MpFX9BIY+6fSmKa9Y1eHwIhAMWXc+SwKm2I0i7jY+oG1K6KjkzhIiU+HEPLm0fenx8Y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 124161, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi6B+VACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrkmw/+JAXWZvE8YxujHuIGSDuBVOFTJA+0dh8GemZycbz9rsTGD+Ha\r\nerjk7YJNYX+9hnAJjG48yO/fpb0t6v0nIR8gXbJkqOm9/ct2p7eoZ3YsqeML\r\n00XFyH8xlH0I6kbka2kHKiYIXEdhc1SJ5tB4BwjAaUVOfFcvCMIdr+3B11Iz\r\n9Q4W2OfrKY9YQinzq0hWg5tUeH3BwgnLF+7W+8NHysATPPbzOr/EEZ1NDJzm\r\nrxIrFJLX9tME97pg8MIdH8t22gCMUrJY//24maZ9mDX7ONsnxdAkqT7J2G+B\r\np7ogCckyHDiSjJvlvb6Edf/wRoDf21mIlgDB3ouZsB6Ml2p87ihafv2sXtIR\r\nOKyq4xSo1IUjzggn6aqQbpkU2Z4ZFJLhuU742kFATuyOblIBrav2DDoVduKh\r\nJNT8JF6rKMnoVBout1h+uS7fQ/hTscWyRS6sb/hBRcJZsvm6FH/v9RxpfqHi\r\nBXs2t1C+JdyAsCei1flamlOzL5y06uDfK2evlRMpXVtuSrcLOGVTzyM72cy1\r\nv1JzTzi2RojRixf+Ih6HiZKBTvc3SUXAjT14xgnAXcBwON5y9fcXfOTEfkq0\r\nv24o0Ns+dsAGJJaDYol+fTCxyxgmdWAbKw9i8hNdOuaIj4Y/5tL6im1o/Vjb\r\nJRfJzZHe6id3aWPNSzEqb2WmYx/lH8AwFg0=\r\n=Y8p1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.18.10", "@jridgewell/gen-mapping": "^0.3.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.18.10", "@babel/helper-fixtures": "^7.18.6", "@jridgewell/trace-mapping": "^0.3.8"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.18.10_1659379605291_0.50342986506439", "host": "s3://npm-registry-packages"}}, "7.18.12": {"name": "@babel/generator", "version": "7.18.12", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.18.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "fa58daa303757bd6f5e4bbca91b342040463d9f4", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.18.12.tgz", "fileCount": 22, "integrity": "sha512-dfQ8ebCN98SvyL7IxNMCUtZQSq5R7kxgN+r8qYTGDmmSion1hX2C0zq2yo1bsCDhXixokv1SAWTZUMYbO/V5zg==", "signatures": [{"sig": "MEUCIEf/tRYyJl6tw0jmBVYYUH4Uieo8kVJFfWehkUvhr89aAiEAlKTce0DjnFqoPO7+d3KP51eR4HKBIEFzWmEK/eln/+s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 124210, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi7R5iACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpvZw/8DrZKxwNX9enyNHMtY3j0wiBqg923Y1h0Ieiz9RUcrMyL1pOp\r\nnvlA+3Et3kjYQA2gDq1PlVTrFRplmM8s9PwikZWRe+bITisqbEzVw4X10vIa\r\nAMMG/Tje6FdeHFFh9V2e2E6FkbKJpFrGcWWSqOKLyocgr7ForlZd+a+eOe5d\r\nSTr7XdjBmaZuNbYZEb4x1yTnjp2Gw6n4TQF5YT8Cn22WUXxkoOLyLuQn/A1b\r\n03aL7GOX6wLI25RJJI2kOPyXBClR7EOjkpj879XLFs7wTquf+3JvPb7YdB+Z\r\n2PI3SKYFAp9ZBx6D4zXQQiipiDQvV8ynPlNgiAvxCfHdHGqrGYdetontP0Aw\r\nV2H3jjPZAGVg1obPyxDrG00xgrh0DqmiGZlVxf09fSHL2jhvw4fYTjK1caIV\r\nWgQYEl2dW1LYEk36aksFD9WUUY324Shp/K7rPmfFy5FytcHaM+KCtJD/GD7j\r\n5sJSYZxRxkCVVko350WTJM/oHOQh+1LFHBB3rW6In9b8ab0wNczEmdGwfeRD\r\n5TLAFoGX3MhQtpvhu420z7QQVbtJFF6hK7ZIWp/qdrS47d+FZv8UhHFJ9PbX\r\nxZ64gowk7ykIuUR4O1wFLIbpf5CwpkA9V7T/BHL4FkuRRd7FMf6LzeiLrDeG\r\ncubwOeOSbAKKLDKiYtTE8wGFpnmKDqO3+xw=\r\n=yNTx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.18.10", "@jridgewell/gen-mapping": "^0.3.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.18.11", "@babel/helper-fixtures": "^7.18.6", "@jridgewell/trace-mapping": "^0.3.8"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.18.12_1659706978349_0.507335841115226", "host": "s3://npm-registry-packages"}}, "7.18.13": {"name": "@babel/generator", "version": "7.18.13", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.18.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "59550cbb9ae79b8def15587bdfbaa388c4abf212", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.18.13.tgz", "fileCount": 22, "integrity": "sha512-CkPg8ySSPuHTYPJYo7IRALdqyjM9HCbt/3uOBEFbzyGVP6Mn8bwFPB0jX6982JVNBlYzM1nnPkfjuXSOPtQeEQ==", "signatures": [{"sig": "MEQCIBRCkDI4YVxTpnWSBDJm2RqFDvZpBlU8Ds4yWcSKHK97AiAkGEsW8cXfAqReZs0Bs78XjNUMyxBrzUF32J7hxKzziQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 124222, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjA6k5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqykw/8C3Bq2Y6GpWkRTuKTMUcgpZGVlAC+NBrSxZ4xJ106ipeh6GqH\r\nR8wF0TkKcsf0Jja3buU3oVlUaQ2sb5XKe0wSEJ9ed10Vts16wWwfEjNt/LfN\r\nTBj3vcz9tUItd1oME6pBtuy6+Dmom3mSRQYBX5P5ScvCEVM1zlHFFvp7gstu\r\nIV2oJ2W62WhwoliIFduRWRh1ZEPJK3mToYxi8J73Z2rpsupPyYWPXBwGgPzb\r\nPXb5AKtib+xYfBOfslkWY2KvZOOqsq32H6injadZaaaz0xzSTk0GKq6guUS3\r\nw+hlye+KzX6tUNomVORnyUYR40dzg5c00BqhFzcJgIu119bA1MnDL5px9bge\r\n0dvciBCyTrDYeE+0B7o4Lcvug/O1NQP5j9nega3jm7zraA5vtFsjOgDo4yWR\r\n2btKhFnpfq78x/N5sL2ZOiSoyBRH5zprHlEU+5tSoImTkg8+JKAMs3cfstnQ\r\nvBmMftUtWxseCl1O/BqaN1oZC4WZS7fEAys406xzTPCFJnkhWssB2K6jsv3D\r\nMhyi53eiqzc50R+CyfCq+kx5QekKyhhExD5Rn9TtUSWIgajEvPWec826neLg\r\nSP59efi9ojBco/Havh3lh7lSZDH4CnMjqxP9qW54psj4vEccGeNGsXsAkDwg\r\njPfc1/4kk52nH8jU0Oi6wOIaOZ7n6ybAsoA=\r\n=z9Jc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.18.13", "@jridgewell/gen-mapping": "^0.3.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.18.13", "@babel/helper-fixtures": "^7.18.6", "@jridgewell/trace-mapping": "^0.3.8"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.18.13_1661184313658_0.28569925719379174", "host": "s3://npm-registry-packages"}}, "7.19.0": {"name": "@babel/generator", "version": "7.19.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.19.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "785596c06425e59334df2ccee63ab166b738419a", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.19.0.tgz", "fileCount": 41, "integrity": "sha512-S1ahxf1gZ2dpoiFgA+ohK9DIpz50bJ0CWs7Zlzb54Z4sG8qmdIrGrVqmy1sAtTVRb+9CU6U8VqT9L0Zj7hxHVg==", "signatures": [{"sig": "MEUCIQDEmm8NX7tIGrBPkCJ/Dq7TWX+tuYSiQaMXjWmxy9KgEQIgHEYdv4xO+YRkHb8GoSCScpGQO5tjmbpT3qzYzVHXP1Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 417041, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFke5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpANA/8DClVS54T5Zye5IJcp3Urreu3FtZQq7Put5+mopau9HaJ0bQe\r\nTJzstWiBfnGK6SNJqKJMvy4at4P+5UmnlYQ6l6W49G1z7KfYnZkINK5Hmhh8\r\nGO8Oog6QJSdYaPZLA+SXVbR9etE/r711JlwjPqQAMUT/yaSwkHrP8RDBao/r\r\nDOci3AzH9G8oeOUktGisq558t8z5/GTNqKVQ41UFGHOY4gD1KbCLF9y8YPIw\r\n6F264gG5159uQ7JLZ+WLPZQG/OhFq0RrcJrkcIkTPeQJKr24DoQoaml4CQuE\r\n1iaRXd1hpsji14xTyIryzjQG42BrgJglH8LRia4cZK4mpl4cHCGr//iJxxIK\r\naqgTCzwj7ctoOQ/aupWNsK/D5e2frSaDkbvYzPPVK0jD4ZTaHM4XjZOE6OzZ\r\n/Z1fZzbLAZWYXjclWkDkJBw5mdpazilXpfxeX0X1o/9rx4bgx2SMWS5ZZcFq\r\nJYDt1Owhefk568qidn904SeS2NcuKnXEpwQX8RMGF9fBYMpnBlPINhA7Jcm0\r\nJguKH+Wy+BHm8mgsrxBBWciXocwaM6Gmnu+93QmVx/u/zjXun5X9ok1Mg0hy\r\n8d5ODlmPvkOd8yidSxZc/xeKlFSCIKEnPGFU4ElhOIIKb7ZCAingBirr1irb\r\nZODnOsfmq6FU7k555Pp2rEk7iNB8Nu4ZbHo=\r\n=EiL2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.19.0", "@jridgewell/gen-mapping": "^0.3.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.19.0", "@babel/helper-fixtures": "^7.18.6", "@jridgewell/trace-mapping": "^0.3.8"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.19.0_1662404537230_0.834823196629098", "host": "s3://npm-registry-packages"}}, "7.19.3": {"name": "@babel/generator", "version": "7.19.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.19.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "d7f4d1300485b4547cb6f94b27d10d237b42bf59", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.19.3.tgz", "fileCount": 41, "integrity": "sha512-fqVZnmp1ncvZU757UzDheKZpfPgatqY59XtW2/j/18H7u76akb8xqvjw82f+i2UKd/ksYsSick/BCLQUUtJ/qQ==", "signatures": [{"sig": "MEYCIQCL0TLz8vnv77vrYFRotyf4LmB3OoP8ZaoHML5IF7Z87gIhAJ7S41vMajE64evC9oZwWxXHKxsKnLK4kWQE8JvNggg6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 417100, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjM0LBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmov/A/+M5i7IO0lJOumu4nb35VpQIwwdYZEIPEvJtEGaxJOIT1PWQOa\r\n4VjGTA3rCqZKk4qYX9dRdmj2a5wANLJ5nRWJ5TBiEf3Qc01wREd42ujegzFn\r\neJkLFt+IjV/MAmkfkfEHbzzJ+QsfuelZMCXnUTBQM/zCGXVXpPGxsbRTcc6M\r\nCy4Rkvc3fkedlu9MRm9wf2soMGgttU/9bHsU7EzXyZ94zW/vH/7z35bYqQmB\r\nOjHbSeuf+7peKBzgPsGSQA2NvEhaAW2q/eCSke223/veepP+Oc2KOXsiy0dr\r\nqJ5gfEwGmL4o8DIVVsqD/kwTfyTvN8c62jr4oslQ+4tiabj2GLYi519eVtL5\r\nxdYhu4ka8s3pYWY03VR0ZEWAuIeJnYBfrnEkkTQKLkFRS+RoNmdjdw7NmvbD\r\nq5WiOBNV9K5vvQY3+vjL+/VKK57Wfsk2mtrPXFTM8x+a0bibxqbhKuGHiX2O\r\n9/OeS8H+FYdjp1c0BtUYASPwr4TbYHdXqI24sr0+RSgK4aQe+QCk27mWS0NV\r\n1CdDgjdx4U5uAS3t8Jk8CMSn2BpG9ufY72m9lnwNinE+/aC0JBA/NPhl7JCB\r\nG9AHeD+R0HetF630Kv9Qa+aM8cH0qYcv3ZMbuprb9rg3AgSQwFS0h2Wnkgqc\r\ntQIrPie+ytMy4M98d+mNZ8dGH1EUFoInJAU=\r\n=77Up\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.19.3", "@jridgewell/gen-mapping": "^0.3.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.19.3", "@babel/helper-fixtures": "^7.18.6", "@jridgewell/trace-mapping": "^0.3.8"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.19.3_1664303808853_0.73656679578348", "host": "s3://npm-registry-packages"}}, "7.19.4": {"name": "@babel/generator", "version": "7.19.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.19.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "60050cf3f0a593d7b2471b4be4f62a56b949237f", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.19.4.tgz", "fileCount": 41, "integrity": "sha512-5T2lY5vXqS+5UEit/5TwcIUeCnwgCljcF8IQRT6XRQPBrvLeq5V8W+URv+GvwoF3FP8tkhp++evVyDzkDGzNmA==", "signatures": [{"sig": "MEUCIGqGXSWmMPSXA2Ghu3HDYb+3WWdtR+qiZ4eB+pf5KgzVAiEAiCmElnJfwxGKaKXnXGK/sN4h38mOrVKOFahRa5V8u5M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 429557, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ/g+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmropA/+OWlkKAFaJthF0i1M2xIPIf83OP3jJKbq4YgJJONgzXfsabXz\r\nX70s+07nCN+04uuwupoS6HZ2j5uPSGXL4BVs0oUlk8/wuB3tc3d8vDDIsL/I\r\nJFdcKBsCW0fHQ9BmCmWbLD3X7THbS+BkNJPA3Vc+4qrLFHeGD8eCdFm+8eP/\r\ndLILYNYbspveHh/NnsZedSuxF70AyQIXdcgr8YPlGCo7c6HSOcAlrEaYKyUN\r\nojBPv/vd9MDw1QOQqUp41HfwpcDGUMeqkruY4tDAe0V8VGhE2f5zoiVfWs3E\r\nHDN4u5u4LXofu2EVJAUaKuMQkN5nQBDib2q+aVrdd9lSWbc1esFZhESkbJL8\r\nJpGrYzKr9EEo0kFdJUaLUDwSa/FAuGb2ybbsqLatI8TBCDrbqh5aK738ITFx\r\nG83ra54whdNNDwHMGzP4qvpBseaU2WszuNxZhxAMbdrKMlKQGZIWGaPTupcw\r\ncFmShuvRhBcikhmssAh0ocQw5IARrcnJB0EGrQ8ngQv9JMr0tVX8lN+y8O78\r\n6OSTdMuKVcZafOg1yVkRjv4Sp91oMmSrcRtauRJE5EZ9dtm8WWf+qBYDKUGC\r\nHqyUZJMPyrE/B5Lhwd+2bpMolsMCH5qHl8LDkQ1Y/NTIa/TLzK8a9qq/bF2s\r\nOtroFrSAHHv3/NSGYDM5O3bAVywkCFjoVL8=\r\n=5mam\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.19.4", "@jridgewell/gen-mapping": "^0.3.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.19.4", "@babel/helper-fixtures": "^7.19.4", "@jridgewell/trace-mapping": "^0.3.8"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.19.4_1665398846167_0.2242645955996223", "host": "s3://npm-registry-packages"}}, "7.19.5": {"name": "@babel/generator", "version": "7.19.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.19.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "da3f4b301c8086717eee9cab14da91b1fa5dcca7", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.19.5.tgz", "fileCount": 41, "integrity": "sha512-DxbNz9Lz4aMZ99qPpO1raTbcrI1ZeYh+9NR9qhfkQIbFtVEqotHojEBxHzmxhVONkGt6VyrqVQcgpefMy9pqcg==", "signatures": [{"sig": "MEUCIFyFvtFzNPg7CwZtQZBGP6Y3laiXWbryKuAqPsFxapT+AiEAoK4hYEwjvL7c+IbpSSJw1mZH97yQfZumvEam6p8oIpE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 429552, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRIUdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoH1hAAmQuw00qMkq0BDm7F0i2ZXZOj90ldjICbe0+wgrbSOH12lCzm\r\nA7n4L4tSVNcPYataaizj8kmNTZbLMV4C49Y2d/LmqUT3ZVo1rsoWjgZaRtRV\r\nagz0J2lNP6jcyZYg1sF5gXtBOS30imqvEVIah/83iqTLmx9JK6HCViA5RpIr\r\no2LVyO6ehCrd0kSY2oUxN6YekwGAkp0ofV/UAFNCixgFEXEKrnK+R1yeNuI3\r\ncTd+zTp86Q6MHSSn23lAg0SgeowVVIvw3H1P/fU3twMoXmT/cBZ4SI0LucWe\r\nUnGfSXiqv5I5o5ndyT8XS9GgT6VJywWku+Vkt8xS3HJLUxPj5+tzTPM9oUpV\r\nTtQTwqhYKov2jgx5PY06744tWU65XMNgPm6uVdNiYrIkVuvfa9RzRZ3jEtbz\r\nzNZ865x82dU5j5b02w44yr1VeiLles/2joBeYIFEiQilirDoaegbTrs+8825\r\nOI3XYecdlEBhQL/Cm7GyKEoHt/ubUPOIppPmNzB1HjwVwAEZ/QhBG9vQJAlL\r\nwKJVFU/oUUlzbUbJ0692nhpNS0kSyMZmNSHtyOWeQpbCzKs79i5PjcVrydHJ\r\nLluvSM8BXPYCBPaFoToLTikNJH7Go1uijwbpf9dGb+XgLHzTuCRP69htvzsW\r\nUJxnq9GYCIocHuS8LCv4JpROuYTE9IfgLMo=\r\n=uNEc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.19.4", "@jridgewell/gen-mapping": "^0.3.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.19.4", "@babel/helper-fixtures": "^7.19.4", "@jridgewell/trace-mapping": "^0.3.8"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.19.5_1665434909242_0.4533638688416848", "host": "s3://npm-registry-packages"}}, "7.19.6": {"name": "@babel/generator", "version": "7.19.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.19.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "9e481a3fe9ca6261c972645ae3904ec0f9b34a1d", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.19.6.tgz", "fileCount": 41, "integrity": "sha512-oHGRUQeoX1QrKeJIKVe0hwjGqNnVYsM5Nep5zo0uE0m42sLH+Fsd2pStJ5sRM1bNyTUUoz0pe2lTeMJrb/taTA==", "signatures": [{"sig": "MEQCIGSqcqcS3Gu214qK895wxpHJfdcbsI6aRwVhTCm+RovNAiA8HJgjjTc4IFiCfEb46p1O8vr2g56xltlqw6hu2ySxWA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 432489, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjUQ7iACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoqFw//cF8fkBuraj3ct34RNwZpFT1QatWzFyBbYgCEAFyHs6WmkkzL\r\nDOqDDhyIHDyI5+6+R3tghrv9D+WJHxAUJwmq9AysElaTyLCv+/xQA51Bp5GJ\r\nD0J4wUGWRZgSzU8iPS4Q9hZfOMLl4CmcSyZ8Z+1sZpjNsfBooU2NiZ+Bcq7t\r\n/SXfeBofddhSC/5W9enMqletOhWiQH7T38eFPGO3Wccc+bX+WNbmiJ0nXWc4\r\nq3+Bn7yk4HbNMAB8j9DlW3XjPWeqXF7acAX3WGFf2QIlpB5G4kSg4nHNydpf\r\nLfJ2XbtdZQuCYrTqT5f7h2bkHJJDwfwfAj0BiQnWDSXnRgm4a/A6xZybpP+2\r\nBegqHHqpkUE70K5fl8N4NMc72LpyBzRxh4x3WnHLLcsB6ybDZjCpcb8wQwEx\r\nRP7ELrVZkT5OwcTCwAtpHecy5aqAcZlYwxmlZZ8dfQVxt0UrzrKzgrOrTPAH\r\ncKoUozFM62A0Dh/Krl9PfirbTP8ksfHmIin2iXPDpQaXDzeKuY6A2QQg5Xu3\r\nCE8UA1nzDb59dF88SvAHR0EIh/RNwMUdgmy2PFC/IvELmWiQNAhuqcGA9o2K\r\nwCn4WuwsyuvjRMXldBqNuNPHCp5hjdrFsKFUMkIqqwcQqw5Up0McV5N9DVuh\r\nXjRST8VFRjJDkikqVGoL1er7Hn2ZqgCOeF0=\r\n=B9Bh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.19.4", "@jridgewell/gen-mapping": "^0.3.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.19.6", "@babel/helper-fixtures": "^7.19.4", "@jridgewell/trace-mapping": "^0.3.8"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.19.6_1666256609854_0.474151643617293", "host": "s3://npm-registry-packages"}}, "7.20.0": {"name": "@babel/generator", "version": "7.20.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.20.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "0bfc5379e0efb05ca6092091261fcdf7ec36249d", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.20.0.tgz", "fileCount": 41, "integrity": "sha512-GUPcXxWibClgmYJuIwC2Bc2Lg+8b9VjaJ+HlNdACEVt+Wlr1eoU1OPZjZRm7Hzl0gaTsUZNQfeihvZJhG7oc3w==", "signatures": [{"sig": "MEUCIQDzy7KLMKe5COyEnQml/JeU5NIydUafWhlpvlGOEG6NLwIgY9GdmoJxd0F9v60fvEiv5jDOQU6yjQWAB4XlRx2Smo4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 440005, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjWoVXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrwsg/5AcV1ev3RlR0Zj4aH/7G7IoM7JQlvMgmVjEJ7fGeOeFDvq018\r\nVnYct+DtM4EZauGYTEV956ndsW5Kn1lTej9SGUQzaMlEK1eGwVhmRrODYPf1\r\nPd4gm3PZqw2znVZBZbp21yrK4xlezcWFDKfNEGxvWfH+lw2F1O8nl/NZbhzK\r\nh7XakFgMubBH4rS/SKRDKRhF7iJD+NqNSqsDJZNMr28ZLN+kFKhFZV1hDF02\r\nXfHmzN1WwMBznU8JRJ9AJvS2c9qsN7HT2GQ+UMPA+hm4FrbmU7ZiVDvuKZ7i\r\n2O9042LFNt+ET1RyGxr8vvY3F67MJw5ZutKveffiyzdEgHr6j8Dv7nwi4z+A\r\nPO5ercfG/xxolJclgJu2+gFMNhxMn8qWDOp3gWdAbGh2zRSgQrTp2Ibcne87\r\nCQXFF7Tho2tktxOHGnhejwrNqfUFNLjNINZ3pjiL4lKfevIpQtCR/sncZdz4\r\nMX1CG2gdhi0buZPw1K83BlCW7pu3PZddiT5FUFA069kN++NctHU+6m4+NkF6\r\nVPVpPu7A9qC8dczMRjTT+Cm9SztYyiKV0WZmHIWmpfK6ffUj1E+oB8TYNgtK\r\nqoWHz8hNM60Y/6kgXk8o/bfH97S/E2IElHNfMZKEYYf3JNIylz3dumM3NN7l\r\n5f7MmipCUpjWgUC7tIOlYVfs2zFe5d94Ezo=\r\n=mq5K\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.20.0", "@jridgewell/gen-mapping": "^0.3.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.20.0", "@babel/helper-fixtures": "^7.19.4", "@jridgewell/trace-mapping": "^0.3.8"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.20.0_1666876759740_0.8488385476411127", "host": "s3://npm-registry-packages"}}, "7.20.1": {"name": "@babel/generator", "version": "7.20.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.20.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "ef32ecd426222624cbd94871a7024639cf61a9fa", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.20.1.tgz", "fileCount": 41, "integrity": "sha512-u1dMdBUmA7Z0rBB97xh8pIhviK7oItYOkjbsCxTWMknyvbQRBwX7/gn4JXurRdirWMFh+ZtYARqkA6ydogVZpg==", "signatures": [{"sig": "MEUCIAhSIrjoWWoX9cQaRzU/VSYatOdiK3jSwC0yctrIF0sqAiEAqISGxYsy3RaZvxVimRrpGV5jG/hFxjYpXNJLGuiGhzU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 453585, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYQI0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrc7g//YOmYv/H5+yYPEbSoqVMHQBqT2mSdmUaMI2rmYpXTHtGXWm5K\r\nAF5WdpCl/DVH/HYJk1r2jognIdSW2W5wc4s34Hd89jv5PRqqocCgYFXxMkPT\r\ngEOYm60f4VyWEzzrZbINI5qo44NRq5W0M8JQ6g9XB7SciYLqglEvCJ47NVd4\r\naMCo9byP6kbNK+d1VDMsfGnbqk2F6ez9z1wGu3FLja0g9ktLBxYcFpLFRsIo\r\nt7TSADEabXhxMOgUVLrGrbsfczHpNNOFnm6rhAGbkdbdtFwABmwsyg1qb1vX\r\nL/GnYYs/q/3V7S12pNfsRTsC+EDmOHnB8jwYaSXJuWoFH0s7tN/Z3u6K3AsR\r\nk+PC+eOs0RUb5AZPPQwwefV6ny9U0VRx+ZGI34p3P5jJoQVhUtokraKUjY19\r\nWV4CFy2ykT2jNEvfYr5L4x8y86PL+u8w4HCdJgiPV9CF9QmEN0FLGucO96U7\r\nKaM04Gorlt51+f8LBitgvro6VO9P3N/9GPQ17FfdWw2Mt/bVsiuOCxVArlnO\r\n0nZw9spz5tDGTfKqLssmppARCXkPcYEv3xmrJJWFgYV4ycS4dk0pVF3KSQw/\r\nhqQUWObpHX27rp2c/d9VAtdokyhAYspGMHWgz9c3QXFwKKA/SS/SMvp7h9bK\r\n9h/0LpSSZUzbQd8wSxkRSGxYGWfTnA7DPn0=\r\n=pGVJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.20.0", "@jridgewell/gen-mapping": "^0.3.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.20.1", "@babel/helper-fixtures": "^7.19.4", "@jridgewell/trace-mapping": "^0.3.8"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.20.1_1667301940425_0.9656926071950336", "host": "s3://npm-registry-packages"}}, "7.20.2": {"name": "@babel/generator", "version": "7.20.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.20.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "c2e89e22613a039285c1e7b749e2cd0b30b9a481", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.20.2.tgz", "fileCount": 41, "integrity": "sha512-SD75PMIK6i9H8G/tfGvB4KKl4Nw6Ssos9nGgYwxbgyTP0iX/Z55DveoH86rmUB/YHTQQ+ZC0F7xxaY8l2OF44Q==", "signatures": [{"sig": "MEYCIQDupk+N3rH4Yyl0rXtHrLjFbDvbIXbVADPjwC1kie8pewIhAOcu2tmgjieW4RVUMxRq2s2OMjfY1nx0M+5ydFcuNpAx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 452034, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjZV8aACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqdsw//er20f5O+iUTyqhDMBADEnXiLBXtHR72zoH5a7mCEJD4eQYpD\r\nvmfh3YtSALuq9jOvf8H5/Rg7q/y5jgo+LgqsspW3KbGoeuIzeqyLkcawogoq\r\nL/Lrnh/OLOqy0JukWlyQvVDhLv/AwsS4HOxP583f1VfDXlLfsAbB8PRiYADm\r\nOsxJsa+tf3mNKOFUkcphOkqQYG6M8E1s9kwjRynG6gjVyttOzwEyb/pXkG2v\r\nNRUSsO+8bJt770zCe6He/8GRVqa0RrC0oawGyqmLOaS8EzXCwFnIuk0bAOXV\r\nPM2+zQY8PDPD7JYqfSLXB+o+AaZQ79etc0L+mGAH6n2Gc4H7bLgFJt1TKQ2D\r\nsAlucjMcovJ7DUtHIIA0naO+M5xfB5npL/IZVXfokXJZc0bS795RwQIWFWm1\r\nK8MMRXaJZ0YPg9zEJTBqtKyuI2n4UJ8NdhRIEK9tKFD48XuWm51j1jB7tCCr\r\n5cHZLdbS2iWqif4HAvhjwBQKp8XRFcDHaiTQWpMn7WarNV+IVviDCC2QxFvi\r\nBr+wW/L+oXMJobWJimFbf1TMJvPHrtRr5mxNaYZE+AhwZBxNnJWHNyNDY37Q\r\nrfR3lwZ//5ohdIy9eSeG+bI23VCgNicecHH4kympQMz2z74Lle45L/EFamCY\r\n8exKbZm8OrfjbAkNasDQKWM5jEc/i3M4wUI=\r\n=hAnO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.20.2", "@jridgewell/gen-mapping": "^0.3.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.20.2", "@babel/helper-fixtures": "^7.19.4", "@jridgewell/trace-mapping": "^0.3.8"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.20.2_1667587866504_0.32860460152983095", "host": "s3://npm-registry-packages"}}, "7.20.3": {"name": "@babel/generator", "version": "7.20.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.20.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "e58c9ae2f7bf7fdf4899160cf1e04400a82cd641", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.20.3.tgz", "fileCount": 41, "integrity": "sha512-Wl5ilw2UD1+ZYprHVprxHZJCFeBWlzZYOovE4SDYLZnqCOD11j+0QzNeEWKLLTWM7nixrZEh7vNIyb76MyJg3A==", "signatures": [{"sig": "MEUCIQDl+myEtootX86RdrTIfXQtjD8eZYDORZzdJizursDtUwIgVV88/gne4dWPmsGSRPm9UOolRDDCISz/IaQjAJFYVWI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 452301, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjaRRRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpBiA//djR1Ymqth1iPAvOktReA1s5GKjjwQ8FUF2ZFb6Sa3ysUvfh0\r\n6iqO4iUxvUUjjNM6TeVucm+tcsX9ECsZrPRIoHqNC/qnvZjJu20/awbJ6piP\r\nX7G+S+Wk06dCVebxTAWf+3DaVQFz0T4P1EWAdZAnhwGLzLAF8LbXsH4OCa4f\r\n6ivEmdl43m7gYYaPgXmAXQWy5h7fIJZsVBXrr/GUv9YOh7uttR/XQjoQBwej\r\nsuwjuloy4ETDuQ7a1iAOpxbkQAIamF2kzyoezQY6guA3OrK86A+5/WYtwzV/\r\nepJfKyfoZgM8sSc5Wj67f56SylxGq5HllPRCNPI7sJaoyCgVVqclzs83LgC8\r\n2ZrGgzGC45pQIO5sN0s4n8kbGuBikbirEdhHvq2oKpf+bsueWLTPy9vngSy3\r\nHNtIt+amv57iAOBJtHG/72piNCFD0Gm55di9A9M/z2Za2YBm4R4Eu8/qFWhl\r\n8sIR8tNKmR8N1UCGhOnvZjkWCEcit31eMvY/K20JhJLEP5bOmYlWzsvcv4Mk\r\n8Gybnv0ANKwrmZleXm/CoPgoTp/aKWcS8LpjerMaRj9TSfDwc1nvCONl3ZXL\r\neqQXzez+ySbgL0lfS4YkSvH5DZ6sB3NDG+us0/H5Jk2hXE9SkJ3qwAjiIbAs\r\nOCV71HZiLyIS20JEqXfAqDeUi5lKXBJ1jek=\r\n=cfGc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.20.2", "@jridgewell/gen-mapping": "^0.3.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.20.3", "@babel/helper-fixtures": "^7.19.4", "@jridgewell/trace-mapping": "^0.3.8"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.20.3_1667830865366_0.9520761698386564", "host": "s3://npm-registry-packages"}}, "7.20.4": {"name": "@babel/generator", "version": "7.20.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.20.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "4d9f8f0c30be75fd90a0562099a26e5839602ab8", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.20.4.tgz", "fileCount": 41, "integrity": "sha512-luCf7yk/cm7yab6CAW1aiFnmEfBJplb/JojV56MYEK7ziWfGmFlTfmL9Ehwfy4gFhbjBfWO1wj7/TuSbVNEEtA==", "signatures": [{"sig": "MEQCIGNl8WHjVpltFm2x/2sDARnxI1aRgguKxLfZi0zfqAfJAiAuGNI5E2ZDaCOnb4G5tyzumjZgm/oMvnU2ATuJNkesWQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 453350, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjatdGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo7MQ/+Py1XCnq8NaoOlZO70YWyKQ9HrvFuAbltRzHQOBjdaUbnwZOM\r\n8bX4Tsu8IDlK3gY4CgJpA68wkhi9PVjQ4FlL5VwOKXEBCFHAA2NxUe1ZiqnG\r\ni9/ls1eBln0/BAudAz9e4VUAHSd395XETzFqIXLealTuUSsABX8/FIBTZLBV\r\nNDa+CsQ0BuynlrVtE9IS4uKXGNq9cZFYwUNPCavstuoCz9r9tnj+otjgpmxI\r\nm7Zxd8c5KCTW3U64/TS7QjzPk7UUDk0Q0ItiHovMjq6kIlYN8QPtCQfzL1lS\r\n/2Bbgfi2wfDud1N3I60oaV7bF+SuvZUHvoctYM2IH/twf8mxao88KtS2E3z/\r\nwprA0p1YQyyoixllSTOPJiSnleubHGFkGYy15dLCH+fnMxdN/ovzu219EJWB\r\ndw0/rD2ND0cmKMZZWYx1wv2WIDyyFgzK89Qi2+PxUqYUZoz/Hrk6PQXtxRHJ\r\nRCkgUgU0JC5AFcfdDZChsWvoefaSiplwu+JOEKipGd+4vs8uZQUvE3k+8gk5\r\ncyKyuFKqdrjupqfzScV07e9ne5nLr3CWnglH8+uz+9WFclCvGTZnAQCyuj8K\r\nIvhQ8UMaKMioPErIMxRULGwiWiuIboMByRPexkNndgHkooTYXm/vDT1flkMn\r\nrmyOkMsm9WVC3CfMIF3TccxOMV2X6UhmyzU=\r\n=timL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.20.2", "@jridgewell/gen-mapping": "^0.3.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.20.3", "@babel/helper-fixtures": "^7.19.4", "@jridgewell/trace-mapping": "^0.3.8"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.20.4_1667946310110_0.8849195161787333", "host": "s3://npm-registry-packages"}}, "7.20.5": {"name": "@babel/generator", "version": "7.20.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.20.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "cb25abee3178adf58d6814b68517c62bdbfdda95", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.20.5.tgz", "fileCount": 41, "integrity": "sha512-jl7JY2Ykn9S0yj4DQP82sYvPU+T3g0HFcWTqDLqiuA9tGRNIj9VfbtXGAYTTkyNEnQk1jkMGOdYka8aG/lulCA==", "signatures": [{"sig": "MEQCIF87V87egbO067KwPN4zOec92DeG3aEpeCPqcbdNCkXvAiAPRsWijaKpEmOS8zsAZzM47MDuKDX+mA6kxUmxqyBIcQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 454800, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhImfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq/sBAAnO4cDmBSWBefVb2o8EV0MS1u0GHWapILgIsASc24sTYzjXj2\r\nck78WepIuWrIiKMzUzrCCjWXWmRGMyjYypctgK9t/dZirG/tFjBvNzUrsvsV\r\nAffQNSdLRQBcp943VjUK+ZAKo8OEWuNVxBFSc7lebuvZScrbdstWRFxWa1vG\r\ncDidjaXY8QTFWAyK0RgTtJ4BA1LV5KqTVRlroC8X1NHWc3IxKEznzyCKUPUS\r\ndX8SEhcP699a5+L3a06JDsnSc/KkS7ar+cLMT5uXc+Ut7/Vlas8WXRjJA1/q\r\nuG2Cexg7l9jBA4jZioNI8J7MW0O7+0HmnvHc3iiUc3CisJ5DKlbgNg6wkVt7\r\ngTX7SLKxL4Uv/uFFoMMgA6RGOyEJ1CD7IGZvxPrCL9zj5rcB324arMFwE+Kk\r\ndN9kTaWJEcfrw9qh/7FMjp85sLOSaKZRx+CeQoWVkVjxtDiPzBxIDbZezgtN\r\njDOHj9PqrztVqefOZLmr2emq/z6I6CMUNsD5K5OpANrIw9bf5f4ud5cogIoZ\r\nKbPcm6mUcbr1ykeGS099LbxNO+HvJAkoO3mvhDzkRJc35RQiSksuwC/Eplev\r\nu6mTkY9gwUBoExHGfmuBoblk7QwEm35FUYJ480MPOSCTDpaOI8riAfM3pSBj\r\nNbvGaJHZz+OM7VFUPtL/GWePzL2JsLoc62g=\r\n=dmlb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.20.5", "@jridgewell/gen-mapping": "^0.3.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.20.5", "@babel/helper-fixtures": "^7.19.4", "@jridgewell/trace-mapping": "^0.3.8"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.20.5_1669630367441_0.4341185042796407", "host": "s3://npm-registry-packages"}}, "7.20.7": {"name": "@babel/generator", "version": "7.20.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.20.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "f8ef57c8242665c5929fe2e8d82ba75460187b4a", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.20.7.tgz", "fileCount": 41, "integrity": "sha512-7wqMOJq8doJMZmP4ApXTzLxSr7+oO2jroJURrVEp6XShrQUObV8Tq/D0NCcoYg2uHqUrjzO0zwBjoYzelxK+sw==", "signatures": [{"sig": "MEYCIQCT30TEAXkqMnCkxVbhCS7hCnhVa7MO3DHhdyPvA1UN8QIhAK7RrLiJT4upKUMefjhh1Cd/z0T93f4arkJ3+wit1iUn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 455402, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpCc4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqwqg/+Nvq7EibFSOPfREIHhki1shJiZHttJD9TvkpMogdp6OdXxF5/\r\nyGZh2PlXHIs5ACIa9lo/G4DH9YOhMmwPpEc3H9l2IjQ7PBy4zlP1GeMPYrPj\r\n3IyYOg5H8qY+cQ6/0cE9Y0Ag5gsGjOjzyNzxyZfBplvzFxj/xvnRB3sYeLmO\r\nQnz4raZ16/UFlwVO0aSE9ieh1i+H6yyRkMj+v7nHWeb9afQJPk9fntkdjGlH\r\nuP/yZfuku2kf6D42zIRp15Aty8iM5FqA5P/3Hyk/4NOvxVHhYUsK7EBc6m0k\r\nHtnNBpgk0W/77EypzqX5RAo9+BaLRWTsd4xGcwgr3ENOlpRMzbnzZFW04vnc\r\n9WI96ewxV/Zsd0wWDmn49jXsf5ODnGk6seBkltYBqgLwi9yhc0UfmILoreAh\r\nmgC4tFUOUrj96G6r8aFNxNgGgdb1JQLvdd9s1h1JfIYwWvAV5bTw5M36FEV4\r\nOC0TtfC4XrFyp3MIBetY4q1lnAGdSXDm6RO4bAU+QV/rWuT34mMVczrnqj/r\r\nLXOUzR2zcZf61IkUEOETXiARIic1ahTg8bgJ5QHZmtecuOKigoqQE7P1jZkR\r\nHp/mPBxE0gE8mDlBFvs5/wX/l9v/tEWiCY/Q8GfKBYpuc+6ZtbXdl1QDmW1p\r\n0ivYOB7hmn4t/jctpbygK1HasVqOFNJjoRI=\r\n=o3Pf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.20.7", "@jridgewell/gen-mapping": "^0.3.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.20.7", "@babel/helper-fixtures": "^7.19.4", "@jridgewell/trace-mapping": "^0.3.8"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.20.7_1671702328246_0.8530851120874279", "host": "s3://npm-registry-packages"}}, "7.20.14": {"name": "@babel/generator", "version": "7.20.14", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.20.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "9fa772c9f86a46c6ac9b321039400712b96f64ce", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.20.14.tgz", "fileCount": 41, "integrity": "sha512-AEmuXHdcD3A52HHXxaTmYlb8q/xMEhoRP67B3T4Oq7lbmSoqroMZzjnGj3+i1io3pdnF8iBYVu4Ilj+c4hBxYg==", "signatures": [{"sig": "MEUCIQDK0MHGXigs/a4UJTnYWWRC8N2vowX/mZO19TU69z9s/wIgC9zb/j+6+vg7iqTpqsfpfH4TDXQRlqOoMdr/1OwJ4IA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 455280, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj1Dq4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqh1A//fFGXMZfU50ehAAXQg8xEWep6HBXGHG8dwMbFNcM3+VDEBV35\r\n59um9rpFMkeliP9M1rFYkFzj5PhygWGGxubujfsxYAIYlo5ygC/j/r2bJ3Xi\r\n18SslP9LSriRFivz8I1Nl8g++3i/tfPVOPjPY8J9+0064YP1T5zZD7JOCqMt\r\nsI1VDZH2zDK/Agzu/TiGVfout2rirPRA0w1W8EYfWSqVhLPUUwv9cPoWnLZC\r\nkunbIj/BS1s1Qwc9zjbie7T4C56XfN8eyQW76up27J3i7T4kxjGzwzhlQpVJ\r\naECT0L3VNZPw/Lv4UrcwcC7saMp4HD8tKjgfdpjYa2Sk2/1yP8rwNW5rzFYA\r\nTPzukAIfnwWDcyGbZTbBS9bZEfAANJJbsHdwXElyjJ5yz8I0g3KZmvwf0+Ot\r\njGphBkdHyYOQXM1y/iblwcGI1M2NYDdIFzxQCRlQIrPdxCxDZRo2VaQvRCVR\r\n+gMT0sHZ+WukpdH52z6Tbxu6APwRhWvZII9wpzUWw7btHclYIZmou+6bxJZt\r\nVmK8DsSh0/jSsxDhHY9op6bjwGcpp/Di7dOrcaH2v9N45AkAKVrRWSi1K0CW\r\n5+dcGpYEI9v6WhalGb7pr54VutK1yS5OTBMtOWkMweKSN+F/G3Nf6+nnmPe1\r\noMlyDrKDIis/ErplDG70xDyopSZncQXx3xM=\r\n=Zu73\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.20.7", "@jridgewell/gen-mapping": "^0.3.2"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.20.13", "@babel/helper-fixtures": "^7.19.4", "@jridgewell/trace-mapping": "^0.3.8"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.20.14_1674853048739_0.00383244673582106", "host": "s3://npm-registry-packages"}}, "7.21.0": {"name": "@babel/generator", "version": "7.21.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.21.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "45d731e84f506ce02a7b22b9ba5861ea88eef64f", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.21.0.tgz", "fileCount": 41, "integrity": "sha512-z/zN3SePOtxN1/vPFdqrkuJGCD2Vx469+dSbNRD+4TF2+6e4Of5exHqAtcfL/2Nwu0RN0QsFwjyDBFwdUMzNSA==", "signatures": [{"sig": "MEQCIBvnYjLFCa7nx5qZFemHg/rB7p5OXDDdDr7M52HkX8wpAiAvbQ0NvgjtLlPcWpI0k+fPsoQyx6LlMUuTccOS4rqDKQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 470825, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj85I/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpXjA/+L9qTbHhFISgzUQxPMVevj1M9aO9V4Y8+GyTwvBvZJV0yN5GI\r\nPc2pf87vCeOUZ7ft10ePWGHZ9miN7tkeV2bzy/innKeKe/KhOF+0GIaKBOd1\r\nmzCk8DeJQ8pCvgEPz+/3H4xg/PocESNR60HuXzi8c5lPiTkS5tKW2Lf5C4Ov\r\nzFlnkAbc5H3JKJ226Mm4dy1qAF+Wm2kDtVgekyRjQh3up4JPl3C7hsDVn6yq\r\nyYYdMFmU+XLOvZm5OZv46AoDKmNc70QSDvQm6QHFEKFJsPUkn5SFBsXknZjP\r\nhj/oGHx7Y1+Fuh/Gk3XubUYjo/YL4VSiuoIyDAn8PLmdBFH8XnUUaaBbouwo\r\nIWoBLPeOBaXRkVrMga30lGCKSBk/yKEI80fXzysS51X/Fb+w4tYDu5EZbfSC\r\nWv4X+dOFuZjl9/0/FKql55EJi2KmSL0CSr+976a+3PX3i0Q0htlirNfkzjzi\r\ncKt0nEYfYE61WHcGtAAYeBak8+Cb21RJQDrce6iFefvbofDasqYKRqxbrJHM\r\nq3kvPg/lQswuyZECcy/d1I92x4AYIy0DzaAJcym/aQtt+WESR9e+HZ3BG8Gm\r\nHgzZmSDmfZk1Efb8M2ViUTidLDa8ImmdEdOHIwntCsMXnjkKWnxgylM4conx\r\nvdWklkfBvLYXyGXFjJ9f1RCR6Wpd8th+poM=\r\n=dlgs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.21.0", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.21.0", "@babel/helper-fixtures": "^7.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.21.0_1676907071231_0.3601361402505423", "host": "s3://npm-registry-packages"}}, "7.21.1": {"name": "@babel/generator", "version": "7.21.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.21.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "951cc626057bc0af2c35cd23e9c64d384dea83dd", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.21.1.tgz", "fileCount": 41, "integrity": "sha512-1lT45bAYlQhFn/BHivJs43AiW2rg3/UbLyShGfF3C0KmHvO5fSghWd5kBJy30kpRRucGzXStvnnCFniCR2kXAA==", "signatures": [{"sig": "MEUCIQCd7cKpR86/hL+j9gIHEDB7cCJ8AF5MTab7Urzh8eLWuAIgAnsfTwRR2xpFuKOqk8wFgQHqkrw8Nh+mWQeialpBmRc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 470946, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9AF0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrGFw/7BpgoTRNGt4SRi/vtSUjPnqrja81Qo2ZYSETqtAz7f5RV4Hz9\r\nAZroEPHXkhm7N2VwxgJfuB9D6BAHbbJ6nv8RbCUCLtc64wjVmIeXfyfa0o3O\r\ncb9IZ14oN7CAOwcmjLsLI1XWU6sndNudJc7JHHOPpH7KpAj7YmFeEB2Bu2L9\r\nsvaEXiL5RGwKoXOjnmMBO1fGdFsbZ4dxIj7pQXp5SZtTPzu8i6LeEXHLS+SQ\r\nbga+QjjlU8oAOWW1dSHIYiUtjRj5bRbiSl3RptrlQqEUvJIdqILoFt+yfsns\r\ntvq+kGVCqWZgRT8h07ImMq9azXyd3m0iGNoBDLCXAp3f8gIZDHFED70XVeaM\r\nvPsM3C5Y6DAbig8M5A/IPNlrDuSLq/gEpd+nM5KoEqg8LZSp7WnEhoShbdPc\r\nwjMuHnn9Kt17ekCH66yOccFAR0Fwocxqo+3ABNtOG9hr55bNdTBJ4ncd3FJe\r\nIJOFXipF5t5mqZhia+N1pp5DzAumo9BTUsE7GJ9ec/r+XfGO4obOKutRleR9\r\n0qeAGxkJ91Qg5HDFuwsNreo9EH7/RoZrbz3QnZ61E/B7z3GwvhczL7rLksxR\r\nZGOHjgEbfAS0M+EpEtTzJQSnTJhJ1lilhqGR/d6hmoSDFpyXGujYtTSnFWKV\r\n522ut4bZzNavnKjbEdRB0HQ4pFdsLdjgBg4=\r\n=PI8Q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.21.0", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.21.1", "@babel/helper-fixtures": "^7.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.21.1_1676935540167_0.13132839232397386", "host": "s3://npm-registry-packages"}}, "7.21.3": {"name": "@babel/generator", "version": "7.21.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.21.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "232359d0874b392df04045d72ce2fd9bb5045fce", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.21.3.tgz", "fileCount": 41, "integrity": "sha512-QS3iR1GYC/YGUnW7IdggFeN5c1poPUurnGttOV/bZgPGV+izC/D8HnD6DLwod0fsatNyVn1G3EVWMYIF0nHbeA==", "signatures": [{"sig": "MEQCIEo9Gan7nXlv/SzWWeBaMxItGJZ2rADt78MP35hWN5a2AiAF3aOX4znOf4hKYRmatHGO6LujpdNsEW+gFeDfK7h7lw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 477640, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkEIvdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrm+w/+IA9robhh6JCwTMaslQMXAeIxbQx6mpxQsJ2Bsf3d4K7IlJRK\r\nqrYDkamiJUIFGayj7tFw1deaD+KuHKn03ki10RSt5+qFHq5sY3qJejhY9GJ4\r\nntUoEtlChA8U1m4hnko7zUhuRN8629xG7GXk+OwDiehLpY7/APzJcUIVyvrf\r\n4ljs8lmgC2egQuAAYLfHYfs9uq6cfyc6wq3NprQN0fQEpALOhx0O9+82jjau\r\nnVhXYs/siQOEZcLr5xohKIVocXYZl0In4LtvEfL9S76sP1ANQ6J0SqHZ2B/B\r\nLx8jZ4/5SY3WScDQlaON8LfxLtxUQVmoIbP8AFU8AuINdhI30jXf/q2F/V02\r\n/E0G8d7JdRdlMUztLdVbuZFLhNK/0ucsuz3OH18aBXeTOtyMOZFtkmMM7jsg\r\nukTwgY18iUomMJD7Lt5KW7ZdRRI7NuFVK07SG/naiI/tWBKYeWrbJUpRKaqc\r\nzza9hInTiYTQ28Yw0zYwV8lyETREwfJ5es+Pt7JzLxqzQdHHDXM6Jt8ApB/Z\r\nddPLRkyoPjMGEFJHayFJRWdprwEL9dfFffZ77/oMHry3beuK9LkDlEl8Zz2X\r\n/Dr+ZSsLp4mOj8U87RS5c8a43N+xA5DK2NicFOGtJtNEXEBbMNFJjVHPt4rR\r\ni/e5Mu83PDnsP0R4vFe5s9MybPbJ5Hpssls=\r\n=DJK5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.21.3", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.21.3", "@babel/helper-fixtures": "^7.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.21.3_1678805981169_0.16553587185619656", "host": "s3://npm-registry-packages"}}, "7.21.4": {"name": "@babel/generator", "version": "7.21.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.21.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "64a94b7448989f421f919d5239ef553b37bb26bc", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.21.4.tgz", "fileCount": 41, "integrity": "sha512-NieM3pVIYW2SwGzKoqfPrQsf4xGs9M9AIG3ThppsSRmO+m7eQhmI6amajKMUeIO37wFfsvnvcxQFx6x6iqxDnA==", "signatures": [{"sig": "MEUCIQCZtuMXLDww6IFXrbcMixd70qxSswn4K7hf5spIp5Om+QIgYG04B++L4dAJ9bRD19VyVytC7EflNp4o5b4mjWZCHKI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 477812, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkJqGEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqqFg/9FpjxiJar4XVNocxWIx/RYvhnW5TXERv4J/GFV97xj6tedx1X\r\nzRjoffPJsXVfVJIwET33DUjgpwk1lxHAauoeRlu886XYiBrtGJdiuPj+e9qe\r\n7p2GH/YMnXnzNxYAMv1x8RHqNfxqUxNjjz/kVvWl15PbtFgM8ySdxsH3V+2G\r\nAytvix4sXXqSfIpUPKdkw4gugtr4WnXPWPhYUKKRnIbIejPjaXEsuZ152REH\r\nz2U5LxTSdm43NQUS66sm0VtNTvucFio0ezdS2ZckFMllptzTTHTt9+icygjc\r\nDoQHg0fcfRJj6z6v+wKH2Q75UPA1j9jNId4D7XdlBzGxgFs/xOlvaVNPjBMF\r\n5cTqt2MMa3EYGeMTYbNFPAalJ/vpxU+A/Cxdy7kGF5tQ4p4Y/eHybm1Hrsr7\r\noh05bn0jP3pgO1ZVBpCb+TyeJnZy2RnPF70GdCKZNSMlYSAWJjl2DzH/co3z\r\ngccZIyMlDq+Ulu5BMOBCSq1xM//VADBQMIFexclbdwpGWIB1ojvzOYzVUyd+\r\nh3+pKwsGWraRnfNQA5eqYnwf1E0Koq5caaiu89r6c3hWACbNaTp0ytVnP4wx\r\nI6TTTsc2NCrZ+oy/GFJdtxwD2BX3Ok8MBj3mD3DY1YSl8G4BB788D/++2Ne3\r\nbhC+v9kjJYdXrGSKn7x9h2aMrwq3fuAancM=\r\n=xmvy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.21.4", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.21.4", "@babel/helper-fixtures": "^7.21.0"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.21.4_1680253316468_0.9395474954832135", "host": "s3://npm-registry-packages"}}, "7.21.4-esm": {"name": "@babel/generator", "version": "7.21.4-esm", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.21.4-esm", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "2dab3ffbea01eabbd75a2a836d88fc2e0e55625f", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.21.4-esm.tgz", "fileCount": 42, "integrity": "sha512-g0Rcx4fvFAJfKfc1On96et7RNhVDoYhtVh4bwfJTC52sVv1QjOng6JEoBR75MNI1Vk0mrSXY5jvHk1muuyQkGw==", "signatures": [{"sig": "MEYCIQCiq2afKhvnY/E1xsZdvkGEsuuNxkhUtA+gBu4lj1fq9AIhAJrQCQfouQoa9z85telMoVpneTnhHb9vECWO7y5/amgh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 477848, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+eACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrVJQ/+JGRxaXwLnyr/4dgcdyrzGfjwipBfV9L/q7m2gAZD3W+eBAH4\r\ncchI2geVLSAyaru1+ooewD1Q+YMLGuDt0enYM0k0A9Js2HnokSV33aU2cdzO\r\nlPe6NFM+17BR8S/1oAov0YWTkHHqLwthb6Tqe1k9T4QXjoUrHgXNGL9qP9py\r\n2tjgYeKBwaK2kS3VgNBhkP+8WXWrwVjFiwJDZ+Pft8YUb4Gg38ORC3dJsjci\r\n36e/f28e6sDhUcmP2tWAqAzuocsV2RO6AnuYjvaPbCP873FnBnCHEqx93L9f\r\n4A3LdRJHGDCPF4/1nJbE0svidP7hxNR0fBm5t4lidOe/Oap9KoD9a9oMf5Ls\r\nlanBOm057C8yazzmRrx8lsD1xcX6Q9Uo3lA9W2rCDK9rh8KAB7DmScKx6gZt\r\nIyh6wj1Y9hoKsBusXNm0Jy1PO87W+kVFRS6dsiEG5of17I/Q+YpHIU4i0b73\r\nQlkPDxDXHIUoBVqixFWqJuaYkwv7Dr0YaSgO4/G99tIEVU5Cf9hjobJmAuJW\r\n4HcYzDL+oLwy/m7o10fxaLuOXTBt391O6YTQJPyiQgVwh0geheP5k5EgVg2k\r\nhj4pgtFmumjE0/fVR95HiRt5sXJSDUaP3j10Pgcq0xNTS0UmnvtjgiZa/7zC\r\n6+dVpxHWhhp7WZAx0JL5HrO+fIcG9n0uPG4=\r\n=LkoA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.21.4-esm", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.21.4-esm", "@babel/helper-fixtures": "^7.21.4-esm"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.21.4-esm_1680617373933_0.32961515922907947", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.1": {"name": "@babel/generator", "version": "7.21.4-esm.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.21.4-esm.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "f5e0377361e61e032ada6fb57fe19065c1ea4241", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.21.4-esm.1.tgz", "fileCount": 42, "integrity": "sha512-mn8mTcLNxNXAM0aI1EYAZHlTvHmruzmaz1C3Yq1DUWRm3LNgNI14uvmT7nMhf5sGUytwkc4ITWBJiM2XJInmoQ==", "signatures": [{"sig": "MEYCIQDtWRiAw5mRToUiZFDeoxKZEmoxYhJmHebA84sX0PxwIgIhAKxyLV1mfuZT8VE+hixJrqg8SVrJ56VNfes5UFIO27mq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 460088, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoLqw/9GBjhB8Z9uy3enqGGfKq1W9LNDzn6Tlebfm8q/p/TPL12ym8q\r\njmQJSgmmZeJr132Pf9OVwLfPrZYp4feC88xNfMfA0HDAzG6UZIuNBptag4/e\r\nOiOCvcUFnLe21ElAcZmHFnhME6sIuMwL6IdoQoiZ4VcWzjJlxfUuyxJ3EB2U\r\n7URb1K3UAWzwH9SlZOGQ3X4+EiqAi/HOrhfwU/zldCg+q6OrZIpl1icFAt3p\r\nYogGYQjzFTWPmtqntIbCN2iFSLOB1rrV1YjpmI3LqajQeYk4M8op4/+rP3c6\r\nIRygVVyKxEqZ6BskkamtJfdBvzl/8JcVtLAYH9huamt12mNEITNN3NKutsQ0\r\nGpS3ydmV7QM2wi3+39Mr1JPsK+8jB9Zp0mNuiN7PmY00gNXkxzXT11EIcLOq\r\nRO9BGYlYwslqROc3L0y0H8uP/bkrutvFc9DJ2/T/Kzb12RZ6yDE+nRaU7cO2\r\nGFf6gN8jzV3NrH9RKGbx9V9le6B/WfCUez9oAM1NbdeczFVt1uJPxNKYKyKH\r\ndgOclx7bD9Pd10J+9AygWdAuc+EW35TZ35tqcPLlEzcA8FMMHJxTEW2d4iYY\r\nt3CkBjnqMS1kOZynqiYpP/sursd8ik1sFp6P9uNrUuEte3uAITRbweCorIQ2\r\nD4/AuGq0CLbq5r9cKppLKArnFGttFuTHAzo=\r\n=4IYz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.21.4-esm.1", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.21.4-esm.1", "@babel/helper-fixtures": "^7.21.4-esm.1"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.21.4-esm.1_1680618086130_0.20637691731081054", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.2": {"name": "@babel/generator", "version": "7.21.4-esm.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.21.4-esm.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "bb6a8d091b49e7cd5f480db151a6b1059865593e", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.21.4-esm.2.tgz", "fileCount": 41, "integrity": "sha512-UfxjmrIhRa5B17BE0eTcAx+YzQg7Y3cEPzgncHWiymQ/rtpJ4jLBon8wIWaokfqDLNYSJU8+Tw35/TOZM3O83Q==", "signatures": [{"sig": "MEUCIHW/ovpV1C6PO0rx97Go5/hvbeOAAAIMuolQ5prNbP9GAiEAh3BVKRgevCjR5jwcBai+DuNH+2Y5MsR895g9f2XBdG0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 460065, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDafACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqg3g//UfJDf9TviBcRBrC15w6zRxztq+2W+WYMF06Ghk/GU2Zpym9T\r\nlQlKdCdu2BFtNCg4Xqmzjrv1zxpGQXXr0+KDlJuuxJkFedQV4Ub7rW08Vh8C\r\nKlLHQ30AsseVMcu/EgzHnrgroUcQTBl7psIFVst/3i1gGXRjswrgkQIru4ee\r\nkPEYPPJ2bDvS0sXkGqqgubaRpwTom/xuG6EsaiUUrSJgtVpVcbs0Cn388oUM\r\nR7B2o5AoEuJCTllLNM4Fg4u2fn3iDRiPu11qsYuNSYfxNtyShR4HdAcllWCW\r\ngg94hwSZfOmTJNgrcoss+Hxh3rDdRxj3ZZBc3QZ3FIL9diEh1XFUomMDn/Nh\r\n5rUHrptF/c1v8j3a3Adyup2gZmkP5KyKwPs3y+555GM/MKv7HcxuX454EaKR\r\ne+wjyERh2on3Wo36wUStiBMH9gjyVTqg7YrLLAGfSwofdYIdK5Da9qB6Tt+2\r\nMgQ1sKGjnBC6LDnMLgLfLv9GsAkPCcQVliseaE+nSRQa5mIIolDdaKeJ7K/g\r\nrug6hk5fPnrH+G2cykjJoBCdIaMFsRIQd5gEU0z5eNFN+skU8o0yzJBQ0KkF\r\nGojHbSB8/ciPH3K/+Zl8/Bnl48tIRI8m37jsk1WuUOBAXklApMjZ5Hr/cXUl\r\nCQW7xDk9ntyQaLhh0528SJKcksiG6efm8a0=\r\n=MNt2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "7.21.4-esm.2", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "7.21.4-esm.2", "@babel/helper-fixtures": "7.21.4-esm.2"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.21.4-esm.2_1680619167181_0.9458300028796471", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.3": {"name": "@babel/generator", "version": "7.21.4-esm.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.21.4-esm.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "2c8d8efe624bd6e9dd41f4ba4a77f70acb7f744c", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.21.4-esm.3.tgz", "fileCount": 41, "integrity": "sha512-5hPLda7cjefKkh2L2eJLNC1JaMZFpEd9zvxVPPBHsChhm71iX02oSR42GMq4gL0T/tfwLTl5NMcyq2I7sGSyYw==", "signatures": [{"sig": "MEYCIQC8KvMe8j0S44wHZOjnKHxdWMtGIl/Xl4SBq7h+UpEGmgIhAKAAkNDh+U0wgT0x2PhrvAsmtWn6KudHI+sSiDReJnGs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 477831, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr06g/9GLaVQp4KePVF8I2tnoumla211Z7m9L1EVuAyeozo6/OKafBm\r\nycmdMpmh3PwuiIkApvMqO/mHFhAsz4jdOzplSBpExr+VUCkFbyn8ggOYZh1R\r\n9ecA15Ne3zqR/OZAhXRfhZJzBlvv+UxLSMnh0fur+vCpaeU3402OwX3Hnz7+\r\n9LTenn+rU+8NukO+WMA27jIqYUN7ZBoIN/AOqUBASA7GSkqo5h/AzE4Q3Pov\r\nEuMoZvYV2+w/SVlHJh57r4OY488NgoXLTsEnMe1zVbMAkFsyDr7F7YcA5HCg\r\nA9zLVoLKtvoyzVTQbqAQUOoxX4CjXMg/QKfSwPabjx8eZqqcK6XFRdbbFf/J\r\nElOyhzCYg8Luvv0U7GvABCBRzHxOVsiQH2Guun5ep99o63VfbpjU26hhVhIu\r\nW3DJvtnS7adL0shQlmNiYYmxPTB4BmSWRXdqBmUMkktVezJHO7/gd8jPDgyy\r\nlUhidvDx3NSrMK+iUdxnDTvV6i/rt6XcjqeYTa/hQWPs0GLl6QB/j2ZJk22H\r\nw1CVh18FqggQIZiipZQkfObIWpxMEp/Nc4qMwf1BTiusRGuvcFl0utYtRTjd\r\nUkFr2ZyYzpkr+Nm7nZ0RtF05bmhf7+JlQahGXVjyroLrzxyAafjc3NQgewvH\r\nLOSOa0KacKQL1x3xb2q3YRcNC74/XQBE8Zw=\r\n=pDp2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "7.21.4-esm.3", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "7.21.4-esm.3", "@babel/helper-fixtures": "7.21.4-esm.3"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.21.4-esm.3_1680620176060_0.9782833519386303", "host": "s3://npm-registry-packages"}}, "7.21.4-esm.4": {"name": "@babel/generator", "version": "7.21.4-esm.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.21.4-esm.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "892913189991df229f52b687cda343e2cf39e509", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.21.4-esm.4.tgz", "fileCount": 42, "integrity": "sha512-vONdDeWvcWyK1ayv+87LpUPWbKiSYwC1MSzxstat0479xSreqUJh6dwA8Exxl++rfgrNJ7wgyQeoMeg+6+kPaA==", "signatures": [{"sig": "MEUCIDoUSDUPprXXHVlyewtSPe/47IDeQ8/5RzIbkTQ+/PcsAiEAjBgur4YmOfF+gZ2+eq+/mTaol1TSf1gNloZOzJGG5sU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 460085, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6XACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq7Pg/8Du1N739tGeIOYTKRDZZlvJp8RAxND11wruyO4CTJkYTWo/Zr\r\nr0I24T4+oIAwr9bZM3S49pIQ/bXWZRTvcSHkJ/8+OGPEFwSr+T538anLR4Lk\r\nNbXeIJzNsWUKT0jE5UDRIHZdYcfzUAH9ylGWiddVCWBQ9Wk9oT3La6tdOXUL\r\nXDB26KEpeHJR7MZkq3ii9CUdvP1GYmECYhvYV38GnN4lg6n4XM5P0t6n9slM\r\nleVzgYp9fIZ4egZsIx/oe22ExBDYwdJf0/D1B+D/bbhb9TyabYhmzF9r52lI\r\nIZzgVDogtcXp5FRTt2bOCbcH8fNsehmkh7hoYZEcN+d+ET0PJ5bGjYmnhQgL\r\n6sLbVzdmYqIXvOnhvWfPt2YmZHwlO1QgNjP7xXLxx6NPeg73ehr19TskoAbc\r\nJqyy+8kpGpZgQMpSMbZJH+aS1m3N1X8TkXqhQB/Xyok4PQWYujTmNV/F6PmO\r\nP4iIHlk4bjST6vXJh1hpLM4wWB8LEpSBuhEAGpPp0zTLj8gwjFJEHKULklTk\r\n9INTDsOyczWboNSLn+V9l4L7PWcLYefA0gyxPtTjAclEfs3unXw4w8aN+xJs\r\nhRuGWpZIey4Joun/nOtcJAp/pap+zzIG3JUlPfBLdlogEBl+Lz8b4Dp5T4ED\r\n04XCCF/CxeC0nDh6nGr2oTa4fxOMk3x/bg8=\r\n=VAPl\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "module", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "7.21.4-esm.4", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "7.21.4-esm.4", "@babel/helper-fixtures": "7.21.4-esm.4"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.21.4-esm.4_1680621207685_0.22986816133902677", "host": "s3://npm-registry-packages"}}, "7.21.5": {"name": "@babel/generator", "version": "7.21.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.21.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "c0c0e5449504c7b7de8236d99338c3e2a340745f", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.21.5.tgz", "fileCount": 41, "integrity": "sha512-SrKK/sRv8GesIW1bDagf9cCG38IOMYZusoe1dfg0D8aiUe3Amvoj1QtjTPAWcfrZFvIwlleLb0gxzQidL9w14w==", "signatures": [{"sig": "MEUCIG5TBo9AtZavOa+HThlo8way41yZTZXnabqegBvw5866AiEAje9F1WnjK9B5LoQ1dQ3KSg/JTg0VMwcW0DSkKd7ODx8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 477820, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkTCN/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjPw/+KFmkGAoINvD80SieXUMQi5fClzNzg+jz8drSHWmbxgOHUqv/\r\nUFNpEA7Q99j/EYhUUKEW6XDKeCOLEaW0aLpzinBCMGc8jEDl/4lUzCLn+glE\r\nn7np3TwR6GZwregZFMz7O4IXajO/tCczm4PuZy1aQjqSM8hXI3BM4LA/xsvq\r\n/r8G8h6U5SpmmyWTVF/NxBgaKCVQNUlxJLE4UHV6RvMS+ujmjSZfsj/RHqU0\r\nNYPkMVLbrYgXQgdtf29dWx0Z8Xa65MibwkEBxqjlvXnvK2+0ao7MjBY2k7iX\r\nFa1WOPFeVVyXt6yu34xGQ2SBwbls3aSUgojDMtd0fFnZMQt/W1n6uugZTjjl\r\n9tT9hoqIbHLhB//MVpGMcijTtdzVSTtuhr2pWW6PGuRMVPcfgmopov401Qg1\r\nlXV+GvZvfeDzcDNaeZC0Lct8/t3TWGjajVTXwRp9ASuSglnCMQyntwkB6Pld\r\nGGw48gaOfuofqlA0okK6f7OENuf6fxioXZiTBTMkU+mhbjN/HgDyLrZ1C2JC\r\nD2uQmsafs8UwzVPE5tVsJU7aYvfZwfcwMA88Lb7febbX14UTSCON6wDB4XmD\r\nkmg/t+v56dijMePO4ID5poyxFo+3xh62c3kB5BtVUUDTNbpdOlPAGVpfVktN\r\nZyfTkOxwPwLR21KJVopwPnjxAv+olKUKjM8=\r\n=LqPp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.21.5", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.21.5", "@babel/helper-fixtures": "^7.21.5"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.21.5_1682711423525_0.26057880066191186", "host": "s3://npm-registry-packages"}}, "7.21.9": {"name": "@babel/generator", "version": "7.21.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.21.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "3a1b706e07d836e204aee0650e8ee878d3aaa241", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.21.9.tgz", "fileCount": 41, "integrity": "sha512-F3fZga2uv09wFdEjEQIJxXALXfz0+JaOb7SabvVMmjHxeVTuGW8wgE8Vp1Hd7O+zMTYtcfEISGRzPkeiaPPsvg==", "signatures": [{"sig": "MEQCIAvjmOGDzCIkTEHlYwQXfg6p18wqt1lwgDnNqU9PtWdHAiBngEXbAgIStyOQPwI2G7HoxaRg10OVgWDCIQpUBGx++A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 478851}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.21.5", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.21.9", "@babel/helper-fixtures": "^7.21.5"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.21.9_1684749739229_0.24313418215599447", "host": "s3://npm-registry-packages"}}, "7.22.0": {"name": "@babel/generator", "version": "7.22.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.22.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "59241bf17ab7a9b0f7c339e16355366ef2a1a6e2", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.22.0.tgz", "fileCount": 42, "integrity": "sha512-tyzR0OsH88AelgukhL2rbEUCLKBGmy2G9Th/5vpyOt0zf44Be61kvIQXjCwTSX8t+qJ/vMwZfhK6mPdrMLZXRg==", "signatures": [{"sig": "MEUCIAKOLCjxwoHlkFaZvnjnxPnUVKICY2ZTE/QxY57Lu9b6AiEAmQOla4e1cUp4gWwwmooDShwJo/wS/e4qcLp83Qbr4o0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 482281}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.22.0", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.22.0", "@babel/helper-fixtures": "^7.21.5"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.22.0_1685108738065_0.31391103153655364", "host": "s3://npm-registry-packages"}}, "7.22.3": {"name": "@babel/generator", "version": "7.22.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.22.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "0ff675d2edb93d7596c5f6728b52615cfc0df01e", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.22.3.tgz", "fileCount": 41, "integrity": "sha512-C17MW4wlk//ES/CJDL51kPNwl+qiBQyN7b9SKyVp11BLGFeSPoVaHrv+MNt8jwQFhQWowW88z1eeBx3pFz9v8A==", "signatures": [{"sig": "MEUCIBLdGMRDS0gEVuuJJ3tGFjrFV2XGMgVqpTwwh6HGbG8vAiEAtiesAu/ALVrZC3M4qogLepzr34+544WRQT3AoJziJMI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 485641}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.22.3", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.22.3", "@babel/helper-fixtures": "^7.21.5"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.22.3_1685182267823_0.7806052192497626", "host": "s3://npm-registry-packages"}}, "7.22.5": {"name": "@babel/generator", "version": "7.22.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.22.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "1e7bf768688acfb05cf30b2369ef855e82d984f7", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.22.5.tgz", "fileCount": 41, "integrity": "sha512-+lcUbnTRhd0jOewtFSedLyiPsD5tswKkbgcezOqqWFUVNEwoUTlpPOBmvhG7OXWLR4jMdv0czPGH5XbflnD1EA==", "signatures": [{"sig": "MEUCIAF1pkET2g2PNTA7mVtj8ezXaaSUy1DHdBKLOwSdMW2MAiEAsWaoqYYWMDQphIcuY0dhcyj4IvRczEgA4zIqD8Mi2iQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 485641}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.22.5", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.22.5", "@babel/helper-fixtures": "^7.22.5"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.22.5_1686248485312_0.16592814338697992", "host": "s3://npm-registry-packages"}}, "7.22.7": {"name": "@babel/generator", "version": "7.22.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.22.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "a6b8152d5a621893f2c9dacf9a4e286d520633d5", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.22.7.tgz", "fileCount": 41, "integrity": "sha512-p+jPjMG+SI8yvIaxGgeW24u7q9+5+TGpZh8/CuB7RhBKd7RCy8FayNEFNNKrNK/eUcY/4ExQqLmyrvBXKsIcwQ==", "signatures": [{"sig": "MEQCIHffxlKsqF4r+v4zYP+ZwrD5LX/NJgUU1VbFREU/PRHbAiBQj7pbrB+NWgbQmznF7Cngy/phdMszrjtzTezTfJHlNA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 485479}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.22.5", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.22.7", "@babel/helper-fixtures": "^7.22.6", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.22.7_1688634236477_0.40353885102180254", "host": "s3://npm-registry-packages"}}, "7.22.9": {"name": "@babel/generator", "version": "7.22.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.22.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "572ecfa7a31002fa1de2a9d91621fd895da8493d", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.22.9.tgz", "fileCount": 41, "integrity": "sha512-KtLMbmicyuK2Ak/FTCJVbDnkN1SlT8/kceFTiuDiiRUUSMnHMidxSCdG4ndkTOHHpoomWe/4xkvHkEOncwjYIw==", "signatures": [{"sig": "MEUCIQCsPt3m+1eVNGUdt5Dh2oBlzAxE6f9x5n1ebx9GumMY1gIgcsBjnbiTonAgH6LFhJyMEn47qPhoIKlXpLrNa6bL3oY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 485667}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.22.5", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.22.7", "@babel/helper-fixtures": "^7.22.9", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.22.9_1689180811290_0.5630689675173342", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.0": {"name": "@babel/generator", "version": "8.0.0-alpha.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@8.0.0-alpha.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "7a9b131410af28a3bf6543b29d803d86298446a2", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-8.0.0-alpha.0.tgz", "fileCount": 41, "integrity": "sha512-4+Mg9BvYCpAo36gqrDRH2evhwhEiNuwv566tRMv9KX58dWkpX7fmATID958rmprwMbXL9HB4BYS9KlP+ljLGZA==", "signatures": [{"sig": "MEQCIFTPMBXXRQOGuD3vTzGNOvrjPbslPKN8a4+UwX2J94iIAiAxyYp0M+EOEnqc/DAx8tm5gSg1S3DOQ1oD3OxkFfvLig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 922533}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^3.0.2", "@babel/types": "^8.0.0-alpha.0", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^8.0.0-alpha.0", "@babel/helper-fixtures": "^8.0.0-alpha.0", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_8.0.0-alpha.0_1689861602366_0.15127546142993542", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.1": {"name": "@babel/generator", "version": "8.0.0-alpha.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@8.0.0-alpha.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "c1a356960c50fff930e6229164a5ec81f0f6623f", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-8.0.0-alpha.1.tgz", "fileCount": 41, "integrity": "sha512-6uv/9Lz1nKGG3pO0lA4jdH8YjCugN6Q64i8Mog08we0SuDbx5spV/h38oJABp3pYkTxANC0w8ALNRUNjD1pgFA==", "signatures": [{"sig": "MEUCIQDoxx7tXanMaQ4nmKOLJYtr1MfPnqb7meKarv3c4P1hHwIgNq+jmSLnCKwKX7iPZP4NAEY2YOnb0DlpjruDp2e9QAQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 922376}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^3.0.2", "@babel/types": "^8.0.0-alpha.1", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^8.0.0-alpha.1", "@babel/helper-fixtures": "^8.0.0-alpha.1", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_8.0.0-alpha.1_1690221130066_0.09934053577133328", "host": "s3://npm-registry-packages"}}, "7.22.10": {"name": "@babel/generator", "version": "7.22.10", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.22.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "c92254361f398e160645ac58831069707382b722", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.22.10.tgz", "fileCount": 41, "integrity": "sha512-79KIf7YiWjjdZ81JnLujDRApWtl7BxTqWD88+FFdQEIOG8LJ0etDOM7CXuIgGJa55sGOwZVwuEsaLEm0PJ5/+A==", "signatures": [{"sig": "MEQCID5QYoKfiZDy/zMKrA8r/dWLHPlT3lGMy2GYX0BUJ8zFAiB2mf4SRmrx0/bA3MZk2vmGdtWoNsaoyfzU8fYroPkNww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 485151}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.22.10", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.22.10", "@babel/helper-fixtures": "^7.22.9", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.22.10_1691429120460_0.8741323913521286", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.2": {"name": "@babel/generator", "version": "8.0.0-alpha.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@8.0.0-alpha.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "b7297d5dcd4fad02eee9c9fc584a991c051923b8", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-8.0.0-alpha.2.tgz", "fileCount": 41, "integrity": "sha512-e54eb7+TYFYAC5+hAIBLeiYdRl/bASjqdmz8KQDzNKeKSC6tYloEAlODw/k++xgNTCCJzneNiP/s8PxphZYiTg==", "signatures": [{"sig": "MEUCIQCbSvp3FdwgtDUPgVenhNBxt97oQ5LyDmwwkw6KxADW9AIgCLdI0d3SR3h5+n1xX7K6czVdhtKCkHtRXGRmuUnEGrA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 922376}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^3.0.2", "@babel/types": "^8.0.0-alpha.2", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^8.0.0-alpha.2", "@babel/helper-fixtures": "^8.0.0-alpha.2", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_8.0.0-alpha.2_1691594101835_0.6057900756629719", "host": "s3://npm-registry-packages"}}, "7.22.15": {"name": "@babel/generator", "version": "7.22.15", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.22.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "1564189c7ec94cb8f77b5e8a90c4d200d21b2339", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.22.15.tgz", "fileCount": 41, "integrity": "sha512-Zu9oWARBqeVOW0dZOjXc3JObrzuqothQ3y/n1kUtrjCoCPLkXUwMvOo/F/TCfoHMbWIFlWwpZtkZVb9ga4U2pA==", "signatures": [{"sig": "MEQCICD/hGFWzcMLCUYyVOfbpdGP8xEDPb1506zX9ZR2CyFWAiBJa6YEGaX9Nt4ZUZ3+RpmlvL3WbqejKP4LQbd2G6XI3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 485365}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.22.15", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.22.15", "@babel/helper-fixtures": "^7.22.13", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.22.15_1693830317118_0.343760933362107", "host": "s3://npm-registry-packages"}}, "7.23.0": {"name": "@babel/generator", "version": "7.23.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.23.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "df5c386e2218be505b34837acbcb874d7a983420", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.23.0.tgz", "fileCount": 41, "integrity": "sha512-lN85QRR+5IbYrMWM6Y4pE/noaQtg4pNiqeNGX60eqOfo6gtEj6uw/JagelB8vVztSd7R6M5n1+PQkDbHbBRU4g==", "signatures": [{"sig": "MEYCIQD5IQI4LetOUuvlwoqlpqzf0PSs3L/Okye7CI99JaLa7AIhAJT2kuaRFzkjENxqjU4/UsYHkXGNuABggMhCqXcjYmNW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 487035}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.23.0", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.23.0", "@babel/helper-fixtures": "^7.22.19", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.23.0_1695629486714_0.2372688476500684", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.3": {"name": "@babel/generator", "version": "8.0.0-alpha.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@8.0.0-alpha.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "df06de6959bde625830f1b3c656a4400abee3648", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-QjbaiyfsBIN75td1GZl/vbhCr2bCXBqtE/E/0Cwzi/hy2xgoqfmAuTr6m0uhTL4foIjB3TgSM1BFz+tgCL8DhA==", "signatures": [{"sig": "MEQCIAm1S9kuBWmK7lVfmOuDt0y6ZPZzrUJniTwiemdBjgnjAiBM0MNcsk3BG8DEkTdb4EhjhLvkh4MTYNc+RCMG9vG+lg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 481215}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^3.0.2", "@babel/types": "^8.0.0-alpha.3", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^8.0.0-alpha.3", "@babel/helper-fixtures": "^8.0.0-alpha.3", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_8.0.0-alpha.3_1695740224935_0.37381856257035184", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.4": {"name": "@babel/generator", "version": "8.0.0-alpha.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@8.0.0-alpha.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "7080d8c998163681133a370d7c1c8316872aff4f", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-hPJE2QlgK0n04gn77vUt6HDSni1we6DoMHYWGQDHLCt1Zv25DJbNDrKde+jcojlMk/Ey/5TYmg27wC97E3Nh3A==", "signatures": [{"sig": "MEQCIAycZfekudVURla5egzln3yyDE6i82pDrgP4AO38N9EVAiA68D+pzi7t1ODZmtcRkVK5H1W41M4tVshFekQw6goElA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 481215}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^3.0.2", "@babel/types": "^8.0.0-alpha.4", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^8.0.0-alpha.4", "@babel/helper-fixtures": "^8.0.0-alpha.4", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_8.0.0-alpha.4_1697076385354_0.9146167594944388", "host": "s3://npm-registry-packages"}}, "7.23.3": {"name": "@babel/generator", "version": "7.23.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.23.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "86e6e83d95903fbe7613f448613b8b319f330a8e", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.23.3.tgz", "fileCount": 41, "integrity": "sha512-keeZWAV4LU3tW0qRi19HRpabC/ilM0HRBBzf9/k8FFiG4KVpiv0FIy4hHfLfFQZNhziCTPTmd59zoyv6DNISzg==", "signatures": [{"sig": "MEUCIAUU8CykTpBIWB4ImTTnOqEfzzIn9SBQntMetf8l3AXUAiEA0m6N4DDcQ9cGOuVo7mWsG4y1QeTnzRz3/pEP/7nIegQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 490682}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.23.3", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.23.3", "@babel/helper-fixtures": "^7.22.19", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.23.3_1699513447029_0.5204184243480043", "host": "s3://npm-registry-packages"}}, "7.23.4": {"name": "@babel/generator", "version": "7.23.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.23.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "4a41377d8566ec18f807f42962a7f3551de83d1c", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.23.4.tgz", "fileCount": 41, "integrity": "sha512-esuS49Cga3HcThFNebGhlgsrVLkvhqvYDTzgjfFFlHJcIfLe5jFmRRfCQ1KuBfc4Jrtn3ndLgKWAKjBE+IraYQ==", "signatures": [{"sig": "MEUCIQDC9srsDCJLF2vgnBsq2XcSh8H1OSyQXTxJF50/jbj5hQIgeLyMTZLOhKBF8jKOlxa8fa9cqc5nYxMigxISXTg1t+o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 491870}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.23.4", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.23.4", "@babel/helper-fixtures": "^7.23.4", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.23.4_1700490137289_0.7312296953726607", "host": "s3://npm-registry-packages"}}, "7.23.5": {"name": "@babel/generator", "version": "7.23.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.23.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "17d0a1ea6b62f351d281350a5f80b87a810c4755", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.23.5.tgz", "fileCount": 41, "integrity": "sha512-BPssCHrBD+0YrxviOa3QzpqwhNIXKEtOa2jQrm4FlmkC2apYgRnQcmPWiGZDlGxiNtltnUFolMe8497Esry+jA==", "signatures": [{"sig": "MEQCIHDpgd5Anhe7X0cLQMczHdkf7O0g7aQba/H9Jx+nIA45AiBOWWVL4GIKFwou+2Ef6PyRp1vJHj1bzw2iqZrUHiVA5g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 493028}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.23.5", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.23.5", "@babel/helper-fixtures": "^7.23.4", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.23.5_1701253544960_0.9788381389152376", "host": "s3://npm-registry-packages"}}, "7.23.6": {"name": "@babel/generator", "version": "7.23.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.23.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "9e1fca4811c77a10580d17d26b57b036133f3c2e", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.23.6.tgz", "fileCount": 41, "integrity": "sha512-qrSfCYxYQB5owCmGLbl8XRpX1ytXlpueOb0N0UmQwA073KZxejgQTzAmJezxvpwQD9uGtK2shHdi55QT+MbjIw==", "signatures": [{"sig": "MEYCIQD1hxy2qPcYbVhOd5NAMJoxwd/SVAaiV17X2YulKba2zAIhAKEvnqDMetCbEWspLmTHlyL9+gQZSK6OYpBrb9whplxS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 492457}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.23.6", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.23.6", "@babel/helper-fixtures": "^7.23.4", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.23.6_1702300202672_0.8426199080766783", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.5": {"name": "@babel/generator", "version": "8.0.0-alpha.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@8.0.0-alpha.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "368c5f6c0a3fb3d25f169b1d8e7a54ed6fe340c3", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-TV0ZRXjxmOYiWVSwRg3Sfe2IQ6ocnU+TkJTYnl3yJ+jUNqV/Kx8j1AtBFCHrWKbzIGmMwZIrTHhY0ZdJ5Tw2FQ==", "signatures": [{"sig": "MEYCIQCsC+usGuJupovoLg/OJgPBPx1gpeOdAfKWmPifAV+8kQIhAIiSutGtTtPL+oltHwUQ8rVi2JtryYS1C0P+heHy9knk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 484939}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^3.0.2", "@babel/types": "^8.0.0-alpha.5", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^8.0.0-alpha.5", "@babel/helper-fixtures": "^8.0.0-alpha.5", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_8.0.0-alpha.5_1702307943510_0.41555928560949384", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.6": {"name": "@babel/generator", "version": "8.0.0-alpha.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@8.0.0-alpha.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "c049e54e880133a6b63dbcdde1c59055c7232d14", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-aw/8HUdJOWtazTC5LcpQ2KWze1tcxyxgVFCwgkm0ehtfb62LvxU1GAeiay2nZUJIo3EtbMPlp7Pn9CEa5YRn+w==", "signatures": [{"sig": "MEYCIQCA4chs8it8nn/41f4tcy3v59wkc8UYJFMlmdtDoBDTXQIhAMb9RzNsie0/ml3ULnsyEndS1ukW8y5J1MyALUDUoFYe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 484924}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^3.0.2", "@babel/types": "^8.0.0-alpha.6", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^8.0.0-alpha.6", "@babel/helper-fixtures": "^8.0.0-alpha.6", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_8.0.0-alpha.6_1706285654375_0.29211509429967153", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.7": {"name": "@babel/generator", "version": "8.0.0-alpha.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@8.0.0-alpha.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "c96f727a2de6da4edf97382bf65dfc459c141b34", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-u30UWJd1/CJ+6SBCJ7Qa+qsg/3PEGZKdAsOb50tLXBKKnEif4kfz90fH5DVCZ3nvtgWlUBR+WCqAJL/FRs/oPA==", "signatures": [{"sig": "MEYCIQD9WkgYhK3eMGJz6TRV9XEwAYqRY11OEWyVjCKRLUwrBQIhAKVVw0SvUfwW/bV8V1QiGq0bSnkGmX1RyCRjOPC582GQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 484924}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^3.0.2", "@babel/types": "^8.0.0-alpha.7", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^8.0.0-alpha.7", "@babel/helper-fixtures": "^8.0.0-alpha.7", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_8.0.0-alpha.7_1709129104106_0.10664544115980035", "host": "s3://npm-registry-packages"}}, "7.24.1": {"name": "@babel/generator", "version": "7.24.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.24.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "e67e06f68568a4ebf194d1c6014235344f0476d0", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.24.1.tgz", "fileCount": 41, "integrity": "sha512-DfCRfZsBcrPEHUfuBMgbJ1Ut01Y/itOs+hY2nFLgqsqXd52/iSiVq5TITtUasIUgm+IIKdY2/1I7auiQOEeC9A==", "signatures": [{"sig": "MEUCIFeXHZBFdQXNnpK/PlLH/p9EjWM6ilZGIZAIEj6CTIt3AiEAhTq06vJVfa6YLGhD5UaknWDfq8v8GdQGw257JGTlN8w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 492761}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.24.0", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.24.1", "@babel/helper-fixtures": "^7.24.1", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.24.1_1710841650880_0.778166199405278", "host": "s3://npm-registry-packages"}}, "7.24.4": {"name": "@babel/generator", "version": "7.24.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.24.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "1fc55532b88adf952025d5d2d1e71f946cb1c498", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.24.4.tgz", "fileCount": 41, "integrity": "sha512-Xd6+v6SnjWVx/nus+y0l1sxMOTOMBkyL4+BIdbALyatQnAe/SRVjANeDPSCYaX+i1iJmuGSKf3Z+E+V/va1Hvw==", "signatures": [{"sig": "MEQCIFjZcKoRTa8LffbrW0VQUzauXwy50b/6PQxWeFgGEgzzAiBUgNYtvYL8ADOVh+bCFq7uy5HqEX7g6EtNIf/xdh/Hjg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 492886}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.24.0", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.24.4", "@babel/helper-fixtures": "^7.24.4", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.24.4_1712163224986_0.8959217506358579", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.8": {"name": "@babel/generator", "version": "8.0.0-alpha.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@8.0.0-alpha.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "b968c5f082f59b90afeda0a9232a64c6ace7490e", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-Iryy/xA1be1YG0vMIcf9sqnP2TJYBQUjwJDHoN7hpQ0NY50w2zxRzP6LKRtZ7kpGBJs8+d4Ge0w64yNUkidfWg==", "signatures": [{"sig": "MEQCIEn9Ae7NvQO8WTVvI9L9lSCYJdfIlFvaDJwAnGjoo64AAiA7X8G0MInqBczPPgrafuAJv45MzSa0bbTG2h3Mg0zUvQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 485065}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}, "exports": {".": "./lib/index.js", "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^3.0.2", "@babel/types": "^8.0.0-alpha.8", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^8.0.0-alpha.8", "@babel/helper-fixtures": "^8.0.0-alpha.8", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_8.0.0-alpha.8_1712236796662_0.27193029972760696", "host": "s3://npm-registry-packages"}}, "7.24.5": {"name": "@babel/generator", "version": "7.24.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.24.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "e5afc068f932f05616b66713e28d0f04e99daeb3", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.24.5.tgz", "fileCount": 41, "integrity": "sha512-x32i4hEXvr+iI0NEoEfDKzlemF8AmtOP8CcrRaEcpzysWuoEb1KknpcvMsHKPONoKZiDuItklgWhB18xEhr9PA==", "signatures": [{"sig": "MEYCIQDFeJV062oeblHEyYgTD2LoPn640AH9y2iCpPJ3dEVvjAIhAIrqF5OKMFDRexIBEkdvVfVgwO6Ky3XkhDDOlKNXc+QI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 492875}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.24.5", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.24.5", "@babel/helper-fixtures": "^7.24.4", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.24.5_1714415662535_0.0994055685227695", "host": "s3://npm-registry-packages"}}, "7.24.6": {"name": "@babel/generator", "version": "7.24.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.24.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "dfac82a228582a9d30c959fe50ad28951d4737a7", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.24.6.tgz", "fileCount": 41, "integrity": "sha512-S7m4eNa6YAPJRHmKsLHIDJhNAGNKoWNiWefz1MBbpnt8g9lvMDl1hir4P9bo/57bQEmuwEhnRU/AMWsD0G/Fbg==", "signatures": [{"sig": "MEYCIQD0wxKm3TqM1ZUgdujJ32Etj39Ioxo5CwXP+NvJnL5K/AIhANszt62fA5LMqLlO1Pv0B9y7fF/EZBsPgJj3NB0RpVNP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 493366}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.24.6", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.24.6", "@babel/helper-fixtures": "^7.24.6", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.24.6_1716553478749_0.17867263358553487", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.9": {"name": "@babel/generator", "version": "8.0.0-alpha.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@8.0.0-alpha.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "cb35f7d2297fa78f7aaf7ea6be416c44bbf1be25", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-8.0.0-alpha.9.tgz", "fileCount": 6, "integrity": "sha512-dNXvWps9uq4lNW7RJzB5MuGAHJ6Jt1/9Rto5+6QDdaK8xjDu/paoqGxzLuBGeNXcZCdCvj/m9+PETtrGMaOueQ==", "signatures": [{"sig": "MEQCIDs717mxVH165A7sgwXFqo04sFfYYU3a2A5L4O5yzEjfAiAIqvk+9sLHmQ/QKIhjEU4xxHBQsJweuAhKIDguI3whLg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 488198}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^3.0.2", "@babel/types": "^8.0.0-alpha.9", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^8.0.0-alpha.9", "@babel/helper-fixtures": "^8.0.0-alpha.9", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_8.0.0-alpha.9_1717423464840_0.6434668844335956", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.10": {"name": "@babel/generator", "version": "8.0.0-alpha.10", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@8.0.0-alpha.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "06ed1886d02eb37a993a5184d0e32c6452db5bcb", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-8.0.0-alpha.10.tgz", "fileCount": 6, "integrity": "sha512-lGPwT3uDQ2uz7HvTFOylV4qZ+y7fPRH51kFFX31zMauCkv70I1d36OdJtDOnCaej19q3VzQuxuPOHGr9glqanQ==", "signatures": [{"sig": "MEYCIQDRT/utuR9pI5HWN1ACWbAZ8PY+ji7O2ibkns6sDq4FmgIhANqsnySVS4MtsrSZUQXb4BMP048sFbbsbx4F3T7XMn0p", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 488202}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^3.0.2", "@babel/types": "^8.0.0-alpha.10", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^8.0.0-alpha.10", "@babel/helper-fixtures": "^8.0.0-alpha.10", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_8.0.0-alpha.10_1717500014824_0.4921704974528758", "host": "s3://npm-registry-packages"}}, "7.24.7": {"name": "@babel/generator", "version": "7.24.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.24.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "1654d01de20ad66b4b4d99c135471bc654c55e6d", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.24.7.tgz", "fileCount": 41, "integrity": "sha512-oipXieGC3i45Y1A41t4tAqpnEZWgB/lC6Ehh6+rOviR5XWpTtMmLN+fGjz9vOiNRt0p6RtO6DtD0pdU3vpqdSA==", "signatures": [{"sig": "MEUCIQCO6UvZkCF9dQka6nCZMsjqnJ61k3HPYSsVOyrhpKnyzgIgBGHX0ZO/gF01GGwG4xou+ecaDDd7I/0v6pXtsboC2SM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 493397}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.24.7", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.24.7", "@babel/helper-fixtures": "^7.24.7", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.24.7_1717593328710_0.6168339942740306", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.11": {"name": "@babel/generator", "version": "8.0.0-alpha.11", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@8.0.0-alpha.11", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "9e68ee68da989fa812c45820f9404f01c99fc642", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-8.0.0-alpha.11.tgz", "fileCount": 6, "integrity": "sha512-Dsinj5/T/Pm0V0ZP+kJ9xA4t9Mh32kDwJFiuAlaGLm27tJBsynl66jAYt1talztNavY78MikmrZpSRHQv47Hiw==", "signatures": [{"sig": "MEQCIGhT8Pm38+tNNDRRqo6ZMxQxKpOgV53h91ICeUbis0afAiBrOQh8J7mqn7yUvN5SOQbAI0JHZc8v4x79GeoZPgnDuA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 488202}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^3.0.2", "@babel/types": "^8.0.0-alpha.11", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^8.0.0-alpha.11", "@babel/helper-fixtures": "^8.0.0-alpha.11", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_8.0.0-alpha.11_1717751739253_0.9941523556943401", "host": "s3://npm-registry-packages"}}, "7.24.8": {"name": "@babel/generator", "version": "7.24.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.24.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "1802d6ac4d77a9199c75ae3eb6a08336e5d1d39a", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.24.8.tgz", "fileCount": 41, "integrity": "sha512-47DG+6F5SzOi0uEvK4wMShmn5yY0mVjVJoWTphdY2B4Rx9wHgjK7Yhtr0ru6nE+sn0v38mzrWOlah0p/YlHHOQ==", "signatures": [{"sig": "MEYCIQCb1uX0jl+dy2Wo4EcmYVpiZZFCPTb+NPkqyDLjgbQUKAIhAPQGLigHdntNWtJ939m5XQrv3nc1dBvMqutuy+YJV0oa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 494256}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.24.8", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.24.8", "@babel/helper-fixtures": "^7.24.8", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.24.8_1720709691842_0.2664124647665347", "host": "s3://npm-registry-packages"}}, "7.24.9": {"name": "@babel/generator", "version": "7.24.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.24.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "5c2575a1070e661bbbc9df82a853989c9a656f12", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.24.9.tgz", "fileCount": 41, "integrity": "sha512-G8v3jRg+z8IwY1jHFxvCNhOPYPterE4XljNgdGTYfSTtzzwjIswIzIaSPSLs3R7yFuqnqNeay5rjICfqVr+/6A==", "signatures": [{"sig": "MEQCIAyIFYSfOoABDq/Sp9pT9LGJHOQ7tNdVJ7PtQ7AsOdzEAiATUsXuk6xt9emMCt2tVCt1k8hk+YlDbHXjDNvmNUWeig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 496110}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.24.9", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.24.8", "@babel/helper-fixtures": "^7.24.8", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.24.9_1721039671107_0.8596188375387785", "host": "s3://npm-registry-packages"}}, "7.24.10": {"name": "@babel/generator", "version": "7.24.10", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.24.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "a4ab681ec2a78bbb9ba22a3941195e28a81d8e76", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.24.10.tgz", "fileCount": 41, "integrity": "sha512-o9HBZL1G2129luEUlG1hB4N/nlYNWHnpwlND9eOMclRqqu1YDy2sSYVCFUZwl8I1Gxh+QSRrP2vD7EpUmFVXxg==", "signatures": [{"sig": "MEUCIFwd91g+VGNmRwFkKKTJG2dIg3rVCqEOEVYj1BlksgP3AiEAyvAycvkSxNizPjUDMYWaq6K0VHX/oLfyShsADYmbJ50=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 496455}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.24.9", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.24.8", "@babel/helper-fixtures": "^7.24.8", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.24.10_1721123574160_0.5506119511522101", "host": "s3://npm-registry-packages"}}, "7.25.0": {"name": "@babel/generator", "version": "7.25.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.25.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "f858ddfa984350bc3d3b7f125073c9af6988f18e", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.25.0.tgz", "fileCount": 41, "integrity": "sha512-3LEEcj3PVW8pW2R1SR1M89g/qrYk/m/mB/tLqn7dn4sbBUQyTqnlod+II2U4dqiGtUmkcnAmkMDralTFZttRiw==", "signatures": [{"sig": "MEQCIBc+cqMU6dUupRBBj6OOH9VHpZbrUYSqphg55KxcgSE1AiA8P+lDoHNRjDSB4V7Z6sQaipKzpkQTJZn/F7GldQoO3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 494776}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.25.0", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.25.0", "@babel/helper-fixtures": "^7.24.8", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.25.0_1722013165769_0.1869904067854451", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.12": {"name": "@babel/generator", "version": "8.0.0-alpha.12", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@8.0.0-alpha.12", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "a2d4a89edafc2df0f7594ad0432a23521b751180", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-8.0.0-alpha.12.tgz", "fileCount": 6, "integrity": "sha512-1ckInKJWbeGwIaAjWNoLftMA4DCxZqOHE6ZGEk4qPY7BGV627LmyvvDAFvLxOim7ORCom3rPyb/Zx6E2WlPPyg==", "signatures": [{"sig": "MEQCIAuLjI6PKNEqZg3JEoACxRIvvCCtyjGmKoEnsaia3JkWAiATwAXi1JXd74WsoDeW+Q2EB7uoyfIEJk4pIBGZVMMKYQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 488601}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^3.0.2", "@babel/types": "^8.0.0-alpha.12", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^8.0.0-alpha.12", "@babel/helper-fixtures": "^8.0.0-alpha.12", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_8.0.0-alpha.12_1722015215654_0.7197844891585632", "host": "s3://npm-registry-packages"}}, "7.25.4": {"name": "@babel/generator", "version": "7.25.4", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.25.4", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "1dc63c1c9caae9e6dc24e264eac254eb25005669", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.25.4.tgz", "fileCount": 41, "integrity": "sha512-NFtZmZsyzDPJnk9Zg3BbTfKKc9UlHYzD0E//p2Z3B9nCwwtJW9T0gVbCz8+fBngnn4zf1Dr3IK8PHQQHq0lDQw==", "signatures": [{"sig": "MEQCIAguulowHiMT+GzyRDzm3KDAJ5LrlejPwCnkWYHZwbmRAiAWx6zyB/L9LI7S9jcbxSUQed6xELaC4kOL4NzNBG/CsQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 487995}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.25.4", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.25.4", "@babel/helper-fixtures": "^7.24.8", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.25.4_1724319271461_0.6079604435765247", "host": "s3://npm-registry-packages"}}, "7.25.5": {"name": "@babel/generator", "version": "7.25.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.25.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "b31cf05b3fe8c32d206b6dad03bb0aacbde73450", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.25.5.tgz", "fileCount": 41, "integrity": "sha512-abd43wyLfbWoxC6ahM8xTkqLpGB2iWBVyuKC9/srhFunCd1SDNrV1s72bBpK4hLj8KLzHBBcOblvLQZBNw9r3w==", "signatures": [{"sig": "MEUCIQCvDgtpBTK1tEYtDjcuVexpGdFKzO//IRR552LZEI/+WQIgRMsscfaOfmREKyUyHSsExmMWSDJZOMBxbasKtUOPkbU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 488552}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.25.4", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.25.4", "@babel/helper-fixtures": "^7.24.8", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.25.5_1724401870187_0.3215757451271515", "host": "s3://npm-registry-packages"}}, "7.25.6": {"name": "@babel/generator", "version": "7.25.6", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.25.6", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "0df1ad8cb32fe4d2b01d8bf437f153d19342a87c", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.25.6.tgz", "fileCount": 41, "integrity": "sha512-VPC82gr1seXOpkjAAKoLhP50vx4vGNlF4msF64dSFq1P8RfB+QAuJWGHPXXPc8QyfVWwwB/TNNU4+ayZmHNbZw==", "signatures": [{"sig": "MEUCIQDymvxtaxS+sgdHir5zKOpKtZi2hrTUs+WNoJWWTo8FUQIgXYq44w9ch70ZmdLKUYyjKG1VUfa28Bfrp4CAGnyFhAg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 487429}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^2.5.1", "@babel/types": "^7.25.6", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.25.6", "@babel/helper-fixtures": "^7.24.8", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.25.6_1724926465537_0.12572379346777662", "host": "s3://npm-registry-packages"}}, "7.25.7": {"name": "@babel/generator", "version": "7.25.7", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.25.7", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "de86acbeb975a3e11ee92dd52223e6b03b479c56", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.25.7.tgz", "fileCount": 41, "integrity": "sha512-5Dqpl5fyV9pIAD62yK9P7fcA768uVPUyrQmqpqstHWgMma4feF1x/oFysBCVZLY5wJ2GkMUCdsNDnGZrPoR6rA==", "signatures": [{"sig": "MEYCIQDxZeTgP7HNe+0X2i/niLNJ14BLbp7TbyMG5WYbI02uAQIhAN+638wB4FIP3i9IORJ8ENjwtT5GmMd8K1GN/kQNO3xq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 487151}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^3.0.2", "@babel/types": "^7.25.7", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.25.7", "@babel/helper-fixtures": "^7.25.7", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.25.7_1727882097999_0.8867868194571988", "host": "s3://npm-registry-packages"}}, "7.25.9": {"name": "@babel/generator", "version": "7.25.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.25.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "c7e828ebe0c2baba103b712924699c9e8a6e32f0", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.25.9.tgz", "fileCount": 41, "integrity": "sha512-omlUGkr5EaoIJrhLf9CJ0TvjBRpd9+AXRG//0GEQ9THSo8wPiTlbpy1/Ow8ZTrbXpjd9FHXfbFQx32I04ht0FA==", "signatures": [{"sig": "MEUCIQDq1B1mHnxBsODi+fU5SDETDOHy0QqAdlkxG/yAZEbLLAIgEBM8BBplwXoRFxtsWDmXVpAuqfH5WvEaZJgOIPZEflA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 487151}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^3.0.2", "@babel/types": "^7.25.9", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@types/jsesc": "^2.5.0", "@babel/parser": "^7.25.9", "@babel/helper-fixtures": "^7.25.9", "@jridgewell/sourcemap-codec": "^1.4.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.25.9_1729610476890_0.5258828621251848", "host": "s3://npm-registry-packages"}}, "7.26.0": {"name": "@babel/generator", "version": "7.26.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.26.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "505cc7c90d92513f458a477e5ef0703e7c91b8d7", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.26.0.tgz", "fileCount": 43, "integrity": "sha512-/AIkAmInnWwgEAJGQr9vY0c66Mj6kjkE2ZPB1PurTRaRAh3U+J45sAQMjQDJdh4WbR3l0x5xkimXBKyBXXAu2w==", "signatures": [{"sig": "MEUCIGVn1asEwGQAQy8D3qWpXFVRosaY08OvsjTt9YmdqMn/AiEA9mmi2nmU+0eqJ0lzmEhFspORD9MmLvcvmloO/2knsAw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 542515}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^3.0.2", "@babel/types": "^7.26.0", "@babel/parser": "^7.26.0", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.26.0", "@types/jsesc": "^2.5.0", "@babel/helper-fixtures": "^7.26.0", "@jridgewell/sourcemap-codec": "^1.4.15", "@babel/plugin-transform-typescript": "^7.25.9"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.26.0_1729863018051_0.058982443223251746", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.13": {"name": "@babel/generator", "version": "8.0.0-alpha.13", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@8.0.0-alpha.13", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "f2d8fe9c649a2fcb5c6bc3eda2bac02e9e9c8dcd", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-CBRdcDMMkUn7WGyQI6hbHKrRpBo1IT/PsA4UrZiIhg4+CP3qLCOZhBsF3vLmRdxe+xDmlM1oRn2PQneXmwFlZQ==", "signatures": [{"sig": "MEYCIQDNW1pEyYI8uBmpticYXY13xjb0J+lmbfKImphi4KkLTwIhAM/tlF6vzmjryJACLYeUl+z5q4/901WbeRkHoqBJJWsB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 536376}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^3.0.2", "@babel/types": "^8.0.0-alpha.13", "@babel/parser": "^8.0.0-alpha.13", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^8.0.0-alpha.13", "@types/jsesc": "^2.5.0", "@babel/helper-fixtures": "^8.0.0-alpha.13", "@jridgewell/sourcemap-codec": "^1.4.15", "@babel/plugin-transform-typescript": "^8.0.0-alpha.13"}, "_npmOperationalInternal": {"tmp": "tmp/generator_8.0.0-alpha.13_1729864463423_0.3758321514597587", "host": "s3://npm-registry-packages"}}, "7.26.2": {"name": "@babel/generator", "version": "7.26.2", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.26.2", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "87b75813bec87916210e5e01939a4c823d6bb74f", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.26.2.tgz", "fileCount": 43, "integrity": "sha512-zevQbhbau95nkoxSq3f/DC/SC+EEOUZd3DYqfSkMhY2/wfSeaHV1Ew4vk8e+x8lja31IbyuUa2uQ3JONqKbysw==", "signatures": [{"sig": "MEUCIFl68UWUJ6OwQfHXEvYWeZPHqnoOBGy7yx6H/wicwc96AiEA3UWSiNkzfrnJ4+HL1TpZnzjKykOOwQCNz3uhUoJESME=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 542741}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^3.0.2", "@babel/types": "^7.26.0", "@babel/parser": "^7.26.2", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.26.0", "@types/jsesc": "^2.5.0", "@babel/helper-fixtures": "^7.26.0", "@jridgewell/sourcemap-codec": "^1.4.15", "@babel/plugin-transform-typescript": "^7.25.9"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.26.2_1730310374362_0.08827049380483509", "host": "s3://npm-registry-packages"}}, "7.26.3": {"name": "@babel/generator", "version": "7.26.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.26.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "ab8d4360544a425c90c248df7059881f4b2ce019", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.26.3.tgz", "fileCount": 43, "integrity": "sha512-6FF/urZvD0sTeO7k6/B15pMLC4CHUv1426lzr3N01aHJTl046uCAh9LXW/fzeXXjPNCJ6iABW5XaWOsIZB93aQ==", "signatures": [{"sig": "MEUCIGvWPY8Ay64Qtap8PhNM4vN9Z5xTRjSR84zSBqDkIwgMAiEApuJ7GXszSHXFEHHyWcLwPZAbH8TgeBvPHB/eWwK1ZDk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 545578}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^3.0.2", "@babel/types": "^7.26.3", "@babel/parser": "^7.26.3", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.26.0", "@types/jsesc": "^2.5.0", "@babel/helper-fixtures": "^7.26.0", "@jridgewell/sourcemap-codec": "^1.4.15", "@babel/plugin-transform-typescript": "^7.26.3"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.26.3_1733315742230_0.03755728458102148", "host": "s3://npm-registry-packages"}}, "8.0.0-alpha.14": {"name": "@babel/generator", "version": "8.0.0-alpha.14", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@8.0.0-alpha.14", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "d83bdfd23f50fd6f988634da3e4beb2f69df5a58", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-BDgIshc0hM+/1Te9K83IFmQHVvZp+BSqGNUM/hdkbHtP4zVX+hPGQTw5hNB9+12z05LdAtVSHQH0VplSCdSiGg==", "signatures": [{"sig": "MEUCIQDhAcW6UomaxTEGqLrm8twrUuTsElqvjWjPa/kG40ztBQIgMg6W8iRM64xlBPMmf4XbOQ/wYgkaZJMeotd0PBzDxq0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 539476}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^3.0.2", "@babel/types": "^8.0.0-alpha.14", "@babel/parser": "^8.0.0-alpha.14", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^8.0.0-alpha.14", "@types/jsesc": "^2.5.0", "@babel/helper-fixtures": "^8.0.0-alpha.14", "@jridgewell/sourcemap-codec": "^1.4.15", "@babel/plugin-transform-typescript": "^8.0.0-alpha.14"}, "_npmOperationalInternal": {"tmp": "tmp/generator_8.0.0-alpha.14_1733504053735_0.6877105774175074", "host": "s3://npm-registry-packages"}}, "7.26.5": {"name": "@babel/generator", "version": "7.26.5", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.26.5", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "e44d4ab3176bbcaf78a5725da5f1dc28802a9458", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.26.5.tgz", "fileCount": 45, "integrity": "sha512-2caSP6fN9I7HOe6nqhtft7V4g7/V/gfDsC3Ag4W7kEzzvRGKqiv0pu0HogPiZ3KaVSoNDhUws6IJjDjpfmYIXw==", "signatures": [{"sig": "MEQCIAo06BmWDwp7MqBYDMDrYkzSVUV7Vn6yQJ7+ci+PBU2MAiAtP9o7nK6qWbYH9MHCIvj/oM/RYsxBKHCfRl38BvQasA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 550897}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^3.0.2", "@babel/types": "^7.26.5", "@babel/parser": "^7.26.5", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.26.0", "@types/jsesc": "^2.5.0", "@babel/helper-fixtures": "^7.26.0", "@jridgewell/sourcemap-codec": "^1.4.15", "@babel/plugin-transform-typescript": "^7.26.5"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.26.5_1736529113901_0.2553126994201287", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.15": {"name": "@babel/generator", "version": "8.0.0-alpha.15", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@8.0.0-alpha.15", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "87bb0766e8c89963ca53def5881c027d80e25d94", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-8uDMEOmSBef3+vCVBH9pQ9s5fUjdoS3MJqZqRBLwOImBNtP/hLtSk+vk4O5FR5Sc5PB3WCn91to+n1v8kNgs8w==", "signatures": [{"sig": "MEQCIE8iczfOQXyIFL1Au0nahyBE8wo65bfRrFyiwopaJO9DAiBSy1w/5wYablgXmfj/6np7WDH0W3GguZlsmzZJi61oCA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 540812}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^3.0.2", "@babel/types": "^8.0.0-alpha.15", "@babel/parser": "^8.0.0-alpha.15", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^8.0.0-alpha.15", "@types/jsesc": "^2.5.0", "@babel/helper-fixtures": "^8.0.0-alpha.15", "@jridgewell/sourcemap-codec": "^1.4.15", "@babel/plugin-transform-typescript": "^8.0.0-alpha.15"}, "_npmOperationalInternal": {"tmp": "tmp/generator_8.0.0-alpha.15_1736529880740_0.41388657803154527", "host": "s3://npm-registry-packages-npm-production"}}, "7.26.8": {"name": "@babel/generator", "version": "7.26.8", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.26.8", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "f9c5e770309e12e3099ad8271e52f6caa15442ab", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.26.8.tgz", "fileCount": 45, "integrity": "sha512-ef383X5++iZHWAXX0SXQR6ZyQhw/0KtTkrTz61WXRhFM6dhpHulO/RJz79L8S6ugZHJkOOkUrUdxgdF2YiPFnA==", "signatures": [{"sig": "MEUCIAbTIlVl8LCE5otZ/6Z4s4Brj+Pg0wOP67RshZotccezAiEAmnMPbdNm68znTXsrtSaaOpijBWyo+Zjm2AIxCnc8c3Y=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 551968}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^3.0.2", "@babel/types": "^7.26.8", "@babel/parser": "^7.26.8", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.26.8", "@types/jsesc": "^2.5.0", "@babel/helper-fixtures": "^7.26.0", "@jridgewell/sourcemap-codec": "^1.4.15", "@babel/plugin-transform-typescript": "^7.26.8"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.26.8_1739008768205_0.5088847812728596", "host": "s3://npm-registry-packages-npm-production"}}, "7.26.9": {"name": "@babel/generator", "version": "7.26.9", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.26.9", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "75a9482ad3d0cc7188a537aa4910bc59db67cbca", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.26.9.tgz", "fileCount": 45, "integrity": "sha512-kEWdzjOAUMW4hAyrzJ0ZaTOu9OmpyDIQicIh0zg0EEcEkYXZb2TjtBhnHi2ViX7PKwZqF4xwqfAm299/QMP3lg==", "signatures": [{"sig": "MEUCIBgVEVRAS7i/K7h65o9csf/Fnq1UvCn0itTaR+GEyZJcAiEA6iG2prqVcOvdSyeQSt4ihIN7lNJW61qa5/DAPVhjo6k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 558991}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^3.0.2", "@babel/types": "^7.26.9", "@babel/parser": "^7.26.9", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.26.9", "@types/jsesc": "^2.5.0", "@babel/helper-fixtures": "^7.26.0", "@jridgewell/sourcemap-codec": "^1.4.15", "@babel/plugin-transform-typescript": "^7.26.8"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.26.9_1739533687199_0.47240618371665644", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.16": {"name": "@babel/generator", "version": "8.0.0-alpha.16", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@8.0.0-alpha.16", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "466f711d658e967449853e6b8d2881b5b8adb077", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-CWR2yaddCYfax3CZL37LI7ozJfY8B8Mtxhjgli1oOFHEUVi5BDk9ICQfDzmJzIsXJV7R0VueOlJfEHQcIBtrjw==", "signatures": [{"sig": "MEUCIQCV4k9L+l25zegD6b1RSxd+Gq8u0f+5WCJdxcauhHreagIgHm3Gn2kG71NJ0vgacBMzp2FuC6TDthfKbVm6dHjJyRY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 548828}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^3.0.2", "@babel/types": "^8.0.0-alpha.16", "@babel/parser": "^8.0.0-alpha.16", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^8.0.0-alpha.16", "@types/jsesc": "^2.5.0", "@babel/helper-fixtures": "^8.0.0-alpha.16", "@jridgewell/sourcemap-codec": "^1.4.15", "@babel/plugin-transform-typescript": "^8.0.0-alpha.16"}, "_npmOperationalInternal": {"tmp": "tmp/generator_8.0.0-alpha.16_1739534356972_0.41870507398265366", "host": "s3://npm-registry-packages-npm-production"}}, "7.26.10": {"name": "@babel/generator", "version": "7.26.10", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.26.10", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "a60d9de49caca16744e6340c3658dfef6138c3f7", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.26.10.tgz", "fileCount": 45, "integrity": "sha512-rRHT8siFIXQrAYOYqZQVsAr8vJ+cBNqcVAY6m5V8/4QqzaPl+zDBe6cLEPRDuNOUf3ww8RfJVlOyQMoSI+5Ang==", "signatures": [{"sig": "MEUCIQD6Ob05pFDUu7UKT/h3NSKRn4QSs1oiC5OHF2LLW0iKhwIgTJfvo+1UQQwXmEhME1NW+xv7lnL7cXtjO1LbFnzJPXw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 558995}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^3.0.2", "@babel/types": "^7.26.10", "@babel/parser": "^7.26.10", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.26.10", "@types/jsesc": "^2.5.0", "@babel/helper-fixtures": "^7.26.0", "@jridgewell/sourcemap-codec": "^1.4.15", "@babel/plugin-transform-typescript": "^7.26.8"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.26.10_1741715711718_0.5944886047443863", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-alpha.17": {"name": "@babel/generator", "version": "8.0.0-alpha.17", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@8.0.0-alpha.17", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "55b21aa338a7c94c4168268469930e8775204531", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-EY7priOiX/dMPF+swNBUQWQxrWxkkUtSb3Gikm5aOUTUdZIWqI7qgV6MWOjdd/yE1Ya0x/0tbmavJBOJxqBnsw==", "signatures": [{"sig": "MEUCIQDUeg0235mGQ8qdRmZjG6/q270zELduYeMp50FIwo5OcAIgZxgDGFK3cr1ZdMVZUKdk9aq8etVfzdQVeyvFYh/7h8I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 548828}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^3.0.2", "@babel/types": "^8.0.0-alpha.17", "@babel/parser": "^8.0.0-alpha.17", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^8.0.0-alpha.17", "@types/jsesc": "^2.5.0", "@babel/helper-fixtures": "^8.0.0-alpha.17", "@jridgewell/sourcemap-codec": "^1.4.15", "@babel/plugin-transform-typescript": "^8.0.0-alpha.17"}, "_npmOperationalInternal": {"tmp": "tmp/generator_8.0.0-alpha.17_1741717509807_0.972499876508055", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.0": {"name": "@babel/generator", "version": "7.27.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.27.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "764382b5392e5b9aff93cadb190d0745866cbc2c", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.27.0.tgz", "fileCount": 45, "integrity": "sha512-VybsKvpiN1gU1sdMZIp7FcqphVVKEwcuj02x73uvcHE0PTihx1nlBcowYWhDwjpoAXRv43+gDzyggGnn1XZhVw==", "signatures": [{"sig": "MEQCIAoOf4J77J/UdcxE8W/c7mmZ0Tf1RZC+XIUjLO+vJDWlAiBOKIs9pFlUX9axyoShVCgTeb9ku9o5cXbRxb1ogeWKOA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 559855}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^3.0.2", "@babel/types": "^7.27.0", "@babel/parser": "^7.27.0", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.26.10", "@types/jsesc": "^2.5.0", "@babel/helper-fixtures": "^7.26.0", "@jridgewell/sourcemap-codec": "^1.4.15", "@babel/plugin-transform-typescript": "^7.27.0"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.27.0_1742838109287_0.5584034674812781", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.1": {"name": "@babel/generator", "version": "7.27.1", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.27.1", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "862d4fad858f7208edd487c28b58144036b76230", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.27.1.tgz", "fileCount": 45, "integrity": "sha512-UnJfnIpc/+JO0/+KRVQNGU+y5taA5vCbwN8+azkX6beii/ZF+enZJSOKo11ZSzGJjlNfJHfQtmQT8H+9TXPG2w==", "signatures": [{"sig": "MEUCIAarCT9xChJO36exmuMtAPqkZuvqpgqlYW6AmYY3jh07AiEA66nECVidOBn89G4XD9mdFAEjWUcz1saGTTrHP0kD6+U=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 560098}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^3.0.2", "@babel/types": "^7.27.1", "@babel/parser": "^7.27.1", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.27.1", "@types/jsesc": "^2.5.0", "@babel/helper-fixtures": "^7.27.1", "@jridgewell/sourcemap-codec": "^1.4.15", "@babel/plugin-transform-typescript": "^7.27.1"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.27.1_1746025746403_0.21547025423879207", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.3": {"name": "@babel/generator", "version": "7.27.3", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@7.27.3", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "ef1c0f7cfe3b5fc8cbb9f6cc69f93441a68edefc", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.27.3.tgz", "fileCount": 45, "integrity": "sha512-xnlJYj5zepml8NXtjkG0WquFUv8RskFqyFcVgTBp5k+NaA/8uw/K+OSVf8AMGw5e9HKP2ETd5xpK5MLZQD6b4Q==", "signatures": [{"sig": "MEYCIQDj4cfdXx60ACxCSKzEjKDfnjLCgRYpTv8y4VlWzURV2wIhALxAjtxjkeF+HF4sH7orvHn4cuCUISE2AouESxBBxcYN", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 560526}, "main": "./lib/index.js", "type": "commonjs", "engines": {"node": ">=6.9.0"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^3.0.2", "@babel/types": "^7.27.3", "@babel/parser": "^7.27.3", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^7.27.3", "@types/jsesc": "^2.5.0", "@babel/helper-fixtures": "^7.27.1", "@jridgewell/sourcemap-codec": "^1.4.15", "@babel/plugin-transform-typescript": "^7.27.1"}, "_npmOperationalInternal": {"tmp": "tmp/generator_7.27.3_1748335162598_0.582156748397473", "host": "s3://npm-registry-packages-npm-production"}}, "8.0.0-beta.0": {"name": "@babel/generator", "version": "8.0.0-beta.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "_id": "@babel/generator@8.0.0-beta.0", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "dist": {"shasum": "40023c8fce9373379516ae5ca8b306eacd388139", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-8.0.0-beta.0.tgz", "fileCount": 6, "integrity": "sha512-1mS+6SWMkuRY0294/DzAI1L6ag4lC4CFvXtHp1iQD4iroYm/mYuRxDgP0Ojx8oi9MOybXEOsw9YoRo+6siK04g==", "signatures": [{"sig": "MEUCIQD8dwYBl0E8Eb9TslOJVEJTDLkEuqbry+IyI9gUskQ22wIgJFi64u35LeT5W67fbWc0ZKc3ss5Z5t4K4m/AEKTVAGk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 549782}, "main": "./lib/index.js", "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "exports": {".": {"types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./package.json": "./package.json"}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/babel/babel.git", "type": "git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "directories": {}, "dependencies": {"jsesc": "^3.0.2", "@babel/types": "^8.0.0-beta.0", "@babel/parser": "^8.0.0-beta.0", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"charcodes": "^0.2.0", "@babel/core": "^8.0.0-beta.0", "@types/jsesc": "^2.5.0", "@babel/helper-fixtures": "^8.0.0-beta.0", "@jridgewell/sourcemap-codec": "^1.4.15", "@babel/plugin-transform-typescript": "^8.0.0-beta.0"}, "_npmOperationalInternal": {"tmp": "tmp/generator_8.0.0-beta.0_1748620283478_0.22648753769968843", "host": "s3://npm-registry-packages-npm-production"}}, "7.27.5": {"name": "@babel/generator", "version": "7.27.5", "description": "Turns an AST into code.", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-generator"}, "homepage": "https://babel.dev/docs/en/next/babel-generator", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "main": "./lib/index.js", "dependencies": {"@babel/parser": "^7.27.5", "@babel/types": "^7.27.3", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/helper-fixtures": "^7.27.1", "@babel/plugin-transform-typescript": "^7.27.1", "@jridgewell/sourcemap-codec": "^1.4.15", "@types/jsesc": "^2.5.0", "charcodes": "^0.2.0"}, "engines": {"node": ">=6.9.0"}, "type": "commonjs", "_id": "@babel/generator@7.27.5", "dist": {"shasum": "3eb01866b345ba261b04911020cbe22dd4be8c8c", "integrity": "sha512-ZGhA37l0e/g2s1Cnzdix0O3aLYm66eF8aufiVteOgnwxgnRP8GoyMj7VWsgWnQbVKXyge7hqrFh2K2TQM6t1Hw==", "tarball": "https://registry.npmjs.org/@babel/generator/-/generator-7.27.5.tgz", "fileCount": 45, "unpackedSize": 561723, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIBTw48NYsruD6Ta7uhtxI5TzNtgWKVaSc+MgByemdEl1AiAc8QeNHiCnAR3vpTlFTd6y46B/pAHMO4YRAFmrcV869A=="}]}, "_npmUser": {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/generator_7.27.5_1748950028524_0.15419671294352133"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-30T18:35:01.103Z", "modified": "2025-06-03T11:27:09.009Z", "7.0.0-beta.4": "2017-10-30T18:35:01.103Z", "7.0.0-beta.5": "2017-10-30T20:56:40.615Z", "7.0.0-beta.31": "2017-11-03T20:03:39.475Z", "7.0.0-beta.32": "2017-11-12T13:33:31.929Z", "7.0.0-beta.33": "2017-12-01T14:28:38.077Z", "7.0.0-beta.34": "2017-12-02T14:39:38.899Z", "7.0.0-beta.35": "2017-12-14T21:48:00.416Z", "7.0.0-beta.36": "2017-12-25T19:04:59.099Z", "7.0.0-beta.37": "2018-01-08T16:02:41.664Z", "7.0.0-beta.38": "2018-01-17T16:32:09.895Z", "7.0.0-beta.39": "2018-01-30T20:27:43.645Z", "7.0.0-beta.40": "2018-02-12T16:41:54.077Z", "7.0.0-beta.41": "2018-03-14T16:25:42.240Z", "7.0.0-beta.42": "2018-03-15T20:50:16.433Z", "7.0.0-beta.43": "2018-04-02T16:48:07.198Z", "7.0.0-beta.44": "2018-04-02T22:19:49.180Z", "7.0.0-beta.45": "2018-04-23T01:55:44.265Z", "7.0.0-beta.46": "2018-04-23T04:30:08.463Z", "7.0.0-beta.47": "2018-05-15T00:07:35.619Z", "7.0.0-beta.48": "2018-05-24T19:20:54.701Z", "7.0.0-beta.49": "2018-05-25T16:00:37.279Z", "7.0.0-beta.50": "2018-06-12T19:46:49.676Z", "7.0.0-beta.51": "2018-06-12T21:19:16.094Z", "7.0.0-beta.52": "2018-07-06T00:59:12.299Z", "7.0.0-beta.53": "2018-07-11T13:40:01.755Z", "7.0.0-beta.54": "2018-07-16T17:59:51.976Z", "7.0.0-beta.55": "2018-07-28T22:06:55.904Z", "7.0.0-beta.56": "2018-08-04T01:03:31.380Z", "7.0.0-rc.0": "2018-08-09T15:56:49.205Z", "7.0.0-rc.1": "2018-08-09T20:06:39.779Z", "7.0.0-rc.2": "2018-08-21T19:22:43.224Z", "7.0.0-rc.3": "2018-08-24T18:06:39.271Z", "7.0.0-rc.4": "2018-08-27T16:42:49.846Z", "7.0.0": "2018-08-27T21:42:00.539Z", "7.1.1": "2018-09-28T20:03:02.693Z", "7.1.2": "2018-09-28T22:19:57.869Z", "7.1.3": "2018-10-11T15:52:34.213Z", "7.1.5": "2018-11-06T22:21:39.361Z", "7.1.6": "2018-11-13T21:10:45.723Z", "7.2.0": "2018-12-03T19:02:17.070Z", "7.2.2": "2018-12-15T10:05:39.468Z", "7.3.0": "2019-01-21T21:36:37.773Z", "7.3.2": "2019-02-04T22:23:18.519Z", "7.3.3": "2019-02-15T21:14:50.706Z", "7.3.4": "2019-02-25T18:35:40.269Z", "7.4.0": "2019-03-19T20:44:59.167Z", "7.4.4": "2019-04-26T21:04:28.911Z", "7.5.0": "2019-07-04T12:58:15.740Z", "7.5.5": "2019-07-17T21:21:41.176Z", "7.6.0": "2019-09-06T17:33:52.816Z", "7.6.2": "2019-09-23T21:21:35.622Z", "7.6.3": "2019-10-08T19:49:45.251Z", "7.6.4": "2019-10-10T14:29:16.863Z", "7.7.0": "2019-11-05T10:53:29.873Z", "7.7.2": "2019-11-06T23:27:32.574Z", "7.7.4": "2019-11-22T23:33:14.664Z", "7.7.7": "2019-12-19T00:53:13.484Z", "7.8.0": "2020-01-12T00:16:50.996Z", "7.8.3": "2020-01-13T21:41:45.258Z", "7.8.4": "2020-01-30T12:37:08.942Z", "7.8.6": "2020-02-27T12:21:30.120Z", "7.8.7": "2020-03-05T01:56:10.277Z", "7.8.8": "2020-03-12T18:48:58.985Z", "7.9.0": "2020-03-20T15:39:46.977Z", "7.9.3": "2020-03-22T11:02:49.208Z", "7.9.4": "2020-03-24T08:31:18.254Z", "7.9.5": "2020-04-07T19:25:26.802Z", "7.9.6": "2020-04-29T18:38:12.335Z", "7.10.0": "2020-05-26T21:43:31.644Z", "7.10.1": "2020-05-27T22:07:53.298Z", "7.10.2": "2020-05-30T19:25:19.859Z", "7.10.3": "2020-06-19T20:54:26.402Z", "7.10.4": "2020-06-30T13:12:14.856Z", "7.10.5": "2020-07-14T18:18:04.921Z", "7.11.0": "2020-07-30T21:25:58.217Z", "7.11.4": "2020-08-20T18:59:47.403Z", "7.11.5": "2020-08-31T20:02:21.761Z", "7.11.6": "2020-09-03T15:54:19.900Z", "7.12.0": "2020-10-14T20:03:12.704Z", "7.12.1": "2020-10-15T22:40:51.036Z", "7.12.5": "2020-11-03T22:34:28.970Z", "7.12.10": "2020-12-09T22:48:03.246Z", "7.12.11": "2020-12-15T23:59:31.118Z", "7.12.13": "2021-02-03T01:10:04.244Z", "7.12.15": "2021-02-04T21:38:00.707Z", "7.12.17": "2021-02-18T15:12:55.208Z", "7.13.0": "2021-02-22T22:49:46.216Z", "7.13.9": "2021-03-01T21:43:59.219Z", "7.13.16": "2021-04-20T11:21:19.124Z", "7.14.0": "2021-04-29T20:10:09.360Z", "7.14.1": "2021-05-04T01:56:07.094Z", "7.14.2": "2021-05-12T17:09:33.181Z", "7.14.3": "2021-05-17T20:44:17.291Z", "7.14.5": "2021-06-09T23:12:21.792Z", "7.14.8": "2021-07-20T18:02:45.819Z", "7.14.9": "2021-08-01T07:53:23.447Z", "7.15.0": "2021-08-04T21:13:03.943Z", "7.15.4": "2021-09-02T21:39:31.486Z", "7.15.8": "2021-10-06T20:54:55.246Z", "7.16.0": "2021-10-29T23:47:41.057Z", "7.16.5": "2021-12-13T22:27:24.363Z", "7.16.7": "2021-12-31T00:22:17.741Z", "7.16.8": "2022-01-10T21:18:32.539Z", "7.17.0": "2022-02-02T23:04:56.993Z", "7.17.3": "2022-02-15T15:44:19.244Z", "7.17.7": "2022-03-14T17:07:07.649Z", "7.17.9": "2022-04-06T15:55:20.907Z", "7.17.10": "2022-04-29T16:37:41.730Z", "7.17.12": "2022-05-16T19:32:17.590Z", "7.18.0": "2022-05-19T18:16:37.142Z", "7.18.2": "2022-05-25T09:16:31.878Z", "7.18.6": "2022-06-27T19:50:07.530Z", "7.18.7": "2022-06-28T20:30:34.192Z", "7.18.9": "2022-07-18T09:17:27.318Z", "7.18.10": "2022-08-01T18:46:45.652Z", "7.18.12": "2022-08-05T13:42:58.533Z", "7.18.13": "2022-08-22T16:05:13.870Z", "7.19.0": "2022-09-05T19:02:17.403Z", "7.19.3": "2022-09-27T18:36:49.030Z", "7.19.4": "2022-10-10T10:47:26.377Z", "7.19.5": "2022-10-10T20:48:29.407Z", "7.19.6": "2022-10-20T09:03:30.124Z", "7.20.0": "2022-10-27T13:19:19.931Z", "7.20.1": "2022-11-01T11:25:40.658Z", "7.20.2": "2022-11-04T18:51:06.729Z", "7.20.3": "2022-11-07T14:21:05.497Z", "7.20.4": "2022-11-08T22:25:10.291Z", "7.20.5": "2022-11-28T10:12:47.678Z", "7.20.7": "2022-12-22T09:45:28.471Z", "7.20.14": "2023-01-27T20:57:28.938Z", "7.21.0": "2023-02-20T15:31:11.412Z", "7.21.1": "2023-02-20T23:25:40.373Z", "7.21.3": "2023-03-14T14:59:41.307Z", "7.21.4": "2023-03-31T09:01:56.714Z", "7.21.4-esm": "2023-04-04T14:09:34.110Z", "7.21.4-esm.1": "2023-04-04T14:21:26.279Z", "7.21.4-esm.2": "2023-04-04T14:39:27.447Z", "7.21.4-esm.3": "2023-04-04T14:56:16.235Z", "7.21.4-esm.4": "2023-04-04T15:13:27.879Z", "7.21.5": "2023-04-28T19:50:23.719Z", "7.21.9": "2023-05-22T10:02:19.412Z", "7.22.0": "2023-05-26T13:45:38.218Z", "7.22.3": "2023-05-27T10:11:08.051Z", "7.22.5": "2023-06-08T18:21:25.508Z", "7.22.7": "2023-07-06T09:03:56.684Z", "7.22.9": "2023-07-12T16:53:31.621Z", "8.0.0-alpha.0": "2023-07-20T14:00:02.943Z", "8.0.0-alpha.1": "2023-07-24T17:52:10.280Z", "7.22.10": "2023-08-07T17:25:20.625Z", "8.0.0-alpha.2": "2023-08-09T15:15:02.147Z", "7.22.15": "2023-09-04T12:25:17.284Z", "7.23.0": "2023-09-25T08:11:26.984Z", "8.0.0-alpha.3": "2023-09-26T14:57:05.158Z", "8.0.0-alpha.4": "2023-10-12T02:06:25.576Z", "7.23.3": "2023-11-09T07:04:07.234Z", "7.23.4": "2023-11-20T14:22:17.480Z", "7.23.5": "2023-11-29T10:25:45.123Z", "7.23.6": "2023-12-11T13:10:02.983Z", "8.0.0-alpha.5": "2023-12-11T15:19:03.703Z", "8.0.0-alpha.6": "2024-01-26T16:14:14.530Z", "8.0.0-alpha.7": "2024-02-28T14:05:04.311Z", "7.24.1": "2024-03-19T09:47:31.034Z", "7.24.4": "2024-04-03T16:53:45.199Z", "8.0.0-alpha.8": "2024-04-04T13:19:56.877Z", "7.24.5": "2024-04-29T18:34:22.699Z", "7.24.6": "2024-05-24T12:24:38.989Z", "8.0.0-alpha.9": "2024-06-03T14:04:25.012Z", "8.0.0-alpha.10": "2024-06-04T11:20:15.009Z", "7.24.7": "2024-06-05T13:15:28.879Z", "8.0.0-alpha.11": "2024-06-07T09:15:39.444Z", "7.24.8": "2024-07-11T14:54:51.993Z", "7.24.9": "2024-07-15T10:34:31.239Z", "7.24.10": "2024-07-16T09:52:54.301Z", "7.25.0": "2024-07-26T16:59:25.938Z", "8.0.0-alpha.12": "2024-07-26T17:33:35.810Z", "7.25.4": "2024-08-22T09:34:31.741Z", "7.25.5": "2024-08-23T08:31:10.457Z", "7.25.6": "2024-08-29T10:14:25.768Z", "7.25.7": "2024-10-02T15:14:58.185Z", "7.25.9": "2024-10-22T15:21:17.165Z", "7.26.0": "2024-10-25T13:30:18.221Z", "8.0.0-alpha.13": "2024-10-25T13:54:23.596Z", "7.26.2": "2024-10-30T17:46:14.710Z", "7.26.3": "2024-12-04T12:35:42.408Z", "8.0.0-alpha.14": "2024-12-06T16:54:13.928Z", "7.26.5": "2025-01-10T17:11:54.088Z", "8.0.0-alpha.15": "2025-01-10T17:24:41.033Z", "7.26.8": "2025-02-08T09:59:28.487Z", "7.26.9": "2025-02-14T11:48:07.424Z", "8.0.0-alpha.16": "2025-02-14T11:59:17.183Z", "7.26.10": "2025-03-11T17:55:11.906Z", "8.0.0-alpha.17": "2025-03-11T18:25:10.065Z", "7.27.0": "2025-03-24T17:41:49.470Z", "7.27.1": "2025-04-30T15:09:06.646Z", "7.27.3": "2025-05-27T08:39:22.768Z", "8.0.0-beta.0": "2025-05-30T15:51:23.679Z", "7.27.5": "2025-06-03T11:27:08.703Z"}, "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22pkg%3A%20generator%22+is%3Aopen", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "homepage": "https://babel.dev/docs/en/next/babel-generator", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-generator"}, "description": "Turns an AST into code.", "maintainers": [{"name": "hzoo", "email": "<EMAIL>"}, {"name": "existentialism", "email": "<EMAIL>"}, {"name": "nicolo-ribaudo", "email": "<EMAIL>"}, {"name": "jlhwung", "email": "<EMAIL>"}], "readme": "", "readmeFilename": "", "users": {"shuoshubao": true, "flumpus-dev": true, "omniscient100": true}}