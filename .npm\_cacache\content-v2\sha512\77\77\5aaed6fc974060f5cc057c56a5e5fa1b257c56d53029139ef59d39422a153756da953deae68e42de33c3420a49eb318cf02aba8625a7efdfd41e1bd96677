{"_id": "jest-worker", "_rev": "209-7855dbd8b341f62ab7f751429e3208e3", "name": "jest-worker", "dist-tags": {"next": "30.0.0-rc.1", "latest": "30.0.2"}, "versions": {"0.0.0": {"name": "jest-worker", "version": "0.0.0", "_id": "jest-worker@0.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "734cb5626b50b7d9b4ad88f35a93c22a47c0515b", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-0.0.0.tgz", "integrity": "sha512-9cwIRN/LNzy2KkyqFGeFFONRI+v7wS6oNXTCSRRi/6chGPwWt7z6Yh+OOeul4fv1RUSTOV9IIt5OvIj8z9tNWg==", "signatures": [{"sig": "MEUCIBY2i8HOc7MgwSDLVrIiVlPRfRnM5tdXtw5C0MnkEj0GAiEAq/9pgEBhxA1MhoCiibx1YLviIwLlM4cdmmNWg+Uy274=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "_npmVersion": "5.3.0", "directories": {}, "_nodeVersion": "8.4.0", "_npmOperationalInternal": {"tmp": "tmp/jest-worker-0.0.0.tgz_1507029176458_0.6641926621086895", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.1": {"name": "jest-worker", "version": "21.3.0-beta.1", "license": "MIT", "_id": "jest-worker@21.3.0-beta.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a2192b160c3791ed60a72f6459eed436b581c0b2", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-21.3.0-beta.1.tgz", "integrity": "sha512-MRplDqpd6FBv4x9wc3b6WrV3tkn6aboGJyqnvfZkXaN+Mcq/iq+QZay0z5g2twxHDPF095kxxbOSjuhsEoDzqw==", "signatures": [{"sig": "MEQCIAR+UO+LJAqmXVvHJ8uXikFZ3FAT6t6IltxPXGoIiuOMAiA4Db3h0uAJoqbPWFpXjP8XAXPbIbAgw1liunHp/+/lhw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"merge-stream": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker-21.3.0-beta.1.tgz_1507114114509_0.4065683470107615", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.2": {"name": "jest-worker", "version": "21.3.0-beta.2", "license": "MIT", "_id": "jest-worker@21.3.0-beta.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "05eba2afe4bffffdba6fb0a49016b628382b8234", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-21.3.0-beta.2.tgz", "integrity": "sha512-l8RKm5HlvrMj3cgo/SA+ziFMmrhN7Yuq1xI6en8GmJtpI364LfC+AS58IlZotUfHPl5GvLWwRJu2bHDlNKqSTw==", "signatures": [{"sig": "MEUCIHF6WuTSga8u1bqK+M2HDoNDMnSlUL2dcnhHSRM9JwoDAiEAuMfsFy0Duy7AXKK5PsrQpum48VyUsKHRm1m8o/tbEF4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"merge-stream": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker-21.3.0-beta.2.tgz_1507888444113_0.31019993452355266", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.3": {"name": "jest-worker", "version": "21.3.0-beta.3", "license": "MIT", "_id": "jest-worker@21.3.0-beta.3", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c318d690f7d264c736bae919d853403cfc0fab4c", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-21.3.0-beta.3.tgz", "integrity": "sha512-+aODqBmzanxDz1zqrIT7uNUjDNbJb1CWTIVMrFGCMoXM4qBICC2gS/H3ckQP25SBmwH2o74ud3sWFmOCi+UrHQ==", "signatures": [{"sig": "MEQCIClm7MHB8jNE6b+EwFXsMZUyTdXyWv3OzMf9z4JBnZUZAiBzK1IT6V2mLQqh5NOuu3ZEhFZLqXUuTUhC8LQXhI9jng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"merge-stream": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker-21.3.0-beta.3.tgz_1508960043873_0.7625195428263396", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.4": {"name": "jest-worker", "version": "21.3.0-beta.4", "license": "MIT", "_id": "jest-worker@21.3.0-beta.4", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "be04b3cc77e8eecddeb9fba66a863d2ba1012f15", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-21.3.0-beta.4.tgz", "integrity": "sha512-BMvzDtCvmA7Dywx+aCDDE+mkonX3QAM454geQCX81ISKR8XiW1FaxqleVxLt1HLzYPZsohohC6KctdbdPCt/WQ==", "signatures": [{"sig": "MEUCICHfhOtFWR465N+Tb8AY60WRVHEQe7nK/V6zDhGAG1XDAiEAwrVz5XV10WoCUcMdktyBF5xX+UhNq4nAgUgtdJlko5c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"merge-stream": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker-21.3.0-beta.4.tgz_1509024413898_0.5665347420144826", "host": "s3://npm-registry-packages"}}, "21.2.1": {"name": "jest-worker", "version": "21.2.1", "license": "MIT", "_id": "jest-worker@21.2.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "231a24007e73f5faa647d7bc02761d8fad957d73", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-21.2.1.tgz", "integrity": "sha512-/nBhvKOjJrAA+cTxmwBfrdFNoj4G1FicvTtmH+wnpgboLJ/Djqux+nLOhkE3bmiXr9mDrS0uD9BQMyR4+8Nq7w==", "signatures": [{"sig": "MEYCIQCuBH9TEmu/vjT0m3YEQvBe/IkvdtVveTxjMDiEBd2GHwIhAPBcWxg1o5jGIfYUCaqx6AKClEGNCcj/P5Vjn19S52Zu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"merge-stream": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker-21.2.1.tgz_1509493688681_0.9927847944200039", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.5": {"name": "jest-worker", "version": "21.3.0-beta.5", "license": "MIT", "_id": "jest-worker@21.3.0-beta.5", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ca603413fa37f7ff0979411c88c9a26500eb9fcf", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-21.3.0-beta.5.tgz", "integrity": "sha512-42O5kYKebDfrZZ460fiGEM6T+f5U9kbWhViVHer4P6cdwjiDm/Y309+j3hxVPi3Mef8DBVoiWYMiX/5YFqYHKg==", "signatures": [{"sig": "MEYCIQD7Z4hVRktZJwmM+wXom4NjFCoSMdCJV4XY6Zbu0XmAIgIhAOv+Fxjqq6iObFkEYooK+LO/KwigGX9bWTFchC4cmcJq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"merge-stream": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker-21.3.0-beta.5.tgz_1509628646954_0.8366929683834314", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.6": {"name": "jest-worker", "version": "21.3.0-beta.6", "license": "MIT", "_id": "jest-worker@21.3.0-beta.6", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "519de691e5ea9d6b6ef0c93c6808a55d86fdc39d", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-21.3.0-beta.6.tgz", "integrity": "sha512-Hy/KepQKbu26n+bIwgLcQHlUQXrxPgq/QcB68H5/GQrK1W6U7CbrgxId7YIaKahGTQ4EC57TEKNcv5WrMqzwyw==", "signatures": [{"sig": "MEUCIQCUT8S9QYdPMwGneAwFe7184D2q74etQUlR1MiOG0zkwwIgD71Wc7YaRZPfyaLW3pZRFRG4hvvqdRjltpVKB5e4exE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"merge-stream": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker-21.3.0-beta.6.tgz_1509726090239_0.1286516182590276", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.7": {"name": "jest-worker", "version": "21.3.0-beta.7", "license": "MIT", "_id": "jest-worker@21.3.0-beta.7", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "87eab35587bda9482f0a6290a524876140e723a0", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-21.3.0-beta.7.tgz", "integrity": "sha512-rCeSzdFl39Bj2WHHM8YaE9cixqMdPNiFnX+GLLI+s2zS6Vtvdoy4+KR9mnikGgbRs+eat611ZimeJMg1U1p1IA==", "signatures": [{"sig": "MEUCICDW0ZR8qmAmNsxgdotADp/LUypNXjO7eSnVVqatRJkUAiEAsithkrS486l4q/HU1+U0d9Ew8d7y57UJJexO4iijkJY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"merge-stream": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker-21.3.0-beta.7.tgz_1509961184877_0.7800029458012432", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.8": {"name": "jest-worker", "version": "21.3.0-beta.8", "license": "MIT", "_id": "jest-worker@21.3.0-beta.8", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c8109f1e0f916e4f5f9c27a027dd339cb7952b5e", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-21.3.0-beta.8.tgz", "integrity": "sha512-GdKa7UhhTtjuBekLaWgYBh1ca0/Fpm3/7srKVUxbFwnDBVH01O+5DpLdstIfFrEHAjg7aXUopNNmm1zdYu+V3g==", "signatures": [{"sig": "MEUCIHUxjFmBfBmv2KNlYmZq8kLAliKjE4+CFNVPtYYny3lrAiEAxzGKuMdaxVoZ6r6bQO82i/BQ3bt4DAHzial4sUq1p+0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"merge-stream": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker-21.3.0-beta.8.tgz_1510076616801_0.11614330834709108", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.9": {"name": "jest-worker", "version": "21.3.0-beta.9", "license": "MIT", "_id": "jest-worker@21.3.0-beta.9", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0be9bf38ea7e77a22d21d01098e90e0ce8a7867f", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-21.3.0-beta.9.tgz", "integrity": "sha512-dID+zcYsO2oaOF+Fr5g8c2NjPKDs7DwKTQOemWJZDvVyXbhNDzP+Lmd5EoCe3Wa8LkF7Z3WI67k3EXC8y9I3Pw==", "signatures": [{"sig": "MEUCIFxQvD/HSojeTinLYw72RvUb36UVtyZXpya1vtB0C7C8AiEA/FK6yqFy1O+s3EV8ayH2DAvalwTmWhjDhLIAsQ5nAjU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {"merge-stream": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker-21.3.0-beta.9.tgz_1511356651408_0.722123100887984", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.10": {"name": "jest-worker", "version": "21.3.0-beta.10", "license": "MIT", "_id": "jest-worker@21.3.0-beta.10", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "326bfe0ba591a398bfeb4a590925ddc206747e13", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-21.3.0-beta.10.tgz", "integrity": "sha512-wkCDc/7UhxWLLPn2Cvu3ARMQKMfITkEKTI31MHHo0if9U+czOwzpZt/omTXNDQDIjIO+cRA9PkrAQLqLQPMI5w==", "signatures": [{"sig": "MEYCIQDZsbBNef4wfmh1IDqdn3g9eudtrEHIxMxbnD1xIc131AIhAMrQVLlMSg4sm+gCXCO1Hn1hITX2LwsYVMIbPt8LQwgZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"merge-stream": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker-21.3.0-beta.10.tgz_1511613563790_0.9269310683012009", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.11": {"name": "jest-worker", "version": "21.3.0-beta.11", "license": "MIT", "_id": "jest-worker@21.3.0-beta.11", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b29cf211f7cad6316a93c5b7c698c770a5ff459e", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-21.3.0-beta.11.tgz", "integrity": "sha512-qdP1nF3Lel/fTXLVfUt8FJgXFxmJW2KWm5OSNhLOZy5dfrAGqfc9yRBm71txpmSdr+CLef0vFK4mNyZi36klTQ==", "signatures": [{"sig": "MEYCIQCQ8VObLYUd3pB7vYuFMCPZKk9sdKN7wo475Q88LY87mAIhAOzfITrlSZl43sAr2RQ8q27vKjcSfr/j8sLwHdI6Ahnc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"merge-stream": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker-21.3.0-beta.11.tgz_1511965878104_0.7202728148549795", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.12": {"name": "jest-worker", "version": "21.3.0-beta.12", "license": "MIT", "_id": "jest-worker@21.3.0-beta.12", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "cfd3400df18bee0f8d038b0d15d9125089d939c3", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-21.3.0-beta.12.tgz", "integrity": "sha512-5+9AHP7mv/+WXS0N6rWlkTi0Q66U/MxUi48ye6zqffeQf/IGG7+NQ2lEKVQhnyU2R4Pg3797JcrN+y3eeRIl4g==", "signatures": [{"sig": "MEUCIEiA4EkJHVeaNB8JXf+6yhMu3oojgnbwcJl78vA5fB1AAiEAmpbv7xJSXbJD9LyIAPfBF6ZL6yMueQsmA8TVcLUrZWY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"merge-stream": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker-21.3.0-beta.12.tgz_1512499714269_0.6539331250824034", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.13": {"name": "jest-worker", "version": "21.3.0-beta.13", "license": "MIT", "_id": "jest-worker@21.3.0-beta.13", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "17c467b8e9711cf50240e3dfdc5bbaf2d2125100", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-21.3.0-beta.13.tgz", "integrity": "sha512-LjUuAkyE/VlhV3Yw9LWTpgP+Qen3t/3QEp95vQbd7rUaJ4wi7gtXpjCzCXUCVjtg+6c2yJh/mre3B2zbCA993Q==", "signatures": [{"sig": "MEYCIQCGbjKWmeq/cWV5pFNpkNRi0gm8ISJkInitksuXdi6WHQIhALziJfcqpowhvseaCTSoykOhKsoFAOQyRWI8UcQbfyX8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"merge-stream": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker-21.3.0-beta.13.tgz_1512571027944_0.11341526359319687", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.14": {"name": "jest-worker", "version": "21.3.0-beta.14", "license": "MIT", "_id": "jest-worker@21.3.0-beta.14", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4247db4f55903612eb41b609d16663c864946e74", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-21.3.0-beta.14.tgz", "integrity": "sha512-TTWrAVYoDr7bhgTB/lpySRb8znMhzEbTUHsLAmGUNJfmPIoW4pfQLBOOSBbl+qlBkQRnoMk3JlJ0+F3gkY+T6Q==", "signatures": [{"sig": "MEUCIEK9BVfD0bIo/c5JtStoci9X0jSD7jkCTponxZm75rzvAiEAvTS5FcTmDzbmdP7nbdRduoW+Qy3lKtLswjTVth1Yvlk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "9.2.1", "dependencies": {"merge-stream": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker-21.3.0-beta.14.tgz_1513075954573_0.14793565426953137", "host": "s3://npm-registry-packages"}}, "21.3.0-beta.15": {"name": "jest-worker", "version": "21.3.0-beta.15", "license": "MIT", "_id": "jest-worker@21.3.0-beta.15", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "bef2fccaff63712bcbc1b9e3087cb4ff989b7ae7", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-21.3.0-beta.15.tgz", "integrity": "sha512-668z3nss+2evul7IgYAybGpWiq57knOIIAlLVfHe6zuk8s2evrOqkv/54YT6B7MIX/k95DKBCmWpbXwgHEdSdA==", "signatures": [{"sig": "MEUCIQDS/6Jr2/hnq/ZHM3XsM/JMFzmDiaD1Dbe3JTzDeJaqMwIgNkTEh9yrdgrlJb4YC3OZCo7MtIpRstEcg8zr1xD5oVc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "9.2.1", "dependencies": {"merge-stream": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker-21.3.0-beta.15.tgz_1513344457178_0.4429902534466237", "host": "s3://npm-registry-packages"}}, "22.0.0": {"name": "jest-worker", "version": "22.0.0", "license": "MIT", "_id": "jest-worker@22.0.0", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4a276938a2077e1d72b6a2acd1d43826a9fd07f8", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-22.0.0.tgz", "integrity": "sha512-s3pa1SDAYmIxH7IUmP5OFVl8cw4VULsuESk3DUloatE71iFmUjtnLxtY9ps1C+Dg3b/dJuDOn7i0qKiMKBX7bQ==", "signatures": [{"sig": "MEUCIQCawUY0JrA0P4MTQU7vnUdSKfLTC7DBbf5bb8vhRhisoQIgTfzR0O+1RIcRpFcVj758WF/DO8Nra+VumREXJ1r+gHo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"merge-stream": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker-22.0.0.tgz_1513595003541_0.27948045008815825", "host": "s3://npm-registry-packages"}}, "22.0.1": {"name": "jest-worker", "version": "22.0.1", "license": "MIT", "_id": "jest-worker@22.0.1", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6f9353e91a624ac44ee0b14f38bf43a62e5aa34e", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-22.0.1.tgz", "integrity": "sha512-7QO8mD0XOhwrJDUlpMdqb2cqTabrL0qwWz0yDuP3GTzw7SNPt9TXnkHvT0sPzabtFgtAWafVhu6e8C4HyvKsTA==", "signatures": [{"sig": "MEYCIQCwZrUTJXk9QpMw6r30DZakj7vqFsoeLdWNQjni3aGMLAIhAP6fAxt0NXdq8TIcSxt6d59NG6O4giZqGzr5XxwH+FDt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "9.2.1", "dependencies": {"merge-stream": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker-22.0.1.tgz_1513628964125_0.5104117873124778", "host": "s3://npm-registry-packages"}}, "22.0.2": {"name": "jest-worker", "version": "22.0.2", "license": "MIT", "_id": "jest-worker@22.0.2", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b93c2a06dfa38ec026dbde041ac60901c05ab6d6", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-22.0.2.tgz", "integrity": "sha512-WSuhZB4jkE/8hUgkd+DO65u+EG+lEmgwhJbz2qZqhhe7m5RSqKJ6g6NzACfXIaoaXEuqvc6372m2fyJ5l6WfUA==", "signatures": [{"sig": "MEUCIQCVfo+208SGP4Ggg+sP7+SWjMR5IgoM5mTJ6q0gHXRqnQIgcGQPwzbRMQXGXpCJP2EO9JVI3EdTEC498lEPbkJirjo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "9.2.1", "dependencies": {"merge-stream": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker-22.0.2.tgz_1513691584130_0.6812368561513722", "host": "s3://npm-registry-packages"}}, "22.0.3": {"name": "jest-worker", "version": "22.0.3", "license": "MIT", "_id": "jest-worker@22.0.3", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "30433faca67814a8f80559f75ab2ceaa61332fd2", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-22.0.3.tgz", "integrity": "sha512-fPdCTnogFQiR0CP6whEsIly2RfcHxvalqyLjhui6qa1SnOmHiX7L8k4Umo8CBIp5ndWY0+ej1o7OTE5MlzPabg==", "signatures": [{"sig": "MEYCIQD/ErhbCVY7t21WbBH5v8Jw6w1+sp9BSt14eCm8ymnwVQIhANHb/5AsdeaZp5uVERxOiquFpg5L2z1knNmNZ3Uiy3DU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "9.2.1", "dependencies": {"merge-stream": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker-22.0.3.tgz_1513695534162_0.375342208892107", "host": "s3://npm-registry-packages"}}, "22.0.6": {"name": "jest-worker", "version": "22.0.6", "license": "MIT", "_id": "jest-worker@22.0.6", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1998ac7ab24a6eee4deddec76efe7742f2651504", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-22.0.6.tgz", "integrity": "sha512-4GlBXpqO/RrmaNF9unPkwNbqlQqR0VtYX4QpYfSUzxBEuuRT0w7PyKj99k15KUC2gRNlNK/eN/bY81LAeW1NEg==", "signatures": [{"sig": "MEUCIA2m4To3RySEM6H2ojyITkqFXXE6NgdhWawMZSu5OIz+AiEAk3WpRi3sbnhQTqXf1YTr68BYKeWt8pPAbHB+4WMnOX0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "9.3.0", "dependencies": {"merge-stream": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker-22.0.6.tgz_1515664004493_0.058518264908343554", "host": "s3://npm-registry-packages"}}, "22.1.0": {"name": "jest-worker", "version": "22.1.0", "license": "MIT", "_id": "jest-worker@22.1.0", "maintainers": [{"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0987832fe58fbdc205357f4c19b992446368cafb", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-22.1.0.tgz", "integrity": "sha512-ez<PERSON>ueYAQowk5N6g2J7bNZfq4NWZvMNB5Qd24EmOZLcM5SXTdiFvxykZIoNiMj9C98cCbPaojX8tfR7b1LJwNig==", "signatures": [{"sig": "MEQCIAz6Mv+NWU+zkaZ8zwdEQQU9Jj8HmGpKizQ98AAkTywsAiB6Q50tgc3nAMvxEjg6wVW/ZbpZJwTTLBMySxtR+AyJ3Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build/index.js", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {"merge-stream": "^1.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker-22.1.0.tgz_1516017434237_0.7194203475955874", "host": "s3://npm-registry-packages"}}, "22.2.0": {"name": "jest-worker", "version": "22.2.0", "license": "MIT", "_id": "jest-worker@22.2.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d88d6ee176d6409f206cbbf7b485250793264262", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-22.2.0.tgz", "fileCount": 6, "integrity": "sha512-RKXSCsSSn5cPpJASHs1bOAnunpgcTo+DnLEcaCvVcf5yx/4ks5TEuTTCJMQasjJwwYpHW2MJX20i2tYHfrInEw==", "signatures": [{"sig": "MEQCIGW5Ql1BuGqA0cvnajmk+ngKHgbbLWkOyE9Td82cNx/zAiAlnNTfOFmkxfmW9/YPz0CdUloTeEPyL6zkc26PXR/iuQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25804}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"merge-stream": "^1.0.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_22.2.0_1517999159676_0.2637796202018099", "host": "s3://npm-registry-packages"}}, "22.2.2": {"name": "jest-worker", "version": "22.2.2", "license": "MIT", "_id": "jest-worker@22.2.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c1f5dc39976884b81f68ec50cb8532b2cbab3390", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-22.2.2.tgz", "fileCount": 6, "integrity": "sha512-ZylDXjrFNt/OP6cUxwJFWwDgazP7hRjtCQbocFHyiwov+04Wm1x5PYzMGNJT53s4nwr0oo9ocYTImS09xOlUnw==", "signatures": [{"sig": "MEQCIAKnjjf0EGIh98NDMk2It/GnXZH1xRzLhIlu4memHnUQAiBgUj5tjk9jyCRv5ZZDRIcO8MwCsXQqGKt+zrCPz8EkOA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26203}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"merge-stream": "^1.0.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_22.2.2_1518193692981_0.36704386466582917", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.0": {"name": "jest-worker", "version": "23.0.0-alpha.0", "license": "MIT", "_id": "jest-worker@23.0.0-alpha.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "7f8db5e97d4f6cdff199d726585daadde52a4694", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-23.0.0-alpha.0.tgz", "fileCount": 10, "integrity": "sha512-zhRBqx625qoQsq1AuBCxNSG9TOSfXeg238axcEC25GzRE7aeAb4FNJCTRs5kIOMWie6TbUpoRaq9wGGB4RbkHA==", "signatures": [{"sig": "MEQCICO/5QFCmnaioFsquKvI6dBPfxWeMIJMdXmd4L7n8vGFAiBc6S4YctVfvnQJZVB8vUFke1ZfAiPMJtCw1WlMPXhyIg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22803}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "dependencies": {"merge-stream": "^1.0.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_23.0.0-alpha.0_1521125735690_0.3681409112796552", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.1": {"name": "jest-worker", "version": "23.0.0-alpha.1", "license": "MIT", "_id": "jest-worker@23.0.0-alpha.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "489e47641b7f360f1a305eeda38642d6874d7660", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-23.0.0-alpha.1.tgz", "fileCount": 10, "integrity": "sha512-8znEq1Apfx1slJsKLM3FHzs+rt6/ucqoS9o1k86naXGGjR9sleehmS904uAM7Jdm1gYjLi7gPXqj2N3A6JOwHg==", "signatures": [{"sig": "MEYCIQCcV2DcNxwlu+gqgXIoXaPY/XbwuEsnNF0rbUaJxlMgHQIhAI7lenzKhAmo9VMpJupQPBBPWkaFH2RJC+rhebMz982r", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22803}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "dependencies": {"merge-stream": "^1.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-worker_23.0.0-alpha.1_1521648014071_0.6960559003266771", "host": "s3://npm-registry-packages"}}, "22.4.3": {"name": "jest-worker", "version": "22.4.3", "license": "MIT", "_id": "jest-worker@22.4.3", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5c421417cba1c0abf64bf56bd5fb7968d79dd40b", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-22.4.3.tgz", "fileCount": 6, "integrity": "sha512-B1ucW4fI8qVAuZmicFxI1R3kr2fNeYJyvIQ1rKcuLYnenFV5K5aMbxFj6J0i00Ju83S8jP2d7Dz14+AvbIHRYQ==", "signatures": [{"sig": "MEUCIF8U/33i+N7nm8+oHEaJvn1hz5Y1EQ9c8Fekgg7m29oBAiEA4o/m+Ptys8JbXfOVzpDSrf76aJPrUxUzxBBmJiO8aQk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22618}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.9.1", "dependencies": {"merge-stream": "^1.0.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_22.4.3_1521648488870_0.18498585957563685", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.2": {"name": "jest-worker", "version": "23.0.0-alpha.2", "license": "MIT", "_id": "jest-worker@23.0.0-alpha.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "ad16c80e3b73822f22319ef323ac12fecb66d9b9", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-23.0.0-alpha.2.tgz", "fileCount": 10, "integrity": "sha512-3jQLPaEKGUP7yrVaiAVCis1vnQ9XNnYKUz1fTKuEZIX9uJBw8fR1YRF7MyxLZQNU36+Yf3Oy1KEkkQcNxK+9iA==", "signatures": [{"sig": "MEYCIQDhkLloe7KN84KlB4EAXFM99aIK1bM0iOUFzemseiQh9wIhAJDQgRIvjy2dYTI/T3YDkcmrhOhyE7nyOBG0ld+dqxhV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22803}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "dependencies": {"merge-stream": "^1.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-worker_23.0.0-alpha.2_1522060847156_0.5509658409029636", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.4": {"name": "jest-worker", "version": "23.0.0-alpha.4", "license": "MIT", "_id": "jest-worker@23.0.0-alpha.4", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e16bee22d0f6b0187ff13c5093edd6807b02c868", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-23.0.0-alpha.4.tgz", "fileCount": 10, "integrity": "sha512-tAZGcHoAQ+ZltgoU+32SU/OmINFL96NMfPJW1lGAVV6nJWdfFHNn0nHvLfY0Jdw1EaP05PVrL6/8WDm3VYOHzA==", "signatures": [{"sig": "MEUCIQCzU3lkUgaztQ1HFSf6F/w4gI3m5MwOWdBBrzx052nIRQIgZ8QmS0Y/qIJtroaAJmWontufxadZ0ruVFOfh1IPscnc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23139}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "dependencies": {"merge-stream": "^1.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-worker_23.0.0-alpha.4_1522067499337_0.9032334426184534", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.5": {"name": "jest-worker", "version": "23.0.0-alpha.5", "license": "MIT", "_id": "jest-worker@23.0.0-alpha.5", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "003d3b106b126070c5c563b20d48cf2141df0f44", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-23.0.0-alpha.5.tgz", "fileCount": 10, "integrity": "sha512-ha9ImA6i45qZgovOmu2qZ6lfpLGoUrraloiTXaPFFg/Zf9OxP/QcUwehv1b5/ugiEwN914aq3LdOVCO9CntvFA==", "signatures": [{"sig": "MEUCIAxA29PK3veV2hy3+NshtV5fX7qf8Qjd2pJ7VDtsfc1iAiEA+30go6bc7LlS053bcceX2/eGp9VtukB1bm76irsFJns=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23139}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "dependencies": {"merge-stream": "^1.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-worker_23.0.0-alpha.5_1523387898706_0.20845530536284618", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.5r": {"name": "jest-worker", "version": "23.0.0-alpha.5r", "license": "MIT", "_id": "jest-worker@23.0.0-alpha.5r", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "20ab007ec743d886d843b3da4f23a5f8441ba18a", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-23.0.0-alpha.5r.tgz", "fileCount": 10, "integrity": "sha512-8RCKzYa4+u7owlK4Mk6wz1oz1o+kDyng3qtY6ZFsZfpr8BFHu5gWn5JgUmMBYYTwPl6NTI3tFokQSrP9x/8bNg==", "signatures": [{"sig": "MEUCIHlYVDzSvLYLgZ3pbFrBSDl2juDBIDk0fNHLCJ479gHZAiEAy2H4iTcCf0D5j3Ynp1N3vO85A8fVdjsMxRnNTSJ5KXg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23140}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "dependencies": {"merge-stream": "^1.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-worker_23.0.0-alpha.5r_1523425969034_0.7713974098971996", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.6r": {"name": "jest-worker", "version": "23.0.0-alpha.6r", "license": "MIT", "_id": "jest-worker@23.0.0-alpha.6r", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "26cd8c2e37a2209788c673d71ad4cbbaec717b2f", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-23.0.0-alpha.6r.tgz", "fileCount": 10, "integrity": "sha512-vlP+qyyl45+V+zsM5VNYzuSyNAskiCo4ISejVP5OUn5hXtiXixpsa2p5qfUSdXVfCtKDugW7uG3ScwnZEu1gvQ==", "signatures": [{"sig": "MEYCIQClssGCXmtfuS/OBbmhCBiJB1meGjKBi9o8I2e+4mW5AwIhAPIS3Uygc4TJTanYg5eEZH6du3Loc2hU3643zqlPv7/A", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23140}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "dependencies": {"merge-stream": "^1.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-worker_23.0.0-alpha.6r_1523516494677_0.8680124888922869", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.7": {"name": "jest-worker", "version": "23.0.0-alpha.7", "license": "MIT", "_id": "jest-worker@23.0.0-alpha.7", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "d58131933610cfea2daaafb2d4afea7f9ad63c87", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-23.0.0-alpha.7.tgz", "fileCount": 10, "integrity": "sha512-fXmPpDVVnNA/YB0cQkjJFLvKnPy3TKEq/jaIke1jbwTwPk207uCWtlro10sVQoWh2w4UwqkVYwVG0+1dtJ+Eyg==", "signatures": [{"sig": "MEYCIQDc2XuyJZcBh8k49slNZHJjSVonzl63jq4Y5zmG49pbzgIhAMjj/pAMdcqPOEvlPkvQ+H2gS3EgAYayI4DuJqM1mKP4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23139, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1kMPCRA9TVsSAnZWagAAV0sP/RBtBPRjkEWzz8rbvw/1\nVEQsAHlahtK1mIZNfbtph25poU9QAWzU7/Nv3fz8+gCLK8RZZf0a07HndqRp\nKGEdJigm/HVKy+GJg2RflLULco5jer8bkLb0pXCc/olTdZRAM6xCg8MUFcn9\nk0i4fOsrZy+1vh/r6Qucy3eM8SJKTDn51h4eVMeTHZqEOsdPvJV6MoyCSg5v\nMuBi73N4MRE1tJuJSndo1H/LIo2c1oPhnK9aGkGQxP18xhfY/Y7HqlfZd77V\nHkEU5p9I8enrkV6Olj88mpXh3bXrhyD+P4AoY9jCR1LWLwbx3DitV6m4GGV8\nHhCGzBkRG5eY4IUN4q6dyyU96FyU83HN+NC/tIf9q3IL4QZrRnx9vuRgDPri\nM79ay2toxaT7QlsMalK6khJHuYjUM7p+Kx85kZhQswxltJ7uJaP/2pqm+sfE\nUPEuQrYhAZy9lF1QaNXJ8ljSGFwvfyEPTw8websAyu89R/PQRoLw9Ra4b0b+\nK+C1tKHJRV+FBwlEkmqaOqfg6z/ElupdppjZH/c2P61xc1yoZKjiHNn8JAoq\nn4g0ExFhaGGGokMioSSlxiIX+5JF3dJhdkL5oDcdZc/enwpr++AC5Q7rBx6B\no6uZRU2eHZE7X1Rhw8Stt7QEpnqjm6laUX/eqP+seI3GXTv+c11744oDpDzt\nuziG\r\n=ZcIy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "dependencies": {"merge-stream": "^1.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-worker_23.0.0-alpha.7_1523991310864_0.893980803381144", "host": "s3://npm-registry-packages"}}, "23.0.0-beta.0": {"name": "jest-worker", "version": "23.0.0-beta.0", "license": "MIT", "_id": "jest-worker@23.0.0-beta.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "adabe2cd458cf88a06530ba731f60138a6f0db30", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-23.0.0-beta.0.tgz", "fileCount": 10, "integrity": "sha512-CNyPWseYV4ZI/9N0sFin/7yRv01shU1HfvADkpclEApu+7xQEdGklprhIHDdqfQ0ldYBzfKMFPWE2NuQru0zgw==", "signatures": [{"sig": "MEYCIQCfSADNfCdRmwsgHahxjYPiSNwrwH9n9wl7Ig2YfAtdFgIhAPac6ed9zA2M6mz0EGXVLX0d3Xhzi0yJQjq1s2gzcMJO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23138, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa2byZCRA9TVsSAnZWagAAoxcP+wRWwA9PpYeHb2o8OQ2N\ns/gtXgVqA5KJeZsoxQ63odFyvMy02JZ1zrMnEP5h6MI8QhLEXTM4FeFV2cyj\n+jIi9TGG86ZzwtAa7gaiZwAJmUV8Qckiuhps6CsBihBT0d8thI5Mw+FbH8vY\n6SxHZjYVAxGmdtFxWkd8ZTg3+U4AP2/pOwSSFWGR+UTpgqNKdwTyPIdW120Z\nYIrxLcCtkpxPM7+cGl+ojhoV+qNYHOQEMVQ1OcOlGXebiUij/y7Q/liQxIt+\nXesUOszRfTwnlDNG41RghGHFhaUBwJWH2tIR7UEUxKVygc9StrHfazLN6BD4\nXiAeoUiTqDK6nRmj7NVLb9v23FFa1PD6CHnmnbzfTQ77t5M6WZbnHAPHU+Or\nzAzN4+4hWXz6dnMIE5XKLSpdIw6aUVbmT4IWacW7hU3EWxOM/rjtmi9uvayo\n9iC/1lbx4e4kawlpStWmqfLtbeISYmp0BsqEYB1/iYVhzTpDaNHl/DX27SD1\nynVehF82jHh6Wp+3aNFYSWiH1FMGbZGvC29fhk/Hmw6c3Phef/VmFHOLYQ3a\nISwd3Pso+jorsDMxUR3n725M3/636pdJxrMhDk2tP+r6aGVrmTAKQrQp0OxC\nMqqq7bABjrUad/XASVIl4qb/70VCK/m/Q/Nmi+LA2f2JGl3VabO1Wa6D1Mt5\nlgFg\r\n=gYKo\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "dependencies": {"merge-stream": "^1.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-worker_23.0.0-beta.0_1524219033272_0.009274239883118485", "host": "s3://npm-registry-packages"}}, "23.0.0-beta.1": {"name": "jest-worker", "version": "23.0.0-beta.1", "license": "MIT", "_id": "jest-worker@23.0.0-beta.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "2e3f815a0dc947f283138115bf06707c24c71043", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-23.0.0-beta.1.tgz", "fileCount": 10, "integrity": "sha512-XUKUove5I9+q7t2Xqn//4KMjbPuYnW8vb0ji+GmbZoPkCzZRhbNVRxSk3sTahCZlltvIzSUfs4SHhSkWUmItwg==", "signatures": [{"sig": "MEQCIAq5KtbIuIfnv6XFDf4a3OwLefRhnHj0sU0qsTATssZCAiAwbsTK3qdlFO2vIlMwjNIo470SMFX6PqN9gZyU5baOoA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23138, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa21xYCRA9TVsSAnZWagAAWg4P/j/I8tZ/K7apCBdzpk2B\nQl1jIdVxJ7h3mRPFxmddLslGWeua8v2qDsE1ADPkjn8S+Csd1KO1VH1Op9+x\nN83cAD8tZS9FBXk7It4OVDYD5wtcI227Fw+6aHsfrApgyVaFaLfpwsSfX8Cx\nG7D3q8iGnhUIdCau4CpclsjVuOrtJNPkbQbQH8RaRxGHU6xfqEhsAgMWwPkh\nd/+yuVC7FjvL1jVMEdM0yQRXPv+ED6tthc1egmjqEisptr3rNjUasnj6vvlm\n+ewDYfLCv7Sg8vDZjVGa/22rEpssnDZ9DIhBdnCvQIovQh4flRo4MscsE9ul\nyfBsfADI5j49zY5OQMDWhCPVsnxwvw4Ql8QAmctsZVYTGHTFDb6qSEBPNmOs\nN9Jtew8bFwC1zftuQdPUJC4D7Kf7AEHUoS8SKIPBP5F8Jy2GC2GJQ4lCLF9b\n16vQyZN3rphKGgeg4DLCq2szwHaoa5YoIl3IGHahgT4n+P3tpxG8kO6Hvg83\nel9EGj3VLeSV22Qvtfy8rxz0flGejjwO+GJBZ+bHRrqLe8xD3HJ0BGJpEEmp\n85u+nJlm0L54UdN2bN+RjM24h7bZHYAVBsLHnrCIwXBoXqmAMw8nL9Tynkgv\nia3KJp1mfKOTYpX9cnqe3Srod+55SKd9V97t7aahvccw+j4vzaBcPlLamtxQ\nUl4X\r\n=0AUx\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "dependencies": {"merge-stream": "^1.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-worker_23.0.0-beta.1_1524325463703_0.3765296828270117", "host": "s3://npm-registry-packages"}}, "23.0.0-beta.2": {"name": "jest-worker", "version": "23.0.0-beta.2", "license": "MIT", "_id": "jest-worker@23.0.0-beta.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "40ef277824e9f1a1cac47c127a6193c0f0c4f95f", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-23.0.0-beta.2.tgz", "fileCount": 10, "integrity": "sha512-irEMybnUbkoxE+l1rLkw8dT0TX1+V/xq87eFYT2CxKBQNN4kl7v7wVOKb/nOHtxRuyQiFduDmc1m+inN63fWTw==", "signatures": [{"sig": "MEUCIQDxefgttngSgqyHS7wv6IOlOdmwzm/5VsGBJ2Xf8CrUrQIgYOt+ypZw8kj8vgtgM2DONlAgd/gxwBxry0LqctnJWJs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23138, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa4kHwCRA9TVsSAnZWagAA3VIP/ipzEeukBBAQqPgvIbrj\nqfyPwThNS6qtiN2CAoVOgkzSwdE6Ql+H/7GvDcnBpCVNTNKDrdDKoxMBhVr9\naXEokzxHe1r9FeoMHMTj/e82i20CBSOWHEZ9ywNtNEi1hI9tdrefkw1UYhK6\nXx/j5Em/wVJk5pnqQcj8zLJRtyz5h1aAN21s16tN26g4DMBUJ/tv+hcbUCGr\n0tybJoISwZ/2eSeLMAXC8xsOjXtY+LP/Zu8YAFPISFd9jb9UfNrk2A7DAYbN\n3Q9wroZSi+GD+kLwe5AK+XMBxUCdSg8qtFzieKxhE5orC4kxkONdlU7gijyV\nuaqJaG+2ODO0ANUBXPYP6xLR7xv+0GhvDDUTuqXRM7V+BAWau49h+8dhfNBK\nSrw90lzE9C0eUkycdQpwEwRhjX19lcaRP3U9K04+pZpv4tUigwnXq0PavG1X\nslG8FnyaEpMyGlwFyRZRX8P0i6GDwTdr6XILCOp4UitMRGA+iU6yziwgLNCs\n/eIViQ+dvkvMD93mC0vkqr9zYnvoiBFbMw69g1tC3cHeaarfXPuibpj1xFTk\n182iTHqqDJE9rTDAtndT1uySOiA9bTKiBOdTHMPXvYVmZE+53OY9ASEqq4pK\ncFkrGCiwQZk43iNxxao2fiwQ6ISFW5jF0ErhvZ1dXvl6o4L9F8T+1umOO6DX\n0OQu\r\n=a5qB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "dependencies": {"merge-stream": "^1.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-worker_23.0.0-beta.2_1524777456320_0.6331109426207158", "host": "s3://npm-registry-packages"}}, "23.0.0-alpha.3r": {"name": "jest-worker", "version": "23.0.0-alpha.3r", "license": "MIT", "_id": "jest-worker@23.0.0-alpha.3r", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "a88b777a30b978828e8e67390e1587f02270bbdc", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-23.0.0-alpha.3r.tgz", "fileCount": 10, "integrity": "sha512-vRxXUeQDfLvYd7xXfsm1bCJ+jGE8MFKkM8AAxlImURsQGZVct93gupAdfUgOzdrwAI8E7SU9chSuVgu6D9wDHQ==", "signatures": [{"sig": "MEYCIQCWJiosr+eMN8kuhapVglSR9R+AitUW+jmNnmBkibnpLAIhAIsMmIc3JaKiY6ZQ1J81OgWP7I702lGylE3M5YCIkXRj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5xW0CRA9TVsSAnZWagAAjvIQAJZEc8s611NRf/h2ZXtl\nO0wgZ/p3d5mnYZt/MXYv7aPumbnbUGTbevsFh3qUT+KCUC2VrMkqplv8yOWM\nnh58CG2XfY8qreVCNxXKfaai4Amo2t5kMQ3jo3eWPRbtYAHC+e7GcEwmP/ek\nNPYx7sTOxRkwdlTzRxUm/Hz3M9wCFaUHOlWeQVpw9NFw8FF3aRJIbcdXjqhP\n2zIvCF70REyBtj/0Paf367ksvuh87tELumMRO6mVOsSn2lq69Hd1jCVEUj/R\nMmAvMG4SglD/FaM6vcCD3NmO2Sx2vVAftvARyfa9SxVMi838xHN9vAJIwMfR\noamz7dO0BfDZo/4JUGj80Qcz6q87OmDareE5RJzZD5Em01PW8agpA0MH9THr\necpXtdhHcYR3CAjex4SCHkrqpLpSoFeZRmchUSuMF+PuPK7LwdW4wp89557D\nMXz+bNyyQYByVaggczPEyJqnQBRQiie57eEwSqMCKvSssMNGJvdtLUlNfO0u\n8wy/uPOyTQUQGgfNERf6aJcGFFir3fboqu7JcEpod1WkDbLzLpGHyhazz7i4\nL5T6oN1Kkc2USvdWIQ6CJMR7pDqHBq/mxWc1/9fRqgohME2wZH8VixoPUYgy\n/00JIM5ScfJZewkBuqJ8whRGf/aPllNPQbRH9CcfAk++A9/1pht/3PA/qd9+\nkODO\r\n=6cC+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "dependencies": {"merge-stream": "^1.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-worker_23.0.0-alpha.3r_1525093811702_0.7366269195950377", "host": "s3://npm-registry-packages"}}, "23.0.0-beta.3r": {"name": "jest-worker", "version": "23.0.0-beta.3r", "license": "MIT", "_id": "jest-worker@23.0.0-beta.3r", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "80c8894131de793a7be6ff7aa91ecaf53f947533", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-23.0.0-beta.3r.tgz", "fileCount": 10, "integrity": "sha512-Kl4rbK2i1lnvOAR2BFl3szhCoRn4luN7dezN6ZRFWxlVjezPrrnO3L6JvPyly6a4R+ytorPxxC6KECjlPb1wTg==", "signatures": [{"sig": "MEUCIDI3IrqSF/xQHqGkzrpgyvAWJFf0JbAdhnNDdoHyMM6aAiEAitRxXTurcibX83A//WDvPsFsCGdxq6VdBpP+qhClBDM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23437, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5xbPCRA9TVsSAnZWagAAsPwQAJ+eLl1ETPBgydVnbWob\nXKVbRLDClZujzsgMIouRR3QIJcDuTMdwpHLGIyX3VzerEGoCJiU+c+0P9oIy\nQ2Gz53c5iL2H4pvKt3AUMeFiLukdyYjcrzY4omkWucc/og87NvVC+PkfCgsg\npYgvMXdA5MW9aUAQfnDVL8tt2Ykyj0GXgikSbwwuIXxYSfAquGERiyK6L5bh\nmp9SK3bSBm1UUk/9Bny49MAE/lyg2tyn9MP4PrhwbGMC4dTqILcERUG9KOMy\noQHK7ZVswq9j9pu+dWf6kPbRoAdvGVGbghV9KQ5j493TiF5bv5uUo2E2+SYE\nG2co9g1W2yY0RugOqb492DvjPaGs9qeAPUIyQaIswPbLSNRmvJKpRmfXTw//\n06jvJF18W3WJWIdE1iHy9azP/eW9a6xXCbtogXp02KKGXbjHSI9qySn0sq9U\nernzzhOQjpJB+i/URt6rrlEMusrLVCFg55pApJFWJl/Por2yKvOmDyPhVtDo\nQDGgD2nW07FKryDXNU7ByoX/NP05diJCBeR/O+KIJWqTuguMZqpKHspbUIYo\n65YnzNLUiTi1iGYodgbEMJuzWX4J6631IAEkTFFEoZKAIegjTUbDRqo1Syyx\ngmRkvbZhLsgXOGufv+2Zc4OqeZyrBxzeqjA24Rq+zso3qF6QI5ILWAzkgeyc\nkOxk\r\n=EFYM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "dependencies": {"merge-stream": "^1.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-worker_23.0.0-beta.3r_1525094094507_0.860665422353958", "host": "s3://npm-registry-packages"}}, "23.0.0-charlie.0": {"name": "jest-worker", "version": "23.0.0-charlie.0", "license": "MIT", "_id": "jest-worker@23.0.0-charlie.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "b4e328a1cd4bbcea460e76f2d304112ad4d1a407", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-23.0.0-charlie.0.tgz", "fileCount": 10, "integrity": "sha512-tw+Xr+2aZFoZWPn0J0PkfD/K9lkQ2tWNTlzwKOpZ/sQRojfBTHky4CD/8brbY+q2NEh0H7R4DSxlgGJZmHogAA==", "signatures": [{"sig": "MEQCIGIc2RpNB1/tV7KQ47I1aOzpPVOerKKEihGjUORW5SBZAiACKwE+iqOIMuq6wb3NLOQhpzwe9qPs2eSbWQcrZlH17A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23439, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa6ZlYCRA9TVsSAnZWagAArKIP/jrlCJMG4YdZjhF2Aa+n\nRXhZ2mxcjdDtyx3ZHLlgySfB0cHLzOTedJSEjUVT4bkg3zFU4bJoemVfEgSy\nuw9FZ8NsIV+eWo3S+smzEAPhgos9jIm87JxU3z0a+jMCSaPHvqHwKrFEyBwh\nmV8nZuUOd6I/Vp1dte00Qd7CJjkFo4x3U2qAVuE+R/9+c60yUMXaCAXeNF+e\n0bZAa/KqTWJTQaw8WV4kIsIAL7gCgbX8K+1M5ryXFUDvGWBzcO+6vgwxSQmV\nJXvEDASucXZ+gS3cvYNsLvVk7rB7DTsuSYvOZHiGblcMT19cV7h3vITej5jl\n60hDfJFixuOIYdqZrPsrQYw+rBVoHB9zSG02Ig7hygc31evPOk1I1/7K1HyW\nYZNzE1jFZRSLEnjBWDZ4QgN/cLP0n6oAlQXGSSiUAzX1QdOd3iO1+fWrhvkC\nLZ+sxEr8pZANTpGGNaaag+6QBFJEnY7RMBjCq4Mklo0T2POA58NFZtGtF95z\nFRLCVzfcd7nSBAitg94/HeL1mltIqkLM/flhYWTjNm8ArHQ9mqrbvVOq3627\nL81iunTawvW8+SPiybhcQIA0unitQYz783Btn97rggCGVmSdSS1OyAABGyFU\nTK6gd8oh4/4y3dmK9vZu4RGwsT5eSuH4Fn3TGRTzXRpCzGasMKw8R1HoyaqX\nhXqV\r\n=IYp1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "dependencies": {"merge-stream": "^1.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-worker_23.0.0-charlie.0_1525258583596_0.9557692703093459", "host": "s3://npm-registry-packages"}}, "23.0.0-charlie.1": {"name": "jest-worker", "version": "23.0.0-charlie.1", "license": "MIT", "_id": "jest-worker@23.0.0-charlie.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "ac8212ee454808a21b617a72a3c7678a3b29b0e6", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-23.0.0-charlie.1.tgz", "fileCount": 10, "integrity": "sha512-KAs2Yxg8qzPpWRMV17W4OmAWBw2sLb1ecyHGyZ9bc6xg+hdQuMDeDxVfEjGwWacJynF3nLwS8IJG7QeSIGUS0A==", "signatures": [{"sig": "MEUCIQDmBotfaIvO1I7DOPoqgKDsu1cGDFMQMjznf6odT8OBgAIgWHTUs13gDoEtL3ISRfl2pSJKmrowtsQcAXzbab3xA8s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23439, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa6vwnCRA9TVsSAnZWagAAiaIQAJoefFm+8uIK3g62LgSW\nbeJawJDZOkGRDDDfiXaW0mfaclgQeKrq/Sb2z7hjz1JRsyGzQ9WGZFjCO3xi\njvqRTWdP7yZNh2SBwaLrQ1Aocx8wkHE8ECQn0MFQTJG+vh35tgUgJ+ihql6r\nPrx6j8besxoQPPLpiBmIf55EppqDFTSWwsPzM3/PD/sI3GVoFbuCQ+z2cKzA\nFYtV5/PNxKjdDB+vihUIzUBXkGCh5i9yh+qJYi8taQ6ggvxZBmpdMpKbJQU8\naRmtUy1yrq/wklbFbzavhhZe9hCIB1NitSrPaYL1HryruICbJyj0EhF3XkF8\nkPNfCmZlf8iUFaHj9p8KjqQorze2z/jyHXrGLyGTV1cIinRG65bO1UkdIevB\nhsN9jL/9AhwRptckoxpFKCjOLKjWaFxwC9dIsnfXcjEHtc9JhnO/0IyxIiBV\nCq/1YoQPVfUlDMnkVnYde4GeUq4n0ihTbQm9CasSDClheYu+SiNQrj/8iMqR\n+ytHPMAdEtSyVAvxiCSB+Gz+iv3fZJkITLSPx4MrCjjCAgvZO03cJqwock78\nePNsxDlpdWa90+CKZeJg/yrxzOG9q8NmqvoUcAPjJ16U1s39YmvGadnDMva4\nvNPHvAB1jG17wUXRLgokNFyaWLbWdE9OG/j8j9htPvqJpUP+ZTI02WJ0kcZF\nHmx1\r\n=JFE/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "dependencies": {"merge-stream": "^1.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-worker_23.0.0-charlie.1_1525349415052_0.16603752126685078", "host": "s3://npm-registry-packages"}}, "23.0.0-charlie.2": {"name": "jest-worker", "version": "23.0.0-charlie.2", "license": "MIT", "_id": "jest-worker@23.0.0-charlie.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "b9c5843cb659bcd724ba872666edef327c9893cb", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-23.0.0-charlie.2.tgz", "fileCount": 10, "integrity": "sha512-CNkl340CqIeb60Jah+4c9s8Jh6VrDZSjfRi9qcKQFbGGZKsM7MwUwpgUMAMqlOTg31lV+k10wgBJeHfTOG594w==", "signatures": [{"sig": "MEUCICpBJjbCK+V7pN3OiyqxTwng0ynjOofMki5Mi/jvfAHsAiEArQxTuBECCUFrMuy8dbPt2SqhlEQYW26Qbixr1A8m5P4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23439, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+q2eCRA9TVsSAnZWagAAjygQAIO6cJeSvPuSrnbQQBQu\nMH+9MWHLF7wA+fkX0BHQ7HKHcoj4ZeHAJhSl8HOrgcxZSCNYxY01zBv6OTHL\na1c1Vxq/nYmmPj5kYrTC4BWU3ZYgKxet3PmbNvkWCJ4/zrNz2XYZBPZ2v0De\nt6D2YlZzxtUPqpI+Ok96/7lmzQROoHjU4hRy0T/mOCv9PrloSCTJvs+gnU7y\ns5ohV76vOrEkBRk4+UHzXTF2RTsNc2LwS1UIl9z8NFF0FK+h6M8tXFUFnz4p\nITP4v4VQMc59sgWS/GcBP+vqLpgE2L3LzMF+7XJWG/aTVHsBsfyZq/YXyL5r\nAoL0AvpsMHQ3KLava5tTTkEKBppaGg/vboVQfkHY+eCpzSPlmlD2cxsvSSQL\nJuF4gcqT2sikNDr0V9edQeR5oTM9TMjnMLh9zqlv80KZjNWwREYgfRmKczBt\naRGwZaZcMYIoMQpJ5nZHiBhouWfjPytoJzWW/UVsiaFJzpEzkzretFWGIc30\nZlLKnCzCdXTOHImIwPPc/vE9m7KRk3M2HUEIbMKeERV7y0j8mdJVWzr96cn/\n/X3jpJvEfUKNm+kQOy6EHtR5oq9guZTcHzJeSH3l1KPAeO4BTx1FLpvuaz82\nqhwUQeUh11Vwb4kyQgz+55THM6fm2hiqGAMvameC/LXV0sTal0YPQSPpFQLP\nzkO1\r\n=GFkE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "dependencies": {"merge-stream": "^1.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-worker_23.0.0-charlie.2_1526377885834_0.7860545292282195", "host": "s3://npm-registry-packages"}}, "23.0.0-charlie.3": {"name": "jest-worker", "version": "23.0.0-charlie.3", "license": "MIT", "_id": "jest-worker@23.0.0-charlie.3", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "851a8e15e1891cf32f89653a222ce3ea1bc0ab83", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-23.0.0-charlie.3.tgz", "fileCount": 10, "integrity": "sha512-ZNFXIiUlzcpfovLz9IebKm+JMO8Cz7ev3+Jm3eADfOgki7eloYWnH71leX2KZ4F4Om89imImYnCrmhVZBYn8iA==", "signatures": [{"sig": "MEUCIQDDaXD9NQTZ6X9QHTNd5/e1xVo1NIXXDg0kSH7uurfZdQIgV/xoh1c6JyEuDm9SAzblTYD4h/g7MauJ5Sj0Bsi3QvE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23439, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBDAzCRA9TVsSAnZWagAA0YoQAIVOsx/lRN1TpyNcGuhy\n6z1q0C4w9uvICHXmfLtBoHnMitn0y6Jb7z+TxsjKrp5D+/hI7XQkrykO39Vo\nhD1trFWPBUUuTaO3a7yPxtc+D4xlUZlwU7kW9XTx3ml5DKC1ygrqMB9/VlEs\nejsijm1FIIMJKkHFLenUTSK+sv4HoYPlsP3UmiHTJiQ2vJmtwhhcfJX2m0jr\nLxVaEZbUa+UypevCnn5M1fxT62pl3WDmwj4Z7rqNHCfGsDlbQGfw9sUL5aMJ\npqr9phNapfAYdH0LknnI6fAJ6ZDFw7oA/QOuMZV8Rre+40cvnylVhFtgCB1m\ntkpWBaU8dawJXSdPkQlvg2lV6PWxEOy1ZwZmlucdXNm502hLVXWk3396pORE\nZlkYxR4RC5bA2By6DV+6lAz/lxrumno/qu4O5zCwsF2fxDqXR6O2GYGzAEzI\nY0Kxsvbkg+G9xcj+A3SRiEfobZNAj0vQCtDCd2XJHyDANhiAnp206XfgQLCA\nAF8vzTXiONLvGMyMUeF/3JIlPaKOvZrAGJGVmvRfKNWGSC92BGrcaeD2NMGN\nMwH3Ajfy58ovM37iTjrNgpgMIIMUMdhRdVBlcy16odPlTmRG6P22MKB6ux/4\nF3XuwgZE9Kdj5etgWL9ZnX1f0djCPnArGQGABObGso9JuY6YUwdjiZEhWRcN\nYSI7\r\n=KUti\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "dependencies": {"merge-stream": "^1.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-worker_23.0.0-charlie.3_1527001139088_0.2619355062458322", "host": "s3://npm-registry-packages"}}, "23.0.0-charlie.4": {"name": "jest-worker", "version": "23.0.0-charlie.4", "license": "MIT", "_id": "jest-worker@23.0.0-charlie.4", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "606b9181425db8c5924b9af6761c4002f55adc48", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-23.0.0-charlie.4.tgz", "fileCount": 10, "integrity": "sha512-XXHXPZGM9Srl3gbgCV5uRQKJfE4ZagIj+Gnz9g+xnxDsSC4ULtjjPg52pCG83OHcFLfX42OHe++wCbV5Il9oPQ==", "signatures": [{"sig": "MEYCIQCecMjm5imjDhAcJZzWor/W5Zwv579WLF/8wviiCRhWswIhAIyMsT/sLo2xWEW08x0pcWQMLsfWAscy1xLCCa+k6rD5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23439, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBUWJCRA9TVsSAnZWagAAagAP/33phV+XhUPniRzhqmEZ\nwPpTFdl3sV72R7f7tcsYw8W0jWHKxvdt8TaP+tgh8d2MRSRjVgmZaUjKkb4e\n4+tfbguNReAMyRvtR4f14zm3DgmCb8UALhr7XG07mRJ3fu1pP9RnU071oJwf\now8Sm6GINnLUozYbApZHVX24FKQwkUXCdtIJWv5C61owYmSzmJPVVwXfkDsC\nunrxkBe0M3LNTUwUbuXN2fCgeQtejCtnDTpH4M1UQMjX8i7r8RtHJxNa6d8K\n1zRjgasIW+7owE34oYuXuxhPI51G0IQfN6ZF923ufMMYRCGM4bD9tuKfAzkS\nSy7ZuA3tbuemGF4oIZvE5HhPU6voM+eq5+C0SpyJlVPhdJkNwzBuv/2LVeVr\n/O0v7NjTeFgmGw+B0j/R2uo7BAXuoCFZlNoi9ajcKJzxRhsDi+H+KmfyC3IA\nII7MNETJnzomf1NdWJPj/I7HwYZsyBuDvV0AbuEwadOTkBALEEQ53pPolCOu\nAYgYXZQse3qs7W1TVZvnuQ43vAEJkAfnQPJUK5MeePaChQEhN1fFtfJ0v0G0\nylq2uGJKKLeQHkSy8uSQuRjUm6NPfdDlfk4a/e4/I9Kyl7orBeYlRuQVhOsk\n+mZ28yAhCyvVtY8+RcCoMJoU3xIMKLoDxTDAehckLk/vtsYKCYECxhgG0ABq\nJJDK\r\n=syaW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "dependencies": {"merge-stream": "^1.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-worker_23.0.0-charlie.4_1527072137074_0.17914931157823255", "host": "s3://npm-registry-packages"}}, "23.0.0": {"name": "jest-worker", "version": "23.0.0", "license": "MIT", "_id": "jest-worker@23.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "e6b1378b81f8e6a108f3be33a1faa830c22ea450", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-23.0.0.tgz", "fileCount": 10, "integrity": "sha512-FscCvh+qViQyWP2H8ncbzN+tbXwrE7T5fDhtRgsigD4rrVrt7esvb+xzJrvUnJAAYOekf3CzyTo5eDiLLte1Ww==", "signatures": [{"sig": "MEUCIFOoNCxEBe2CWP/WjsfA9DiwL0XL4OnBoZump/mBLF9gAiEA0ex43qy5qeLtvDEHCxlKeHh990z0+5n70kHEcxVOyvI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23429, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBvXCCRA9TVsSAnZWagAA43cQAIx3v+uuluK0Gme6cr0b\n79hMUJDG0GqFUQFrQv3zrU6mVnka/xTlbMDrwV1vJxn57EApJSR+GnUv9sfx\nWQmod6j1VaZlUNuClAWUR0q8IZLNt+jcxqsc1x78eqAonq+WckT1VJAecgGB\nzlTziwM6qlOGZcb4hy0KdQ+jeroGab/Mip3qXKkeuP7Dmy7TT1bQZNjVYW6D\noer93JPIbtehMpcp9ynIBPMYmPXvbaW5YPCvwLYD6LcPMkILoHiMaM+XZp6c\nl//EmjKuRhvxUoLEcXKpDtZzJS42t/uRnxelzEZ0YgSc4Rb5g+vWVkRGSuOz\nfXKSkF1897PuUaCGZHa4iEY+HzG4cviKrqiqlUAFaHBWlieO9frmA6Cvf2JQ\ndtEdSbJrbHbrOz+gy0bUFA+tp0sBXzleVDT/IAiWyZJOHDZQYt18zonso/Nm\noTZzCHGL7ahD2yddIGzLACt5J3PSn5o08KC8IzBR5S+r5a4CoKHeEXMuckP6\nfO5Nn7jCD+Rjz2hVPkZdThZtHjbKuCGFuUmvhi9PE2W9yPPQLtD2JgNX7SKS\nImPMyPVXFGYcLLqEz2GFv0p8n0iciMOFxGoO1Q+GC9v+CjlNO98Lf8vS4PGu\nR+ObcJ/7OKPnM0QLPh4rhOgIP7WlIj0GG0zBiLLj7OEYiZhVeGKkwsBfueou\nycuB\r\n=oP/K\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "dependencies": {"merge-stream": "^1.0.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_23.0.0_1527182785049_0.8521930410306562", "host": "s3://npm-registry-packages"}}, "23.0.1": {"name": "jest-worker", "version": "23.0.1", "license": "MIT", "_id": "jest-worker@23.0.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "9e649dd963ff4046026f91c4017f039a6aa4a7bc", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-23.0.1.tgz", "fileCount": 10, "integrity": "sha512-s1ok3GARiQLxk8Z+Y05NfKgLjN4BVKSrIiKywWiHZ11Wy7bVOSwNdnXzc2EfGoIfPLC/HWR5SI//wfEy99nGmw==", "signatures": [{"sig": "MEQCIBGsOAxYzh/WDoYfgBfHSWk0ErjAXkvcvt/Ymm+eLJjEAiAX628XG7s8YCxM6esTHPAUq4S2t0uKUla1k4Dov8ZGXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23429, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCs8wCRA9TVsSAnZWagAAMKYP/0SXKU5pcgNFzED6ngHk\nYoth7s/myJ7LxSwrn0enluorzmlYPimOTNqzObWqr2xz5igjOQM8bcvuorl+\n8nNG0um57KN8dI3qYkJPwGe6FEQMLNo1VjhMqLfeg4t8OqBB83GIiYk5wf67\nH+zHwEkQaidAkzi5LLP1R8YDULp6l7Xj3Z/+oMSkp3s1e5JUTm65SWb8HTGH\n5BuTbk4hT3+xPRkW+KTicSuc0LXUWDulczuDg7AQR8r7PSReC8cKSCMsH2LX\npVACAPotAoW+YsLVZYU65ht5fahQhvgsCH7uz6BBPfefL7GNMUWTbFkwEuhN\niXdiX1el83/EeItRXv6cgekJMajWarwFAXYmV0OYpufwFs/1oT2K8T57xLQS\n4QwAI9w7L3TN+CAnDx8Wz7U6Ujyle8zRCs3ELgzYSzL3bXJeRJseiYSDGaj0\nYYaJ3CxKUs3PPTJhCgeWyoHKiGDE6eHIKGacuYPk3nyCsTsdwrIYp64kpdVY\nN5fGQSuvw2U4vvcB1wYcsNFWlcHfq426fTseGX85d/eBE5cJrfUOkDUNyZj+\n3rLdYJ6CyweVq14qApdzorEKot4Cf2OXmT4LzSotH+1dgYoBia4NMOTsh4sy\nUaPInMbsUNDs1YJaRKzTJete6hkukf9mCqDmS78TXcq+C2LWCKCzNh8ILxI+\nWko4\r\n=FbEA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "dependencies": {"merge-stream": "^1.0.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_23.0.1_1527435055492_0.47630399545728186", "host": "s3://npm-registry-packages"}}, "23.2.0": {"name": "jest-worker", "version": "23.2.0", "license": "MIT", "_id": "jest-worker@23.2.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "dist": {"shasum": "faf706a8da36fae60eb26957257fa7b5d8ea02b9", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-23.2.0.tgz", "fileCount": 10, "integrity": "sha512-zx0uwPCDxToGfYyQiSHh7T/sKIxQFnQqT6Uug7Y/L7PzEkFITPaufjQe6yaf1OXSnGvKC5Fwol1hIym0zDzyvw==", "signatures": [{"sig": "MEUCID1lft+IjEbexVoFBFBbeYagWE0sE7lFP0/70Z9yuCLGAiEAkCa5YOrMXQLkbfnKXTbQI3r2aKpZ7KAKCK64Hf1/oJw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23429}, "main": "build/index.js", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/facebook/jest.git", "type": "git"}, "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "dependencies": {"merge-stream": "^1.0.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_23.2.0_1529935514523_0.06039057485118593", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.0": {"name": "jest-worker", "version": "24.0.0-alpha.0", "license": "MIT", "_id": "jest-worker@24.0.0-alpha.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "61d5c79479dc174e1f88d9979ae75cee2130fdd2", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-24.0.0-alpha.0.tgz", "fileCount": 7, "integrity": "sha512-Z0DB5NJjaBgzt4NwTWkHE1cuIgiVuD+NZjCRJWW+cZBbLP0bMpk27J/CyddElSBRtcqL4qkHoNVBk1HS7QM+dQ==", "signatures": [{"sig": "MEUCIQDYbKpBYlyEe1G8F1bRLHhA1gl4JzpGHhimvHzk9mbdmAIgYin+FRQfMyyWMXj0gq6jz+z3lpWKAVd5NekLDsEJBR0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26497, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbyco6CRA9TVsSAnZWagAAhwwP/jtPrxMEEkkEs5xp3NsU\n+DKOevHNQO3lL1aRqXCwRNod+py4H8ybWugzrIIOb681epMSeKD6NK9vEz1v\nNQ48JQj4OmJgqV9SLL86kLvcIh+32acBr/nOTPU+c+bRDrfnstZpy1n4J0Vp\n2HWAuaLo/1nexGQXoCDU87zXdXNNeHTIdoBJ78d2cSvhWCUdMZfuxrScQ7ub\nLvwWT2+bhgjx1totks43/I9WYLLWl9/lM476Kl7k9bVhl5HbcLWo/StnSmSb\ncFe/DXxW/zcg7D/QpmL96ohaTRnY/yBmivUgSfOs6a2fSObcUsXL5IWMpXKs\nFJE4S6Jt/02ZQHkM/SZd6326tDi1aK3ia9+wlGRHSMrjNACPAKSlN2atr71R\nho8CMMB6o4+O7HBo3Mp45wq6uih0RZ/gsBq/kIKIN857f3Ue3sbRUdRr1+k+\nUGLbHOoFJ9SxsprOhChJktGVXvEnyjbMKaI/u9tbmaSFEFlYgfMvpjHl9Qbl\nlCwferLLjuLPoCkyX+20qyloliQr9OR3fTCfXqxQoMM+NJxMZUhs53UbT3nx\nBt8JsxiiIcpDDb3Vmki8GWAKOfZPb/Ve0qJK+PnODq46OKrqiNs9IIztGvbI\nXx+R07SOUKOXLyEICnmb7YF2AbTvaBmiRSTtMwLKLCwcbpilva8AIfVoFEXd\nN2pU\r\n=JJEJ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "gitHead": "22f67d49ffcce7a5b6d6891438b837b3b26ba9db", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"merge-stream": "^1.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-worker_24.0.0-alpha.0_1539951161616_0.7814093350008589", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.1": {"name": "jest-worker", "version": "24.0.0-alpha.1", "license": "MIT", "_id": "jest-worker@24.0.0-alpha.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7b82ab2e534cf66640c42715d0a8f16b1ad5cdeb", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-24.0.0-alpha.1.tgz", "fileCount": 7, "integrity": "sha512-WlYhxJgLsfFIqnvbzVkzompu8cauOBhSRQElRr5PNOhhVeucQvfO3Dd17L0x23nZIkUPT6HuEDjgZfvbLt4+RA==", "signatures": [{"sig": "MEUCIQC8bXvOwScDC6AQq4bO6jZnIw1plzFQHE3u/wAHiYNzZQIgZSYkh4okfZFG6Ebuarj6ptMFZiR2epQAJiyFshDeSOk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26497, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbze5VCRA9TVsSAnZWagAAn/AP/0CuEsIfHkkhVO2XdrNS\nXCs0DF+/2L7UVr8ocx9MG9MV+qYRprDy/vplI8ibmJhJQkG248fvuFyND0PS\nluOwbvEUGmFBA8q6TIfqOIFgLUy20zEKdAOolUnueudYHpQuzAPWQM3+SUax\nCr2kENmfA2QjW27BPpdiKOgEcL/eAlJjvxqZKItyEpvj5SraUrXvjFPJmQVt\nHIhJ/CzFZ9q9ytWYaHdIbgBWNacGXNFgIGHf617gnWxVtusbAapWsDUa4Mff\nPp1lXVK2x2kLfLus8VJ6tXkN1/5D7ASd/nxk7DAuIFQYeUOPKURfO0rPiWY6\nURC/ZaF+HF77NJ/YjHNpwHRmcxrsOU/BFmCCDCTFNQyMCQjU5+cBhwuxxOS3\norJi3rN4wEMCH1Q46In+1FmSHz9rkjQk3GX2SW3M5MRK4r99zjSfDvlRPsb3\nUQz9LgP5pZggl0x7tOItNztJFdehjfgxqOYk5A7gQWnm6cykbe306dszJyjD\nrGn9mSiQI2n2yS9EQSPOuE5NG+I7Jb8rTP8s0nSC7/E+ejNMdk7s6emf04uE\nJyiXocuWLYdoO8thGwxEK0MK9K3AeOQepvYtDTVrSPlSCRDobMjpyQfQ+7kZ\n8nmHDZFVo9X2EfXv1G3zk5XkHeBkruWZfOMmPNIy70TTyMN0SzSa7+kAMZ58\n+jCI\r\n=Wd26\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "gitHead": "4954f46708415174c48a58f296a605fbe1244a31", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"merge-stream": "^1.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-worker_24.0.0-alpha.1_1540222548747_0.7561586816865975", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.2": {"name": "jest-worker", "version": "24.0.0-alpha.2", "license": "MIT", "_id": "jest-worker@24.0.0-alpha.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d376b328094dd5f1e0c6156b4f41b308a99a35bd", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-24.0.0-alpha.2.tgz", "fileCount": 8, "integrity": "sha512-77YRl8eI4rrtdJ4mzzo4LVABecQmmy7lXsXc00rIJ9oiXJYbz4R4eL6RXcxZcRbwwqYjFL0g9h6H9iQaWqC/Kg==", "signatures": [{"sig": "MEUCIAyUaO6kGLey7sexJ1dlLXi367QSVDHQalIW+37XC6b7AiEA7hC9Dg6t6pgIVRVyTjVFrva6GTE2q9zFnbLAL1qvhaE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64967, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb0aAYCRA9TVsSAnZWagAAuT8P/3n7di0npkQ75Al9GhOe\n5NtLjHMOdYj52jHT25bGLjp+c1aVekoqkon68mkbQdQcJmLW57yCwD+jWRY7\nrsIXzfcZ8HiJZRFS2s5xAABrsL6CG4xOo9q1N72xt4advtmdpT1eV8H3itgT\n4z6lDnT3h3GTt7FKODbZP8Rl4XhYeXZI9RCkp67E+xlU2VQE0zg4E/DIA1po\n3qfcGgPUF8MNWBhlG3lW3C/8tqwhOpcC8lB/Ua/3mo7CQpwplYXTGOHesMhz\nNdWfJCR07HuI82pbZD+IHXbTQ2UAbkEYnPMm/ETw8V92Ymek28AAtBo/yp2R\nCxGG0dKVww6Ix7WmKUYZlgdAQutW6GcrXkMcV1lA6x6fSIZh3wxHQUEFhfuT\nMwMB59ZXkB3ZFcS4u02IZ2kvP5BGG7Qi0H0FEIBcs+C1AB4+x84jJPJukBPd\ncRRD2lDXBgLmqW0/Gvcx057vHzqWpakmx0NSEBXmOH2RFuBuZpfsqkiM7e57\nW+yI4PPdPvFwXLAsRFaAHZgKIQgZtwhu8rPP8Fg1ii0IrfzIbz52szFquUSj\n3PIdL6pvxMjl5wafs+gwOju1h1W6n+THr6qzyu5XhPVyBf1e2d+OyQ7hrB/Q\nB1VL14nKzn0UbvKJQJdqC7Cbl6koHbUHnoAAd5oNLJom57Vv1IMGWX9QPgRo\nR5ZY\r\n=920b\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "c5e36835cff4b241327db9cf58c8f6f7227ed1f7", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"merge-stream": "^1.0.1"}, "_hasShrinkwrap": false, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_24.0.0-alpha.2_1540464657246_0.6694456870838097", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.4": {"name": "jest-worker", "version": "24.0.0-alpha.4", "license": "MIT", "_id": "jest-worker@24.0.0-alpha.4", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6766d11b66e7b2d61f79711d159125657084d021", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-24.0.0-alpha.4.tgz", "fileCount": 7, "integrity": "sha512-kZYIxqwkvaQggTBiOnoGoI7pyHAsCgFg+1C9NCSpkaQOLQ/MpEdY6cTPTbLwWdtzSloWnECAAj8p9es2VLQd/Q==", "signatures": [{"sig": "MEUCIQCvjj7lJA1+24O2IHi1cN0Wlefqhk+hK2maHF017wULlAIgZnQHuqMT1MqD2kM+K/1JniLaeVRLCU/sn4AAaDcTt44=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26536, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb00HLCRA9TVsSAnZWagAAWvIQAJ4XVNayR83m0DuFb0ue\nKZ1X70xbiRHb96a+ennYYNowfDjzodfKRmlr7LeDEfQPz/g/qrYeYxUSH1DZ\nx/egg9L6a9VdLA9n1JaXrzu/0lk476W+BADrx+vLnAEcI9/ZUpWvX2h79DUd\n8zVag6ETKazTQdhV0tNffN4Rfy5BJxwHK6b6drM1kA3cxHL8459lAt2A5lIh\nXh460s+O4vLW9FHgMfL+WOh2IMhiWBNoUPOl/FKomyncEaHKBl86XdziykCP\nCzOuInPua0/YOn4GLs3YVyWKg3Qd8M/aUdU8YdXqMAYBQ4ggrrH6+nBWrpHQ\njmLDl5+o3PdCEL5kwYYZI4mO60khQTgD+O8HwQGA59I3dJGGC/JY2QXG6hyd\niEqswnwj2vZ71j9C1vETT92ntfd8E8rLOWIoVPzVzzDouOYMfedAHTAWd3gV\nl+BhnIRtjlUhZEia7eleebyExQSEojisgxNs1rkJmUJTzAPoqTrC9Vz141FO\nfUafw7VxFkXf6XjsXjKkHuA/EgwL1u/i+JiOc23qtXjz6+nGD2UmF6HbV85z\nNdtWQPZq3qz4ncNTBgZSjUWwQzzuPla5i7ABsW4kX6Tno7OyWoC46WemRQCk\nZDrtuW3F865eqJdQhviRlK8i3mZpmDeHL4qMcxqRnRbl1XdAMO6b2J2dLj91\nTS12\r\n=KIId\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "e41f0bb257c6652c3100b97a1087f9f812fbea0d", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"merge-stream": "^1.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-worker_24.0.0-alpha.4_1540571594503_0.5234640823029466", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.5": {"name": "jest-worker", "version": "24.0.0-alpha.5", "license": "MIT", "_id": "jest-worker@24.0.0-alpha.5", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "82c3d64274f5134ec9df03d1a159ee4dd710c03d", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-24.0.0-alpha.5.tgz", "fileCount": 7, "integrity": "sha512-JL6nOBzVxF7f5Nc2/Cpek2fjKjjuRtQkNIP0G+eu0i787oX6xvibsSASVONaavY6D+hxppk4aYrgJh8/UTgkuA==", "signatures": [{"sig": "MEQCIE1OdZYyD+Zo/HTQVehx0q2CWej8YSd50YPpEvU8cH56AiBBVyESw5PGGcioM7EbkaTmO7FeBQiZJ3eIT+NlcbWViQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26536, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb5YfNCRA9TVsSAnZWagAAcYUP/Ajhih02YUtjizNqpvV/\nIYYGoOjNhd0UTQnXBC/oOB7ED9sDpe9AC0z3klTWXiSE4WAGLQDvfXEt2x19\nmcXyuU0xtljxG0ZPG1j4IvBUSIrL1+jTTYbaoV2UJoZdg7QDQ12lalfb6tyj\ngVQW7SxCGrQkKEisd3Ppkf3TN/BlppYu7/spSKuMY/laR777Druc1Kj+h1LD\nVbcEmE7a7uR2cgotFx+JG5WItp9qydapUYZD+vuo3zjrnz4BPjHu/J5i7zhe\nttvxBboF3mBcZiCh4SWPyWlCRNsGDsT2WA88uFWYSvUYlKuILlasdcj0cOpo\nyLtyg9BsBrYRp6JsS22c/O0pI4SBmj66Fzq/00HhelPDjENRRy3QkA6PPouj\nSoO7yNs0y40Si2ljWcoj+2gdlGeZT00zPEM8TYfhYwhjwFch4Xc05EuMBBXH\nnW3IF0C4kzom2Cg/2GAnWHVQcgdqPu2WsmYr5eoTp7wrXyU9YZV0tpmhHKTA\nQ9LiGtXYDLZurXz8OdVEQyUBmL6drAHzOcih2p7eFxvhysbRmgoosnZUYFb0\nHie/VAplb7t8SFRELtJQMQAhf9dKNQKmsQBKFLdrfGcaISSi75xVDEmqXNbu\nePAgkuLpZ0MCaMGbXydSR2haWqy09QNz6ZLe3gwWKkOE3D5xUnTm1qWBp8b2\naJ+Y\r\n=Wuq5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "2c18a53e8ff2437bba5fcb8076b754ac5f79f9f8", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"merge-stream": "^1.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-worker_24.0.0-alpha.5_1541769164930_0.617883942264494", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.6": {"name": "jest-worker", "version": "24.0.0-alpha.6", "license": "MIT", "_id": "jest-worker@24.0.0-alpha.6", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "463681b92c117c57107135c14b9b9d6cd51d80ce", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-24.0.0-alpha.6.tgz", "fileCount": 7, "integrity": "sha512-iXtH7MR9bjWlNnlnRBcrBRrb4cSVxML96La5vsnmBvDI+mJnkP5uEt6Fgpo5Y8f3z9y2Rd7wuPnKRxqQsiU/dA==", "signatures": [{"sig": "MEUCIQCnjIguWU4pus0cEkBJKnpyfES4K0H1kN7Q3cPNDq6FqQIgF1gz2B5Dce8EOU8biug8tRuHp5V8xBBRFemONmTylM8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26536, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb5ci1CRA9TVsSAnZWagAAmS0P/j/acWf3TNffxpcOxs0O\ndoGVGr9yC+J3pElBxQky5FIfv6Fakjh5LoFUuan8K0qRaOOQ4hsuK75Pf0U8\nHprTDVow9wxK6P3Pb4Z1/i9DdffDDoTA7Y/0puX/FIJfX/r4b3qUjOXqfPKs\n6Ba4CplogPh7Uqltmz1eaCrQyQLcsNNarpHiSNDLkSTmZ5rQVWrZDIk11Fh0\nD8IZe1jwP0sxpuT+6nHPjcFelqygROCedzmJ9u0Q+0PIMRfZAV/OnLmosvJC\nqN+ZoiyXIU546JeRFD9LyvRNLbW5ru1ILwu40aHzNa0z/nPHcZFR6DDECyXa\nAssBHGtnu6uVLN0Dz7o7/lfGk9Jjoxhrf328iIam8SYQpvgteB/iNP6uMcu6\nuaHUqZML7PGWFf40AZh5169Es6UbE3Q5nrx9mT1oe8LWB+gEj33uQzIOhdtS\n6oCEmSFLSdnR8nChRQ/O1qNi0RCXbhQDrMs9gC4ggYEXDinxiMI0XwztvYY2\nLlAt1agiewtrbTZin3RrNhdjQlYr6z+SgDdqwdE/yImLTp8A112KWgbwB3jN\naxK9lYwWLOr5AbKd7L4H6grmoVxca4MyAFwl+F8/okToga6ywWRVAn6FB+X5\ngvtsj8cgFqk7Pow0q4TXecY+z59IPveoik2bojNILuY3vAFemgqcS5kqxCsH\niGPl\r\n=OzEt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "49d08403a941e596eda1279c07a1eaf4d4a73dad", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"merge-stream": "^1.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-worker_24.0.0-alpha.6_1541785780717_0.49043937866079834", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.7": {"name": "jest-worker", "version": "24.0.0-alpha.7", "license": "MIT", "_id": "jest-worker@24.0.0-alpha.7", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1c593cba7530c2a1c66460692fd1f891b01e60fa", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-24.0.0-alpha.7.tgz", "fileCount": 3, "integrity": "sha512-JCtj+b+8N7/4ib219E9eACB5FzJkdtnk346MfXXYZjZAO5am6MBbBb+oAkd9fwyQN0vb4x3/xIzFZCVoDilH+g==", "signatures": [{"sig": "MEUCIQC2BFoxhFuMEecMRrw7mWj3NpGQLL/MeXKV28YZDPmU1AIgRgICXLLg4HZ0AFPiO7VmJ4tB1+Jls3pS2bB2lG6Up3o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8983, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcD+DWCRA9TVsSAnZWagAAWhcQAKLyHuhwpsxV7p6mmswR\nvRu4s4LM8Ii60PltJPQYpfESiXH7uP8HkZ6MJa0aGU4Co7z12ThLNShDyUyt\nrzn0qnXqQs1/2/5IINwIYAC1xjf/O+B4NHBweq/rdTgovwgZeLrXQem1Yr1K\nPR/dUkmiLQCMvcBzMeLLldP/fEUI2tZi59AKO8WJ8UZ/lAEtpUba5A0lAgr9\nzKe+aiK37iyKyDEOSg3e8U9PEVk2TwSYInrrfxd5LvLOpo2NbclHn9rVu5Kj\n21MkdKIdzsvK2rN7gK7+gBFIcgceoMq0Y/w8yJbeAnVIxM0B/3hGBU5TP4TC\nOF+Ec1tdlpZx/U9h4YrHFkGUe8qxJrxdlniSDQpYpH/DJwWANZAoOyHDqbP1\ntp7FhEZb2Vi+Cl7wkeKhBVzmrmJFvx+U2a7/hfbgXRnxEHK507TwCSkTWO0Z\nZFm2by+BQOIOc301YHTsdVUsiuO41tgq4GYWUxurNgXiOZRRwZQTK/oNdq3Z\nj6mokohfyNGbCGJ7lTggjgMNFLzXsn2/j1LMpOymUH/BlsosnDqEHAcOltnz\nQRnWgGJ72wIeSB6Bi5xzKymTJEki5MK/FHp28K9VeGhyqoVY9Lrs/8DYBMVs\nAwWo+E+bqMm0SfY14G4z9Pr8nChOYMTtXTYrIGoRDmvTUmPvDipbwDxc4tJi\neCx6\r\n=Lhrb\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "gitHead": "4954f46708415174c48a58f296a605fbe1244a31", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"merge-stream": "^1.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-worker_24.0.0-alpha.7_1544544469882_0.4194998533265395", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.8": {"name": "jest-worker", "version": "24.0.0-alpha.8", "license": "MIT", "_id": "jest-worker@24.0.0-alpha.8", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "95fac45db2dc1f40654012ce1acac83d4d31fec6", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-24.0.0-alpha.8.tgz", "fileCount": 13, "integrity": "sha512-E6G71iJdTmaDE0tlOR5o7V/KnHLHzYdET44pqZbjryuxH1R6SYSDF4CK6R1CAaFJQCnbOpJweqLz95S9iZ02jw==", "signatures": [{"sig": "MEYCIQCeXAigaoTTyEDuo76K4OIxxO8FpOwc13z3zeSM1uHw2QIhAJ4K6zpmappkVRQ5bjnBSDmsylN2UrNaL5BlbVnOPHqi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49795, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcErdpCRA9TVsSAnZWagAAIssP/2mHcTpc27wIzTjv2pUX\nghFEL08ZzUGQeDJ6eundEmbfPfK/hkXCP8TzHMLBu6ahbhNtdt9YgsQyBP8h\nGWfH4sXUz7pomlMI34VkTo7DzDIiel+P/t1KajGzoQkw1G/FSBXFaLxC8y4J\nPoNBt0VqY7gn6iI0illju4Z0qkQiyMVUHVKdJKxJiPehw8+CA9UhQLUyQavQ\ni1e2vWEGntci6YaSbwo/I+aqoGAPxxRnMk3aW0/C29LNaOQRmoQKiNIyMjty\nC2JSDRh2wxiQC2GQ2d0LiIjgJl9A2SpExmvZed0aELcN52nJmNESB8oRTHZ5\naLUoVV2llHqYiyySrUX88ui2g2YQQHSAF++DRlJ4ice+mbAyG1zJC+3NgHLY\n9bJVhIZ+w+N0hczD26kiqonKSQudlbZzfNpPVkvKfbO7wfAJwVUL7YeJcpB4\nPvcCHtsEuIlxVjvCIzkQSKnID3TgubPssIw911AJbIUB49jDKQfFd47cBVTX\nNhldObGNlLn2toEjSm7nPBN1372wgcQmf2RPcY4YnesDMluy+V3axBnb/ye/\n9vOgl7wXCpPlYrvCL6FezpZxbjU0yhR7StJ2l2ZlEL806zZITeo1mP73brf2\n9rI+mPzm7ZVJFzfko5YXOK6tjfebLq2355JS72I1yq7pJeW3FTXRXjunI9Nf\nh8XV\r\n=RJal\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "700e0dadb85f5dc8ff5dac6c7e98956690049734", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"merge-stream": "^1.0.1"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-worker_24.0.0-alpha.8_1544730472581_0.04718290546743309", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.9": {"name": "jest-worker", "version": "24.0.0-alpha.9", "license": "MIT", "_id": "jest-worker@24.0.0-alpha.9", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2d8a35687744461b5aa8ed553dc4d5a5da2ceab8", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-24.0.0-alpha.9.tgz", "fileCount": 12, "integrity": "sha512-6lveyea6qbLdlQy+H+hn4IcEVgdqlI++KZU0tOdLeCliJ7myj7sDAO3fCVAcuMW9yxekIwrYclj472mP3wQdcQ==", "signatures": [{"sig": "MEUCIH3w7IlmGHxHyQOPgpoKDeCOQwGp/oVwII6Dpx2skVgCAiEAoEfWybAlOKPKBzKCbV4xUMssBnhXlC8Mt5XCT26Yo0A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38629, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcGlTuCRA9TVsSAnZWagAAZPwP/iqnLqyckaNbnhBeaRXP\nZ1DWSCQ6i+d17n/8tKiDscKRBBkRnjoa8aqXZdTTGsiIk5TAAnUcIbOjZlUZ\n4uH2wsUu7DlZLx+jcBeRQpD4kBhVASNl2kwIVBWClhbvuO/fmlX/iXZZrdRW\nEHqNulh1bVRyjLPFQcGBfjmk/4UcBeNx7p9/BtZS3SLONHUuk4zZ7RM0tzee\nGO4TYCsLLpa9dsLUjve5X+pjUC2i3DRQTQSmgprGAmSthp7LVV5sjLmM5vVf\nnkLMo5oCoyRYizkjmsUJj6EA8BQfQpe4fGBxmpB1ENtHJhReKuRNI1Ivi2ym\njFzCJiYZygCijK7fQHOuTAwESa8GUEcuqYO6lMmkNDCGPbd3vJX2kzLJ+0+F\nDZOfbWgka8+0xnlXbz0E6OBKPtHmfXpKmZKPpgm7V1t+J9gJceTKfjUaRGC1\nJKclFV4CD8h2T2r+lE09qBZ/7bmpnRg4rFjB8pCfHoV2VRjBa35MrdaOvW9p\nrsjYs2ByrqH35BGfV0kXF5/TntoUGHxtshWku4xMozTvVR2ZYY0deYacaogi\nkgmESv6z+1ekUm1Adu572YBvFTL+253MJPD7Zyek6GoOKL3G1/IbDc6oXwjJ\nwPge39EK/ie5zdoJYXSH471s1W91lyJYqW8pQ567jDdVI/KsBNG8x2pk/t6g\nYoUc\r\n=eres\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "c7caa7ba5904d0c61e586694cde5f536639e4afc", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"merge-stream": "^1.0.1", "supports-color": "^5.5.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-worker_24.0.0-alpha.9_1545229550308_0.9967062755230511", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.10": {"name": "jest-worker", "version": "24.0.0-alpha.10", "license": "MIT", "_id": "jest-worker@24.0.0-alpha.10", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "046702c758ae3214cd279e602055a26b066122be", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-24.0.0-alpha.10.tgz", "fileCount": 12, "integrity": "sha512-oU+4FRtzBkHzU1aQSYuUJMaP3QNN1nsDH/nQ2EgKduVPOKz5rdOrTfnyPdr/r/q70iAJRmrnaSvlfnEWgWXoiQ==", "signatures": [{"sig": "MEUCIQC6eoxICwY7hGjKAHBkvpcGyID/IJOnYoq9fcpUAL1v1gIgVt1jOH4bEcK7Gvwni2194p+nGFPWbEqoCAkXoipiHNI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38740, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcNimVCRA9TVsSAnZWagAAtGQP/2H8SMfma3qhDaofwXeu\n0u2fikc2nGuVJBbiJuPm+aFUkSfpxXs2Xmwci6sYbsQElnJZ2t7Dwq3NqUmM\n0lckE+tnW5jmIWmYYRFEt8hSc55SuGwSqreVgQOjEWU9q1AvBWNObRcb7Yqo\n/gc3XXKrB1j6Au7MmdrvUrH6QLegcgYPTiHPrvHAHddKsvxT6zH4a84fayEH\n+5YJM6lP0O8AYcdcDe75/LWQsCN+lGC3dnui+Y7awbVEiqWMpIBPHnt/nc6q\n4sGt7tswoymhXTtZeZr5aXfvrAe4qEyobX39TyVaVUYtsQ9hpiCB9CsZZEbW\nI0jbUgCy8zmxgWuArtlap4xQn2+vAEaYVDeAtd5fI7y6VXXEdQ6xlUmGDDAt\nJddxJlGmAlQ3kMlNPrf9JZuCF/mo4jpsVewIeqkcsf617c6H4Wpzt99oceyd\nyR5P1QIw5emEC9TNWA4ICDgH5LYIlw8FcuWtSCme7ZKR5dZ/ujZSdK/qLRzM\n3fbFBbxCBlCXI5xPMSCXsh/1DDs+CUUkjyVxO8j3/yIVaebRroPXRCnk9Io3\ncRS28nGbSbFhulnaIQh4flN4AVMnw89zyGqLZY6TKzU3n1RuqrnBdg0XSZUU\nrGeZNrmPEPZCPsC/jmDp2crY6ehvPHFZK98FBtMJ+/DJ7vlVh9Ry5XdYF8JF\nwaeW\r\n=xT/2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "722049ccd66947d48296dcb666bc99fccab86065", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"merge-stream": "^1.0.1", "supports-color": "^5.5.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"worker-farm": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_24.0.0-alpha.10_1547053460870_0.3169135777345684", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.11": {"name": "jest-worker", "version": "24.0.0-alpha.11", "license": "MIT", "_id": "jest-worker@24.0.0-alpha.11", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3cae89bb8ffcd52a2859164bbd8697fcc160456d", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-24.0.0-alpha.11.tgz", "fileCount": 12, "integrity": "sha512-5rfc/oIw0WrUd5ur7vbmW38R8rRnv+eQ3t8ubyJGgKBXJ3sk9OMP0bC+AdsLjZCNEoNblnrS8kCBztGNRpitHQ==", "signatures": [{"sig": "MEQCICNXf0mNO3+GwOWzoCEvuwkJA/8lHJUN1PdWVWq4z6STAiAqHMFC8vVc4A3vO80t33DH5AYcmaQ4uzaa5OK9h1RpsA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38684, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcN5BSCRA9TVsSAnZWagAAduYQAJ59Jvh492j3thGMVWDD\ntjsVazn8jECsAwB42dli303S57A//uJd89ahZ90Lu6B7nAKzwu9amwnA3xRK\nQnBAaIao6/WV4r2bHpyzxUiGFX/hFIvtFPv8eTV1CeS+na3FhvU4jbr9QCHs\nPNWemqRhli5GoRf0Y139wJY6Ku9JBpNkqlGaKMq94QFNYb3hCt5QeBOeAB3P\nvJbn9peXbsXz8CePg5yaB46g1HrMNSGC/n2r39wUsq/FQsxxyfTe+shoJ8sa\nOg3R7dlkMSEM5Z1PMjQwD141G+Rj3Oo/Uxs10kI5ggmYiapNRnpuUNIH7WeX\neuabNzeB3G/heTfBJc4g/JUl2bXXEjT48mUgIFj3+WnE3AtethbOgcnsYFg6\nPrw+qhdkVvQJZcm6S5Ms7uX51G2WlgrkVJrIwcyTuQv8nn90y7e5ML9CcRdW\nR4yvw+EB20NkIHTPX8hL5Xhj5Onfq87flGmojolMC8AAfx5ZgC8i1LEXZSgj\nAVmHp6P74CJnlGsv7GRdgu5nDzmpN7l4ppAoJoCr3YY/vCMvwvP41tYZS8jX\nydcvFhoGfognRTOjBJUBc8yjlttPpMFnhZCrB9122cTL+jREq9o0VXX8+rem\ncO//XZHMyJj/C7p80wDX93c30w7mcv9zyCaNRXAIlzeDGbQjF+P9jd9deLM1\nIQ0I\r\n=Wfa0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "6a066c6afe2ae08669a27d3b703a6cf0d898e7b7", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"merge-stream": "^1.0.1", "supports-color": "^5.5.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-worker_24.0.0-alpha.11_1547145298337_0.30812601552155683", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.12": {"name": "jest-worker", "version": "24.0.0-alpha.12", "license": "MIT", "_id": "jest-worker@24.0.0-alpha.12", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b7ca1fb774b4eddc342768b7a63d89be5e6a762f", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-24.0.0-alpha.12.tgz", "fileCount": 12, "integrity": "sha512-BzGiUwc2LPyrvOuCMqdiLqWU78C+lHbHI/hcJgWonTda0RS7aCcrgSJx5t9+56U9rzMMxDC75S9khJ0oi3fYQA==", "signatures": [{"sig": "MEUCIQDfqyEn803zsLKZP3CK3I8sNODbMN5t4LwnNOkSNOa4jAIgYrSUOIBvXfb3uRZziu3Q4f7i6CsfX321FtLZSk329nk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38684, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcOK+0CRA9TVsSAnZWagAAde0P/31Pq+vaFZcOzwRIEaG8\nxo4SioGxXBe1pkfH4bMSnmcz7OwwU75KKZI31zyYgWfcg5zoaN0DKebnfuWs\nBa8VCsAnae0lAFzNUgl7476gdS0RPGzlApABORkoN16PxOX4fmJiP8D7yTBj\n1ESuzctX+uo7rpglHkeaXhpsI1pEHuSWRapvWbyN2Mb6M/Kq3EeN5T37usNS\nOY8hBYIKthccIczF9lJoC66rlHkLPsIdkveLCCM078yyylW8TWf/zdQKqOQc\nMABQWhnxoBE0Xv0v2Gv7+yzmt8VfB+CMExWmwib00nL9Zfji5/2mmQUn5b2l\n92E3H5QZzvTZrmIX6xaGEercWCwbhY5dwT3bz09A86f6rBfYXlqXg6h+V5p+\nngNKSBV0ogUbdwlyz52RuhOkxeF0Gf2K7uTV8mAssDHNyzU4A/rD/bVW6Bsf\nkfEf3SfV4lNu/KRyvdQbAmoNIKUcxmCHuRPnSfz5/6HQLvnVoZF7X7MBSCdM\nMd1cRVSh1uXXQmm4FiWUPwpYxU7/5usrMRwGpMroASRFOxVyvIa/RP92F0EN\nIfpMRF4zT0nefOM4WRC0+oXODedqyy33sZ4x7DPhA/9ot2V8YGiUOwYA8OzZ\nE/IQZLMFUU7dD1WgGjXmI93z78CANDG+JMtRW4QUiz2427W2a+2gg05u2/1y\nnt2s\r\n=eGqC\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "4f2bcb861d1f0fb150c05970362e52a38c31f67e", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"merge-stream": "^1.0.1", "supports-color": "^5.5.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "_npmOperationalInternal": {"tmp": "tmp/jest-worker_24.0.0-alpha.12_1547218867535_0.10605551597684992", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.13": {"name": "jest-worker", "version": "24.0.0-alpha.13", "license": "MIT", "_id": "jest-worker@24.0.0-alpha.13", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "388d2136af6bdda63e27d5f0f5a09c671cccda3e", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-24.0.0-alpha.13.tgz", "fileCount": 12, "integrity": "sha512-euVesuF7gpHuwsKPnUdVeB8yHcfgPKK/UF45WuvBjuJS5YD0sT+y1ngdubKrPeGGkxbWgSuU80jPtSnoHlO0fQ==", "signatures": [{"sig": "MEUCIQCObdTWBXWWCVH6lFMnc3/H4pzeAcnvOVgYKHVE6YqPzAIgF271ztEb8NxQly1+Evd3ZyCTvnuRhKQRu9ceHwizQes=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43107, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSIUQCRA9TVsSAnZWagAAAPUP/1N289JNwZ61zsJx2bIW\nQ3dKPv5E+ELiu0akSCqVR+kQU5+2q8f2oEv55sInbZap/kYNSXr2O5kJI9vR\ngud7sWjM3D7E3ZAg/VMQxPU+D1aFm6TFSqCmIRSJJfsHl2k5m5qYWT/Bzl68\nOBy85Xn+65QF0XRJbAfs9Qt9MTNItsHE5kpmdrOz7X/QigXBNWj1TM0uLJCi\nxGLx07KYsndZPSg0F8DKfQXVPgzHQC767Ee/+fiDIg79uTvVYIyhMa2gYfQJ\nehAZwKQ4sMM7g0wI8ONi4anOq7lJr0vqvNZRBnyIpPgGzpxe7jAupP3IEPXS\n083rhrPBoo4rnt33sYrFq6aHJjpp91NUdSaFbMhCX47l3YDQ6jtNxdOL7hE/\nBm5WLQkavN6kQmkXymcuD9izj8unl4qjB2UvLl/M6llw6V285WaMcOEKHG3L\ny2CJOzaDDqvtXXMKMwjBXP0wO+CgY9PLqs8r+hTFRTCKlziasxdaYLnmPtcs\nJ+uWGb5ME6fj5fDWGYR192iSDBAP8jJ/oLQ/m8h/j9Y264EKyiEkvjS+Ab0r\nwlq7yHkPj+PAb2XVg7jBEQeXPOe5KagfucFnmXYa6UBEwCFmIxc7Kokq6JHC\ngzb9oiRTz9OXG2ZwvBhzO3ewPaCir1G9FEpwug6U82HPczWc1ZiuuI9uVwWM\n8dXg\r\n=DV/C\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "6de22dde9a10f775adc7b6f80080bdd224f6ae31", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"merge-stream": "^1.0.1", "supports-color": "^6.1.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"worker-farm": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_24.0.0-alpha.13_1548256528478_0.23551729449690217", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.14": {"name": "jest-worker", "version": "24.0.0-alpha.14", "license": "MIT", "_id": "jest-worker@24.0.0-alpha.14", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c3b76400d9d9dce866a308916a9f290a2bed6634", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-24.0.0-alpha.14.tgz", "fileCount": 12, "integrity": "sha512-PNXl5YOpVhcD+aTTUa9yJ0MdEXuAb+xA34erM4CSXdEOnALWijmYIENFCFY0wginRNr4tYoPAZExin5L8VTGrw==", "signatures": [{"sig": "MEUCIAzd1yh5AN+cmmLT9LMiWxqU0Ph1NRLr6fkRG2OyGLvVAiEAzNji7QoYP1H7Y6b44oBtIRU3zjjxKcnWfsOPzFC+tWo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43027, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSefeCRA9TVsSAnZWagAAbJsP+gKW+w93BIfc8HApLYOu\nE4QbNJjHwTezW/yLX0TJjPRXmKciXTG+8w+SzHL46ft6l+GGD8tWMMMXOW92\n7nMJGjEJPoCd4ZNEtFSSjTI3OUAxLRvCQyhjIZT+TJFg2g6jksn6SFYUC1Uz\n8clL7NmtDx5lwGgPdshrEmj+7+xXUpovoNAOo8diCCxnoK61CunbPZSaHljt\nYs9MCvUTxH5k/3YCZrTHVS4q0pmzx+V3u2gAqGCD7vd1kHulRBttfBFx8jV8\noB4bO3rmH4UOKhvb520dT0qz+Et/nbytlzWC7RJ1A2IEp8u09vjT24gPdzEU\nabeNLQnBbWnweDFJ8dxrxGuN4Mlpy0VYw0NcYlWtmvpmtNfbQGcILs8AwEU6\ng0vRDcE/v8iM1SjYH/JtR54aeFbFnw91VVAC6zAtiNsv5W86psOKGGS1XCw9\nUhvGV49sr897QroIsYpzc1sImFP+bQJ+wVXHkI2yBncRxvMRIODUoUIVhuSn\neCbCy6765KQknHrlxBzIw46jndZgjPGSWnPjA6AYnzFsOUR4PfQZkVFBKLvx\nM6wbuY8ccNvS+B/taqGJFlbafQLz9lWLMwH9VhuM9+NmjXYHnGUYx9o5bB3x\nMMu7/nIAwZGbOce6NQWhY3hS8jhPJj+xbwGnj3EtfE/7z6puHk7BTw05oEp2\n30eI\r\n=FIgT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "28971c5f794330e8acc6861288e6daafcd32238e", "_npmUser": {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.10.5/node@v8.10.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.10.0", "dependencies": {"merge-stream": "^1.0.1", "supports-color": "^6.1.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"worker-farm": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_24.0.0-alpha.14_1548347358226_0.14356972200221718", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.15": {"name": "jest-worker", "version": "24.0.0-alpha.15", "license": "MIT", "_id": "jest-worker@24.0.0-alpha.15", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8c48e8679060fe475557a5b6a36c9901900ce722", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-24.0.0-alpha.15.tgz", "fileCount": 12, "integrity": "sha512-NKYPF5czVE/1/EnOr/c99tLxAfbvwMZHZ7G7ugQsEYpaHArc7YdfHM1CC37vEkKlpAvtkkE9eau1yxPdsEjnCQ==", "signatures": [{"sig": "MEQCIDm6cSp2rGc3zeSBZwx6QBw5Pq+Co8SCxa91DkpbBw3QAiBP3xs0MCDg1//D2lxk49oFo4/EFZqqRxq9aDrAMQc+bw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43027, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSfthCRA9TVsSAnZWagAA5p0QAI1IwXL+CIQvA4rTAlmD\nrewjJnL3rQp7ecaT9MwbnbYhv/h4YJVDAFDo4ZVb50P9gMay89cvT9qX1jXb\nktjzOenaWkfNPdum/ZV9dQ7IZcmb+7VqJMoFTqdDPeeHt/1nxsc5ddT/IbiK\n6QAoZt0Z0fHE2h1DZZAyvfr7K0GUy8xbVRsvpoDL9nNZMRVSG5a8DnfkRfps\nUOBcoolTTUaOgll+i39/Eh33uXecj8xVyZM8IgppO1W5dCq8lDn5R/lXd4hL\nF3FgRLD1lNjlTSJpDOs6+AUR3O2PmsIbyxsem+d/V80XEbJOGaqai1XsCVCB\nO+o4PRjlCS4jU4lGeVwTZ9+pN7ghaVKBYiwzYlcrxajYrOp5bB3BS3xK5h35\n3Sbw+tfexVC6msmnjP0iG0oNKLw8RwduPi3MBpD0kAWWNQp83+a+CLiSaMpP\n/n0PdvQxuDA//og+AslW6c6d9tce1Vw4NszrMh6mQZ/BtMoBwtXU4xVQjWno\nz0yL42DQ7gCE34uKn0thYmROAyMVo51XjUOrlOe0J0VYEYOpDTdP9MwsF8Hy\nw6AwMRJl3qQzq2YNkoJ8IH/gLCNgNu8rGVhyung6Pau9IZOQLkh5ihiTUExv\n7sHGBSJvV1uuO/tg4GiRC7zj8ureQc5Zzom0BhJ6lVJ29e3SiW7KYcilQdTm\nVhbB\r\n=rWHn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "28971c5f794330e8acc6861288e6daafcd32238e", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"merge-stream": "^1.0.1", "supports-color": "^6.1.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"worker-farm": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_24.0.0-alpha.15_1548352352327_0.6685603072560629", "host": "s3://npm-registry-packages"}}, "24.0.0-alpha.16": {"name": "jest-worker", "version": "24.0.0-alpha.16", "license": "MIT", "_id": "jest-worker@24.0.0-alpha.16", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "141607e0232fd88fe7885624d97000449210fb1f", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-24.0.0-alpha.16.tgz", "fileCount": 12, "integrity": "sha512-8KfLsizaU7T1VWi65ue5zGPonK7LkPmh1khg+DdSgd6rskA81YcR2TvxWtCL0QFdZFaCJjAf7VnNAASNA6DYOw==", "signatures": [{"sig": "MEQCIAvcOPfeJrhULqwUx0RanS1nb9YlhqoPP2r9mIbfXlDUAiAcWwjaG+TwT2kfhakvn3MJv6QN+1gyTqt7dnqlBLePDg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43027, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSxIqCRA9TVsSAnZWagAAqSgQAJb+8ukWN54hcOm2I6Da\nuW9vRN/N1zayvvOZPLch1vr/6rdRZlHSQ8L3Tk3eOV4syA+J+a9tnL0TgjvX\nYv9QGfW+CPw/6UFgT5K4W+WOzz7KMWZ3b49ulINKJcEAgLn66jAJKzyHLuGa\nNzRHFpIHSaGP6ZU7o1AwHST6FqCsvppZbQx5j8RdAtgzAJ3IueGAkFaOHGyu\nawMTMqEs3UhjNWf5a30iH7V4WC8MSLRP3dfg5MTpRkjsdrfHnQGauFBMm89W\nWvdFQOn1YDYZSg3pglOjfZevJUAzgVT3kWFfApUdoXZVGA2LwTe23gCYSBYr\neMch8IbMwp/PzylNyFIk+nwDWrh91zUsVF7wxQRTAm0F6ZhODQQY0oyuSV3J\napH/x5J+AA8EKvnWN+Tdg7fi3oGzbEJ9LLYErjTFNE8J53ze3BLIrwA39w19\nKCjRa/VHwHUKr29aHUD+4rUV0hosEhuwht5eKvQHzjlut8tuc9gJgRE3m7LP\nFV66zkGzPFmrA8O5IkJyHb1BnpZP5jOTnZvhXY7JB1cecjAG82EgzK6DcyVJ\nPVV59ceXeyhqPTE1B3btNixi6Ilru2esoTFrGnmw5L5/uMJ4wMYavHT5oh+I\nODKDuv+l78VEcwpH6LAKom/3Uvn+zlmD4mOOc1IAVjxS2V8YcRGOEJuLBxTk\n/hkW\r\n=BE3U\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "634e5a54f46b2a62d1dc81a170562e6f4e55ad60", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"merge-stream": "^1.0.1", "supports-color": "^6.1.0"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"worker-farm": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_24.0.0-alpha.16_1548423721727_0.987057463312466", "host": "s3://npm-registry-packages"}}, "24.0.0": {"name": "jest-worker", "version": "24.0.0", "license": "MIT", "_id": "jest-worker@24.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3d3483b077bf04f412f47654a27bba7e947f8b6d", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-24.0.0.tgz", "fileCount": 12, "integrity": "sha512-s64/OThpfQvoCeHG963MiEZOAAxu8kHsaL/rCMF7lpdzo7vgF0CtPml9hfguOMgykgH/eOm4jFP4ibfHLruytg==", "signatures": [{"sig": "MEQCIAsp2KTxvHQItvQ/GInGiHUM0YicCtPkA9Nbu1s4pEQIAiA8ZLlc/uRPeTDSkLW2AEn9Kf33kiL67s4Tj+B449h/UA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43018, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSyWXCRA9TVsSAnZWagAAW2gQAIxcNUbhMaXMQsoboaUB\ngCyFZUV1bhOQTs3ZS+UIKwWhJEIGDG1vKa4XRxV4m7gdKNP3RaP45PlhPrMa\nVIPKzptu7cx0Q8EZlEO6GAbwdo8xAf8+gi9+Xpkis3PuzBupsgubRujfNbUw\n+NWZqW0sklXXKdkbdrCD7gZCkAiyMmQlafjKHwQRdCUv4LkeNH0PS1iP/KWW\n3P5xO3NIhj5OQxyoxUWkT+L6N5vNwwTJr2MA8pNZ7vPqmbZCLjQO9Fon3Evu\nGj8Zz5Ag43typV8mvL00cY9h5m1NmW52UxblEV7H515VnNrxan+I3wvEYhLs\nfLqsQTZMU++FbsCpOsZKi6nJaS0P2L0Y/DfXlql6yMdWDBHU2i8Ws/Mk+hoz\nU0bvJRpVAIkcxIoIiXXQ6SUy7QHb/S5kGLgZ3vt914hK8trA9J2BJgeoiReu\nytKdlc+/ZYzKwBGNNZQAq6/Vkn6pHby8D+vVuEMzaxS35j6lmjlAAU5VmKYh\nb2eGJBGDCx8BksMw/xhzytlhm+RNTijYqMLsQL5pEWwEhfrd/Z/Q5/dF/gQZ\naxp93bVBSbHc/1am3LMWMRydyxl88gDDIJB5VomuND/mDAmEkmYRg3V6dACO\nD2RsiGc+f9nxmERoB/w2keFKJ/Vp9XAcvUOKjRgFJsgkFRBAkNVU6OYRp1dx\n1QBc\r\n=r+IM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "engines": {"node": ">= 6"}, "gitHead": "634e5a54f46b2a62d1dc81a170562e6f4e55ad60", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.10.5/node@v8.11.3+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"merge-stream": "^1.0.1", "supports-color": "^6.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"worker-farm": "^1.6.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_24.0.0_1548428694596_0.820475996143643", "host": "s3://npm-registry-packages"}}, "24.2.0-alpha.0": {"name": "jest-worker", "version": "24.2.0-alpha.0", "license": "MIT", "_id": "jest-worker@24.2.0-alpha.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "453b893808267de70dba32403e93022f7f7d4457", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-24.2.0-alpha.0.tgz", "fileCount": 31, "integrity": "sha512-mmWdth5JT+g3/mUkBkk4FQkRIHRYxXejwwSmdcpARfs18H7nnjTQEUEoq8tjjAHO65fQ5gm0lrbbbc2o3WnboA==", "signatures": [{"sig": "MEUCIQComlb/Dqdcfy6Y35vyPdtm5fBxiczpqxrPn5SrMQgZsgIgFT2d0PXAnmoVfkqyQvqX+jIEp86rSFfc+UVgmLRbBSk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60633, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcfovECRA9TVsSAnZWagAAZgcQAJy57dAdMNPOyiMgBLGS\nJYGa9reDirgCtQjPTOVpWGlwyCUK4hT6Zg2ZzrhlGoIatNmRe1lFAqqKZ/1X\nMUL0AxuOfkmwPD/16YHZ5dxE6QJfJBahocOkXGijGe2Kbxac9yewbMZpU2/Q\ny9EdL2DBkhDtgl1foLtyfbgz+9btJdshIOedaKm6OdtBUmR6dj7uIrefxhDg\nZiHhjFCsPxA6lZ/Oa/0iRPpCslzXIIigA0Fo4UHHUHwvTZFwFNbJpYQ8Xde1\naBxbD2WVeTfVQtIuPk477hQvUFXfmTZzX4nBmGa9nLoD6drUOhohUZoqobVY\nhjRnfvbx25RmDB39KfRFc6Jv9y1jII8TS0D2to9gkRm05mqsB5YA05aNBF2E\nTptbNi1bdPL3KaY5mlpNxpt6EEl6Cs+QGlMKB0fzAMkAK6tnH0LfcB8MgpmI\nAH6W+3XvKXszbYZj4d35yIbjCFNM3MJ+52Wer8N5uUGQG8Ykm0UHzrG2+8JV\nxwkBj7O2c/u06DYCM6CutO9DGzQWb/xxwWar4OmkrhMiXTs8YoM4RBbgX0dz\nby3ylxG4d4qFqiB2uemGJzryB49eDImsqTys882SbXy77nL7+wXlYRjv9eAi\nucVr3add0nvH0lwXsbG+dpKXiDy6QVCENdTccnJDr4MVD7b89FpasS2N3+4m\n2eB1\r\n=9hQ1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "800f2f803d01c8ae194d71b251e4965dd70e5bf2", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"@types/node": "*", "merge-stream": "^1.0.1", "supports-color": "^6.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"worker-farm": "^1.6.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_24.2.0-alpha.0_1551797188489_0.5843905950684194", "host": "s3://npm-registry-packages"}}, "24.3.0": {"name": "jest-worker", "version": "24.3.0", "license": "MIT", "_id": "jest-worker@24.3.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2e02eea58f8e43d32e5d82e42aa411dee127dc2d", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-24.3.0.tgz", "fileCount": 31, "integrity": "sha512-gJ5eGnHt73cCpwKGbx0drrVCypgUVINZ5nUAvzD57EUCFc1kzqA0wpPmn4LVWi7mkNeOE36daBbAyWPEmEf+CQ==", "signatures": [{"sig": "MEUCIAGekJSuBdeLZiFjMxYN2hfm0gEPg2Td8LPH78ekB2IlAiEAo35YhOj75MRD3SvUA1rPib/aVOBJcKED9na0amsRW8c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60625, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgRWvCRA9TVsSAnZWagAAgnQP/jBV8lBGLAnEN8mxZoXM\nug8ZtAiyYM6MtJ8QT4v1r9jpxPBDbXPZF4fFjtyx5e8KxAVk0Ay283czIjqh\nQOKFeylZR6vA08+RfposCeyLz9d2I/htvNpXu53tPGENoKWW1rDS6uc+xk3u\nuIXpf/hNQiU3a+OhHYhGQlZvtZq67WO2V9MzAaYjcXFHxk2iaV49gKjhJBli\n/El/js37T83GbquY1XILTNcIv2ywp5y6qcw5pSAGkzleJRofQSyBf3j6RA9o\nqxQMRb0X46A2mroFE9awq3x0VAUxzkvzfulkMMg5Q7QblZzIWqJf4Luk2kz/\nqLIOgW7Po0mK/e786CUjM/3Eqj+VY0HbZWQDTIdtmtSo/NpCdRRgAoLMNI/z\nGrNN229LSGw8/DcjtsqVfEe3Q/GSIIrsscI62gc8awCB+5f7uXGlhR5oEHkg\nkZlV+QeNowlPN3jry646bX4q3ghGw+MEo51kSufPA56jJLbokFcckp7J0ITp\nooKrITf8wWPf5vAT+y+wusaY8F7tLZblhwj/LfCV/XJ5XU+OPoPL52WPmmcr\nlP+Pbdr7DTfSuCKplTzoLopCy6m59tha9TkIj9NumVlQalOE1/f3ZH93+h6J\nZidrGpNBkjUF9KXsc8jVtLRsh6Cwjv5M+xhvoQPGCdDUtQVd73YiB1GLWMkB\ncBIZ\r\n=9B1E\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "3a7a4f3a3f5489ac8e07dcddf76bb949c482ec87", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"@types/node": "*", "merge-stream": "^1.0.1", "supports-color": "^6.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"worker-farm": "^1.6.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_24.3.0_1551963566982_0.5771391109280639", "host": "s3://npm-registry-packages"}}, "24.3.1": {"name": "jest-worker", "version": "24.3.1", "license": "MIT", "_id": "jest-worker@24.3.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c1759dd2b1d5541b09a2e5e1bc3288de6c9d8632", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-24.3.1.tgz", "fileCount": 31, "integrity": "sha512-ZCoAe/iGLzTJvWHrO8fyx3bmEQhpL16SILJmWHKe8joHhyF3z00psF1sCRT54DoHw5GJG0ZpUtGy+ylvwA4haA==", "signatures": [{"sig": "MEYCIQDIvFE1Rkd2XxeYq3Angzi+x51FoWNdBTBTv28foLHaRwIhAIvZ5gYU4FvOcGWewLT39ipfTfxzTk3x3MCv5ZWRzoqd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61451, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgaVTCRA9TVsSAnZWagAAjQgP/3RPAHEkt13tf5XoP3Fc\nh+WZzAK7Bkq3L0JRv5xQqnz7bJDNOg6IG5cT6ZU6QBFnl4UA67XHlL0U1NbV\nI1INj+a3tWjEXjDt0SWTZ2MiNSh0ECfJwzrdJIi6f2Cc41ubo5qp69HbE0Hx\nLlz4Yvpofsn1isBxdxiRLc3OOQNMzS3BGKfLGi2PzKLDgjA52gb0OZ5MLgO9\nNDa5z+piB6iWbcIrSlCe417NFHYnM6kctZsbzicFQg0BKSdh95/70MLgoxi5\nZElDpO10bfmY4ig75DlY9Vyc2ePNT/oUus2yn6TVou6mmHhXfEqVKoYNBir7\nrp2gyzIkdWrkXC+TgJj2Vz5ULreHtXDgarQSxQZFW0wVDOGSEDEcTDfu//GF\n6ocXHlpdMBvJVQs7BNYb66T4Lzc2VNL9vYgsmkQ6DvqbV7nFAceJdfv/vYpd\nGa0xsy+f+y5rpbxx7nO87/gef/wi3ABHSp7fCwCUCuMYAliEd2MnOtsDKtsF\nN+NPR6EbuUCjWBdYHk68Oau2/Y+yeVmXdA1W3LdjBdt6QKoEGFzk3ea8AYes\n66riKgDYCLRwB9SPbP6SdX819hm9+YV0HFW5G4D0hK5xOjxZUTCsUi2UP6Y2\nE4K12NegEhs5acZjVRgjlBcX9Xzjl4SnFtZlf/1R04epc/0bBh2K8TA/fKM0\nCHl3\r\n=v17A\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "65c6e9d5e398711c011078bb72648c77fc8a8cb3", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"@types/node": "*", "merge-stream": "^1.0.1", "supports-color": "^6.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"worker-farm": "^1.6.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_24.3.1_1552000338861_0.8865384131060525", "host": "s3://npm-registry-packages"}}, "24.4.0": {"name": "jest-worker", "version": "24.4.0", "license": "MIT", "_id": "jest-worker@24.4.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fbc452b0120bb5c2a70cdc88fa132b48eeb11dd0", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-24.4.0.tgz", "fileCount": 31, "integrity": "sha512-BH9X/klG9vxwoO99ZBUbZFfV8qO0XNZ5SIiCyYK2zOuJBl6YJVAeNIQjcoOVNu4HGEHeYEKsUWws8kSlSbZ9YQ==", "signatures": [{"sig": "MEQCIEt17P3rN6aE7nrnI6Qg223cfWzjQnuNJWza6ySrM4ZDAiAYqj2n558NAJtnJ5CweKmtdaXvpH66AmuwwESsAhL30Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 64417, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJchndtCRA9TVsSAnZWagAA7aUP/R8enAwcsVSf2fkfMfM8\nZy3bCwKijfphilaVAVPpIqltk7QgCsYcXWxy5tmYGp9Y//7bd5IzGUgyBapL\nvoPh1h4zGw31htw3xBGkRULJsyksDI6EWajSZ/8Rx33VUwb413euAHYRv3+N\n24le5eyC1FwDksXORjBVdR2g/AQF5nfibLG42nSsDlAC9eeUOHlssNs7+TZP\nl0tnqhAkxNDAA/s6QAgatXX1jL051XJY/5LgZWdYTmVnm+Nqne+yuVGHbcQ4\nutRq8MPkW8VQzRvmiZwVabI9ueILaYwmSn59BGiHnAG06c9hKWaY8gpe2sEY\nbYIsxxv8GxpflD6e+iDt/jC0i3JM2Z0kRPbLRAVZTL+uIyMGKcbR/9QkRsj2\nncVOfm0us5oVgWqm0KONslFJSfdFt8sME8X0vNfC2lia3Ht40D6OhIqDXfGD\ndaqSqOOJWEhXN6jZhvvlJkVK9NFBVkaQNoeRxweX2B0v2cW6gkPVqnKnDxWx\nDDgHAhWeeo3p4tfUgCVbYdXHhQtHkGXbdtzSIRFQdDJxa9eOypxmM6cMO2bb\nB5ThSURTUI9bK9D+MERb/fGFkvq22rui+DNtqBDkkqNVKzgAWeVLTir9h1se\neW/8S2Nu6lxwnUrUVNCfgJZ8OhtIVnLy+qHPridRK8OUCCKkhu8388zgbA7s\nhaKa\r\n=CH/n\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "a018000fc162db3cfd0ebf9f23fdb734f05821a6", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"@types/node": "*", "merge-stream": "^1.0.1", "supports-color": "^6.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^4.1.0", "worker-farm": "^1.6.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_24.4.0_1552316269093_0.5011234739501604", "host": "s3://npm-registry-packages"}}, "24.6.0": {"name": "jest-worker", "version": "24.6.0", "license": "MIT", "_id": "jest-worker@24.6.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7f81ceae34b7cde0c9827a6980c35b7cdc0161b3", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-24.6.0.tgz", "fileCount": 32, "integrity": "sha512-jDwgW5W9qGNvpI1tNnvajh0a5IE/PuGLFmHk6aR/BZFz8tSgGw17GsDPXAJ6p91IvYDjOw8GpFbvvZGAK+DPQQ==", "signatures": [{"sig": "MEQCIC8L9g9HOhS4QY+HUYER+Guf6iRT7yBMrwqCoKqA9eEmAiBJfziZtOfsu0HByq8eik9no4mQ/2Ebyh7Uw9UO042SBQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 230834, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcopAJCRA9TVsSAnZWagAAGBIP/REcmzcpRpHPBaIte1J0\n3JprbkPZAznPHST6+IWktng5pZTS5tfQ9C1CGMcZbi173L2xPEYjWt4LChu4\nRTPQgusBq2u0f9V6ykYEAgO0S8nuDS5kKp0IOXpVeBOSq+t1Vm2+cye1XCee\nghw5MQBGAQUe9vOBW3luyrTw6u/DSfXlM6vE3oVWGpuf/0FB5kpyZ3peJ/xp\nyiUCyRzMD3odIw4819n9+uJXA4NxWBudgAM046pTT678vTwxpGVF1Rr9vfdX\n/FPRkGWmXcBGUqTh2R5P2JiMnyD/vG32TPuyWr5f83J0qF5NnmpFDAsPnwaa\nHmgzbeJqgeo2gVSzzAlc2tu5Yh3/Bu/aD2wQQDM47ERyKR57LtYqzI7c1iD9\n+6GPeleMdUh9dVzjgmzMSyKJd+GAAGhWxlT9xcNAmfBkcINFbeP4pn55pKXk\nJMwb55HJmrC5DwBXI2rnZLrEzlFZqBiijO8RdTXCJo1XU3+sIC2uWWHD2k2A\nFKTFw0Y46pKOz+FIlteeiR9bN239dyNoXy6wGxzI6R9xEmPwhbRmIreL6a3G\nWwkaqNlGCe02iPvm/JNatl65pPp7zuMBu3DR1zfJjwIP3IOUolPjcYQ7r7cO\nppqRLS0U7NOKgGc3RYqmraW7F4JNpos+mYte1CTyqhDLkgipRGtKzVa1RAPS\nhMsW\r\n=ZSwi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "04e6a66d2ba8b18bee080bb28547db74a255d2c7", "_npmUser": {"name": "rubennorte", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.13.1/node@v8.11.3+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "8.11.3", "dependencies": {"merge-stream": "^1.0.1", "supports-color": "^6.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^4.1.0", "worker-farm": "^1.6.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_24.6.0_1554157576832_0.5693082911800758", "host": "s3://npm-registry-packages"}}, "24.9.0": {"name": "jest-worker", "version": "24.9.0", "license": "MIT", "_id": "jest-worker@24.9.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5dbfdb5b2d322e98567898238a9697bcce67b3e5", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-24.9.0.tgz", "fileCount": 30, "integrity": "sha512-51PE4haMSXcHohnSMdM42anbvZANYTqMrr52tVKPqqsPJMzoP6FYYDVqahX/HrAoKEKz3uUPzSvKs9A3qR4iVw==", "signatures": [{"sig": "MEUCIC4PVNEnf+/b38mkZDtB/RrDdVRf7bKACS9rDj172bB2AiEAp9eeZevJXtctWqSFqROiYNe/WJKxezELkqcTWwFceaI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65847, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVkVjCRA9TVsSAnZWagAA1MAP/3s0hPlWeboI9qX/kcP2\nVTdp+dM0DYJk7x8jeWu9QZ+HjJidJ079ovtRIUVz9heky0quaT45GQC64qBv\nNUlEBfwXnA6/YbbCzQBzi/9b/gTQo0opd7RYY3JW86cJQAOxsdF86sbmw7Bh\nfjBo6J3G4zaVpoQkC4yjCLU59HOixseX7VRwFeGP+qcer8a5GzU2WwjOO1BE\ntwInNmQ+ELT12Vp5G/vt66btTAhIDMZ3BjC6/OzetthEOy8MpB9RdJjZpbCx\nm8hXO+EqRlnIL+w52njQKzjnVu+G/Bbrl+RdIu0yIcplx1yaIpJUSjQeUpqD\n9Y8oAShIB7OWRcc0ddZWj2Ajd6Zvkvt/97hjS61Xklc68XCGYoiEVXq+jbDf\nnsB2KRmqyR8PFEbGSswu5xdkpCNeISXLrb5xQqDVBAJqCuM1Rwe9G8kWyXAl\n5c+bzjyilfCVcr41cQ0H1BaexytsKVrO5AS9IUuByezopGAxY4i7gKeSGuP4\nvAT/8XH0FVL3DoFLMNWJfhgYe4kRaVtsqoCKW83uHgORcWvYQj0S7yjXhUl2\nc/YlMMXpsBpKde88jlz/pB+Xbe8yELCWhWCUJYLeifHwlAyNy8EPN6Gy+jU2\n2NE/0I4SkY5Fc+ObbMM+niJgp0jVN1YORiwg0xpfqsoaF4keSNCLx5tTOz6l\nXVvK\r\n=RQ37\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 6"}, "gitHead": "9ad0f4bc6b8bdd94989804226c28c9960d9da7d1", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.15.0/node@v11.12.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"merge-stream": "^2.0.0", "supports-color": "^6.1.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^4.1.0", "worker-farm": "^1.6.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_24.9.0_1565934946613_0.29555343853323257", "host": "s3://npm-registry-packages"}}, "25.0.0": {"name": "jest-worker", "version": "25.0.0", "license": "MIT", "_id": "jest-worker@25.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6475262f808c852df3fe570a133e007314f92938", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-25.0.0.tgz", "fileCount": 30, "integrity": "sha512-eFK2iNwjT1v5OHyX8uTNU9K/j6liIH8mRKi7wKJ4BDl6ervAO/lgjOMQ7PjB57LAvBgGsV/9f51zDQ2Rm2dwmw==", "signatures": [{"sig": "MEQCIApnWVV6fq6DJKaVmlhwcIjsdgQm9ss8A9d3+lL0z8ajAiBLKNcPBv/Sg9Lpse3zQhneg0FU55R+kBwjjF40A0YlwQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69136, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXgrBCRA9TVsSAnZWagAAOOgP/0jv81Q+LnClONhZKbWU\njSC+8+dMN+DzDmz5noRmOGukxuJy/PkDCv7K8u55j6VB3XzgF5ut+uJloBKa\nwUKayyKN7ce1kHwPbhID0oNM7mbN26iWAoDfE7zXo2y81ucREQ3zWXE+ZFye\nGE8Xlb7z2vLHYbcsQ/eGjMFbDfyVq/s+9JbgrTcHa4Qe1h9Rf8b1oaGAzPzo\nawg0R5nlDW53NCUg9ElB3NWxDbHiDScOgeJW60dpLBykZZYNEoCgWxFvN/PO\nVwAaedk70n0hyZfTwcu69BvbUDFKKiKspVj7jYEPzIDnm/GNqDTt5+lalrXS\n3Yr9IsCldc7wujCyW5hzMdiD8C7DRi/k5wvUtiwoQ+WxSVX3VZyxflA8IYfx\nG5u+Ee4zKSX3atfJJZ27Db11ZWTKcCzW5ZtgSgDqnMtshFNWB6XpJKXogtFI\n8pAawPOp/MUD6nOVeU0WwrwQtIhE3XoMetPOnp67sTd5ST5MxQWA0QUX2we8\nIW/VU/jFBMx+3KxUHrLwUYkA4tPZP/silGYQFmoG7avyosjQDeIl4wYa38aK\nJPe854T8XKhHXitVbKVOHa8vpvntRb8pr/t5YJvZbt5hJdAPVdYG0LJkdEny\nO91VYFLu8q1D1iyc2jEvdRlUjOzi8eTLcfO3pRUkIT6ODks1Zr781bzdzI6p\n1Tng\r\n=Xvu3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8"}, "gitHead": "ff9269be05fd8316e95232198fce3463bf2f270e", "_npmUser": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.16.4/node@v11.12.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "11.12.0", "dependencies": {"merge-stream": "^2.0.0", "supports-color": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"get-stream": "^5.1.0", "worker-farm": "^1.6.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_25.0.0_1566444224329_0.12563833115291612", "host": "s3://npm-registry-packages"}}, "25.1.0": {"name": "jest-worker", "version": "25.1.0", "license": "MIT", "_id": "jest-worker@25.1.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "75d038bad6fdf58eba0d2ec1835856c497e3907a", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-25.1.0.tgz", "fileCount": 30, "integrity": "sha512-ZHhHtlxOWSxCoNOKHGbiLzXnl42ga9CxDr27H36Qn+15pQZd3R/F24jrmjDelw9j/iHUIWMWs08/u2QN50HHOg==", "signatures": [{"sig": "MEUCIGnhuWD1ALJhMIFMgvGO/liBdLl5c2M/zvYYzs15gOWhAiEAvYpIdLeoaKUPgj70qvx+5Ia4c2FWaDRfcKEO1rRP+ok=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75113, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeJ56ACRA9TVsSAnZWagAAJR4P/0KdBIbHCO5iCt0pzvOL\nWMzpU6ZNbv4UbLcyOtXmHOv//M+HCxZZYS6jQoXo+uTurbh1VJepUx8e/6Wq\nYHjqVhZn3J3pp97gbV7x+KYKt64MOkLlnGnn3lLHxnQCXZIY1ISM+5NAX0Am\n/qptSAgtWkhWhHcMynYh8OY5MXW60CwHZX7CjmjXJObHnNyVMcdD5j3MtTGL\nOtjpG9oHrx85EA7hNvnmgRPAvf/U8HrHeEbV9ZleqBhLoLSrrJ1I56DHEj49\nWTIMNpa7+eofwxLhdmsLvugB14TSE422pLNGzpKWQmel4k8RBHXyC7FGN/5f\n++4d34Qp7jaSSjHZi29CmnnwrF7vzqZXpRJEx7TZyVxMBQWTBeFtXr8CGNmU\nVwn1jsZv/Hy570fksstCx+UpVVEUszsoifkhQzccWoJrUM5D9L4qpHga7xR4\npWHAV73mYBLPEYrSfxEM6+CgcYl+C/gUt0EId6/6ajM5tZx6fg7gj/8UewIs\ny7BpSPnF+7UxH38xZA0wwsfQVjQ3jZn06nO35x7U7maqIv+W/10mQRIocYTP\nHPoD7+2QgCS5pnIOAw243cbMqpbinUPAvxcPDuq141o+FQzRaQOxukQFjKHc\nADzoYRm6aIXZ7H092WKcMds+muCnWKjlglmavRckJWG8fHNV+BDGyVHuuv+L\nz2ro\r\n=xQOX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "170eee11d03b0ed5c60077982fdbc3bafd403638", "_npmUser": {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.20.2/node@v10.16.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "10.16.0", "dependencies": {"merge-stream": "^2.0.0", "supports-color": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^5.1.0", "worker-farm": "^1.6.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_25.1.0_1579654784560_0.4353547814972194", "host": "s3://npm-registry-packages"}}, "25.2.0-alpha.86": {"name": "jest-worker", "version": "25.2.0-alpha.86", "license": "MIT", "_id": "jest-worker@25.2.0-alpha.86", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9ef61eed265922014bb38ec740b6e382bee6ce91", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-25.2.0-alpha.86.tgz", "fileCount": 30, "integrity": "sha512-lKfM/z03USnfAh79pg7eS15bdndAIiPlvXgbJrVjibLIX1SB75SYSqBLK79E/BtNyVtuNIB36Ewyp5O6hIRauQ==", "signatures": [{"sig": "MEUCIQCoIexAFUtv9jxYxcnNL7RdVqcdVxyuelpGiSi+J4OnKAIgf0WDS2iWxdPqhN9xsGKIgtRKFX79yXqpBErrUYwgsuY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75097, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee5HbCRA9TVsSAnZWagAAsn4P/RylwMX6KLBZm/7aeXNX\n/LJKi+4KXPF/fUs4DndkKrOugFKD2vLw1Y1Ko5LdNJsSDCHQhMsUgngdFTPG\nuSKXqDd8BZyWe4Gs6j37KB/VfBndnTmALLy+bkPbTsmAtQvhNkDknfyKu6Vb\ns3uwb9Vdmh6tyHpRBQRnYibXU+oGc0C0iRY28WKa+QWKcuRQOjMKY/5a6LHe\n2UrTMQ53LCNyvdh3U1ZkI65q/MrxTtSZEwU6di0Kvf8DzIkpUcE2Zhgr3Slr\nyHrmUHGkXEgh4JneI+LO1kgkA/tsmqXMYBdVP37PWxfCZhQqbt8tGLg0Ml+r\n3z76gy4jlcCi2iaRMCXkubarCTNd8CgbfB5Ylxx2Tj9J8EeCAT2nleBO4dLH\n3v6prNKdbKa1ELeOqRNPKDJQQGXghrbyQwFZUAhQyYbKRSMlQI1ZUlgs+kZL\noAbhk46eO35XnfhU8u/SMZc4cvASf7iKmxFZpr9NMzdmA3qTAS+C0/RZu3NQ\nhKfWRRLoJ3R/1yQGPs2nBhjPEy6yNRqbYpAtB/kDequuHnEDfH8eyNL0rlBG\nNWBAj+5AMpWjYrPnwL0C7kssxh+m0QIg9DVhYq2ATkAOIfcKcW1WhdiakIl+\nNicKmnh+pYQF+xw0yHeg517WavRRNTq9/FNSot3FoWSKlYKbdCljVHFBnUTB\nQ3wM\r\n=alY+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "cd98198c9397d8b69c55155d7b224d62ef117a90", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"merge-stream": "^2.0.0", "supports-color": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"get-stream": "^5.1.0", "@types/node": "*", "worker-farm": "^1.6.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_25.2.0-alpha.86_1585156571171_0.5735065090299345", "host": "s3://npm-registry-packages"}}, "25.2.0": {"name": "jest-worker", "version": "25.2.0", "license": "MIT", "_id": "jest-worker@25.2.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "m<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2716fbf74fcae7d713778f60187fd1f96fa09d1a", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-25.2.0.tgz", "fileCount": 30, "integrity": "sha512-oGzUBnVnRdb51Aru3XFNa0zOafAIEerqZoQow+Vy8LDDiy12dvSrOeVeO8oNrxCMkGG4JtXqX9IPC93JJiAk+g==", "signatures": [{"sig": "MEYCIQC8BN85YtlZJ9MlOfsyy4rCKvkkVlfh/adzKR0+svZkkAIhAN/0h6fRpE7rR+oRocRj9bhTluh4v8ToxTGHuJgSmSNX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75078, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJee5uiCRA9TVsSAnZWagAAz9sP/2A2XFoZICb+2Hki+Ysr\njl01XNOInAESJ5yf1y+TdifIJphqIGCIHYbWwcqt81sQiSFtCjmyj7C3dw+H\nTnqWJ+Y9YTSah5fKUngzIPCS5saq4/9TrOF32YezpQ15VxpbOQyPBM4Ttb+5\nKcizKyUT+wlGt+SbobamvrETWWJzmu628Ieo4pgtogHr4qfbe3o3lbMFH3eR\nohPVu60PtDCKTHpaR+BYGrlY5eXG5jZF5XVV1RL6ZicPKwBFDQxYdnIX5V9Z\nQN+X8+R6BKcl1VEynaGEhHj8TsaTlTe2qfsTNEB7mga3z3gQXv3za3vlc/JV\n2rGYPF5hCNiVWWVH/RCLroLuUw15UwnAr/f6c6Lyv3YNoXOVcWsjHSMqPCHR\nAlZejo2W/3ptzuwBAHMHvXHbNDsRvQa1yeiag9m/eHW6VZOFmI6zL1jSP1R9\n9FD3Tvv7dSZPJB0PBdQkMPjQS6AvnAFBqzzx2DfEhiao5lmRRqYutIeEJB/A\nXByGQn0HGNWFe+bBPqVxIYMI4aXC3HNWsQiUFCzeg7HBHOmCjXQV/vzw9eHq\nHjZsRZ3h87f5A02jtxdImm8/fROcYhNRbYqgILESLD6gcoKSTXUxsFyf7iG+\nvHKcaqGT0xbuE3Fwf1dJj5JiSAlNzsqFtoVbALJnKd1QRrz/2iV7V083QLR7\ngicT\r\n=h3bB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "9f0339c1c762e39f869f7df63e88470287728b93", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"merge-stream": "^2.0.0", "supports-color": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^5.1.0", "@types/node": "*", "worker-farm": "^1.6.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_25.2.0_1585159074465_0.9310836133836751", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.1": {"name": "jest-worker", "version": "25.2.1-alpha.1", "license": "MIT", "_id": "jest-worker@25.2.1-alpha.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "27f4d6e52f52f4a519a832fb13ecdded20a4266f", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-25.2.1-alpha.1.tgz", "fileCount": 39, "integrity": "sha512-l47r/DNn3hH+NgRlECs42idqALP8l0bquV0WYTLV0NuaPel5FyFDXuP0rogKQynZ9FkPwAR85b1dgradNKxUHw==", "signatures": [{"sig": "MEYCIQCDVyKr+fxDOIuGoCkQnqlptZIeE3ldvWm8olZjxVPNygIhAOXywZmgS2/WUufHUOCljfqzHYY6dhh6yZG70+ArIo0S", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90452, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefF+mCRA9TVsSAnZWagAAmo0P/3TpjC4vos02PPG94HfJ\nyuLdwMsq/53KjNkaacIUYfh29Bujx+RbO+1c6b4+7zZqsMcvpGciQLy5gRZE\nJttYfYDVv9Wusb3ZkxysC4NcMzRL4uQ6XVtG3MO88S8XmigGjb7CkLHY2LpZ\n1yyCopjz3nSeI5RV3NhK7J5JrwYewt2RGMRFOxqpfKhXOy8X6afvQhU+xPSV\nrwzxpCi6B4D1iKTWISU6ScdXBblRcBQV/px151SUXXkCSa6syK6HnYQ4Joia\n8TxGBlbcikdTwuoD4kjbNGw9iIOEm6Tl9rgVh5qKhr+9EU0caSaXCr/9OVA8\nSJGBAbdrs69PvftNhkWaLph2MDgMFfTD7jMbD45xt1iscLjrLh3s0fN5fDyN\nMCkCmXIEFQV8VAWCB7Mh0fV5lV5gwZGC4KGQW5WSeM2Z7iLyPCkodrpKv4H6\nhvRshwtb3RdxGvcliszdGmZCoXcMakQSI+xdaBsyFDJIK2Lg8LT3P9rzUbn1\njNTiyo6IKbz90nXIEwMBVfTj0cBEiCC31AJDdYbC0yqa2JTmfTzeex93ZTFb\nhVHoKinvFAQqZkseIcVzFHmBQe5E8LZK08Y+2HeOqRalFx5l188bopn0oVcn\nVV9jKkDBdicCMcRmrc3GZ3fJPq+aGy2SDGelkKTwgzt3GcIc5quiainIYekS\nsUQt\r\n=dGz/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "5cc2ccdacb1b2433581222252e43cb5a1f6861a9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"merge-stream": "^2.0.0", "supports-color": "^7.0.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"*": ["ts3.4/*"]}}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"get-stream": "^5.1.0", "@types/node": "*", "worker-farm": "^1.6.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_25.2.1-alpha.1_1585209253670_0.15103644471364586", "host": "s3://npm-registry-packages"}}, "25.2.1-alpha.2": {"name": "jest-worker", "version": "25.2.1-alpha.2", "license": "MIT", "_id": "jest-worker@25.2.1-alpha.2", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5963acc06c2807ce2b954731d76e83baa8bb5f9b", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-25.2.1-alpha.2.tgz", "fileCount": 57, "integrity": "sha512-Zo4R4/yR14pzvFDvq8/C1Bs8mu3v42NHgD3wKgDbD7Hag5RiITx+u4+dyTnHgz7Hhyg2jwIwfgiUmC0U4//mMg==", "signatures": [{"sig": "MEUCIAg+yIvp7Oo3+LeEhJF25C0sX3kqvFHw3Nf/PdX+lJAXAiEA9vhkA51+2QCT7xw3Oth/ywkrSFJXCiaUi2QRwpNaaaA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 110860, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefGNuCRA9TVsSAnZWagAA1Q8QAKJnu/vvgtZ6TpEXN70X\no63rE3OCVzmV3xMhgpZNOIl47Db+r2PP0CCiW4Wqk9lmXlWJb4cbSIQmDDH1\nhwE9Bmh2ucjf2hqStbkkXD3kSyS8pgtQuQSHLw0oanNpii0AKOQPvVLGI0SF\nbBi6ADpvDdX06w31w00365uNj+W2raLHI/sc5cwZC/vcpURY7wJvkAtENRXx\nuNHi6Rc84lPfhOJVXsLCRT0uPPyBGR6gHLgMpYawn/lRc5Uceud7DKeZ8GcJ\nyJ4wluL5gwOUtU+PsoSem+htjbPbjmqVpL6b/s7lp7cwFUug6JxgvzzzOcio\n1DgQjNtK0yOJufJr7DE8bKIFPAcfMTbZU2hE9kDYXyxIry9dw9cLgQdcR0wp\n3YNRfIikF4QOTJAzvIX8bQqAsFvEJhkRkWESBqYfg8gocxEwt0CORUIQIzgy\nJqXKJ5Z1k07HbcWIGcYvzBVSG/EqqBRLj0lJlow37MYtg9HQdxwrJhEiZPQy\n3oKp8jSzZKd3xi+iOPjzEXDKQPjWeA9FIfU2WbaK0qp+psGrCgaHRZsuCj0s\ngRLhO5USlCH3HRkrNWye0GatVlagJfj6HRh75ltTaTD6/w5TcVCJYdk8uPpA\nUr0z76OMB+Hkix0U22DLV1gCJhteBAZ/Fz5hjtgcOVnP6DqL9EI/tKfy/rni\ne8j5\r\n=rPBh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "79b7ab67c63d3708f9689e25fbc0e8b0094bd019", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"merge-stream": "^2.0.0", "supports-color": "^7.0.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"get-stream": "^5.1.0", "@types/node": "*", "worker-farm": "^1.6.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_25.2.1-alpha.2_1585210221782_0.32600001909308607", "host": "s3://npm-registry-packages"}}, "25.2.1": {"name": "jest-worker", "version": "25.2.1", "license": "MIT", "_id": "jest-worker@25.2.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "209617015c768652646aa33a7828cc2ab472a18a", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-25.2.1.tgz", "fileCount": 39, "integrity": "sha512-IHnpekk8H/hCUbBlfeaPZzU6v75bqwJp3n4dUrQuQOAgOneI4tx3jV2o8pvlXnDfcRsfkFIUD//HWXpCmR+evQ==", "signatures": [{"sig": "MEYCIQDUUqX+59LgVBEfns4i6cSm9sj+KQ6F5YbyrlnM7ANFnAIhAI6Ek0zvPcY7zclkJ1UwMtOLkAPFRr+ae+/3rW87mZqk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87297, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJefG9QCRA9TVsSAnZWagAAabkP/jxg2RPh61a6VTOL6YrU\nvGINyKzK0aZQP9eVDVMcb8x8NQTPJTRIj8cNFH/soBngg090aZj13gQ3BFHO\n4h1SVik273ZVfKcK/PEhiDsV/w05/NNmL9VW+0djklX9Kmvn12OdOyGi0VHp\nAq63WP72w1yThBz9/CekCSL21NWjtP2w8FyusjATZ4r6+mDCi/js8Um1QHwe\nL9DUUcIKr+TLZjguvGX8006s0G8RajjxZmfRlEU0dDLxhnUm2Q4Nu/Eggfmc\nEghLOr75F6m2ucqpD05rrpiEOvzLD8UfOUJSZ1FK2q/fYVUeQLp2SIbh1qGz\nPFdL9ehDObuiWls78+4nwnl7HmEOJb+eGqMtonh+ZipremMAgOoFZg2ooqwF\nRD4We0d1J7C7nl66u+Td84IPOEItdAO+eLZJ72jK/7sBGRFFm1uLHSqmbqfe\nGZmkSBuzkZihHUqJL7WeB+glw+lgj52zEE9/Suv0WHyMkZKyKw5bnc2r4C7B\nuMc+4Tai22VUc3iuIzVEGo1HCFfyW7f0CBmvya1cBL95dw548jqIRWGuQoJE\njkpuxuTn9uBqH/AR4YoOtuZw7w0lgUBYyxfTxtpcJAJP45fzvmdV2LIsYKpo\nHzuwRGKPrVgPQvOdCodbhur08WrBQT4YLFPHTnBGY/fj7MSJz35Xj5o+LJm0\nVfaf\r\n=tDYj\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "a679390828b6c30aeaa547d8c4dc9aed6531e357", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"merge-stream": "^2.0.0", "supports-color": "^7.0.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^5.1.0", "@types/node": "*", "worker-farm": "^1.6.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_25.2.1_1585213264600_0.5747888296400028", "host": "s3://npm-registry-packages"}}, "25.2.6": {"name": "jest-worker", "version": "25.2.6", "license": "MIT", "_id": "jest-worker@25.2.6", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d1292625326794ce187c38f51109faced3846c58", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-25.2.6.tgz", "fileCount": 39, "integrity": "sha512-FJn9XDUSxcOR4cwDzRfL1z56rUofNTFs539FGASpd50RHdb6EVkhxQqktodW2mI49l+W3H+tFJDotCHUQF6dmA==", "signatures": [{"sig": "MEYCIQDSyNB8M9RV8aIP90Hfr8GjyCHlQ2TITPtcpJQ9n4FAgAIhAJ5ctpJ6yAf+t2K8W1aUtN/CoGMjpey7hPZwv47Cas3g", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87297, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJehb57CRA9TVsSAnZWagAApRkP/2u7XHv5N2kPO+iKG8Mr\ngUPzlILWxuRJ5H3Siq3z4+ms127yUbS5WOZ6Dmk4RSGEZvW1WPu8v4+Kd26N\nmS8Qa3ZDzMiJhsH9k7LQfchqQDS5yIK5AuTFqOOFy3Gj9TMnX5LL4mEeIR5I\nkRk0Gwl9K13oUKbL1p6zi/SMM0ebnni4X1a+I8PqzC+OS4/ny6m4a/Adb9bD\nQ9CpNntsDqA2lmrdZ8r6wMAYzgE4BlBWf4IPOXgyNDLkF7/6EnKTrQTkdIvc\nEI3c9XsvH38XCCG9BvqgzwSk7Wo1wR18CI9CWEIaH4ec3mtS0K5AIvi4SQxS\nuzEfz5SdJ3FLyVzqVrcBuri6s5MIEP9yP68Sht7q9gf+mHUecd5o/NYUeQZE\nDpCpGnVTa5JsenOj9lreq9XH0gcMT8sxrvaKAFuE39Pv+iyQGApIta7CYuhy\ndTBzIe5xvcYz0W3dYCdQU7O+a6d+BHeYJt8HUfn5XkFtYZepA4XwbhU2r4gL\nNfvyvJNs9wpO8wDvlKJa/xTJzaA0zendFbJBNT3KODClr97ME5aXKOdYFbFU\nqPpZyv4VqvmjDEH2p7aHO6wPztEJXlKNlZjC1Ii35rB5mhFV6gUdWIW2DNtp\npN4msI2V3tsS53AZmfUGDmIZ1r0tSILdtEZdRBT11tOg08TLt4YMyVhdoOVY\nZG+G\r\n=Ldq3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "43207b743df164e9e58bd483dd9167b9084da18b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.20.2/node@v12.14.1+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "12.14.1", "dependencies": {"merge-stream": "^2.0.0", "supports-color": "^7.0.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^5.1.0", "@types/node": "*", "worker-farm": "^1.6.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_25.2.6_1585823355244_0.4700168075827742", "host": "s3://npm-registry-packages"}}, "25.4.0": {"name": "jest-worker", "version": "25.4.0", "license": "MIT", "_id": "jest-worker@25.4.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ee0e2ceee5a36ecddf5172d6d7e0ab00df157384", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-25.4.0.tgz", "fileCount": 30, "integrity": "sha512-ghAs/1FtfYpMmYQ0AHqxV62XPvKdUDIBBApMZfly+E9JEmYh2K45G0R5dWxx986RN12pRCxsViwQVtGl+N4whw==", "signatures": [{"sig": "MEUCIQCr0sxlMXnXfgn/jyOn8KuaE1ogm8ARKH4RGVLL1ngpowIgPQXvUoMcLBTYusP1UtpcLfxSoIdGwf20sR5vUvuQr6g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73311, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJenMecCRA9TVsSAnZWagAArm4QAIwO6eZbdb6N/+XFqnW0\nzbJoPVyuTcRzACdO7YI2NV1fFZMd6KK9JpLu7u5zuHxfcK+sqF9bRAz8gf3X\ngC6H5YH4ME4hTjkdHojVLRMK3CFdPz535k30xUN4t9J6m1N6Ee43YlgfyXIr\no0WO1vg9GBMwKM+8LUk66nrRA7oM3jpiNb1Z7V3yYFfWCwA6ra4VxnPZo80R\nTypc7qqaBxZVNepgD22Ko13DtKBq5UIhM2/jRshxeTfp/0NM3CpMyj/RYImT\ndjBTPnT80lGkWDbGekfx+erGm93zAcjPlOzPFkdfWN/+wKWRcy6kSnb9CQyh\n0EOQ0d/Dkpmh23GZQYnhBOjDiZkKMcev1ToNtDXNTG8SHoPyABedjIDqN5Us\nLo7DD1EzvDot2M42hFt5GjLwo8Rlei0d7kWe5bEaRJbXKmHdwZc11OAMQbj1\nVgYUQLvB9dqEY2ksYtVORqzx5YHbxXm57HXD/BD1kQI2qruXqmweVDWi4lRa\nzaxkwUhbaFMoAY794HBs31icmgd3i+mrM43gefoR08+r94B+p5GY+fNg8i8D\neHWmESOCKUP1cUtu6XsvOY3MmLZGLkdMSXZffLY53IlVk13ubVgCcenY74pb\nwyXhbwjuCqZyP7UyRYxbo9nEviuimpYXKiwZVxxvtwpmHbzdBfWtquE2nsjk\nqRKn\r\n=auHu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "5b129d714cadb818be28afbe313cbeae8fbb1dde", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"merge-stream": "^2.0.0", "supports-color": "^7.0.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^5.1.0", "@types/node": "*", "worker-farm": "^1.6.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_25.4.0_1587333019575_0.5823781031819089", "host": "s3://npm-registry-packages"}}, "25.5.0": {"name": "jest-worker", "version": "25.5.0", "license": "MIT", "_id": "jest-worker@25.5.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2611d071b79cea0f43ee57a3d118593ac1547db1", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-25.5.0.tgz", "fileCount": 30, "integrity": "sha512-/dsSmUkIy5EBGfv/IjjqmFxrNAUpBERfGs1oHROyD7yxjG/w+t0GOJDX8O1k32ySmd7+a5IhnJU2qQFcJ4n1vw==", "signatures": [{"sig": "MEQCIDsd2eQjcrqyu93dLPpOnZsaayxeEoDvzqbfoQgspUKWAiA7Ls2V0IN6J6IvPNPp5/0vt0E9lm9LMbEiOjuMS6wI9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 73317, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqIfJCRA9TVsSAnZWagAA8VgQAIxxliFApnBraKSbD/9F\nCHbMPlqMc4T5dEg/CYGuHiJSGdFat564jWuWA0UCgbuPaKtR1YuQ4jqpx9NF\n53JRqZkGpx9XRLIEpvV4Nsd94y+yMFQZAkaKvef0pQMSexsSK+JlXh1EYv5W\nLdWbEnuKJaTMO4b7U07OeQgsZoM4toIShiwBT1H4zwwZ91i53zi/HQtrEEmn\nYGF6twjFafnN0sJH7IB/YwjVztkTcM7Yp2hlF2HFVof2ZCE/bhQ5RryO9xMD\n1+MDQPE5wPE4DMbTHE7zdRw85HQOF61sHZ0ecILYJOFvlHssFQ/HCWrzurxN\n5l/7SLcolOlkoOQ0SFo6wjcXs73iEXQVxUz3SyX/gBgkvf1UA86kdtp6fUoj\nOFh4UES4OHQrnzmEp0ZgA/xZATyhiyQ+OAF0QLzVcrAqB7bGnG8OCnwiapdn\na/C4s0xkFwmrNjtegFREHZUkodmTM57rFg095t0ZcES5upOXBfuSUg4ogWG2\nl/waHtzPhb9SpscYkhHvEsfKkD1WyW0emEe9ci8eR+/18BCbvPWnxN1lca2u\nFhnI2JWktY6xaR+14kfyvEvgXc/GuWSIwZ4KKWZxTJT2Zp56QzeBSK6RyerZ\nAgBBKTzzVFBTUE+S5wMobQ6ISnGFlOpm2xCtq5UHqGGnSwf5ilxivs8rSXEy\nA7g8\r\n=jDyO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 8.3"}, "gitHead": "ddd73d18adfb982b9b0d94bad7d41c9f78567ca7", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.20.2/node@v12.16.1+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "12.16.1", "dependencies": {"merge-stream": "^2.0.0", "supports-color": "^7.0.0"}, "publishConfig": {"access": "public"}, "typesVersions": {"<3.8": {"build/*": ["build/ts3.4/*"]}}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^5.1.0", "@types/node": "*", "worker-farm": "^1.6.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_25.5.0_1588103113084_0.2436621066397966", "host": "s3://npm-registry-packages"}}, "26.0.0-alpha.0": {"name": "jest-worker", "version": "26.0.0-alpha.0", "license": "MIT", "_id": "jest-worker@26.0.0-alpha.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b8d9bd339b4871f2d3bc2f86451e3b51c474b6c0", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-26.0.0-alpha.0.tgz", "fileCount": 21, "integrity": "sha512-MRbOyBjHSyshdDO6PWlGrmNan6XHjQPOfCr8S5uSjfKJS75MbbqTN1s24dVnKY88SlFJJAxs0gLGL48cygIMdQ==", "signatures": [{"sig": "MEUCIBEb+YFg8HyqbJamfcqaGINY62e/L5qyBip1AwFq4x6IAiEA3YMmEj3N+yicAJUWqot5pZdn1HqFrR4Tvf5Ds6QIL3s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61484, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJerWPFCRA9TVsSAnZWagAAEYYP/Rbrtp25ItkZdr2Fpmr9\no13G6sOLaDbkEjnhJbl3O9oBCORBHs8dcqIVP5FJdDXUMtFE+4zkwC7cPefY\nY2Ltdlox8zGhNJO7UxteV1Zh7hlweJEoLaFHPqilpgMNB3uUcXd3EO0+Vo2y\nPrJafHj4tm/ba5onjs3mrbNQBa6YKC99+ab9Qmnqcs/lgie9QcKw1El5+coa\nZu8wi1cLpxChGvK8i5YdideA/3scUq6v+tzweatgIQYtUe2rDXhmns161JNQ\nATiutwxlYJUkVm/jaw/+KUmBh4gS1yarYxpH0sAKWLcC3N9YSyVqyb4t7fQL\njpYReaHNgVVljmPU0e26hS3gtw3pLFoNIKwSPv+2i0i/lje4/ivoJNP1BscT\nyosZH6iZcOCBWnRnETSXS/i4xHF7vvFlxSUUBI+ooiAxMT4VkigCzdCPhFXl\nqmUFYoeUunWfyPhEWpZfNKOnIXS6jZE3LS+lmgNCmYP2zlxkNmupCbExNmPo\n1fdzPK9/5hOCM6GwvIuhaarPe9l/0q0ZyZqZi3RCypctUgL6VFrzER/5fBai\nWZ1+CP8lnptstCWnASwAUPJqoSwnwhFMlBcWNC2VXh9pzhwfvvET7DI6dGcR\nERNGrbSQZ5LTT199nr+0MjNdJhJhbTq2llO16PNEkPt0h9FjWq+uLK9y8wIJ\n+AFa\r\n=XMCg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "ba962e7e9669a4a2f723c2536c97462c8ddfff2d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"merge-stream": "^2.0.0", "supports-color": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^5.1.0", "@types/node": "*", "worker-farm": "^1.6.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_26.0.0-alpha.0_1588421571522_0.4057372207739385", "host": "s3://npm-registry-packages"}}, "26.0.0": {"name": "jest-worker", "version": "26.0.0", "license": "MIT", "_id": "jest-worker@26.0.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4920c7714f0a96c6412464718d0c58a3df3fb066", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-26.0.0.tgz", "fileCount": 21, "integrity": "sha512-pPaYa2+JnwmiZjK9x7p9BoZht+47ecFCDFA/CJxspHzeDvQcfVBLWzCiWyo+EGrSiQMWZtCFo9iSvMZnAAo8vw==", "signatures": [{"sig": "MEYCIQDNQsHhRLE20W5Bgc+z6pc5+xSdrC/WnnVO11dsJIJ5bQIhAIiSfjvwcjmLxg3EnlCtPf0HED7hH3qjhtVWEFHL1tYk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJesFZ5CRA9TVsSAnZWagAA/78QAIO/54z8j6jzHwC/+1bL\nUjG9EwRuOaLVtzPAzV/RjLVb4z0Abx+5/rmK+9FmqBVoT96wDOu3+TDxGjc3\n/weDQxLm8w0Zhz7m8uCtjGLRDedxLm2WrvAnzOMOdinFySqdrnYbvk2Gc1ux\nLHz/QYzB/YSv1K6EYZ1EFPYX0sZmfC8SlgBlKr76FQ0bDATJQ3DQT5MGlrWm\nOZ3t9SBw9cMFDWbAs5LCdsdM1Zp+R0pJ34gi3QHWCSf+Re6vsIM/td4NeMMF\nqyX0g5/13kn/5bV5vuWZM0W4OeO55isvF8rf8p3Z4//xHWWCF4VVkYLaUUny\nMjFG7SpQMqeyDKrr6mwVxLwP0sxA0CunhzB84QT+qwahdjfzKxQMmLeNGKs5\npkL9GtZf7Z0VgqEmnORz4aR/al4114H62oZlA5iVLtnuqDdzArQmrJkiWa28\nXcWZ91iI6hlXhPinPe6FiNkah9mqSCJi6KX7ji8rtG+Zd2FW3zKtPcvKUtTt\nHT8s3KmkIqQf4wNzBwm4wYoyw0MnflAU1KoZ5TQYqq3W5QhOYsc2X6Wi8f8w\ntdRjnjOk07M5sDW/DOyA+nM3fuJa8rNlr6XC/UI1FDsOzf2yQppg+W9vOFGR\nj5IToASleT894kqrfE2Ol+5eVOm9FgHM+wfm2E6dghnKk1qlshzDcx6MKG55\n5L69\r\n=b8Ig\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "343532a21f640ac2709c4076eef57e52279542e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.20.2/node@v12.16.3+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "12.16.3", "dependencies": {"merge-stream": "^2.0.0", "supports-color": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^5.1.0", "@types/node": "*", "worker-farm": "^1.6.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_26.0.0_1588614777205_0.9850545113958091", "host": "s3://npm-registry-packages"}}, "26.1.0": {"name": "jest-worker", "version": "26.1.0", "license": "MIT", "_id": "jest-worker@26.1.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "65d5641af74e08ccd561c240e7db61284f82f33d", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-26.1.0.tgz", "fileCount": 21, "integrity": "sha512-Z9P5pZ6UC+kakMbNJn+tA2RdVdNX5WH1x+5UCBZ9MxIK24pjYtFt96fK+UwBTrjLYm232g1xz0L3eTh51OW+yQ==", "signatures": [{"sig": "MEYCIQDPlEtqpJrV/3pLxZh5XQl5zH/IybdNKgxQP4pWUlnESQIhAIUJtTkhGmBc0NV7rd4rziFzeRC5H34HC/8529As9mhd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61530, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8hx4CRA9TVsSAnZWagAAmTgP/RoJ9xe4lZ2L+/s7PlVE\nEXA9Uo3AQQv6wmBvkPK0Iz/GiLmuX9yn1QEcZko5DMnVrXIHbBMxoD3nXsbu\nnWMdbZRNSzHoQYX1PXACuM2k7187klWYJE5Fb7uujOz0CYUgxgrdcs4cEESI\nl1ksAxKogmhDnI73LuNu6+60EHaF5B5dc6HT+5b0H2lAKt20Of/vxx77P4Ao\nJNmxr4Q1a/AnauuUkRI9Jud722bIc5AGUCznZk9PLbhVTPxoNE50m7Uj9dL+\nxCzAOWji4cqrs0m39Q54IR70Pql1vpHFqRZmZ52vubyR+S4j5ZZ/iBbgQ5wP\nGigZsOUHfM4EIsp8R4+P4PZB4o3JETacOcLd6EnkiV6zppt0+O+ErmuVCWpf\n1brmLbCMSW4ssk3hQtb1QrQlCrPN+YlYx7ChgbgoJ4LbO4sKCV42R5hOTg3W\n26skLwZaCHCswkGgNB5PpIIVVxJMAvpK7oML9UsGiSuE9QEuIPZF1T+Qd2cQ\nwOAOA688Z+YxKDE1DLwknrkJY7ly86lKPGZjzELPC4WL/nXbrq1gnOugeH5J\n5CMGbSymYjr808gtSjdHivdxr9TKkKqjee0BHTqunjchAsg5dTx6DPsDEmJO\nuqrhXG0GKqU0/F6esOcY0QNeMyZl2aLBkUXVphX/Ei+lUT+tucprxfJKBYVx\nl1LL\r\n=M3bH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "817d8b6aca845dd4fcfd7f8316293e69f3a116c5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.20.2/node@v12.18.1+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"merge-stream": "^2.0.0", "supports-color": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^5.1.0", "@types/node": "*", "worker-farm": "^1.6.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_26.1.0_1592925303718_0.4316340426929288", "host": "s3://npm-registry-packages"}}, "26.2.0": {"name": "jest-worker", "version": "26.2.0", "license": "MIT", "_id": "jest-worker@26.2.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9b25ad542e3b812e388796efae214a0ee79f4a35", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-26.2.0.tgz", "fileCount": 23, "integrity": "sha512-c5<PERSON>zzcepylKWHBQYq6ASy96n1wSEjruid8H2eo5kWyXjJgAxxPU7bVeG9/kXqyD6lcT5R/iSW59pbeDChbCULw==", "signatures": [{"sig": "MEUCICRixFNSQn7sgtwdRLIF4vlAw6o7zC4AWM1OBtx9t+QRAiEA4OCTH02VrLm9VOY7Q2t/bVGwHjaQCzYU1Yqu5tNO6CM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66641, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIpzZCRA9TVsSAnZWagAAO08QAKPeYjtYTIck/GGBv6vI\ng5RDGRiwuWj5r8bBewNESKqHeguIzjhvtW+42gDOMXaCaj53wzPFj+90wrw5\nyG4spEu1HUgBWBYkj3Kz2sekSKpDjCfX1S9mRQ7cASp05RDQFGJfuhqRt7iS\nXigtO4k7E5FqEl4WgBqZzHn+9/YUXAlTg0SlpWMhmSYmTEqxWP17RYbay3Ah\ncaU89wwmYuBgDAvvpZWmg1A3s4fodZeWvhFO+gUvN3Hk9HEufnRZCB/Opz+T\nEaEwzx81+KsXrRs9zrtKZCXa6065WxsnIPcEua+UU52KXj34hz3dKS4y+7jX\nvp8HgUenaMWN7DEY/MRWPJ7Zr4lIPAkP7RZSH4yGdEPxeqYuxDIUV8phrH5g\nzhzu2yMX6sHXhXOVEeUMKRToio05Ko/f55bbTF3G6hlyAhP/u0vE1nQBi6kl\neyqlumswjGfpXWIk7XW23B1zLDLTfnOCTs2BRNyJOuRloHZHmn4Y2TObtoTN\nA5hKaZcSh8vCc3dUEZWUqQUY3DLUqMnpZow43uXvGtSMlgVBcjFnljACDB5B\ndcbvbELyUDFugV1w6oL+UzizgBlndxcc1vvDrZ0UxhLZ/b/Z9fzpx28veTrQ\nE03Ly4iDUKihCruKoSPxO8H49+XR01PpbxVJftsoEpWikZLCiQmB8xadHB47\nlo2+\r\n=QS3e\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "4a716811a309dae135b780a87dc1647b285800eb", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.20.2/node@v12.18.1+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^5.1.0", "worker-farm": "^1.6.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_26.2.0_1596103896901_0.5535750396303447", "host": "s3://npm-registry-packages"}}, "26.2.1": {"name": "jest-worker", "version": "26.2.1", "license": "MIT", "_id": "jest-worker@26.2.1", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5d630ab93f666b53f911615bc13e662b382bd513", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-26.2.1.tgz", "fileCount": 23, "integrity": "sha512-+XcGMMJDTeEGncRb5M5Zq9P7K4sQ1sirhjdOxsN1462h6lFo9w59bl2LVQmdGEEeU3m+maZCkS2Tcc9SfCHO4A==", "signatures": [{"sig": "MEQCICFcYj7YaODcaaYC/uzNwClkKV0PEg5u3n5CBmjSFXP9AiAbQttNJV3V+bfduGtaSbl0hX3jfklcRbiQdjAG4756AA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66867, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIrB4CRA9TVsSAnZWagAATuYQAJZo3ZcxEH/MczCSqBXq\nQPpIZoto3D/LPue+w+v4F6MOOpGORUcR7e2uZlpGroDjDfiD27qjrSalHs2r\n5i1m5pebMVDYcywlYyWh8m2PB6rTFQp8Fohi/eAsmrp54Wq3Jd5ULZuIjMI9\nNR4osNA1fsJ+WwzXFaGnw8G/U/uBnABQrCO5wCkfB938XZ3bmp/bglpyEOGZ\ngHA8Y7BRW/NLNNtibkvo4THVZDOqBQisRzzyZdX3LAFVO2k/KNoCsLUQSi0N\nrLnzvS3U+wn8JPTmm5PVIXCj+Is5c8xNZGuYAUIokZQv0MMut4DOr4TefMRY\nWlRWQShl5Zecd3EHKSjEfvqBaPh+YlaWBhGLSG75Jc6nm9mn/njtwyDJK0jb\n5VUSZem/CQyNpJojPXERV7BnqLEjG1t767rEJHma1+VzVLbU1DmNA6yZ8y30\nZ9XxQkbkBTj5Q0/JELn7zcwv2lIOhIfueUInjC7BCCBthtdIB7nW6S0n/6vS\niCmz7dX+mr7/oPIy5fsjwGIuW8zNjWw/IhLf7tqB1N2/0MrJmQCQDTcqOUrW\n0ypyz83V+5TmjUP/92vGyNcaz8o6K2t++3ovScmMxYRs1+vtVgvVt7B/81lY\nZM/7F8MHjRWgriXsLy2MpdZRsoel8vsYlScrfnz6VkFcED47tLF5ywNTs0m3\nOzhP\r\n=bat4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.14.2"}, "gitHead": "da61421faee6fdbf2a2b355b10d4e6eb1a842233", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.20.2/node@v12.18.1+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^5.1.0", "worker-farm": "^1.6.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_26.2.1_1596108919698_0.6228837811902372", "host": "s3://npm-registry-packages"}}, "26.3.0": {"name": "jest-worker", "version": "26.3.0", "license": "MIT", "_id": "jest-worker@26.3.0", "maintainers": [{"name": "cpojer", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7c8a97e4f4364b4f05ed8bca8ca0c24de091871f", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-26.3.0.tgz", "fileCount": 23, "integrity": "sha512-Vmpn2F6IASefL+DVBhPzI2J9/GJUsqzomdeN+P+dK8/jKxbh8R3BtFnx3FIta7wYlPU62cpJMJQo4kuOowcMnw==", "signatures": [{"sig": "MEYCIQCkArp//6yttQTk2snKpGByRD671z50jiMuXJh0Qg32UwIhAM7/qFDH3NUoQrYxTj/Pwly6wKqN3rWmHw6e9CzJPB6d", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66859, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfMTAeCRA9TVsSAnZWagAAgqoP/i6CCeiJAVymWmf6C89U\nPq/mZpK++l4sWIW3zTAEKxTlbyVNGmdCcBrT8g/rCkk84+H/ddr+UHBC5iX2\ncoUBQBkGSKDcp7wcFdj9FE8JdscZR5He1/cqlre0Sa33gLsjz/B4n7S6AyYD\nPigUCGIIF57D1k5IDpn82MKWfLF2THI3u4d7r5vHrQbDaguwYMLChv114Fzg\nWrabvybw81dg9SK2tNUxIgdZUz5KOHfPSbF2hvjTq1w4Edpqzb2oNrJ7PovJ\n0SkSIuuCiouGxQ0mdKLjzzXp9H4n1HXTYUbfIzsj/dNDOhnbCz2e4DxSHmOs\nF9mAM92+jz+ctFZkP+DVxULuz29WKzBpxzMEQr50zjgQ7IAUhBMAk8Zhb5Qr\nfdNXL4Xlz6qpBtmHBNSlgBqn9TUE6oQIw6kk7bPeOUs6HeEiK7GY+EVf/zcU\nioIGyrM9+nntOFT2ijTvxiF/bxgXQ4CPBvx6mF7fXCZnHWS4jQzUp4ne2dMg\n5P2WS9c82UK2DX4xzMueOgKXMETAlfvuSotkstWGmj1UB1maw/1r+0QEn6Ni\n1QtUu9q20nlwUV1ST+Bw43s/TjkeDCb62vUU0TPbo5hB42hP+17h2G3UrY7x\ndhZ9PvpWwxqfn6oZM6IpL3fTPnldfjRuQJTYKcTvbJUuDsEeixWjqFem4BB+\nNoaE\r\n=gIYV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.13.0"}, "gitHead": "3a7e06fe855515a848241bb06a6f6e117847443d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^5.1.0", "worker-farm": "^1.6.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_26.3.0_1597059101699_0.8255749392463096", "host": "s3://npm-registry-packages"}}, "26.5.0": {"name": "jest-worker", "version": "26.5.0", "license": "MIT", "_id": "jest-worker@26.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "87deee86dbbc5f98d9919e0dadf2c40e3152fa30", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-26.5.0.tgz", "fileCount": 23, "integrity": "sha512-kTw66Dn4ZX7WpjZ7T/SUDgRhapFRKWmisVAF0Rv4Fu8SLFD7eLbqpLvbxVqYhSgaWa7I+bW7pHnbyfNsH6stug==", "signatures": [{"sig": "MEUCIHnfbvOX68yxhhTcOIBGTC1F4WhKqTigqfp9YTKHUW1lAiEA9eyYhyUpnKXhrtsY6CfuRZESJl+wxstIT7Kg0iiGcms=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66949, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfeucnCRA9TVsSAnZWagAAlrkQAJZGPoJ18c1/dLY+EBCR\nKmtAnnGX7gljs5t/YrRHpz/+PURQonCD8rACt2g6E+/UJMqh0GiMq7EtsV5M\nV4z+QcoHFPMjs8SRwHl19Prk+uApBZWaLKR3zrrVqugS2B5QDYfOmz333ePW\nSSWnyLW3X5ZLZdUT28vOZpM9VJykdc9AHPXw1cq8Ob/oJgW0GVcE0/8ySe78\nipDoI/xP/tLcJvvei6mThkt5JBIYyj01f5UbN3A/o2+dIvqmLJHivNt/eVRM\ndg6wCAosBUAL/UlAeVQBvEq9XfGf/4IVAqNKY2F7dPiR3DTrdEOstenOhYrQ\nP9JvljpvFKaR5REUqL8x0/i6PcjWnOwC/yGmGOiJ2Bl5TGoEJ7vvDR/cAKhz\nHvNKb5e+qXQFC/uX/FNnFXZIMtc/GiW1ijQ129OzgdAY0D75k4qfNsGjf0iy\naoY2R5sJDpwa1KJIuX11buCSQ2rLDj2uWMnOUoYspPvO5VYvWyHTLBBh/NTQ\nm+/1QORwT/dSy8jlDp3NK3rQkPJVW3UNSlLkt2Fq5kVS1xn4P7E6X0vzuRMw\nBSkxQ2R3OoXAVoevYobcE4qrFpVTgEk5vQpZ6PF0OuPU4fiyBwxZ3+AfaGvG\nlrfWjmH3ip9tZJPDmRwWueLFLSaCUgDMupNsTamPxNO54Pqax3X+TvM0FxzV\napFo\r\n=hWFm\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.13.0"}, "gitHead": "68d1b1b638bc7464c2794a957c1b894de7da2ee3", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.22.1/node@v12.18.1+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "12.18.1", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_26.5.0_1601890086531_0.3615726104089676", "host": "s3://npm-registry-packages"}}, "26.6.1": {"name": "jest-worker", "version": "26.6.1", "license": "MIT", "_id": "jest-worker@26.6.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c2ae8cde6802cc14056043f997469ec170d9c32a", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-26.6.1.tgz", "fileCount": 23, "integrity": "sha512-R5IE3qSGz+QynJx8y+ICEkdI2OJ3RJjRQVEyCcFAd3yVhQSEtquziPO29Mlzgn07LOVE8u8jhJ1FqcwegiXWOw==", "signatures": [{"sig": "MEUCIQDcS9HChY6pLUjHaYbHxgj8hPwUw1WtRZ9f2nduRruyewIgB8TZs0heYSGYx7xmWz9tIg0pn8mgVAY+xTcNQqYnIGM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66949, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfkpzfCRA9TVsSAnZWagAAVrkQAIQVqDgcjjLUIo+R/L23\nItqx5jZQKp4wMBmUVL3+wf6Iq+P5WfN+Pvne0ZaqC5d+Guupv/SSaL7Lts+/\nbLchMdlILdFxf6rPpsiBo9gQjs3E/AfZO8y2D0lecOI6wpU0BoBeP3yLVLDm\nUmsXjVrKcJqL+JiC4AY9qwBbUiMYypbBw+G+3K8Un9fC9gOU3vYSLrCD+eSU\noV80U3aeucQtzyMxlajpZ5/XFqIZ76odRJQnd/fNPp83b46IzrSryKJkPoyp\nNwpHKXbCiJjtzwr2JoUSXCOdCLVZthUkiNkz9fx9cLKZ8xFKignQWQjoTuAd\nW1efpMjWJnzdrXLIPO2+dO2ktMeKOe/3xdcRUil37Rp7RBf+/1DqtTRFmSdr\nq7GsnSrCKVJw3776fvG2c2NNdMUydZ8N65xpiNVLrjtIr4KIsUvd1fjP9t/m\n1WUyJ599vSAo1Cxm4VwrPm02L9SeB5d1iLcB56YXLpySTE6jtF216djZ1cal\nQq6tCAw7qfB6eh44FCiqphmRVJwb4BxAWfCYarkdfbZMCDDU+Bwhi0b81Von\njEpsydLF1DHQ9ldt+fxFZfHVSD552eBSKLC7we4he6Ei/XVEHO+GzTRJWMgR\neQ/H+mhJicvc/WtNNjKFCqdNJN7HDB0MiWFX4e5Bl5al4k4kAZ09J3bcyxGC\nbKPc\r\n=moGV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.13.0"}, "gitHead": "f6366db60e32f1763e612288bf3984bcfa7a0a15", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.22.1/node@v12.19.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "12.19.0", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^5.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_26.6.1_1603443935236_0.4175677960016828", "host": "s3://npm-registry-packages"}}, "26.6.2": {"name": "jest-worker", "version": "26.6.2", "license": "MIT", "_id": "jest-worker@26.6.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7f72cbc4d643c365e27b9fd775f9d0eaa9c7a8ed", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-26.6.2.tgz", "fileCount": 23, "integrity": "sha512-KWYVV1c4i+jbMpaBC+U++4Va0cp8OisU185o73T1vo99hqi7w8tSJfUXYswwqqrjzwxa6KpRK54WhPvwf5w6PQ==", "signatures": [{"sig": "MEYCIQCZG+fQEctMdPsXo8asx4NA2l5F20ecNdhl1jbT+opfGwIhAP8SUP1IeQzrRrFtyh6vLTjIOymA90WOUJKcB/Fj474W", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66949, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfoADBCRA9TVsSAnZWagAA+BcP/i+oQFaHIEEL4Ij/QiDU\nYdXRZwN1ekZSVpSjce/8i8jVKtHFA208SHVcAoolarXOyKN7VPtsMPO72usS\nHMApenM2zaupVV+lGRHm0LfyLHYlzeKNq+7WdQZuhxorYEVG/e0FNKq113rF\nVEn6wxutm+AIY/te79jL5bEeJtB/IS9BNhest4r1eo/LcVvBs5Kk+xTcAJmm\ncCCROctRQXFdYTqvmYLItxa6i7V06BbPVCuY8EEvCGdxwu274ruKFsVm/Dfk\n8+GCHF9p2PUa3U9ofN95+f7LWMhMGR8FWUbQAP45fEHSXqM9FjmnEk2DuxL/\nrHxdVSggs4tpk+gTk+3/I54RlAE4uWdbcDmL8GYadRPyh9mxuLDM4lmUdkNd\ncZfNr7Hs/zwXfALabz9PRWv8jNA1LWmqoVVe+qFDTwNJqB/cpY9Io+kYhvO1\ntlf6DDCQYAxwWaTxAso+a4m2RE26PN4W90BwTAZ3OK9YEZjDoSuFP+sXBq9l\n5tnuKH/k6CyIRGlBaQhUBE0L7/XVTqeXaPR6x14HQ1xgTe4XODdJPOTzJA5v\nb1sxLSKeS/K0z80xvXP4sOt2KrBDJQYyzZzoOgYGIOca8UU04HY8ssOW6AfG\nh6VIvYzVy3UJkFEk4dfCcJBej3POf0wrYSJgLtBmE9bT+mbxpGErRZYbg9l+\nN3Ib\r\n=4WZy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">= 10.13.0"}, "gitHead": "4c46930615602cbf983fb7e8e82884c282a624d5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^7.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^7.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_26.6.2_1604321472926_0.6412195637080418", "host": "s3://npm-registry-packages"}}, "27.0.0-next.0": {"name": "jest-worker", "version": "27.0.0-next.0", "license": "MIT", "_id": "jest-worker@27.0.0-next.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "595dcff841bc261b9f38151724d7a55b750562b2", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.0.0-next.0.tgz", "fileCount": 23, "integrity": "sha512-QF0k4TkN1WK1174nuS40fGsBRdq8qtMOqdGJMjMPbP3qzgmLWFms/XkNCNdOKPWbXtS29/lLCGGsHHSmEK5fpw==", "signatures": [{"sig": "MEQCIA/RnFM9gwb1lMbBPKq7Uc9zU0EI32BYUaeSjh9qlv0FAiAlnuV/8DmAem/XEdQCMdVvUp7ST7vYpYpXkCHGmE37Pg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67082, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfy8JzCRA9TVsSAnZWagAAhgMP/iVRjdhUvc8oiVcJj0Un\nVdvAEr0oWhEIdOhlIbaZWxhmJkwCv4HzOSb2NysgZTNE4fZY1UdcdMjtE5t1\nIVp7a/Xgmd8QEwsVXgnVUatPZRhch5MgPUGp9RlEfk8HmiXtXe+NmAk0V/kC\ndhaLIUS+4v2CZhJk87/BZeozZtuJx1grb/sKXY0ytm9C9lxhp0YOqRuhCgwP\n0VBnt2roICsBQPCse7/P7lPLXfKTcXRJQFw29ffhVUpqNUAA+akVcSeLT18O\nqCTXKByWPunuCQi6v1jJdP8yYtWOsOL0EsnzzbXSNCY5ux9PlkrYcGxzI4KC\ng/9hAopjOIuH8ALCc9Qo4RUvj4yD+nC8C2ygSXyXNroZ5tHQ7tdgIv11Hdw3\n5nH5XIumv435rqZx0KHMh1Q9t1AMhBRkdsDiJL7BNTaw+qX/dHahPkZZwI7k\nX6OZYZhyKq7qZ0Obww8I2o9/xkHTKpzkzkdJRf0qAlMq2j1zqclkUgLRZ6Lp\nS6CwptxXYkA6fu+vkNpAUTLi37D0BAzBMcohsLz3lJt0DHlNoJoovc5JHzWm\nx7riLi1VlHNCpkBev2/QMHbrU5A/Pi3yjbznvLCIQ9D6OJ77V2XUBdApUTPT\nwUGGMbWMhPYDJ3cnOB8n2vVuBJ/S1x4rUdETz7S4zKhgpUyN5oiD8ARmEl+g\naHWh\r\n=RDzG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "4f77c70602cab8419794f10fa39510f13baafef8", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^7.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_27.0.0-next.0_1607189107172_0.8294230700029115", "host": "s3://npm-registry-packages"}}, "27.0.0-next.2": {"name": "jest-worker", "version": "27.0.0-next.2", "license": "MIT", "_id": "jest-worker@27.0.0-next.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9812082b23d1e4b253922cb0fa20f5f7817e2e67", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.0.0-next.2.tgz", "fileCount": 23, "integrity": "sha512-0LYfj33SHCDAx61LrW5zyxn2vxvt94QX5EJTJVYRR9kdQ6sBFiTCajsCwpWLqkYY61faPKwqGaHrXhm6sgdRMg==", "signatures": [{"sig": "MEYCIQDW1b9nHqFR88z6BFEJ8dFuGBMopyc6XipiLEwCG8ypNQIhANW7wx4j2X5YhnXP6M81fT+arcpTTBx3Jva0X99R5S8R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67080, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfzj3BCRA9TVsSAnZWagAAIz0QAIj4ArSpd6baPqeeK79e\nY5+KGYvc0ZY2IzdmNg3cWJsNZKye5kUuT8PuuCcA4aQdTyN8ldTURfNYNQh6\n8dH5HgZM7Ob0Z5FD2hg4fk8jzpnwvAru6kzzWono8hH1INFn/bJm8pZp0gtN\nBEtfhTYnOber9fwSjgu7LsBr9zo9IWIWvgJS318ZkaZMOBuSI9kF2/gzi/Bv\nIA7zsulxwYUimZBDIB9U/SguZ64SLX4xZRCbRlx/BL6zSEUq3M1pa+dnPvJw\naXRsTVjK/xLgEuEHa1ZNZ1kvHeFpm2wja+k7m4+v0jAHYkUvy2UgNCw4xZS/\nEBix3ze1XONkt/gbPzNH5p7EStlTQ1PBAlD5JQKtdkH1d/Iq1+DuM7h9/qsQ\nPyp5h7wLj1RXZxLx7fAtYLafNSSt0865XwSMuvZD01VzH8NxfMzn+gDbKUCI\noAXyOeOxQTHrujf0nYkDDnAv2GEJyj+ku/M3eZOHGUJ4c7GlT0MJo5QNFiPv\n9w8Jrea2ykZ4kzFRcfXfEugpZ7ZmW22xXUM3Ad3dXo1tK3aj8m++P25ZT1FU\nPMydRouaTgO4La17VT0cGGS4jZ2t+e7EjnyL95HMDj2d7yjsxQMCLyuweM/3\nlihZxq87GNc2oUBr63Ku0hN3sYYcCCuU5XpImfclUdKWmQDLhZhossprslIm\nyXp6\r\n=wv5l\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "0006b152354237416ffbbc26d78c0b10375c0a49", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.22.1/node@v14.15.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "14.15.0", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^7.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_27.0.0-next.2_1607351744747_0.3561989783797255", "host": "s3://npm-registry-packages"}}, "27.0.0-next.3": {"name": "jest-worker", "version": "27.0.0-next.3", "license": "MIT", "_id": "jest-worker@27.0.0-next.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "bfca9ad032441f1d69fb040337038905146e1879", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.0.0-next.3.tgz", "fileCount": 27, "integrity": "sha512-f/k5TclmfFlFc7b6rVbWYBmS7DhUO+Z+dL8AAxTwSVn/aBvpPvYxpnKmWMgMF6yFPodJfYLasdRRIY9F0YupKg==", "signatures": [{"sig": "MEQCIFD0Rkwp0u5nkDnCqFWQT7npkPDf3UiUKe7y5nUiNrEsAiBl740FmcdBcJ62wLGb9uQLUBXe1DwnBR+3uFPNE4ILCQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81544, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLuWmCRA9TVsSAnZWagAA/QAQAIQVRzgTFRRXGPUEYx6L\nmgSVV/ARXfADGwwDzfCW6YAGdtImQ0sH8HW5eAu+qBKY5Pi0k8kpgVaqt5D3\nxiCMZZSh5LHyQ62+CkHArsuyjMGBatkii71yiLGEUfks1ru0ErDvThfNwzcP\nywzmV+pgMYzNx3grzRCi37MiEJnVRdk+16j8NxSTFFDgDge4tC8thqzxqN/H\nDYiqI30CQHgSi5myWFhPNOAhtsPFqJyAYsp0liBYzIJ6pTqq7pDJmGcqohyW\nCfayd9T4rGWmHlYQuQV0l3G3HZAEy5Jn2mNoThtIhxjImXUcb5ORkx9Zywyj\nASzTL1yhCAFsHQqmjxwUCssp6GKLBgqUPt8+TVWTFaFoeRKwqDzbvTurUxz4\nj5JF2nbY22dkBSRCpnHCvZI8iIxNKr9+dvisSh8SJMpP0rxgKdWTCWh/yoch\nM6CaXIG4qRpq7A3NpGLgrJYkrpZyTGxvxI73z2Ri4aFY1HqH21YevaDo3zOT\nS/zJEOzcWk9T88fYtgPh/Jc+3b+uMExupkA9v8y2CcRrfyGrR27ejxJWvcar\nbFbDNVQ1YRIWhIYt///T6N4tTSjoWLRikYQehKJvZ265JlSP4pfLGZpd4/Q8\nq8J11tr/C2mEi7x5Motl+LshFiet35DK1I6iS3WQsdDFycHmW8eAJXRDMmsY\ncQTQ\r\n=xtp4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "2e34f2cfaf9b6864c3ad4bdca05d3097d3108a41", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.22.1/node@v14.15.3+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^7.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_27.0.0-next.3_1613686182201_0.40121796396196907", "host": "s3://npm-registry-packages"}}, "27.0.0-next.4": {"name": "jest-worker", "version": "27.0.0-next.4", "license": "MIT", "_id": "jest-worker@27.0.0-next.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fb068a865f16b909c1037a411683f058e1f39f4f", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.0.0-next.4.tgz", "fileCount": 27, "integrity": "sha512-lF+F7uPqNjlT9W0E/EAGg8vEVjqJt4D/uUYf4R5ILfqGNGWCVJQm0LiDMrREjMmmR8nn5v/n0NEj9G5Ea5nXwg==", "signatures": [{"sig": "MEYCIQDrcJcl8xmqI0bArO4koqDym8TFG3SvNdXH1r7H7oXtLAIhAKIWKicMtkWB9xDLMLAb6g6178ZxIPryu09Rh7kZ+6U+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81581, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgRipkCRA9TVsSAnZWagAAa+4QAIExpqxtJ+hMbY3BWCNT\nHN4Zu4Gr1gKt81E4XGqoaaBay/D09fYAvQ5cWeYxrV36HJaPZIpoS6+PfXFd\nbwh54ZOGpIeQtJt9u3CVlVylAi0jc+AW4vbIsiOZHHvQmYnm3qZIwgkmD+d1\nAn7czVHhpC+686cBuK5cc31QIUhrjGtj6+lXeCYNQkgaktGkbZYaePVOq6WV\nwMTZyZZf+dk52/FH3sPLMHOCHrpCCWi2iiY6WwtNHhBN1R3CUng+oiPCvJnw\nBaLnrLm9yOoRlccYwg8yxo6l549ebrL9LYuFOiilwLVgXz1qXOufnY0+WYkh\nN8SBsZRBV+uGo0MesAnOiVeNDTjp6fhzAY0FNK6eagf+hMHLZCuDi0s9y/ry\niq7rfQWpE+48ceaLqpehgtsZPCyBoy6RhhlgosQMTXNbWva8d1rO8MRmo5kZ\n3RmUKSqC30lzR/WLUxq2BqbnRh177RKYI0IRn20NwkdclSNKDPBBlFk0wCva\nhCBeU4M/JdG/UjW5viT1Fda2XVoU9e9yxjwkv4tNlPBpkc67MLsyzeAxud4K\nRUFhGGBZppowuLrHw2tMSIEduAp7BDTz+ARY6nI7aK+jqeccuM5pv/Z3cByv\nB2TfLQhEtAJhxE87bTHm2srL00tuy3EN5hGn17OwNQ3XtzVQFkEjpk06hR4u\n9GcP\r\n=E6Ki\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "941c414f0b757fdc619778c46c21fda26b3e5504", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^7.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_27.0.0-next.4_1615211107496_0.9101297466879048", "host": "s3://npm-registry-packages"}}, "27.0.0-next.5": {"name": "jest-worker", "version": "27.0.0-next.5", "license": "MIT", "_id": "jest-worker@27.0.0-next.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5985ee29b12a4e191f4aae4bb73b97971d86ec28", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.0.0-next.5.tgz", "fileCount": 27, "integrity": "sha512-mk0umAQ5lT+CaOJ+Qp01N6kz48sJG2kr2n1rX0koqKf6FIygQV0qLOdN9SCYID4IVeSigDOcPeGLozdMLYfb5g==", "signatures": [{"sig": "MEYCIQDS5aNgTCicqG428StrmjdsXV8x2WI+5U7SAXI59WKn6gIhALP0T9MMQCpSkD3bPC3i7UNL+WxH8bg0Lh7Zs0n4qRE7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82393, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgT1sVCRA9TVsSAnZWagAAlM4P/0P2v3UyJiseaa38DqD3\nQoLSGoCRm1Sn5Qx1qSqBePH8wrRiUT3UVEATHMW21KCM16LGN1WM46nlgLpS\nXbsUzSP0tIlLgvkL/HYuPD+JqDDDMZZbUfq6HLZp2FiqdQzUZ+L1osG9mcoe\nVRBU+e8g6y9+l/9EdpJsTMgbBXFgSE6AT5qHTgeddqv7IuMxzR2neNyoprEL\n0orcDN3xe1+QMBaOnjUuCpLyUGGsbKz1y5n/FtZ/43vaej+XqlWI5IfZT/iy\nNHRTEXpaTRh/qI0PAKjgdK0LaavMLB38MBygnFJHwxGLdYLq28DSj5a5Tpca\ny53g1b6f/jC7Lwc/aquK392FEaDJEdeBNWSjqic8a7c47g0dzZAxWehjIRH5\n7kFGifHmz1TawqCzc78J9QObeaN0v87CalVQkFTCk7AJjXQfoIdvORx1VnI1\nkGRzjAwHJi1XxWXpCVSfJnLGSJSsGqF6E2scXfMhkwTXS689RVUUQTY8rn0t\nKGP8As5Mvz52v2UtEqz4MicWO+52FSgiQe696+AwlzzTzoSHt3HaftywhwFg\nGjrRQU3mxwwvqZ0Zdpgb5Onn0ZsmY1MgA5AK0Qyjs7hm8GuOq+FsV37/ipqw\nxok547lmEyOur5x5tFFczrMmD60v8dn00LnYNuVQcw/zXIsX4R2sWvAcCnSx\no2N4\r\n=zoYG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "0a2b94282170b6d4cc26c2d2003cc04ffebe5e3f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^27.0.0-next.5", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^7.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_27.0.0-next.5_1615813397190_0.09077202372318482", "host": "s3://npm-registry-packages"}}, "27.0.0-next.6": {"name": "jest-worker", "version": "27.0.0-next.6", "license": "MIT", "_id": "jest-worker@27.0.0-next.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "13d2c48350a87a59c25ad1fb34295b151fb9ef07", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.0.0-next.6.tgz", "fileCount": 27, "integrity": "sha512-hPxAGCsdccwrsdP2Kc/6NwI0v2LmmJn58+vmGnDfnfjkJE4+/FZuO/AK4ayTzUtFoDLSZruh3NeDLvAHQDChPw==", "signatures": [{"sig": "MEYCIQCal1DF/1o1u68SM7Yrhuec4W6VfvwSpYepT2/tIH5YcQIhAJrztofjucrVAzeQKOI+N+PcPldfmMwDMjlJoKSMlXPB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82393, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXOcNCRA9TVsSAnZWagAA1MMP/2F6MiAe9pnTxP7lQEla\nICPvxN6+/YDvW2ep8f58VMNgP/acweO2hgLIh7Ugg2AAfGZDIqa1OhXo/3Mb\nHtfw0/CrFX5dQytRAHaQDcjlYAMPqNh9iADulxUzWdQgT1fIgQck+8oD1/Em\ntZUT1yNIcN4C5z1bb8NPR6hJea52ZCdKUe9h7q83hdKie2MY0YeFTzC2lRJk\nkgL8MXzipfBtvwbQ6pOMNq1OQxWlzmJK50316B00UkNQFYVhfo7ep1vdtN9g\nsddz7E+0muCYU7JHYLWUwaZVRAxEiOnnL9g3inSW8bmpUYcN5TyKznrTPDzB\n3IRsAvsTNQoCLQbWSfzvCiuoE58d3UotD8ZN+T58ND5VK5hGU4xptGVQ/z3E\nkmJ/e1NOXWiIJQxVhFl8+yZDFI+EN0zz6gy41cJp9pq4VJ1jnxL220kIHCVB\n/JZYLw5ySL1DuObJKaSoPj6xK40pDwAkeRcioW7zdd8N7B+ysEkj78Dv7IKT\nsXzO4DawofpdnJvNoXZp5rtyPvL/RwRTd34X9Nk7YSwlT1TG4bSm3b0pFm9s\ndF1dF+BziRHkoytTPxM2utt0UkDDBKQZmxiNWqY2pW3BexFH3cCHez/xOmlO\nEEzfl++LZGEwWqmR+/RjoqPBF3HBTT1FBhd/9U7aYCfvuY/iFcuCy4cst8mi\n4ece\r\n=uxKS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "974d2f22b7deeb4f683fb38dd1ee3a0e984916df", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^27.0.0-next.6", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^7.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_27.0.0-next.6_1616701196956_0.33543352263449266", "host": "s3://npm-registry-packages"}}, "27.0.0-next.7": {"name": "jest-worker", "version": "27.0.0-next.7", "license": "MIT", "_id": "jest-worker@27.0.0-next.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2d76116ef03f6e0ac80cb2636b48f6e3f8768e24", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.0.0-next.7.tgz", "fileCount": 27, "integrity": "sha512-YaygvytwDNbNdWsebwEHuvVnmL3pfI3EsMik9HFYxG/rl+mrOt4GgVyJZ2wKkq6sq1qvsVsJdqUVCMbxbBjc0Q==", "signatures": [{"sig": "MEUCIQDodYvqCGmepZLaHOpIhyB5/v+HnMaOpFddGbjh10CcIgIgQ26JSS6ZEljQ6ByDEkGm5DRxLkSnmSz7xBSSyEDyCyU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82393, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgZyCFCRA9TVsSAnZWagAAX/UP/A52i/Yd9sbzYkfR4A1G\nsUrdfKDcYrKn/tgj9L8ABXJ+U59r08DNH9HcbhWEydfIhglhkgGa6Vt6LH+x\nT5XW/2XobnLZ+jRFXoTv2i1qtDrQYdxKhIbrbkIEnhsq62cCUaA3IGPu26QB\nNbyZYf8tRbXQH4MbRbaZLeq9SxcQXo2A+gABfOw8qICtAegbhlzKCrVU9nzg\nkiiYm9nSYL4/Ihy2i85zZwdqKScjM9t5kpATC8a2N7gM5BB+CgQZZFjEyar3\n2RWA5rqlhLLn1zHK0o3VlMNGxqiLwu9dBvySp+tLtNYA3NuFBMfUAGBatKus\nEJmEHFUv98QmPI7+qZdjb3ZHbYii7hz7y9YKvg13Mxi54PkYM15lQuN60iG1\ntCkGjaeHef2HserrlLTym9N0ZaefAlntXura25+EWV7PLW3vXvkznyWWvcvh\niSTzawjP3U1jgDE/KnN9JmekUvVrv8e04z95Ig4V5qXzs9vScZMECWliIMeI\nWGGb6VX6BlmRJHbldhl6CkSQO2IeD/3kxLf28TtlN8QlBHAOdpvDa2Eowyi4\n8WSGmyzqiXfqAjx/wV3MJVkG40CqhL1faFdSA09qYHChz2qxMIUc7ixj/ht0\nuBBwv7PB56+G4f+n4PjODx3SHwIb0C++kiFugd6/sk6Uz8NWeZJPtaGZwSvw\n5lbb\r\n=2Cbq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "28c763e6be8f57bda89238b95dc801460c2d6601", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^27.0.0-next.7", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^7.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_27.0.0-next.7_1617371268616_0.8055073076307999", "host": "s3://npm-registry-packages"}}, "27.0.0-next.8": {"name": "jest-worker", "version": "27.0.0-next.8", "license": "MIT", "_id": "jest-worker@27.0.0-next.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "70656d41d50d2563ff7f4b54ab8f7605518a36f2", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.0.0-next.8.tgz", "fileCount": 27, "integrity": "sha512-eScqze9INJJp7cXNdMaLeeT4n/NwxNT7lMHpODBlK6jDwoqkCajEV9H/pKzchihW2iFaWg5zN9VZnZCS70PmKA==", "signatures": [{"sig": "MEQCIG7mgJRd6fv6koRwtdqkQee2eeMI5Ko09T4OPIHwFCtRAiBKtVyqISjryv9npcUGmY+rOsUdmgZRy1CI96KtzYQl8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82393, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgdMzPCRA9TVsSAnZWagAA434P/jO1Po1YVVsIoIT/z4++\nnSfOwT1+XODEwMqS3FwGtN5c1hmEqB6RB5GZuatFxgqH4NuKyy6RZhUyJnEm\ntveMq7HCT85TXCXGicyAux6SfJHN7t4lGbt4kC56NhEAJc5fdXJhKCpFWBUj\nMReWWQIhKtkHDtGzapTcL9rfuCtcVd6e0XZrpLV0xo1GJRRXFq+uyeVD5rqX\nlf9Ozq5VBwW+gEkbcOrwgwIhP77yLYCGWsXzF766k3/cPvpcucXens3i1Yc7\nII0VzOU/QlgwLejnI0Z/3ni7hWF9dPfQAtUo1kYg8WjRcSX9iSle/NbAVa5d\n2Sm+sWYiBktchcRKTVG48WYTpcsdo/ky45NsMq7RYQ4fkJrfT9H1COZic8as\nXJnEGCii1xRbUhaKntSoISG5LUSXDBbeigdkEfnGdLPSFkptdn4c7sEkCwD1\nZJ8D7mKa0tks+RMd6NWamfjT78ljwYjaTgJzJ2hc/mgErCzNibJu02RqxDE2\n7+X4fJ7RJgiCeWjvieDNb8GOowXE1sUzp5A1sbhgqD8AERj4DEVkXO/UH2UC\nDxJLn80bR3GC8l3cfyZT0wOV+16NZ/5feFkwmlqgcGLKIXXDdQQSeVKi0lFe\nbEyaYmklTz50LdgzZueLh928PFDouUCJpO1I2NgArTK0d1PZM4ChHxf1Jkbd\ndEqv\r\n=mJ9l\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d7ba5030e274b52f029179dfdb860349a36eea37", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v14.15.3+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "14.15.3", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^27.0.0-next.8", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^7.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_27.0.0-next.8_1618267343476_0.8567630000746782", "host": "s3://npm-registry-packages"}}, "27.0.0-next.9": {"name": "jest-worker", "version": "27.0.0-next.9", "license": "MIT", "_id": "jest-worker@27.0.0-next.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "66056ca63f0bd32563f6a22e581dade95d44b01e", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.0.0-next.9.tgz", "fileCount": 27, "integrity": "sha512-uEhpx/rN5f+GIW93K+HO1f+OAjdiTwAZ/taFEa56gpY2TFboHzBG5bNNHwRN35l5e95A5fdtOeSQIhXUM0qEBQ==", "signatures": [{"sig": "MEYCIQDx2c9yXHW5H9XewWh22AHEkWAAMGeuH8vUe2Dsu2Hz1AIhAIlGpxjaJuUoPYpjeYYLxjdJC/3l+4OImUAqGkm0zwCv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82393, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkOi+CRA9TVsSAnZWagAAmyIP/RQipUr0DyKS7g5342op\n783PZ0dWfq7cO7ihDqWoGYq1QaJVm7A7i/TlMq+2bZ4VJD7kI+JOuiCKtHB+\n7p5ENpVLPMnuUEpTcFqx/TU3r/Xnj4HBAU3zgOsLeUs0YSIDkAW/JQrLqhDe\nf1IG2Tp8JNGjyRSLl9RI/cPFy5tW+gb8a6C5O5PDulnh3e+DD0Q4tg6+1J44\nOvcAOX2av1YUm3Lmu+idYRBry2LAIT52XfliFyqHouhBjQzlvYoiSD6f0Z6c\nSK19UhQinNrGHgl/o9N/CvrBx1tqGiytFqytUjCRIu2lCscwneoP8tO2hOky\noXjMZHj54b7z7WakQMjeHYeqTUI7Ck3PUus2piA4abFK5YT7xGgPqjLz+R8P\nuZyJDJTme/2R9252u6ARSVDDchY7Lg93LSHx5PFIi1yAlIFu8Qg1uyR+nYqO\nqQmHvWCwBAvckVKLXyxczw36OSgaNBmdKKHkuYlNeAJFhAxApf6Uk7XAlVn1\nTbmchH7knzNmzzmypc6l8kJjmo+auiP04se42ZvPtj3KkPN0gdi+gzLRuENx\nzESsIYr+TySnz27sOFBExiQVPCny5Km6x8TtOuf/9hYyzU3jBv6yZI3tlOAk\n0EBX6LpY5ZAgBCmCmbZZCeecr87cNGet8D/YNUF5plc0sQyiaFaJsQNN+ILr\nONn+\r\n=6NqO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d836f33f98845794b4eae8149548a81ddcfc6521", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v14.16.1+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "14.16.1", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^27.0.0-next.9", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^7.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_27.0.0-next.9_1620109501933_0.6231860640907045", "host": "s3://npm-registry-packages"}}, "27.0.0-next.10": {"name": "jest-worker", "version": "27.0.0-next.10", "license": "MIT", "_id": "jest-worker@27.0.0-next.10", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e9235e14ad67a7473309fecd3b34762f8a86e9c7", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.0.0-next.10.tgz", "fileCount": 27, "integrity": "sha512-3dy2N5oNIi1PdAz8AGzqOj8kGrpuS3aY2CslZyv0jNaf8pTUuNCt/DNAm/0AWu1NftPBZSVFgrTHlF3Bx+VI1A==", "signatures": [{"sig": "MEUCICPTSPzAEa94/S2vTJDQRSfX04J9MEqYEaTMql0AEaYGAiEAy+o8mJ3juO6OVF9/RMIyZvwA61wZ/L+s1cIEvEtl6qE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82861, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpm4ACRA9TVsSAnZWagAAbWIP/1eMJ3oHmGWR1rf8RTQv\nzk6RPiD9HfdteD2y00DObYBi2zaEqBC6GpiYhc2OECrGCml0+eQnnW6EjCmv\n5OTy98lII+Zc9Ssgsain1BfLb6RSM/s2iUWM/KgFQyFzf9mEQ91llpjMrkqB\nKVLRo8OlXWsyzWc1GDy2p88+9MZpmukLDWTRxoN+4ejDrx6Mwb0vLeV+a6lx\nGoOrAl5kJPyJQlzqw9pi2gkvQZac+FUrVyHVfzGHJbGVTwg5DRdWct3SRSHy\nw41xSZaTndAS9Ew+K2Q+Bxg/KCmzEF4glfcxE4RkuWtmtSUNAY8qatZDiLSN\n/uJPjD33lEVOWliSYcXfdeu/z6+*********************************\nfud/dDkHieLlf1kmNljd0N6oQdvA9l3fMFws+RTGPXSMIBpYm0idtKg+n9Ra\n8nPTthLtpYVgLBuYAdVUJ/HDRVD5Sw/T6JrS7Fc7ejuHPRZo7+aoi5oj+zeh\nvhkVrR1kdfwVXmfXRBw4ZUyUup1gOTcV/d4+8aND1s8HKx0YIVi1ASaSo+Cc\n/2lJVKf6JZXt7F22u8KPX91U1bMPcPEIvt+kZzIPojow1tt34StAmHWakUWJ\nZXQMTdiYM8j573Vi+VA/FfZe6uB0r4ejJ5mwXX/dqftFRZ3Z2D572Mz/e2i1\ncUpx\r\n=8O4F\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "6f44529270310b7dbdf9a0b72b21b5cd50fda4b1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^27.0.0-next.10", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_27.0.0-next.10_1621519872249_0.9822625982275239", "host": "s3://npm-registry-packages"}}, "27.0.0-next.11": {"name": "jest-worker", "version": "27.0.0-next.11", "license": "MIT", "_id": "jest-worker@27.0.0-next.11", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1d422f2f0de070036ce5b3e3055bfe499a4d73aa", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.0.0-next.11.tgz", "fileCount": 27, "integrity": "sha512-rtRHdxinv+vp4QNF4ybYhNzA/al6YBjtb9hen5yC7XiKc2kKMl4Von1AzBX4LiTJyE8UMHqKMzb7s4Mutyd+tw==", "signatures": [{"sig": "MEQCIBBHYUEXGjUu+KjwxZJGXoH4o9LAeC43zvdTP5R/IsjvAiBmBzQMPWzyECFJfmYAJZUe4Vbg2b2To+nC7tkr6aR/5g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82861, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgpuKaCRA9TVsSAnZWagAAvy0QAJLsYZ9Z4ipIVVyKHVs9\nSIPDMg63p758wHmBgJgCHd1L+2EIcgg2m+lYKLy7WEPPMVDz6C1q4ibIBSrl\n8hL26vMS1UKBianko6ibCwIydtPeniZ1Zlbwd5X9MURy3RHPymSlaFe2ipiR\nsBLPkyM8c7RLGKkHBUr3jXLUyjsZgNPayTCjo4h2JFszJYw+KPGs6miHh/fY\nB7mbSxjleoMe9fMGftFrpl9maX4HSnygk9itYP8+b+dDh87pqDIwzZTJNQbW\nir+YS+Ud2CBdawZ4JaCB4BLpvAb0KMTqgxG+6nbEsH50QPbiIYRgtGi4fUMV\nihOm1nnTtlmj7ndi3YwesWKimsfkmh/oxDiYUsWCUBDtlIWCoeu9FVWgA3n6\ntECnEPz0j+Bsp74PRehIDVcTLnayMcEaBJ6+FHL7AkGzBTtdFdrZyV8IfvQq\nNCJjK/Ajw11vuJw9sh1azCxiWL7m5QS8sk4u0RLJzVK6bqE4nGET0MVTRN6E\nI7silshCTlRozbqM1I+5/N6m0TN5Whsu6geYMLZk4eg3tKO3hCaAk8VYZ0xY\nZcmuo8WihQScgEJ3cdrTeHpD832X++pzrhKokpzXTSRyje1joOsfkDJIKEid\nbPAhsBcRkq5GIopxI4f1K6EFBRuornLzI4Mi5BwxXKjr7KO2g3Zn/CjMTX5z\nktre\r\n=YBwS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "e2eb9aeee8aacd441f1c8ac992c698ac4d303f60", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^27.0.0-next.11", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_27.0.0-next.11_1621549722285_0.35689237840768495", "host": "s3://npm-registry-packages"}}, "27.0.0": {"name": "jest-worker", "version": "27.0.0", "license": "MIT", "_id": "jest-worker@27.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ff8bac62347659f06889838b31b433f649f08578", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.0.0.tgz", "fileCount": 27, "integrity": "sha512-KQaF6tKSuclVD4o3qYd6VeOgrxuU4fvBtLbSZV+Et9P3p3AbUUSQ3TtgvxwA4tR3ZMF4Wdmpx5vB5Y+yYMrvBA==", "signatures": [{"sig": "MEUCIClhZfxdjcOjoeDDOXmLbdgdozqfiM09QU91P2qGJOhOAiEAyVVl692u9Hf7v8BjFG85jOwFz4TF6MdKh24EFALsa50=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82845, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrLIICRA9TVsSAnZWagAAeikP/3XB46WqfjIkgDM6bPgI\nVB+W2C0oA6EL/+BCiz5arkSY4hpq+eQ9HNHMKZuNlljGDpSrCFWK9+rZitOd\nDpjIwRHAjUbWzQ+c7g8KLcU53LjN6nnTsREQ10/50Y1VQqJ9BXvo09hhwLnl\nzwNNLS1Xj1qU2MCGn9pOji+o6ZMPhzC20cndl9t7qU65tbqH24mvAE4qcqcU\nDHPZr/EKutizXl73s2Fn+r26qc0FuSDQKqcAShaIiv8mUsoiEiCoktEmjjnX\nl2YkuzonMfSTBh3IT4QpOrx5ulBrhlba89KziwUDKpwJAn2joLNE/087Tins\n1ecI1RJYrEGetdwqFvW2oY1I0J87/oxQP19uTD7benREYvnJynEsomsxy4lp\nU5XA1TrEcpdW/DgSflUEQt0U0jZWid2dqQnWtzxVxz5vGfEpNMN2HOP6TcHO\n66AJHeNl2cRMV89XdKmetUziiiZnyVxRERz8XUY16OT0HlVC76NZ7eC/yUlq\nIcvXJQ7vjPR7I2MCzRs5/pd2AR1xvPMaxNLlevOiIy9MieTOPQijarsJkllF\nluMzRGY18pmgMBlsCAkZf4inf4pes6zLTKO3dQ882B0slYSXmqSSFqUNz/uw\nzyA7dBlNZyDBQmlGzxGVwtqnuuYUTx/vae3Xa2/bJhADgxZ5LmJF1XJXYFd2\nDU96\r\n=2Ycq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "be16e47afcc9f64653b9a47782cb48a5ca243e65", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^27.0.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_27.0.0_1621930503997_0.9356265511107593", "host": "s3://npm-registry-packages"}}, "27.0.1": {"name": "jest-worker", "version": "27.0.1", "license": "MIT", "_id": "jest-worker@27.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b255fcbb40fb467295010c628474b1185cab4f9e", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.0.1.tgz", "fileCount": 27, "integrity": "sha512-NhHqClI3owOjmS8dBhQMKHZ2rrT0sBTpqGitp9nMX5AAjVXd+15o4v96uBEMhoywaLKN+5opcKBlXwAoADZolA==", "signatures": [{"sig": "MEUCIQCy1elHQ8C2KYWrFaNDS/tKuxgMiwHd2FI69u3Dlf/dJwIgDWdq453htkiq0v3N4eXLYkvw28qsQ6BuYEUm04ZE4VA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82845, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrMwgCRA9TVsSAnZWagAA+JsP/25pYzRRAgEMvyOnNsfN\n6m0crNhuLXQZGtbCRKEZYqnyqJtrXwyF4c7IOcyK+6goyW+KrS+vuquuW+8S\nfYDZVg2g1PvCLTIlv4onPa3ad03SAzAxVNbSU3b9x66m+wXUWuYJMx1j4t9n\nXh5fjdvcQ13mVp85ayRB7QVtl9oy9bRyYQ5ViKwZIDsgXV4G9BHIfYwLlyNh\nuOuYVNmOUBbV+fiL/mJDVdSZZZdbZAtTafM/KfGaRVSnDcHZvswaDe8eLPre\n0AnKzJAyRT3OA4boW0cJW0QjjuOy+qLS+mMByB6OU92A6C3uuCHItmCeTrr4\njA1yUCs4RjXfRxTV+1eo90yhzKw/AHyPmV5aaPtSyGQ77TkquYJcXVeWXvMI\nmyj+Vvwn3qO/znggvDjqdk6YBgr3YGwtwirJggK6eQLTUMJf/BeP1M56lsid\nZgxMlODLMf4eTIJRSVVKgz93rk+dE6QctJKFOAqd7++Kar+xWPHn7x5cyqyE\nlFqM+pd57LoU/lMzH7gzcfBNLuOsErip5mGh15Kozxojf2qk/k2xrySomUbs\n7hHWdakqVQ/RB9ZBBhPA7+rK6q1c2dGmeUv8lgYTkbz9vv6O13lwbRg996VM\nMpz582l+ADyCYOhxQalGJDuMBm0JfjVwnwmf9HRMn2zUDGFLcPq0sPh443xS\nHI8k\r\n=WZQV\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "2cb20e945a26b2c9867b30b787e81f6317e59aa1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^27.0.1", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_27.0.1_1621937184060_0.4135301928984665", "host": "s3://npm-registry-packages"}}, "27.0.2": {"name": "jest-worker", "version": "27.0.2", "license": "MIT", "_id": "jest-worker@27.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "4ebeb56cef48b3e7514552f80d0d80c0129f0b05", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.0.2.tgz", "fileCount": 27, "integrity": "sha512-EoBdilOTTyOgmHXtw/cPc+ZrCA0KJMrkXzkrPGNwLmnvvlN1nj7MPrxpT7m+otSv2e1TLaVffzDnE/LB14zJMg==", "signatures": [{"sig": "MEYCIQDqttr9g5lIM5CoumFW61AHe7lASE+JusiaO9cIdUcoxAIhAPPXguC8WzntrsuxfSHZnu+KzgarIGoxbSV+2+dVNqFc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82816, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgsi5sCRA9TVsSAnZWagAAIz8P/RB2wjkFht1rXYEb08HG\neibxs9HRxGiMXnUkrMDPY0BnZIEF4XEPDT2035UXLIcxKlEy6txlsSfHmXot\noXdkM01C8KOiF5CEMoA2m6dzrZRioNWkkAYs+LzIR0MZr0ezF8Ex14+3Nisr\nE21+o/gcsxpsDSiRBpJuZpnxtUMcUcm4x71ZF1sw6agwzFLADtvgRfifKBqQ\nx+OoX7OtvzjnY4Pg0AXgdfvpYqJGlX9p/HN2On3HEV+4dYX/20AOXxyXuD1a\neMVNQ3Do4Oa+uwva81pHRcWmIB2MX4pz1+THuLooIaKnWTr1owl72uHL3e81\nS/OYcTsSTlrYZeK0DlJfZUQSTXKcClkL+UvxLmofinfZmen1ytTGBEogqqE1\npK9IgB9TmemhTm3uneU+NVQZ6aqe0rw/jj1zNJCKapEHhN1fXwc9p8MT1Os8\nJmI4dASEIiCqL3MDn+GW+7QlLy5IjJANetCvhOFJQ0JuvbyNGch0pyZ6qb+w\ncLSv2TPQb2Rv3wEHV8+fwPwMNhCdu5Jx+3J7EyBrB1BrKqFVwhb6SAjEd7f9\nBRSY7OjxJrJVPLahZF2dRHR/0ky9FEoU9OqCAtZza7HX3dvk3VrAFGJAeAhh\nj6PWuEWMDPijuG5KZzJYy2UtlEWvy3KXh+1rWAobDhjN2bP/Dcohe3ScnCky\npmQX\r\n=haBT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": ">= 10.13.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "7ca8a22b8453e95c63842ee6aa4d8d8d8b4f9612", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v14.17.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^27.0.2", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_27.0.2_1622290027772_0.3115783328499193", "host": "s3://npm-registry-packages"}}, "27.0.6": {"name": "jest-worker", "version": "27.0.6", "license": "MIT", "_id": "jest-worker@27.0.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a5fdb1e14ad34eb228cfe162d9f729cdbfa28aed", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.0.6.tgz", "fileCount": 27, "integrity": "sha512-qupxcj/dRuA3xHPMUd40gr2EaAurFbkwzOh7wfPaeE9id7hyjURRQoqNfHifHK3XjJU6YJJUQKILGUnwGPEOCA==", "signatures": [{"sig": "MEUCIGyA0hB1TITy8IahjlvYeEkoqV0yAsVVG7lNyg2H0uvQAiEA4hxeTFAJ5U8Dap8wkbLgSIQOqcTXxOI4YD4SkO/GKgI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82816, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg2gFcCRA9TVsSAnZWagAAFvIP/R+X4eVIbV9zLoaEwRCg\nyZBnlMh+dEHNgn2p7yAP9XaCKoFpldnkRVP18kg6x8SHKiYhv24UUnX4PpMx\nCjvVIrOhOZtgESHNsrr6d0febmm4ekU06rTDGmB5eLvLcDIfOxCDHILvA936\nCj8357uGh6FxAguQ5WAZ5IQFwVqIGqIhIHhTNa5++KptSAP5pe2qKKXfOT0C\nOCsD+r7UsBuwBOq1SKRf4om9ulAMe6Ln0GUoplMINj+pIsHtrZvsnn3kTF3a\nhw0M7vrnr4FRtdFpOyYg9mrE3QoYibxfrKJ+/ye/CCpnseG3NgvtbPA2CvFI\nyU2hKKumUPSrUtT3A7LJaoBCjwLjJtqldz2gKyGQWRosF/rTIO/jVUgbt3Lf\n516aKgGw3Zo2bH54lpE8+s+0QPlUDMmekk3JX2MtE6v7d0F1ij166b6Sb0io\nVfNQVsKNJ1Wf5t7DsVF6cDXXqA3jF49RXF7oxDM4ZiyxCN1XdHlvHKiX5r9d\nRFl4Ejcit1KJuD23hn6IdJ/gjcV2ODC0aDw7fBA7pIM9nBIBVtXR/Ui5STlp\n54AKMzK5fuuHodHwwnSboWUDBIQAdrJ/hljRgspUJ22aIlOJ3YTlObvXbL0/\nt4UzReCmktTmYfMNqw4swd2qB+LZ0v88+OpVyseu6eKBeI+kcot/0TgplMub\nEyJN\r\n=xn06\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": ">= 10.13.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "d257d1c44ba62079bd4307ae78ba226d47c56ac9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v14.17.1+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "14.17.1", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^27.0.6", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_27.0.6_1624899930957_0.31410735150373936", "host": "s3://npm-registry-packages"}}, "27.1.0": {"name": "jest-worker", "version": "27.1.0", "license": "MIT", "_id": "jest-worker@27.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "65f4a88e37148ed984ba8ca8492d6b376938c0aa", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.1.0.tgz", "fileCount": 27, "integrity": "sha512-mO4PHb2QWLn9yRXGp7rkvXLAYuxwhq1ZYUo0LoDhg8wqvv4QizP1ZWEJOeolgbEgAWZLIEU0wsku8J+lGWfBhg==", "signatures": [{"sig": "MEUCIG5nec5wIHC87TscvjwvjMYM02ZWANv3bH8+5P7AaDC4AiEAkiVMrGAuMtuptNvJ/JFMhMWtP3iKKymwQB8Jw7OnazA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82789, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhKLeCCRA9TVsSAnZWagAABPsQAJrrlMRrAcQE/R3qMEVT\n/k5tZHZjJ1L513CpHwvenwBLQCg2lUtKv8GOABXD/x2+oJVBUCRfuuXqvxSU\njdF4gt6GUIuFJDqHa1X5yrx0xQIKMeRkxW5466MdHFmSMUNtmly9Fuhtm+Vp\njlnhnkAo5HWi6fMHAnUZBLBKSNQ9/8t54tC6NFHA4qyaarRNT7+mdSL4r4uJ\niPZ2g3zcDQAtHMPoPWyNuZE9scjcLpmortRCIZY9at+g9v5y7GmtHImgSilk\nC7Fpa2733MSurGVbotfmofkPk5hG6c9RShVj3b2hr2gn8kUt3pTbO3PbK5ph\n04pNmDkv/Yo+4THTEvsFzBjlnN9pW9qSLJkPxMebJWtZJ8daqGKCYQSPydsP\nNoq6y/S1gWR+opJ9BbkoLfkg9YFt1xSa9pEjZhPSoR9Vo0f5D0iDZvZrRnRi\nCPfpoL4BDNDQGk0UQG/puRn3jfp1tSajcsiEXLhNlfhRZ0CkEQ52qVwKymui\n8liaOiiavxy6QrfyXkIXlzUPZOcTTpMdRy3XLtmzlo0ZN9n4zDheQVeu41fL\n0knfiKNys4U6/FO1+n0q3KPgs6H5Fw25HToWvheAR36cqrCsDonDmUDVi+gb\n0fgVOlrsLjpBThybGqY6BO/1Ms+EBSxhdxhAOnDQXFjlcIUKU2AIBEmnq70K\nvFdd\r\n=BLqg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": ">= 10.13.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "5ef792e957e83428d868a18618b8629e32719993", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^27.1.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_27.1.0_1630058370490_0.3803683921432499", "host": "s3://npm-registry-packages"}}, "27.1.1": {"name": "jest-worker", "version": "27.1.1", "license": "MIT", "_id": "jest-worker@27.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "eb5f05c4657fdcb702c36c48b20d785bd4599378", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.1.1.tgz", "fileCount": 27, "integrity": "sha512-XJKCL7tu+362IUYTWvw8+3S75U7qMiYiRU6u5yqscB48bTvzwN6i8L/7wVTXiFLwkRsxARNM7TISnTvcgv9hxA==", "signatures": [{"sig": "MEYCIQDHR0YGhV5uRzVI5JkLZdcrbB8dV6VZer1If7qYLdtcHgIhAOcWn5PjUu8zq5Adv49389VFRxMlVjVt1xTFSUB5x9KG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82789, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhOIx4CRA9TVsSAnZWagAANNYP/iszAllWppaZaPAnwO/y\nbtvETzcxT0XG5iND/P5o2Ap5RrZt7o0PB4kDXBTFPA+gCg4tn9gK2OneG98W\nzluY2ml1KwVRmGmO83cwtFeciyLFCYsX4/EL9EIdIeBmm6rKCIDdw5mOm8MH\nr3LehPTZDEFbbXmTn/tv328hfksYH7WVDcvNiw6E9WnYahAs70+7OaftwGPV\nd7v5p+C3IahUVgcHMiji4WqnabUbyDQkn+DJIhCaYzIzMZXPD+E35YYcPXW8\njQOPdlbGRyIGrBQG725n/WgIiWbYy2/k+TDRMaxnCkOChrROSHZrwJG1YYr9\nfMZXNdqPMzh8Df0KkUQMKCb9SqtTCGbgsu3P8/GoKwP1hNmQjhuGHuMovD4l\nR4g7WY3GJ2c2u8iADmVB1WlviyZZfIZLFPSTNsoOWcwvQCbmqxQMUMfRZKkd\nROCvtyXhdzZLVJ3h/KPx2cjSvDE6QGskEspSl27Lpj59fxVfyv+WVgK8NOf7\nVQfhtXCdONmuCw5vCh7MpT4y7UaRAOymJAxl5HCRL3PR3Tk54gF+5/gwCwTL\nCwpajMg4esXw+c0GGqBtF6tq29kO6XvVTGgOmmfKHE+XdSKjDXYN+TZKfSbn\nsqWIxA8VumsVRkmo2dJumQlVHFZj9QGjIrVePnVjipcMPXrgkiCR/7f9Pugq\n0BES\r\n=8Pf1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": ">= 10.13.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "111198b62dbfc3a730f7b1693e311608e834fe1d", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^27.1.1", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_27.1.1_1631095928042_0.9543975372436013", "host": "s3://npm-registry-packages"}}, "27.2.0": {"name": "jest-worker", "version": "27.2.0", "license": "MIT", "_id": "jest-worker@27.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "11eef39f1c88f41384ca235c2f48fe50bc229bc0", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.2.0.tgz", "fileCount": 27, "integrity": "sha512-laB0ZVIBz+voh/QQy9dmUuuDsadixeerrKqyVpgPz+CCWiOYjOBabUXHIXZhsdvkWbLqSHbgkAHWl5cg24Q6RA==", "signatures": [{"sig": "MEUCIC279pJTfjJ1pdsV52AxhE2ZBhkaHIyvTPd8hz2pTELzAiEA2Te/hFdWhzIJWBP/1npVJ2g8pe3C/Bp8IN1t6wX0D5s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82789, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhPwaLCRA9TVsSAnZWagAAOV8P/0gY6+/R+ioHYrynLBL7\n+Pew6wGDMTRw7zANTYiV2XCBpNalWUk9JXeuCa6Rr49FDHOz+WlJDgGudPBT\nc46AXm1/bK4eNUV637kKm+S4IvM1DylrL5ivDsl0ilcVC1tGB68fwyOftX+k\n51nq5yGgGWyAxIVqu7O35bo4JqQEYda5Hbg+oB9/7+N1x426NN7nONKTq5Zr\ncptoC7ksmohINjwUeBvCUy3ks7G+7QAxVw29UmQWcAAKgR+5lCNCkN4e39Na\nmyWjqXDEv5W+vSw6SwB45GVcQC7Bg604JOqx4m/3W2ROSeUwf+BF0nr/Q73y\nEulgFMWwkZ8xrH7Mu7X/pik3kULvkdTG1XVKQ4wCilNj/mMYs7w45WmjAFPQ\nr/Va0VzqSx1Cu5ftZ1DBa/Xfy5Ltvf4x2KhX//5Z0jP2WIBATEZojSztXOVy\nneYpP4QOY30GhixUBzhOs/liuinxg/ASCMJKitPZmOhTSi69eKoDeXK+bZjl\n44lHGBduBfTX7T2K+WnXVmhnQ3EMBRyCk6y5AlpvCXByyHhzS8QkMQ8wDJlg\nyOkIELTQb+NRIzWUyRQiTUW4Lm6ywL8ips1FCUo6BPSgJuWDQ8l75aply9Vp\n6XBDllo8Cm26BwHYSZWz1jzeK3SyYqJ9Fma+0Y0lnMyFvoBA20eAplZ8X8Ry\nwS+g\r\n=bOfR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": ">= 10.13.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "b05635c539f8f673dfed5bf05ea727a8d5d7bbe2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v14.17.5+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "14.17.5", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^27.2.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_27.2.0_1631520395017_0.8766271203575662", "host": "s3://npm-registry-packages"}}, "27.2.2": {"name": "jest-worker", "version": "27.2.2", "license": "MIT", "_id": "jest-worker@27.2.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "636deeae8068abbf2b34b4eb9505f8d4e5bd625c", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.2.2.tgz", "fileCount": 27, "integrity": "sha512-aG1xq9KgWB2CPC8YdMIlI8uZgga2LFNcGbHJxO8ctfXAydSaThR4EewKQGg3tBOC+kS3vhPGgymsBdi9VINjPw==", "signatures": [{"sig": "MEUCIQD9wrsOS9GtLxKmEbuou3k8RrC5LcuKUzbtKbTRm+qvqAIgGPQ8G45e9HzKHTZTYmBFv/BthuNkXMDBT0tAbmB7EXs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82789}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": ">= 10.13.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "f54d96fec55518640b900d6994b2c4153316d1ed", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^27.2.2", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_27.2.2_1632576907630_0.4936313802143313", "host": "s3://npm-registry-packages"}}, "27.2.3": {"name": "jest-worker", "version": "27.2.3", "license": "MIT", "_id": "jest-worker@27.2.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "396e83d04ca575230a9bcb255c2b66aec07cb931", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.2.3.tgz", "fileCount": 27, "integrity": "sha512-ZwOvv4GCIPviL+Ie4pVguz4N5w/6IGbTaHBYOl3ZcsZZktaL7d8JOU0rmovoED7AJZKA8fvmLbBg8yg80u/tGA==", "signatures": [{"sig": "MEUCIQDEhY+iGsOKMHGpvN9pN3g2bNOEN679jkDtfqsr5W62dgIgWNNaaELOt8ZOKcoNNwZ/eiyNclSu2myv9yAulcZPmOI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82789}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": ">= 10.13.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "ae53efe274dee5464d11f1b574d2d825685cd031", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^27.2.3", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_27.2.3_1632823879797_0.3956543902033458", "host": "s3://npm-registry-packages"}}, "27.2.4": {"name": "jest-worker", "version": "27.2.4", "license": "MIT", "_id": "jest-worker@27.2.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "881455df75e22e7726a53f43703ab74d6b36f82d", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.2.4.tgz", "fileCount": 27, "integrity": "sha512-Zq9A2Pw59KkVjBBKD1i3iE2e22oSjXhUKKuAK1HGX8flGwkm6NMozyEYzKd41hXc64dbd/0eWFeEEuxqXyhM+g==", "signatures": [{"sig": "MEUCIQDhynR3qsHwhkHQ0FIwjbw0tIgx7l5QJzFV0s+FMDqUMgIgVC/lVv0yRDNRx0SLqRql3qLzb7zxHETZN6cUCgrpQ/U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82595}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": ">= 10.13.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "5886f6c4d681aa9fc9bfc2517efd2b7f6035a4cd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^27.2.4", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_27.2.4_1632924286556_0.08060569557364738", "host": "s3://npm-registry-packages"}}, "27.2.5": {"name": "jest-worker", "version": "27.2.5", "license": "MIT", "_id": "jest-worker@27.2.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ed42865661959488aa020e8a325df010597c36d4", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.2.5.tgz", "fileCount": 27, "integrity": "sha512-<PERSON>T<PERSON><PERSON>ZtcNKZ4LnhSp02NEH4vE+5OpJ0EsOWYvGQpHgUMLngydESAAMH5Wd/asPf29+XUDQZszxpLg1BkIIA2aw==", "signatures": [{"sig": "MEUCIQChK8azFqJpLOP/oyKnahEmbZeZOvgkYpv5ieFYXL9GIQIgQAw63i3nDfS0H3Jl+lMoST9BkU1FVRUq82xypspAYpM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82001}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": ">= 10.13.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "251b8014e8e3ac8da2fca88b5a1bc401f3b92326", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^27.2.5", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_27.2.5_1633700358606_0.47419812324526966", "host": "s3://npm-registry-packages"}}, "27.3.0": {"name": "jest-worker", "version": "27.3.0", "license": "MIT", "_id": "jest-worker@27.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6b636b63b6672208b91b92d8dcde112d1d4dba2d", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.3.0.tgz", "fileCount": 27, "integrity": "sha512-xTTvvJqOjKBqE1AmwDHiQN8qzp9VoT981LtfXA+XiJVxHn4435vpnrzVcJ6v/ESiuB+IXPjZakn/ppT00xBCWA==", "signatures": [{"sig": "MEUCIQCM/cMed1Bby5+zNgAbAwNurrxbUJ8l+njaMI+l3FUdlgIgXTzyOniyq06mKWyEEuK1LyFj7sG9ann7fztFlcwFows=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82001}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": ">= 10.13.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "14b0c2c1d6f81b64adf8b827649ece80a4448cfc", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^27.3.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_27.3.0_1634495685741_0.537208926487684", "host": "s3://npm-registry-packages"}}, "27.3.1": {"name": "jest-worker", "version": "27.3.1", "license": "MIT", "_id": "jest-worker@27.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0def7feae5b8042be38479799aeb7b5facac24b2", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.3.1.tgz", "fileCount": 27, "integrity": "sha512-ks3WCzsiZaOPJl/oMsDjaf0TRiSv7ctNgs0FqRr2nARsovz6AWWy4oLElwcquGSz692DzgZQrCLScPNs5YlC4g==", "signatures": [{"sig": "MEYCIQCkHwoWNpVXu5QKCKIBWDDPonw2sT2iuflAXoi8+xdM4QIhAPRx3XYdohm9Pd43lS+JNV3KStfYlJ3hqOi2VHjYG8JB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82001}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": ">= 10.13.0"}, "exports": {".": "./build/index.js", "./package.json": "./package.json"}, "gitHead": "4f3328f3227aa0668486f819b3353af5b6cc797b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v14.17.6+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "14.17.6", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^27.3.1", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_27.3.1_1634626651689_0.8995345320116441", "host": "s3://npm-registry-packages"}}, "27.4.0": {"name": "jest-worker", "version": "27.4.0", "license": "MIT", "_id": "jest-worker@27.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fa10dddc611cbb47a4153543dd16a0c7e7fd745c", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.4.0.tgz", "fileCount": 27, "integrity": "sha512-4WuKcUxtzxBoKOUFbt1MtTY9fJwPVD4aN/4Cgxee7OLetPZn5as2bjfZz98XSf2Zq1JFfhqPZpS+43BmWXKgCA==", "signatures": [{"sig": "MEYCIQD+3b2CC4kfgkoU37y9LFr/nC72OTNaG1R9QmkywativAIhAKINv4zfnJcszKjMpsX/gHl7kue1OhoANZbcN9WW22kG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81957, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpNd2CRA9TVsSAnZWagAArlUP/0bHJBINKD80IzWDOahj\n/POPwtHTnSRUz7gKaKrAlHVcoDiUDgIAYiml/2148ROWZ4q+gY5jyXPHdj71\nJwRiPLtZFR4a57FY+fCW5FBbG1DnofEV6VaTBbg4oy7IaIGBJcfayVGHvVRc\nIOKxb7OtF0vInN+K8gRIsRo7kmW1sMC/GdpA3QFjP3dK6DWq8uqp00BaEtGZ\n8ea4GpJEwwZY0Kh8oVX7hWcXrX8MpD2jRvIHmpp3gXpkECnXVIGO1Mg7tRNz\nUPZ+2n53Trr+7RDqtCr6/8/tXLUuLR/+/U+PZWRYDVhc9lfkOt+eYlPYfOAn\nhhfEeD6BfvRM44t/Jm9uV/A3CJL1wO/Bv2+l8WhNCESH4Q1D74WaCch8pf1P\ntpZSGnvasTp1e3Q6qqSgmoMyi0Gf9O9QD1FmqMtvPLDZwLDO1o4FH+KCpBwy\nInhx1i4KW1YON1/C53qqrYdBRpHP1THfaquzwGKUY8RgWrcnEX9a5elBG/vo\n9hsIJmFby/r/+bW0j0oHn7bmniTjOcOeA0mXmLaP/baRkDGEjhwCuSlA6Di9\nxQH2vgS81dqgHmz9mGZQnWjqNf+LJJliwiSvacq6EXevW9doZ9R51qO5DUip\n2VBoxDyDaYahY1widSHI2/4/y4aP3KyXd1ArUCfb+3flM+RSb9BW7wrADugi\n1TPp\r\n=f4fE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": ">= 10.13.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0dc6dde296550370ade2574d6665748fed37f9c9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^27.4.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_27.4.0_1638193014648_0.035626821695394106", "host": "s3://npm-registry-packages"}}, "27.4.1": {"name": "jest-worker", "version": "27.4.1", "license": "MIT", "_id": "jest-worker@27.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b4e77bd2effb9245171f342bd06c372054b67108", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.4.1.tgz", "fileCount": 27, "integrity": "sha512-cWYUNkfST1i17513Ll3GM5h/lJtYR1hRZsfPL4OQkIBWgKj4kbJREgHhxJxZmOmjKyeAg02HRoMWXr+B/JtFgg==", "signatures": [{"sig": "MEUCIQDiMD6oe1yidnXEQlPw8cLLGv89OJ17zV1dMAjqFDxOQQIgJqbyKuKxpZjpvYNMfnGXNrc1+8WbXtRYx/rXtCFQ2Dk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82188, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpeKvCRA9TVsSAnZWagAATgEQAJqorlWv/5sihgKJj7KE\nhk0keHg17p2/NM05powFNdD2GPWeiSb9iLqptidrEcWsVuiq6B5BpzaT2Tn0\n0xa53/mU8hWyomm7wz/SjIUUWUKDZSxBq79FpekOZAWjljDc1DZg1XlkopJX\nAHJ1vslS7zs0zwsBWq0sPhLFpEazvSn4v4Hjc2L1bV8zijPUMvLMXfCkO4bd\n9dqxI/HqYOL/xbGhn6aUleUPJXq1qlbuijF8NP7dXuCJXJyI06ybT8EhoyQX\npAvU9+RHrhh5M53GP566hxvArtDEp11zHcuE99nLDDpinh8LEmcRX3DBMbny\nJ4Fpxhr1imEZuY04r8JSxYmA5DUUnxxaURDse/tKTnrl5/jigUsMvyaoe1y+\nFVj3oJKWa/Dt2r+fI6g6oVfDPG3N1fGHyjt4JuegRcYCrymgr7FFiKnH/noY\nBjEXuprlmORLTE+dd8W+6K4Pv/OurfL+ctqD2OXzfYjoDrmlcaH/C9Xl3Rg+\nfQ41PJbKCPIXuyXIozw9tEN8hvrvyPDVTymNc/RoTAgkMlnrihO/Ept8+ZoL\nqL+v6B3ia9u3EBSHYzcxOjNGyC39GlqKK+2CAwBlXZX7cDGqHiBfUB2qxwLn\nTPfYtzkNovnUhWTWNFMqXUEndzZ31PGjOLPfkHNakWRhNdoV4xLzsOGqq+/G\nP8y+\r\n=NPQL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": ">= 10.13.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fa4a3982766b107ff604ba54081d9e4378f318a9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^27.4.1", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_27.4.1_1638261423193_0.6325438023665828", "host": "s3://npm-registry-packages"}}, "27.4.2": {"name": "jest-worker", "version": "27.4.2", "license": "MIT", "_id": "jest-worker@27.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "0fb123d50955af1a450267787f340a1bf7e12bc4", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.4.2.tgz", "fileCount": 27, "integrity": "sha512-0QMy/zPovLfUPyHuOuuU4E+kGACXXE84nRnq6lBVI9GJg5DCBiA97SATi+ZP8CpiJwEQy1oCPjRBf8AnLjN+Ag==", "signatures": [{"sig": "MEUCIQC9tidbZQSy0LvR/LfbHz8itL3NZeTwy7jSLrmaLiJ+SgIgFV+knkV208bx/Jws8p9RG22ZaisiKcuU6lgYjgq8boA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82207, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhphC9CRA9TVsSAnZWagAAub8P/iVpsRxiyZw238Wqp5Zc\nTvT2TyvD+auE/bFHsvWZKF9k29Lbs12LSEh79roQ2qiwXtp6+ugfuKuloX4o\nUHmix0AHWbNg3/gmX8cC6o4KzAKrqgwwCyCwWdxNgcYrURUxW7d6GAyy/oO9\ntXK1Feu16J2XDQ+AJDmIlFHp6ALDc0p+suhUfPWPJuXngtA+wdeKpQ8uTHIZ\npvejf5OG07O4FNEzotKu3XIfd60CKfuhO84hbWwKPqIlgUBhK7mmFARKPdGx\noBKgf81gmRVk/jmTxURlpph2rYdhWwdF3HErPNTKEUinQd7GOvznlzl4g621\noAF/Sio4dUj+5OovzQhfWiJCnhAPEYNt6VOJqr22d1OlWbWB0FPHfqF/Jbcb\nSWCa6sN1Giah6fAaC/GO+MOjMv+RQtLvicOE0XnPmBA+KxHnRoRmtAnhq3bw\nVDPEAYZB4rkWrna/j3/coCHTS0Z9nyIhqP/JCABHsOXvYPNEWgIbxaL1kI5b\n5ghYQE3mieo4fp8o5qkLIrwNSofnG+FqBDvsVMUSpIWfyJYq82b/8R8YKYM9\nOYPo1bnBwxEVRNlr53ArpQCrL4RZDyD+myxj8QmbyrRJb0duP0QXumw+BPAi\nyyXsNt81bsTN92rxwcGh5g3GMFocjLaaKwz5EhT7Mx0sPV7D7+HfK/F2fISr\nGj2G\r\n=TosW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": ">= 10.13.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "7965591f785e936ada194f9d58f852735b50ab1c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^27.4.2", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_27.4.2_1638273213572_0.7835163197035273", "host": "s3://npm-registry-packages"}}, "27.4.4": {"name": "jest-worker", "version": "27.4.4", "license": "MIT", "_id": "jest-worker@27.4.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9390a97c013a54d07f5c2ad2b5f6109f30c4966d", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.4.4.tgz", "fileCount": 27, "integrity": "sha512-jfwxYJvfua1b1XkyuyPh01ATmgg4e5fPM/muLmhy9Qc6dmiwacQB0MLHaU6IjEsv/+nAixHGxTn8WllA27Pn0w==", "signatures": [{"sig": "MEUCIDRagofWWYMtmN8Y0RB+WeQtXS6Gm3cJj914B2bGop/TAiEAzcOKC8eW2a0uVIJJvp8alLgOzp6DpI6H2VehmefaJEA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81921, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhstrbCRA9TVsSAnZWagAAKSgP/Aq4f6+d2QGp0Vx4ckc4\neJoYTePBME7hByosgRj/xF/GivndX3Azf8DlNDz7AKMrfVaU7twVGCYbJUPw\nxfHI9EBpIMdHHA3MZRt0/H0QmEjZIN7Q77FeAvHtErAFGW0WhY8ZB9wzTsmF\nsGKqn1c6J51jf8AjbtdU/K195leVGo3krAfz2mXrBxK//wtsi8fzrMakQr3m\nZ3M7Y0xWlzZnLfikBxczBUg/kgxEwpLSTh7HJ4pdm0PEmZZyEVqVIlYcCu54\nOTR/PgK7U4Ze85NeJaK4q7380nkcXlex5iIR8lJh1s35kE7wGdDwtsAV2TgB\nwvMU5iNY8r0R8mrdXXQSv3rOsQsJ0MQHJaHgDkyA6pr/wULNSxtlGCCl4MVv\nLe1Kx4KtCDCV61fAjSJ6yPwzOjrZfYXTAwjPzXSXKRvgVqQxZj5sKYlI/4wS\nmy9VKzbjanHhSBl/3NCxDq1lwJdVuooj7Rm8Es9/fRpUsebm4hva6bJ1z5U4\nKtqLQ5oZ9qiV+l//kssvTp3ziFy6OqavBd0M0phM02PU6144LiC2aI9E4ClG\nFCzmKueDxOSXLofaOKO80oCXCxf7MTRBMQIHpLd5mePzvh9uEZ/OLdCauub4\ntHu/MCARMy/8xOQogpBBxD2CQ1LtlMAC8guJwz2lgMAzCRCEL64jCAxpc0Mi\n4lAV\r\n=IYun\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": ">= 10.13.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "e2316126b2e4b9b4272e5a0b651c3cb5b0306369", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^27.4.2", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_27.4.4_1639111386901_0.12012614282645973", "host": "s3://npm-registry-packages"}}, "27.4.5": {"name": "jest-worker", "version": "27.4.5", "license": "MIT", "_id": "jest-worker@27.4.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d696e3e46ae0f24cff3fa7195ffba22889262242", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.4.5.tgz", "fileCount": 27, "integrity": "sha512-f2s8kEdy15cv9r7q4KkzGXvlY0JTcmCbMHZBfSQDwW77REr45IDWwd0lksDFeVHH2jJ5pqb90T77XscrjeGzzg==", "signatures": [{"sig": "MEQCICyLjypR9HNjbd9OSLTI8CNwwW2o3FPojMPZ+zF6/HQyAiAD80GLiwUbGtg43h8j1Ha2QkdpWoHDDIjVoRmDpti1Dg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81897, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht6DECRA9TVsSAnZWagAAPecP/2k43ZRJY0NGs2+lr+ld\nPNGu7SbHiy4uXs4KXgOdaW4FtMzbZUPyqLveS1cmyuOvGN+z23PSQmmGtWMH\nx6ICCbS4hH5Z1xiR5hnhIX41P3gDPjR4FwHunm/JCrnBu+zxz7m6Xc3xLmSD\nGSe3HiX1LDsU4mWxRne1nYepNaP8s1w2L8eqGYU7LK8uOBTvPrdMu2PQ13OT\ntwJi55SSLlj3ZLhmB1+zJFX9sB5LXVAcm87HpzmWQO5HhKCXrfeBpLg6c+9k\n5HPZlyoiHkUKaSrzt4hGFGMJ6vsbDzOI060/HKFVgB3qLrvSGxKJlQHe8EZ3\n8kvN1l5BnjPj7eMLqQOe9z9ErhyrDor/RDBL8IY96VDSNVYKXnls4eRLI/uH\nZikeumbxj3byOp1m2lPWHKLGpJdVE7XWIfQu4Yk7azs3anslGCsntzN5ZOW4\n7QYuHaVchvT+iLrf+cUvfNlIxb8WHYJtYm6zI06WlLEjcsYyx0U7ejqiYGj/\n8B76uKCni/SsCQSm9yV3QJeT6nFdapnDJwKvSYKaFfiSN+wof3Iqi7pFK5Q2\nJPgb6Vz6Xpn0nghg+GgTIlpbL2EpXuZAFbII41YBQSbiFE3LDuoCQFnuHMzJ\njA9+PBGNwKEO5Mf5pvUWdbtLpxblS8HTyzLaiVsvIQ63B1b+VNf47R815cA6\nJ068\r\n=We/P\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": ">= 10.13.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "73f3a5743b9e5b16c9b7a69e2705f07ba7a010ea", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^27.4.2", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_27.4.5_1639424196786_0.9654374093533502", "host": "s3://npm-registry-packages"}}, "27.4.6": {"name": "jest-worker", "version": "27.4.6", "license": "MIT", "_id": "jest-worker@27.4.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "5d2d93db419566cb680752ca0792780e71b3273e", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.4.6.tgz", "fileCount": 27, "integrity": "sha512-gHWJF/6Xi5CTG5QCvROr6GcmpIqNYpDJyc8A1h/DyXqH1tD6SnRCM0d3U5msV31D2LB/U+E0M+W4oyvKV44oNw==", "signatures": [{"sig": "MEUCIArPDbDgOsCcO+yI+dlfvGbd/JozjZIgPeCafyrs9tKgAiEA/QbSsvu5qpasxu7BSJ0Y2cxI8oYVUj0REA+pag6igZM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81897, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh1NJDCRA9TVsSAnZWagAAJusP/RR3yHXieI8xaoJk4VGT\nctZgTGoyvf+LJevC2LG7Jw+2eQOOL0SmuZyprYQLVVM0a2Yv6IzUXiKTTWdM\ncCrvVHF/3u53VXWF804qUVy9nvhmeOtrXcENEtHzVFabCLO/AM4+rv8It1fy\nZljfpzopxSMfN6RPaYkWvT3HqoyTCdZr6AZq8+gba/Z++DTTrH1LfumaYtuD\nr8+Pfv/QHv96aDqVinmLJDI99VfMzIIB9jsY/1c2W4MgsX4w6HGHOa6zx6y5\nhFSOHWb1eAcyqc/sOH/D3tFsoZ6bVm76gGPgKLt33YMn+PtutPEwCJXIk6hi\nfsUIsULcuY3xDhM6yoz1QOu9W+5nj2h0eZj6EP/xICJS0pXUE5CcmE1NRxIs\n9msfAAXWK5DtjTntnRW2ajoacwL+f/E2MvoQ/I5iJm1VvH5kSYCdO2etVvDS\nDIkFtoy6eHKcip1kTexlF49PXGyygNNoedaWI7f0CPWtCRfbvdkGps/ZTw8F\nPtYYa1LHRIvzlKUn6S3TTtpxWVcN1VHVggXdis/IEX13dz3tcw4T2+2ZyoUY\nsEzpLbBBmRjQ6UYavRGRk/D8xfkVLmjzZrFp3YivHD40ugF7lxQRwc/iMovL\nLP2AS8M8AmJrJyafLleHqhLg6G3wYfmDJV9jqrUx8Hm8oHIO1vbgPRtiTaM3\nIohl\r\n=/Xzt\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": ">= 10.13.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "644d2d3e53536b0d67e395c0f35f8555a67beb1e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v16.13.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.13.0", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^27.4.6", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_27.4.6_1641337411581_0.48958288162379904", "host": "s3://npm-registry-packages"}}, "27.5.0": {"name": "jest-worker", "version": "27.5.0", "license": "MIT", "_id": "jest-worker@27.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "99ee77e4d06168107c27328bd7f54e74c3a48d59", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.5.0.tgz", "fileCount": 27, "integrity": "sha512-8OEHiPNOPTfaWnJ2SUHM8fmgeGq37uuGsQBvGKQJl1f+6WIy6g7G3fE2ruI5294bUKUI9FaCWt5hDvO8HSwsSg==", "signatures": [{"sig": "MEUCIQDk9i/ShOZIcQwjikNAS7+wpfoIFznG9e5FZmVhwsQCCwIgcG0Y6CJUNnq5p6wx0s3Ha55dtk6VZXVWRgSByBu/z/k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81897, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh/kp1CRA9TVsSAnZWagAAGyUP+QA8GdDend9H6qfoYKCZ\n3QAzCbD8oq0mv03ixBx/j04ABEpOsXkUmMmMxv1AXSIUedvneIjGwS2AZTw4\nUtMbKMS7lZgkmGh7tdAn9cYf0HJGq45oyMKRuu2DYaciEGvkhEwRAjoBeAcp\n6U/SwTo6f7ACvedfN2YqG6Cw4X+XMOWs/9Q3+jSCN3w7+Br7KYNpW2+VaQf8\n8ggQiy8Z5TK35puL+KtrEP8i+GX2S3OiLTeHbnpEbyAkYmMbKABX1BuAoCsi\nt5QhgBCyQHnCBYejHe8TMtvUWuxvreteQsv7wP3tiJdbutrOk0Th+uMzDGrS\nfR2xqgxrEGUXSuvR53cAH9g3UeDRpxXAyDX0JUPCCw0lRdjHsfwsp4QstNn6\nBwNbeupLrwLU2pk9iWR8g/SU3nJIYCTaXofMsuT0iPxHOrpexf8HQmtvDX6S\nqntvbMKAX1WKwKRBPg5IWw7MV3Rm3B1z50d1zRCNY3VOgmgQxnm+U5Oo2PE1\n7bZ40gQp4OFZjvHJkUbuif64gkM6hBuQlaSHLUCAIEx1okpX9Ce9HZ4QGReJ\nfhkEpVxbBah5nAXXBVoDD40/oAqpjA7woG5ObzjKEVuJd1mJDVoZdEAlhm2A\ndpItuVnzD7DlVZExpFtojpLybcJuNypMKnY6y2iamKfCXhqsODmt+82Rnuge\nl+rG\r\n=2uY0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": ">= 10.13.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "247cbe6026a590deaf0d23edecc7b2779a4aace9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^27.5.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_27.5.0_1644055157828_0.19170112922657911", "host": "s3://npm-registry-packages"}}, "27.5.1": {"name": "jest-worker", "version": "27.5.1", "license": "MIT", "_id": "jest-worker@27.5.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8d146f0900e8973b106b6f73cc1e9a8cb86f8db0", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-27.5.1.tgz", "fileCount": 27, "integrity": "sha512-7vuh85V5cdDofPyxn58nrPjBktZo0u9x1g8WtjQol+jZDaE+fhN+cIvTj11GndBnMnyfrUOG1sZQxCdjKh+DKg==", "signatures": [{"sig": "MEYCIQCCq2lz92y80mi8awexPCHiO1Cdyen1lfN4haLhzmjNDwIhANfu813hQuKRtAjQpZPePIseKBxkeB1/uWOBUvMw4ig2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81896, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiAktcCRA9TVsSAnZWagAAtBkQAJa2G/opVt+uD3zL341S\nea0V0S7zC9p2rEEJ9u62gKdWn56ldBze9nDQAQDq/0ZwYKIqvFGdTLjniaJu\nqpL/TozwtO1cgJonRkp+wEoydcf187FMKvoH6vTxRzxbziosmL0orjIqmm/0\nkm0UcOIxl3vicFr1bxlR7eQnYd7En5UGOui9xogTkWIaVkwwtja5yGKSVa2I\nnUajZpOYqskD+HjC6xRivyM3BGmzBv/40HFVPqJpdKHkdCVC9eRZiUzT/JfV\nRhXnTU+4KRBZ4COD9+4pYX3zeP+c17RxI6COHZ/oEs+641lxPoCESIZ0F+BA\noxqWEsPBaUa6pC0sp/E9l6dVyomKNATRDGv5wjNMCGe5rfhVRB1zUJVX6k7K\nw0NYbrzg+jQUlTeG34uW8PJqp3H8u+in9mhdpZHevTIfqbhnpF74Vhb1/s2y\nBqqMJEYPJXQnprxvip/KRQF2UeqEa0vPB0dMDMdmZeT7D79kmnPtlghlnSqC\nlG3vbcEg+l0Z1D7up8CGbGm2su+fjbwYKAKmCBQJZCo0VdVfX4Vj3rtSPE0F\ncTQEbGEK6PmYl2vPIHf6Gxw6vv4VCXArQT6+8v/VCiTekLJtgjN0XC7Fmnjf\nMsTOAjs60nexjkaW+pybuCmxuFAM1kEkuswBVxKhZv+H1qJymaAs3wDL7/+X\noXRc\r\n=2mUk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": ">= 10.13.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "67c1aa20c5fec31366d733e901fee2b981cb1850", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v16.13.2+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.13.2", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^27.5.1", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_27.5.1_1644317532612_0.34163065851331864", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.0": {"name": "jest-worker", "version": "28.0.0-alpha.0", "license": "MIT", "_id": "jest-worker@28.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7792a9c3d60b82982b481720711a132062219d62", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-28.0.0-alpha.0.tgz", "fileCount": 16, "integrity": "sha512-fEsFkj7v7hwRaXugjxz660f5ToRkQoGEPQRRRYBcVTVu6QLhC5KB3RiwDSDWEG7YRnweEza5vEKDx5BiIuaIKA==", "signatures": [{"sig": "MEQCID8wTRxP2w2Li4/XfEA91cBDhZx+UvMRJBjzB2dw2IsmAiBpQgO8gXfvAL+f0ogR782L9D5ijvbHw2hpyjCGnqC7dg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67795, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBVa2CRA9TVsSAnZWagAAdtAP/2xrA33NylQ9LZCnjohj\n43AjpphxVYrdWBkLjT9pcRTqcJetcFx2yvtRXZJsZM5+KNZ48BncgqoAvD0q\nUsLfouEGl/xdYstdl503zzhp++BghLmy+ewhipgpE401SxkmvtaAa7giF8SN\nBrKH4B7BfOdwNl2riQbnlCiFjfbqMHrQhSkdfNetSuGpmsZg+CUU97v4W9qD\nWYZuvjRWGpBaTK2548zfdF4bF38yeh7ndS+OIkasEsjuXqdJy6Mt3P7xK/gc\ndU3fVF6VX4GPT2LzudEaALdrIzYr+ObdPvDvXVv4/ZPfMZI6U1EidkEn0xVi\nTScSm07vOHbjbUzvyDZ4lnrmTokMGb3FAB4j1GnqpL+s19Q9urQyC1kezh+T\n3yCQy8WVbYHJ/nkofU+6+n9qsNUanav/soLabHDkQ7zOQ9NOHJrxibbanx1a\nayXUPlCWTF2DtEyZ2p9YcBUSb6+1Iarn3FmvHfi8jZFeuf673oYs/yqZkEK7\nGEEhahFTFoCtOSP5dgaHsVGQE6b+deEfB1ngzpoSAzmSAgcB3PaGgetdjH/9\npScS9DjZd70cX5NQikiXmKuxo6Nj8GbTw2s5WFElsDylzx5TaJiP3GDaREWx\ntPDoHQmjAPq1RjLkIgKptP6/AGIDOqcDxQxq6Wbn2WRn30x4MYSyAIQlI2dG\nM1Y7\r\n=GvZX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "89275b08977065d98e42ad71fcf223f4ad169f09", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^28.0.0-alpha.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_28.0.0-alpha.0_1644517046406_0.8770687766661727", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.1": {"name": "jest-worker", "version": "28.0.0-alpha.1", "license": "MIT", "_id": "jest-worker@28.0.0-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d8244c5f703a82865479d544ef913874652e5ab6", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-28.0.0-alpha.1.tgz", "fileCount": 16, "integrity": "sha512-2MJb3Z8+KvkFABTP0hTvGN1sF9xlIB2BaYi4oWU2LM8ht68lJgs37Q/wuE5awJSIelYyfM5nzPaV3L3ThwQ3Gw==", "signatures": [{"sig": "MEUCIQCKcacf3lMVUDkIhNUC52+QHtMMIkYUZuBwwJt+xmCUMwIgayzKMVTqxiBXrI2GhotsizfnZ/CyoOXGJYJBH2Bez74=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67795, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBqZCRA9TVsSAnZWagAAOiIP/jifwkBFl1+S7z6d7GEj\nhFeukkD/yMzcAnH6KghKCZk13Lzmo0us3IvAmJdazDXmTgulef7Pvt3K56aC\nrR6NPJZ8XrKBQWTryJOhiPjxSAFU5f9Fmt1SuV95LHSFRrgA1gn75jp38Cny\n3orzXpf8TSfDSo9cvM2deAPxPFTDOeRTmh7SeNCutXw4jczNDTyaSJp0G21M\nZntZaqEs4sBuupFxBr31yVMPqRPsVrJm1IUFVXwn3sHohnx1+5jAvJMX2SUU\nJIgCeKAoXXnv1/cqS4zjOGSdrzlyOV9AsnCEsBQiCUwxdjGglQML63mIm1mO\nnuJg3nqN6NpmmTxgcnAc+LgV9urQdXUNUDYlu+lH8T+fJr2yJSoxucx1l1Hl\n5K+M8ovY1KrjMefUOCUAMoVomF17T/b5QNOEXrDIUmwleWo1Amsh+qTyRg/g\nDDL8kkEWoMepRy5nalCMc6VPCSpM2ipCIoueFlc4QSjtyM6OKzIGpsSHlQmw\n3wSV/fV9rkdRfYvuhJvJU3QZ10SXQTkdaBXShDYZnDI85/lvXoo/Anif4U/S\nAlragBQQ7jLDR4zNTQD4NCGPFq1cfEZiBe1AUw2i2fJpTE/pXv95RIs6MmGv\npi0Azzk1BIv28OP/FRVA1wO14JDZBw2hgWG6wM2JT/BbQIduiJqGmbb8LbXP\nby+L\r\n=VsYR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d30164dde1847166fa0faec98d20abffd85e6ffd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^28.0.0-alpha.1", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_28.0.0-alpha.1_1644960409254_0.06588631679319801", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.2": {"name": "jest-worker", "version": "28.0.0-alpha.2", "license": "MIT", "_id": "jest-worker@28.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f1ab0ae43f7033d326dcd4824db3fa0dc2b7e083", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-28.0.0-alpha.2.tgz", "fileCount": 16, "integrity": "sha512-GaSmjjI/ce4CwMCgayZWcB7aHpiqXdFOtTwkQFVycDfD1V/ykNs8i7ls95BVO7hbkrLf5NYJk5PXl7gMxT964Q==", "signatures": [{"sig": "MEUCIQDytEjdYa4Js+5xW4BbbEqf184ZdynerpUv0oIfmL30rAIgUtplKsgBC7JB9AYbxxtBcrPf/LdtfdGll26siVEz9l8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67795, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDT5tCRA9TVsSAnZWagAAzV8P/3RQ+3k4h/IB9NQsh+r4\nen2fjJ4HizlTH4+vxWU6UajhIUBJI+6KKMMHQGz8S8gliOCv6S1O6cdlUSn3\nRaKQ9rEN90e4Bkrm4X2Nwr2kBpxMTAMu3M5P1jgKIext6hqHX+OhVMCEOMOx\nd2vZM0hAq2pXXN6v25iex/tcgmNpESi485Aky+9BpJkkrZl+cap6F/r3QqA8\nSFmslS+84ZF5eDbP5aHiwkff3rJQE9SoDjcBf+4xkv5QutF4H/+mHDXlHj9i\no7Hj6+qZQRo77UwtHUi7Tqw0otuY2/UBdjP3u6zDSqd9xG3Q6fcwvFfgoM+S\nkxTGvhoPrCVpT5T14lj8SgeLoRNoSoJAkK0WMCa0kffBLV7CRVtGOUeNglGA\nhKMoSEpuRucqTIvEX8o7st2K0012GkIXkwk/dqDGyFOwIwifm29bI2AQccR+\nQKogc+qet07LHrTvaZrwskCzCC5BWDRwHF5TRdJtWzHG3pfX6m92M6O2bOi4\nghGT75OQCN5lbVPmHMmc/hFT1Wqc0zr/t1CPs8qK1GBHrGZnVFMNLePzLMp2\nf4zBSn8y8MrP0uw4NFz2I0rIMbk4d8jkMScppOtf1r4nc4+ojP0usImEMKsX\n7M1VuXFMYhRFAeN5jDxuNbT6yihN7skqwowOQ4yOQMOcXsnIfL1owYPI/2F5\nYnOv\r\n=trgY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "694d6bfea56f9cb49d0c7309cdbfff032da198c2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^28.0.0-alpha.2", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_28.0.0-alpha.2_1645035117302_0.4398803279230663", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.3": {"name": "jest-worker", "version": "28.0.0-alpha.3", "license": "MIT", "_id": "jest-worker@28.0.0-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "f1ec4297f97478f72beab6bb6df06bca220ce8a2", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-28.0.0-alpha.3.tgz", "fileCount": 16, "integrity": "sha512-i0a/eoMlbMvLO3Ol+GMdkojg9f2g3zqBzltb07BSJf9qG55FEYHu0ZUvEvrn15Zd+rte/J86nnvccJQF4lSnWQ==", "signatures": [{"sig": "MEQCIEvsG6jL0wD7srUdXun6rKfN3X/JaXe42qB/zqprFVBzAiA4qVepLmmD8PaBBxNVY4Xdio/bVtl/+drvRrbDRXln1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67795, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDmzcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoeGQ//QiBUJxdKAbMhrkiplDz9jGkSGZlpL0PnPFODrb/XTReTJ9iJ\r\n0bIVU9Jc+YYBqaxYUDl3/LcJjtc7JdJtb9QLiteOsMwwarb9GrjmUvEOlf0B\r\nUVt8gpvTKT1ZzQFlRyHnmyLMRxFNVYd6GmqQMOf2Y/PgK4Yi+W0FAHjQib6N\r\nkMDyv3VYSm6wQ8fYfNe1gWtvsSRZ7Ky9q2yV887Kgo1HMcnBVSa4K+qPOcTz\r\ngw5vmOICsr/cd0z9AGXcse3RFrsmLx3iCkEzAk2xZEzcr7P0hEuwIRy8INca\r\ntUce/OaAlKMSzjAWg+Dct/zW/85hZfrutmTQ9wl0G7objHePS1u9JbROTWq0\r\nBPJmWVZdnOmwucnGbPDXSoXKhgcyz/WgyVYFQv1QPF45dHNV6QMYjwnIowvv\r\nlF+g3JlfuLOFizKhKoETCZFwJ0UaIwNCqtcpCR4vOylQ9QNDaGWiyodsyjdn\r\nBYuZ0r3ZtnIB0dtcUhyeoJVWCnrV51CazACZVP5pcDvFu+1eLC2TYq2gqmQP\r\nckGqvJ2U9RNTSAzN5SaqVAm5im4uua7zd5eLLf/MIN/TfkoWxXNbQxMV7RLb\r\nPaRmOsu9f07gTk4cVBO44lO6kjA8YGOq2YnxMz+i6JV1LZzHKHa31Cptzcot\r\nMi3xmozW9+Xaanal9feWuEAqyLlnCgHWK58=\r\n=2IhO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fc30b27bd94bb7ebeaadc72626ebbdba535150d2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^28.0.0-alpha.3", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_28.0.0-alpha.3_1645112540768_0.7222916804822315", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.4": {"name": "jest-worker", "version": "28.0.0-alpha.4", "license": "MIT", "_id": "jest-worker@28.0.0-alpha.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "327e27cd12b432390bac113b189a6350b2b35f8e", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-28.0.0-alpha.4.tgz", "fileCount": 16, "integrity": "sha512-9tRA/WxudWTULvYf+GMoX5f75xKce0enUbtKEs4rfpYsfGXicr2WXxL4TpJMKWB6v1EI9QAOl6RjdBh9mzSYdQ==", "signatures": [{"sig": "MEUCIQCsvbtsl4csmFiUnishttVW7f4iNSZDLg/Pp+t3hvZPKAIgXSZdsFTnkZyrzJs/WIkjueE1E7Py4mmAM/em6HqcDDQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67853, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFNOCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpKdQ//T4hK9G2DU68mPNtHJ4qwKFhq8QAaUthlKVq5nO1zoP/WW2gb\r\nnH7B9xv7N52oHjYzRaGSVDWh1Qkh6o4r87Gyr+UEqkUp2W3ifd4G/8t2//jv\r\n3b40YSf4dioeUDd8XyMm2cRFAFFytxNWXkYDH96iByAMmMbxFPcsczmc3hfs\r\n4QBiRbO9hAZc0mB3dLZVhB0FiqGVuMTtMH/JH1MR5p7xf3KmyJv0KALq3LWe\r\nr+g1Nt2MExvkOdbMGH7X8fRm32j7fWzu0gK4MWRYgvJkafBQBUFFHTt3yzUu\r\n6pZDUFdZXk+p9sKO8S4zPzCYWxeH8/p6N7CF49tETNaNMAdpnXNn3Mq7e75/\r\n2/CAX5MqHgTPmbypKTuAjcc7TLMQVidRroQG4y21CzQpaWDutMuM1Hfk3WYL\r\nJAdmmE2kYt7mlijCNro3lIs3ehLOPAE1+anSmYD2EO8XzVEev4R0Fz2CMLtk\r\neJt4hd7ZVXlqLMDp2E8YyB/EhnWR9cK1YGCYibBr4RFu3HF6qyue2oqZAEyn\r\nNprGMZ4qfqfD7505LIWTnxSZDDjIPctmWsVcatzrLZwvxcvEzJGvnnwVajxJ\r\n5FUo9w9/i+omoyRC3cAj0Q5rwcLJMY1K7kxys7gmcwzUAeeT9guDQUV4/JOu\r\n6DSNyBOP7/isde5QigzZ6RApiZI7htZZ928=\r\n=Aeg3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c13dab19491ba6b57c2d703e7d7c4b20189e1e17", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^28.0.0-alpha.4", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_28.0.0-alpha.4_1645532034462_0.5726474561895478", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.5": {"name": "jest-worker", "version": "28.0.0-alpha.5", "license": "MIT", "_id": "jest-worker@28.0.0-alpha.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "2e6a6f3b24702bbe8e7f83a257f90d0f0735f030", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-28.0.0-alpha.5.tgz", "fileCount": 16, "integrity": "sha512-YGSvc62CdAAPKCNHDXo8L45FXATSbKs4Ao9FVcj7ESbSshVp0yuzC6uWOcJXN9Ju6BBotgj/QZw+yC3VlKvWgQ==", "signatures": [{"sig": "MEUCIQCoP7EAPend6u3pQBeWGIyjMIMyUG5AHcuwLgE14/w3lAIgKU2G2a5uijidtDD6aFTWrOAxkoXwjAWaG5SencZTIgk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67853, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF/EuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJTw//fqA9dn7++dkm4IZyHCeLeCzlduD+gItE8Z+sfiH60SeAAW1H\r\nR6Gq4EUowJfhdClz5WXs9r7cDyDmUN4yOMnRrsfqZ037Qdq1awUUYKi3XFBh\r\n1hbb8PtIC03tsETS6VPFcmlzLqRslp/B9t8JrRBrLA8QI5GCwWxxV1Vk5gp6\r\n2ZTmVjD5QfJB0/8XNlnL4axGsJ75S5EVs3YKj1H0cAMeXeXbQl3+75v1jvs0\r\ngidzKOid3/8UYB/4miSmjbSWLjwQwPpnx3OpJgFV5KNfmTEp3zTmirVeAqkf\r\nrlj43d9oTKgh7yqo7jCX1JpSh6ePWs3qfKZ9CXWD6/5buGliARyJ2ehJLJcn\r\n/MDIuKX1pWSy8WQWMJpFTILPmQUwujgWoiQze8NM+aWeizOcm2+qujUZ17zy\r\nSk9btTv3/zMplcD6QzU5a3p417LRydqylef1q/JLK4EZb9h9weiMiqvzKBwR\r\nS8/uDiiZZkQNn1a+pWBTaNTxkxsQYXYGzCpPoikxQFcdcykKPsQq1lHhmYI9\r\n0twP0CudorEvj3vdSulH1EcnRRPsM/rCjoLs5dxd/0v+Mx99sPTjqlywoy7X\r\nwBFNVto/+R66iRPpfMLSLx7ECodrP7HpYj/aN8JulMYMB7pcyjsabjH9qmFa\r\neiI2zUhhyDHAToA/Ao0mnSkUC3Ad2tx4Tj4=\r\n=XHdI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "46fb19b2628bd87676c10730ba19592c30b05478", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^28.0.0-alpha.5", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_28.0.0-alpha.5_1645736237889_0.37682331672133884", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.6": {"name": "jest-worker", "version": "28.0.0-alpha.6", "license": "MIT", "_id": "jest-worker@28.0.0-alpha.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1f0aee9417506882f98c30f5baec12a99e2ffee1", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-28.0.0-alpha.6.tgz", "fileCount": 16, "integrity": "sha512-OLLFibUkznpMsZSMUUmoQy/ZQJ0bCtMXPAbsmP6XJ1gi3nFmvLJ+7EXxL0v47XBPnENWe8vVtHeHpk6dOUPHkQ==", "signatures": [{"sig": "MEUCICH8pRG+Z2mPkYVzWg6307xXFFDUbftuEJ34iL5LzJ+XAiEAlhFtqOGag0skEyGwkk0NYUm9PFWjXodDndX5CyU8Fjs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67909, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiHdoWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpdQhAAi8yrmrKQ7PPBJTjIwq+F6c4HqY4UqbQmBerni+nEKmKpu3KU\r\ntNR8Gt1/D5xb04idW1hwhKjyTfN+Z5Q4Svf96bb9opU74OVriUK2lxl2ZCKE\r\nTv09T1jNtztLaGv+mcBPWS5OYDCJA2SidrhA6SHHHx9eW+9jgX1sBTixWwSe\r\nAEB74VtyS82q9uzNRvWrSvgtyLsVJ9Y2aHYWvn4vePwCiT4JqRH/thjK8kmH\r\neI2Tn7pFs8Flpen2s/G1bazXviAmnT2Q0j26gmjHh1jjPc7uPRqljAUTv0lu\r\nbOMVcj2YoLTC0TECGY1IfIoW3OFNiJQW6T+045ADMr1ztBvnF4OgTNo+H0tg\r\n+oYrVeZDlhHcloIZMx7WSr+DYb/MuozE0lvNZHQ3RKWl6szkertRFKG85tWL\r\nCajDDVpVNan5lR/Zu3fDYMD2S/R4jRaSNpnbOOng9IcUef2KgTlt2uKI7EXC\r\nQOfFi/y7C1QNBejTJZ4HYr2aWr+QABerav18vSiZa04+kE44ubUD1Q5Aq5Uy\r\n1V4mqStVzD41BFSDJznHRiC3+eVCagYtEK24awrnmlxjmeD71eFnCtnHU7Ls\r\nv2JPfo0dfzZ56nPt+Yj0nJBew3wzIj8gIXHcFZJcmFD0sC2VdFLK2eFNeMQj\r\nIAjmhfjBHi4eKy7uN0kwads/0BChIG6WFWo=\r\n=V9kE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6284ada4adb7008f5f8673b1a7b1c789d2e508fb", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^28.0.0-alpha.6", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_28.0.0-alpha.6_1646123542369_0.5811610245597727", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.7": {"name": "jest-worker", "version": "28.0.0-alpha.7", "license": "MIT", "_id": "jest-worker@28.0.0-alpha.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "119f0a3f325742d78a592f1d492c26b36772bf17", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-28.0.0-alpha.7.tgz", "fileCount": 16, "integrity": "sha512-B76riuLsblIfTJFs7kfTx+N9J25lRlLdnJAY174/zOLPGPII3zb6ofJM4TNp2Kuc45wgWvrwqJ5e02SnzUN4uQ==", "signatures": [{"sig": "MEUCIEiWjhbuxritSCijuB0WmNyn6Jn4QfgkV7QthpgtC681AiEA7xk3ZhOuiI3lLnvSFJ4s1UG1KAXJaTzTK/6d781ZcY0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67909, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiJIa/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqxVQ//YP89ow7GIGR5xrmAR3Mnlhd/5x84V9ELHqHct/yzHy/fpmfn\r\nsIrG/CGhYgtKAEIa0RqxuxN3Q5ibetVvSY66LF9GTjyHyC0Pzn03WVbGoT/f\r\nPAMFpcPCEsj1kiG2Rab45WT7lcuLZRawf+ZQ7XMbx5hO8P/oX421sZRSVTHM\r\nClwmuVV9t7HF36Z4RLUmMS1odUq87TaZvRHuedcUk56I+xk7lWgizs9DGNZh\r\nQUvhkMY+z3onMGUkOtfF7tBEbMqKb5S5dKrYBKcnjMimmZjWXKcpDJEVrfPB\r\nc1gScYy1ZVBtUhy1Zmyg3XSb+r4DYIWTscw9DOwVp3ogQOwXTAJos3HSJbwy\r\n8FmLjOhKEfI0FgGzr4n1s0cnkqwtLvuvFlIAL1RzagYdCvLcNqeAgViHc0Up\r\nCdN/7iFKefs2Rf84E/tF36EcYIRqhbYz5FWtNV0+5vrRRuFKl9VHXpG7xyDs\r\nDDbGwrRkIxdkD9/eJXh/AOaBopD5q0drBVQUqfKBom2PBMWd3RBwQiGt+5pd\r\nPkSJC7P2HSjNHQtIAYqNOgobfQRVOzpeEWMMVImKBiZyAQfCuL9QZIhL5+5p\r\n7MB4EJUo+xhVdh2wmq7RSVlmaSkbtHkrNTYLTLuDLc98dvaZRo3kmWCkvyyT\r\nRAUhy8KxwXDXEiJ7iz1Wp+hRFOyQ/DD9XfY=\r\n=RPB0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "06f58f8ca70abc9c09d554967935b58ce85c48d6", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v16.14.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.14.0", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^28.0.0-alpha.7", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_28.0.0-alpha.7_1646560959594_0.013584128255585082", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.8": {"name": "jest-worker", "version": "28.0.0-alpha.8", "license": "MIT", "_id": "jest-worker@28.0.0-alpha.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "25a9436c5a8f009dcf38a3a1d068ea1ac137441f", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-28.0.0-alpha.8.tgz", "fileCount": 16, "integrity": "sha512-5RL06QlZZsZIC/eAGHiZzLaYvhT0zZOAOcAIEBbr11NQKzrQS3CeJgTcJd2mSD2q+wmkC7bJFLu1o7x0JoyT9Q==", "signatures": [{"sig": "MEUCIQDT30j5ziSvZajVL3YjorYwnj91gXhPyW4PY4zUGbnL9wIgMJH7n2s34jkRvKoBV7r5L+QbMeNLKfqE5C97QBvp9ok=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67909, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTFlbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo2Ag//YDRm3E01tUgCO0lt3mCz7ugKhCLmyjytCMItL1DVwDsLDPBx\r\nw2fM5G07/QkBQw9HvgEaz3Up4y4f+SjieEnhziowJ9VXMkRdcOjhF0vuPQCE\r\nu217ykWjwO4MdFV8icvS/YlcF6e4jPstiinRUjatczg3u33iJLkzwSh3i0V7\r\n0W4YdSTozvj86kGTmY/m+yAqtmLkcNXdVvCIyl4W0SFNkqj40p5AxX77G2bA\r\nZ8ccy7e1nSKkdS++GcK6xU+GHEge292/67m7tBG5h66DZ+IfRmgIMpr+UC2t\r\ni9pWSFxM9o/8CPsMZMJJL+ZuatwUeKFuqw+AjmKc6brfr0UuuftZzJdQ4zbD\r\nbNO62iSYZpTFHrbbHLvUumdcHi3/CspdA3/1fV+nlHANGpRQ7LVzhlJPDfqX\r\nUUuj9WPouQvHXCmPBI3BWQv9FZ9N6QNDFYlRZ1xUQSoPSNTDMFRbMd9b3acw\r\nwZhpq65FH5BOKuR70WhDdL2jjMRmyD430YWd0KlqX0RUw2zLcyg0IswoHHe1\r\n+epo6T2d9/mH3hgyFmpz7x/tKYFfziZT/dY1ccyoDnwnrnVoL2Ai9D9r/ufW\r\nX8MhMHJfilxnQDFWwRxdEE6rNkjvxwb+tOo7dsvMj2TOgRcyDS8XaLYcpn3O\r\nyr2lykYmwYWsAT98bXABLG+DRHf+dTpfRjs=\r\n=zgZz\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d915e7df92b220dbe6e124585ba6459838a6c41c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^28.0.0-alpha.8", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_28.0.0-alpha.8_1649170779602_0.8379601442580333", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.9": {"name": "jest-worker", "version": "28.0.0-alpha.9", "license": "MIT", "_id": "jest-worker@28.0.0-alpha.9", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "6e435b24e2813f35873443dfe0d9ebbee9dff625", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-28.0.0-alpha.9.tgz", "fileCount": 16, "integrity": "sha512-8oMbpvIYKdQbuorxxKkUIsAaWCia3vHreBEFUO+i97kTbGyD7++z4SSLmqv8+5hO3ddoQwdFvay8WaH9s7LnyQ==", "signatures": [{"sig": "MEUCIEq4MdJAea4/FoTdNT9X6ansIqqldQc8cbCSDfA/gqJ1AiEArxhe7gmJRya7eJ+qDgR5OcsgRAIc5wf5E3KuosSTQ0c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 67909, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiXpYCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoVFg/+JaoxDqkNdlUIV1X3F4fM+fc1HULmsfO/FAR89caXEcab98KZ\r\nDRaL0POpS7WWICryWehkPGOGkwqJKWWfAn4Kc7CslO6lBaBbdySmcT3q0KeK\r\na7knnEVWX8WlJAtULo7jp4XWrZlgKPEiC4k1soqdyvG3ensVB/R0XHTNaV5U\r\nHL5iK1CgqabWEdq0Ma1MxGqz863ssMvz3wM9N9QIQ9WC2VKnVcxh9dFWjb63\r\noQMSbZKD8wT80XNKUPpJb8WRdD1DwSaORxYGB68AjdHTmedTwq7eiTEL6sOm\r\nT18yRkChL49J7D/jo8q6NODxo8nbssVcAJpfR9r3wtSGAWRdtdpWzRPRXBsB\r\nq9uAWXg/V+sSn2fMJSdzCDlBLQ64na2tI34BxKTvakGoRTfxf+jUF00ZaW00\r\nuraSbOOd5jg7+j3gmla1+rVJVVOMfWhLUn0AOcbGi9D7PRVMefTkhd+n7uaU\r\n5ogclNjVM6JWo8Y7bTJXvbb8yKBIBU/W3iZlaFzq1bCvmC2yH9pfSBK9oX0x\r\nInl/g7Z34o38NBqYyLquxRgZUBIO3xBYNOq7Weam91pGgdabDGzlytSyYgzH\r\nevGqJUuj1/mNRiJrTRE6DV+BDnTAwWin4c4YyzsAjk0vNFefTIJeJZo+Lhd7\r\n+aOb6cJmgANQjYfFhtTrvl0TiKw0cexnWuA=\r\n=V0m0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "7c63f5981eb20d4b89a4c04f3675e0050d8d7887", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^28.0.0-alpha.9", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_28.0.0-alpha.9_1650365953922_0.44682217621189246", "host": "s3://npm-registry-packages"}}, "28.0.0-alpha.11": {"name": "jest-worker", "version": "28.0.0-alpha.11", "license": "MIT", "_id": "jest-worker@28.0.0-alpha.11", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "1a473675e67a8ee9ee4b912640d54c878e9dbd0c", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-28.0.0-alpha.11.tgz", "fileCount": 16, "integrity": "sha512-wK4/vE4lqAzLp5e92HlXBj/S8q+pmsnZZD6pssC0f38rop87Li3gAT5zcVIccWOPVI9EUilvCs/qeTdNB2M9Hg==", "signatures": [{"sig": "MEQCIFwtVrJlxQFx1aMMBPgRigTxYAySDCMhhu3P7VupSBRNAiBWxPBnrgXbOd1HdGNVfRfYJiNGNipWeF/7JyS86bFlEA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68128, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiYAsRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpUHQ/9GAEfkpmfIFje/aGz8dYR480ofX5xj2AIOFlNqCCCNeSe7gJ3\r\nY0S0srL1VKVQsmwlw4xmtfr/WFzBEP0gb1Xnuz1FZCLzD/sPoIcyx9xSklTF\r\nwWnkuiU3vwD12hL7PXfhXkKv22124ShUnkGsxykZOOmaxkFfa8JxiUKuU5cI\r\nLv0fA2O4dUQbR3SiW8B77AM4+crTq6/VUGC9gWxwmle/VTWf1hIRs/HO1tr+\r\nBlm+rf8I/ca3AyIB1rfaKCeEHVa3MOD7+JY7oo3WFtx/KxxTseEtmi11wCoH\r\nQvObcrW7xFc8kTaXGchy0RSt2kTveqk7a2xuchzDItOAdprROSSEQG+j3Et3\r\nSny5zwVgi423aTlPKvvORRXzUYYk5/2yuctAEaRTDFxDcxk4Pcz5a9YuwIQb\r\nezIK4EMkjJnnCs2U9+ij+OSbgiwj+PHJzahD5SXdodjsK801PHRg2szYpu/V\r\nQncPBL36nER5MKN68583YOqrC8BjeBjE010zFs97WlOMa36WVpCtqhWynp6K\r\nbp9zy/XzBRwoXtl6X0kmqXyy7HKMCLLYPwV4uvknNy+waf+dzbbIEEPusHI/\r\nM5NliGjwWH8WRxiyUfVwgh+kVIQd+lFOEqLe0pX3j2EPKh76SYNVJjIE5mpa\r\n3ifTbe2sEOO9xR3kSEtC8SV8xGUSIuE/Qqk=\r\n=52Ho\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "8b4b78759d255746f11e396efe7d06ac93dbd05b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^28.0.0-alpha.9", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_28.0.0-alpha.11_1650461457760_0.687697128208606", "host": "s3://npm-registry-packages"}}, "28.0.0": {"name": "jest-worker", "version": "28.0.0", "license": "MIT", "_id": "jest-worker@28.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a45b9ed5d7cabc49e463fdb0f47d60af86bca479", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-28.0.0.tgz", "fileCount": 16, "integrity": "sha512-ETSSJaDwDREF7LudjrfGpXs4jDAcKPvcrh2RgaRVXLBwp5e/5MtQQRk4zlaPjYpExhm7hyDJwIsIEq9sJMcHUg==", "signatures": [{"sig": "MEUCIQC/v54PcYANBBPKkLV/McFKkKO1mhQia7OFZ77CN0k40gIgJAXDrVEyBk/I0TH6EtTRXV/yRcpqz24vaFWn+dLPTtI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68111, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZo8iACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqGYA/+OdDYg67vtbtzw6dDG1VoPOdUHmgJ35rRFXzLHGDwnXSzAI99\r\nlfcDb/4hWTaO5Xa+i4aCZ3nReT/jv5UmM70yXsPM+fXdOVyLvkJZbb7mVgtF\r\nTPQlMbE1pDXXatX3/R77jJ2YvtYlXmiURYv0PFsVHWbXjF7HtU6BxMRVJDJi\r\nfG+7JSP4giQ7B8HIwrT28Qs2BCQpRTpm+S0jvHCuKJrC5X8MRB6AzHEIaqd3\r\nMsxxI3hCcSnXjepcTT/f/t7P6zOhrIMo54wftiMbsZyUCuZz0jzOWKzvSzAT\r\nJAbMxZAf8RZRnuJ/2ZvL+WslHu5MYBfped/RAds+fq9Eq9j7ugDURoE+ejB4\r\nc1l6Y93OR8RFebQVgGf145RlT/1VsRw17SPKMau0IFMRVU4gXDpb0nRJsVRq\r\nfX84syDoLvkpanxFq7/R0pTDDM39gPXAy2XF8qMBZgzTbLYFbmJT7GhsJZ5S\r\nhdpLwfGbvs8wZbKhdWYrh2u+TtUxDRVSHqSL9BKGlcD9/J6vOsfOs0nFqnZ9\r\nF1qVOIb991WdXj0cBVgIIdVHIU+zt3wg0O99A91DOTNzWGtPNiTTz0EAVOzQ\r\nLOvky6f4wBwSJ8gftETEf/vllmztlkHPUurrl9D3mnWs12tzionTIPemVAn8\r\nlwiUh6PnGxcT7jIHKaZQsdVhNWlzVD4RG60=\r\n=uzNW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "8f9b812faf8e4d241d560a8574f0c6ed20a89365", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^28.0.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_28.0.0_1650888482277_0.7470449633210638", "host": "s3://npm-registry-packages"}}, "28.0.1": {"name": "jest-worker", "version": "28.0.1", "license": "MIT", "_id": "jest-worker@28.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7abc005335a5937e6a4f8d24c980c7630a849012", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-28.0.1.tgz", "fileCount": 16, "integrity": "sha512-Z3j1jfZwtt2ruKwU391a4/kKdYEId7Vy9+6Jeeq5Xl0glJDnOAvO5ixNmgMokMmbpet41jc4MpUx71ciyeTB/A==", "signatures": [{"sig": "MEQCIGf/fOPFplJksv0hXMHb/LsosuF0aGdHCu+oBvTLPnqOAiBO5E2uYmpZDQOraufw3AY18SMSzF/W2gGeDLr3Ve5YvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68111, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZ8M5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr2QQ/+IVI/eguE83szwXjpLwG5mHbphs8qaWmHHUD55lLuF9u8SzNx\r\ngKYHHIMoSwIzZKa93q5+C3V/zx4dA1R0zPmfk6bDHXCHZFW4FBoeTOxh/rXT\r\nN6R9wQf6hQy36/TD3BgjkSWGUE99z94s97RnSjkcBpjur6eHGTT8VXJqLEpu\r\nyMA6cj65axorW/R7vhFzyegxzt84zrNize0G9TG4gdmPdLwCBgKu3ReRy4Ke\r\nFQYbQ72fxpB9RnFELlhhwDX2fLP5329op08PKGtK4H9F2AT23fCOdzklBXlu\r\nYm5MLC2NHrR6/WuC+TXPEdDBVm/xZi0I/au34RJ8EW7SvpBtVYXrP8dKaHcn\r\nHlhDr+F+WKdgWWqJFyzk5z/t/luOfMXfetqcSZiXeGPLI2HsgUDpq9OBStqe\r\n1FPQcO8Z0II9/p4ngLJ7PTqpB4Lh5zo6PFPDPxrFW9qWWwCc/3vfQ4zx6jxu\r\nCZHt7R/U3nqo67g56rN7bIvX39ZEQ8NeBbGviUF1A4ixAZTxm5StCkwmbVWS\r\nrpY0FAl6D+lDl1/78MtNfuQJC9RrtCIJcAX3AklujrTXzHZ+YTVJIwBxAmFL\r\nkHwpKCSmCPVZGXPYgp7rPCHka81HIXrcKS+tg/GwEJUps9lFTEBnu9WxfPm9\r\nQG2PIlKawKPpSUCFREmMATDtz8rp+0kvhyE=\r\n=l6ig\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.13.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0a08639e4299f07becf1020a761adfec83536018", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v16.14.2+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.14.2", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "worker-farm": "^1.6.0", "jest-leak-detector": "^28.0.1", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_28.0.1_1650967352890_0.4348692646135568", "host": "s3://npm-registry-packages"}}, "28.0.2": {"name": "jest-worker", "version": "28.0.2", "license": "MIT", "_id": "jest-worker@28.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "75f7e5126541289ba02e9c1a67e46349ddb8141d", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-28.0.2.tgz", "fileCount": 16, "integrity": "sha512-pijNxfjxT0tGAx+8+Oz<PERSON>+eayVPCwy/rsZFhebmC0F4YnXu1EHPEPxg7utL3m5uX3EaFH1/jwDxGa1EbjJCST2g==", "signatures": [{"sig": "MEQCIFwHodWuBuGH5vjDH99vb3InhofcyhvgnHHh8uHqW77XAiA/Qc+ktTEWnzLzfW8dnQTCbGhVB7n/iQReHF/xZ1NoNg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68994, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaPRAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpG6w/+NtzRjk190lZqVRjwv/9s4I/27C34BlD2Xbn/Hr8qAGXJPAVY\r\n+xxNwncJW909+bljCf8m9dqAO9Il7/Bk7OfJyB4CanC/viz3Axh8bqUxa5St\r\nmM+mhikIG2ifBzCqz5QtNxWLOXcNwxOOFRIcxIT0q/7VKSn9DNWNEo8I5ty7\r\n/gimqzWkvKXa8IaWLInOhcc79hig/pL9heLIBWHtDUuAzMCbc8gRUI12bDhC\r\nos6lpcZ1YnOOCUguGAaSgNZr27w/hXysoVhyOzhJcOK8WKxwPw9XcSbPn1HV\r\nx/NvZndkBNmpJS99SYpJfhKjvq9KXQXX+Ydx9U1J8zfAKvGs82tH4c3zsu4r\r\ncSRsGcS5ykFegHss15TnY/kNn6S2dNq0NDl1xE3ucG4qnTQq0HnDPDcz61BR\r\ncq0adEgIBhVUljLTihbwdtetla5tqSHEN7Y6aj/UzgRF/ntBogSVADrp2Pp/\r\n/j2m899C/jVe8qrAk69px+gmGBLV6yA78FeQQoBZ8lVW/GCtd/OBgqMpc5hA\r\nZyMfKXiVLPBhvObnuox2cMHgRMcQZ1OE82JSUp6rX98ScT/Cb51Apohbi/sE\r\nApLeobjb6HsF2V2bxwYUZH1Y3+GrDb2TManC3YpQF/LyrBlHSHWiyEik1qq5\r\nBj6tBWv6adAORzlz436dhtR/ACq+o17tigk=\r\n=oMQR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "279ee6658d763f024d51f340fab6a37c17d94502", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.5.1", "get-stream": "^6.0.0", "worker-farm": "^1.6.0", "@tsd/typescript": "~4.6.2", "jest-leak-detector": "^28.0.2", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_28.0.2_1651045440168_0.5777805788374517", "host": "s3://npm-registry-packages"}}, "28.1.0": {"name": "jest-worker", "version": "28.1.0", "license": "MIT", "_id": "jest-worker@28.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ced54757a035e87591e1208253a6e3aac1a855e5", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-28.1.0.tgz", "fileCount": 16, "integrity": "sha512-ZHwM6mNwaWBR52Snff8ZvsCTqQsvhCxP/bT1I6T6DAnb6ygkshsyLQIMxFwHpYxht0HOoqt23JlC01viI7T03A==", "signatures": [{"sig": "MEUCIFdfegq8snqUNXd4uJCFiBVV1mJD6Z+sc/qZS5kcvsPhAiEAom/WKqeqmCX7nS4H+1p2C01qZgs8pWW7rz1DIJNtAdU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 68994, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidP0VACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqRgg/+O+Yih8j/rsxUm8PLiDg+NT6dx77ZMxMkzKHgmWkKKkDHrrO0\r\nAjq3fcwzbRP83800x9qE2SqOE6IGLrtWUFq1Y1cBFVe/z5b2FSK+PpX9GJ1Q\r\nTCfMoyCk4sSlDGwTLdKbG0y8Wie8QLaLwNmeJAgfF9NAzDCBCPXBtbBmJet5\r\n8IsH7v0Bn9oOE+IiYIx1m5fKJUkCiqcFJUunJt6duvwhpwPLobJnO6GP0k1N\r\nayrWlbmclXLj2GdNsl1PUGorEqfuVPV5ZA80LU+fwjR9otpvfLRO3DFPSqJP\r\nYQz6oCtuDPVqZvZnJLAU1QNqbFZ7wWhglnZQQ8gXtZZfVd5N9oqS3LIDbPvO\r\n7L5i5n/cuFbx636tQvbz78wCS+118H5w0Sm1IRUs31U+gEpPh94klfQP2aTO\r\niMUQukNqWo9USGGpycPJRJGhIgjYLEVSb0X1zJKWz6uskZM2Aal4rxS0Owmh\r\nkNnHC8kiQEZTXFe44Rbzvewk+PMJ8YjTNM1r68v5JrPSKw3TKzm4ulGv+KtN\r\n5K5TmAu6ZNqJq9f07ET1mKsA7DjDREcEQARo01EdQz3yebNuW9UvdNgsp3Z3\r\nGMU6YlOk6LwUk3h8K5W9dYtUU+6joWjvxaSPJiqQANkYnlb36cdM3jAHXeP3\r\nJCJmbiW5tMoOkpjxp90RtE+OzGJTYtMa/9Y=\r\n=C9Wd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f5db241312f46528389e55c38221e6b6968622cf", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v16.15.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.15.0", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.5.1", "get-stream": "^6.0.0", "worker-farm": "^1.6.0", "@tsd/typescript": "~4.6.2", "jest-leak-detector": "^28.1.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_28.1.0_1651834133206_0.4367739708306524", "host": "s3://npm-registry-packages"}}, "28.1.1": {"name": "jest-worker", "version": "28.1.1", "license": "MIT", "_id": "jest-worker@28.1.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "3480c73247171dfd01eda77200f0063ab6a3bf28", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-28.1.1.tgz", "fileCount": 16, "integrity": "sha512-Au7slXB08C6h+xbJPp7VIb6U0XX5Kc9uel/WFc6/rcTzGiaVCBRngBExSYuXSLFPULPSYU3cJ3ybS988lNFQhQ==", "signatures": [{"sig": "MEQCIGSdPPHMqDYiuGA8UTt3UGKiHXL/bh3ndtemWozrrpO1AiBQezf6mRg84vfqqOKKXYa1gLe1m4TXWeo1j4/oa3Nn1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69130, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJinuufACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq0TQ//RqQwqma7INuAeXtvAtwHsWE9NWN1JTUoWiwaxoSJmWIhbOzQ\r\nRMFGeVbKynwsAKvkcx6vzaUPWzeJAydo27Bzn2qKdcQPZTiMfS38Uz2qPOMG\r\nM02+6U9OmMWZwkVDgnlmKNyU3ILCoaWr1QtOWXsPlKkKguXcCOEHsOci2Lh3\r\npyBJvP/mGmt9ljA9Rg5gVYh9SPJxiivGbtARYhlcOpwzKAHx2BLeBmS06E8/\r\nZS145jXzkJeTKB7A3qPv/L5KPIbTj7+V8dVuYFHBv+EKzeWPkkc5gKXYbWT5\r\nh9RnCyGSPQ/Fuio3h1kQ/6LG3GtYKTWSA/6Xp++S/Vwk9Mz2CvpRFqeGBKqG\r\nMOghsqSySOc0SJ331II6dMdiy9CLN9GJsOPXdT6W87ix+Qy5ABGLsZrTBK06\r\nI6dC7JiiCb5zsuwJCfOzVncFzHAWOspkQXa436hxNk/o1bG9Yhnf4/dTpk6j\r\n7aRSK2NxmGQKQYEzppwkYK8YOEjkmHmOah0krZ/0VZR9RSYLRWDso6ZvmrRA\r\nHmrP3vU0iRvW6Nv+wcww0ST2wdyPxR1u2mIo0GyNHjdSE9csAQmpnrT+mRcj\r\nWdaD5yPGHwlNvSNJfWurHeuLD78klLBvrQKuuszUqockUxJec3tZYYZSLvLD\r\nQXvuTsguo1k3xP+fCELhXlMiePMiw+b4GMQ=\r\n=GOYd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "eb954f8874960920ac50a8f976bb333fbb06ada9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.5.1", "get-stream": "^6.0.0", "worker-farm": "^1.6.0", "@tsd/typescript": "~4.7.3", "jest-leak-detector": "^28.1.1", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_28.1.1_1654582175387_0.6767192198321987", "host": "s3://npm-registry-packages"}}, "28.1.3": {"name": "jest-worker", "version": "28.1.3", "license": "MIT", "_id": "jest-worker@28.1.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7e3c4ce3fa23d1bb6accb169e7f396f98ed4bb98", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-28.1.3.tgz", "fileCount": 16, "integrity": "sha512-CqRA220YV/6jCo8VWvAt1KKx6eek1VIHMPeLEbpcfSfkEeWyBNppynM/o6q+Wmw+sOhos2ml34wZbSX3G13//g==", "signatures": [{"sig": "MEUCIDvLUh58Qt7HbT7qA32cOas3pME20O540AwsU+9ieoxhAiEA9UIIWU8G8t/N9b3ezswwcQTWkjkYlyeIew9tjsgeQ90=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 69130, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiztLKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqnTw//ZSUp2jEckooJt1bwwnJGYd6X9LMuTzs5aH12ZZMBxYz+gEP5\r\nF6ZeXaHxs0erQAs+7J6nQUacPOKatyEie1nELzFh+1dDdaGNPv7WU/Gmc1Dm\r\nH1ZgXIWFvtZJCrGnxTqD5Zyab5LrPxucqGnCvamuyPadaRSYglFnz0a8yi1J\r\nm/RAZSNWBLQXyN+2LrDS97+G/8bvXWsi8/uROxsdgx5ECb6SOUXe1TjOHGKi\r\nR2xBSqYAcqSEsvvHDH/7hrH3BfU8l04TY7Q9XtLREUJsgPca6gDXQcbqhLu4\r\n/pMgIRmOZWKhTxL/+7+9jPQci34jTdsVtbDKJaepLw1PRptEbcDBTpo+bpKU\r\nKqMJvya4GppiJDC4PTC56P0ZZFxsmwO57jcsXyEkiqn8Q3M0HQltMp+IWfZh\r\ny7CO+rZ+4yfJCDdcfsQeVUc/hAiLaLOfVXg33LlETLH5gt76PHcy9tZxI0DQ\r\nHWrClrJgTFb0O1mAliW7qOqNYfGlVgELMdZ5UWltoAGCg7lct51nXOq6AR1N\r\nxr0+8QBCGlSVhmePpC6e1I//pJjsLGFLKP91nwW9kzBOFXel9D5wta5P9pVq\r\nhny9Q8tZDDfIELLA/b6ZcFz4+qcPkUn8S9QlHtAa/XrmXd9UkyMsOKdhxo/e\r\nwCJeUpo0D4iIYcndtBVHmdTvBKK1zc6biI8=\r\n=KYzF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "2cce069800dab3fc8ca7c469b32d2e2b2f7e2bb1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.5.6", "get-stream": "^6.0.0", "worker-farm": "^1.6.0", "@tsd/typescript": "~4.7.4", "jest-leak-detector": "^28.1.3", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_28.1.3_1657721545889_0.28733920150192227", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.0": {"name": "jest-worker", "version": "29.0.0-alpha.0", "license": "MIT", "_id": "jest-worker@29.0.0-alpha.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "74d4134961bc077b06b980e03a8f681fcf48eb2e", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-29.0.0-alpha.0.tgz", "fileCount": 16, "integrity": "sha512-xrfA7/YwPbZwcxnQD6V/xrSaBmc3pRxK60CuGc7EJWbvvGMYlkTGgioeigTCZ2FMLVHoSfl+AIty7tGfY2ARkA==", "signatures": [{"sig": "MEYCIQCsa+xpiiDTSme7Gss0jL6LshZu+ZjxNBxRt/St97KKTwIhAJ8f7e/8fDpcNZaSGJ6dsFTHcTZfwOfDmHtyQI9ve3UG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 65709, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1IgKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmovHxAAie3A/rG7RdRvRPcA1kcy/Hwi1jJSlie6izqao0kZCIHBq3Di\r\nl7toRNcTA8kAUAX8sjI+/zx5YbrS6Ltpg9JQ78mqVZpVUn/xzvtwqS3N42L0\r\nYh38LNUK1bA1aRN/5N9U5LD3nxObyEd6sISEvpgluJYw8z0hIWGaibodKHrE\r\nUmfCXyKAN8tAwIkRSnn4wuSNaOHsAw4ySj5XQCWXoUR+3+HtnWow28hD8WJ3\r\nVIn7Pj35Lv4UDEyQBOyO4QXIwD1b/l8VnZEX/qAEINs5hcI6liqRS/5uk8I9\r\n6QzX8YvP+tRIOQ+T8ZNgbIE+MrGJYXKFijkMUVOxwOwe0+Cd7uCJSibQJfmP\r\nwh/Ww0lXT6jB0cwdGjff9BXDAddB37p6CijUrP5vrCyynUbFmCV+yxJ3DrQm\r\nD/siv9Kaw4GajsGLpf2C+DWExmmTQtu/xGBBNbOs5jxN9X+q2bwhLwCiNt0J\r\nkK/8rYd+AxokOqDHYpceOT3BFhp4NrHM65LUionvboeRGF7VQ4uoVzAbjOgQ\r\nOpw1THdESmgf6Hl25RvL9G8X4Wtw17i6b/97agXZMvo6nPQPvmS0epT7MYR9\r\nmevq7nFdPLQEXebexbCdVEk1x3zK6sGmkisgBhcXaEVgqBpVxsiXT6ayTG1r\r\nvi7g/EEuiyQNxpqQ80kpDouxc7brHADl520=\r\n=dswW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "6862afb00307b52f32eedee977a9b3041355f184", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.5.6", "get-stream": "^6.0.0", "worker-farm": "^1.6.0", "@tsd/typescript": "~4.7.4", "jest-leak-detector": "^29.0.0-alpha.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_29.0.0-alpha.0_1658095625925_0.7072032940983612", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.1": {"name": "jest-worker", "version": "29.0.0-alpha.1", "license": "MIT", "_id": "jest-worker@29.0.0-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "85337070255194ff2c6814d8426a1565197a867a", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-29.0.0-alpha.1.tgz", "fileCount": 16, "integrity": "sha512-7i9OR88kDA7I+6ovLBucJHgQoDdX424aVguM9AhBYFX1kRSLaxRQUjF4aUqokPznpZUhz8uvtZ0TLCZZZ6/4MQ==", "signatures": [{"sig": "MEYCIQD85leBff0FgSZTOYVFPBbEI6+OSwCzqI0OL21PyEXmlwIhAO5qvccyxbpR4kHIbgkAxfpsHD5CuTfj33+GbEbDH+oE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66182, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi64IBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmryLhAAhAUJsPtYx7pCk1GgwC+HNtu5uVna5ywbBBXWFxgAM7KpHBLI\r\n9vlo74yoBQeozVA6ZcRShFhtNg6+DjbdG+O5rqcml0Oeci3oX5meQYB8Yw3p\r\n1X2av1WhrOSlRA3C7A4AN1nbDw8dlxnf2XKdswsNHUBHC046hLD9hZlMzzcV\r\nbty/FUrqfS65bWThXKS+bleSMMzW4wUhgvBA4Z4V7M/AvhYZqidzOpVSYsSx\r\n9UIhbvuOnrKZZQ4hfuTweeQSRXd4hjrsgdaaBO64wARqO6AvKQFsBmZ6XFQB\r\nL52JjSMhAfgAh1deKO4zqp0OM+8lu03bH083l2wqF5vm/oSPhk9K6HBxmbd/\r\n1s5tnsi3/9w4Qxikh2qIFhIbYoPwR/+EW9aGRDYSEdtNy9hdKqIt0NExo0TK\r\nVafwye3Ic6qZtEv1mK7m6wgqRQRu455JAS6ODTn4GC8a3cCHnrxl3bHMZjl+\r\nHyAMY4OZMQe+wTTiQ6FrJfDd8Tz5/U/fBFAkZ5DhaqS1WWAcfmpcofGs04a6\r\nGCa53FohH3Lo8xqmnuStBciT5vOAyoRHvB/in4E2Djt4NX+7kFH3cvoe64p5\r\nbqUaDbXgz0mXaHEXKdbnqEWiUSBOzi0aZ8sipvuDo7A8D3JkpnrLyKWLFp2b\r\n0ED0vrD9xc0n7NiRSP8LNcwu3859aGS8tB0=\r\n=eyxW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "10f1e7f52d9f876e6fb7f20c1903fdcddd8db8b1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.0.0/node@v16.15.1+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.5.6", "get-stream": "^6.0.0", "worker-farm": "^1.6.0", "@tsd/typescript": "~4.7.4", "jest-leak-detector": "^29.0.0-alpha.1", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_29.0.0-alpha.1_1659601408611_0.617705817055465", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.3": {"name": "jest-worker", "version": "29.0.0-alpha.3", "license": "MIT", "_id": "jest-worker@29.0.0-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "ab3929ffe377908cb2115768707283448176ffca", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-29.0.0-alpha.3.tgz", "fileCount": 16, "integrity": "sha512-JyKTps1fVXPNjX8wI/oo5TpD2kOF961bJLFEwcy+X6sYqaSOyTPp/LaJRwAx0B7XQG/cvl9m/ydTKOxkfvOkiQ==", "signatures": [{"sig": "MEYCIQDr9MyLlYhnyGO6egPNT45Kd9RkxfIBXe3wVL+Zpx6iWgIhAN860Le2s3QFnTYeSV4aleSk36MQCqfbo65FQ/h/bBDv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78373, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi78ETACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoxdRAAlGVJ689T++OH+lLiWm2IFe1nFYOhgiJLqef1qtUVTfAF1wqK\r\nBe2OH4jdxfat0lbm1KMLxAYLDiVdH/l24cv9Qu+u9R0yFuTR7ag/ZqQhS4g6\r\n71wvfD0/DUnnCymlbSf53RYdm8kvAnPrfHQohc7HjMUYdmZzpuH1PVpd2mbb\r\neBaQN9qbU/pdGERCGy3rahkonGwu3eVh3gHXVmaSrn6HDR1xCVyY++FzIKVw\r\nqeVAPAFaIHviw3Tv8EdRAy/V6d4z6XQjo6he5WOYRzZufM0C/prSedOnTTcJ\r\nLLrF/pcvDEKo8NSdQL+q8/2gKnc2zxnf2adF3wEXafeIqE5gDyTEvFXOnBKA\r\nYCIEELKUt0EZqEFo6WTTlZv5BlhF+x3Oo76Y2xf4Gc6WkNgeRp8uiEY/hHb6\r\nB93bYiSVNQDPn5sMhY2G1BzgSxhI1DmJai08JZuqQIsSPgivlc4NDNV0EAX0\r\ns7SldJR9EgoieDAP8iF3tkDifyomQG4IVlDEv/wW8iCblkj3bEKqKO9M6aRa\r\nTUjUnuV68UVZIh/TvMPgOihSE6viEFxr7OR8Nf8qSnjiKp6MwzSeFiv9zoUw\r\naH2GUMD5DpdyGSpCBHRcZEWkVL/qSyf8XuQjBrTnO5j10LOGvA8Kn4eOBxSG\r\nYdwSxIs8x7tI1EWTcIn3w3pvP9zsyNvlWj8=\r\n=0Vhe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "09981873c55442e5e494d42012f518b7d3d41fbd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.5.6", "get-stream": "^6.0.0", "worker-farm": "^1.6.0", "@tsd/typescript": "~4.7.4", "jest-leak-detector": "^29.0.0-alpha.3", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_29.0.0-alpha.3_1659879699128_0.287452577232439", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.4": {"name": "jest-worker", "version": "29.0.0-alpha.4", "license": "MIT", "_id": "jest-worker@29.0.0-alpha.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "806ecd6b486590467400d595beecee4076d226e5", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-29.0.0-alpha.4.tgz", "fileCount": 16, "integrity": "sha512-8v3Z/6IZUDcLnev0F1WuJDwD/6dGwc8m6jBQendIO6kl2LcZ2lrzbK/A6aSoSvjwKvIRSV5QyL/rUW81W+Zh0Q==", "signatures": [{"sig": "MEUCIQCFs5fbHmqTwcDZSAQieFhgU7F9psxAcUEoMzBPqHlKbAIgBfP7pjgf0S/pb7NBpSJKHddx3nClC/50tQlzAMQ6gXs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78373, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi8QohACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqs/Q/8D/qIC3Us2c4kQUA+B9rmioEcxnxe/GVuov7K4VFMuY1Fku9X\r\nR+R546ZmAQ1cLZXGd4Xg4WBcrCh09ZM/FvajNGpcQ4nIvmnM5qOAC5hOpA9a\r\nP63wVPlzHWAdC5JttwFp2bGjpvpKn7Hqg44HZHqxr5EBQ3Lbv6Rc5Z5WjTVK\r\nxlP1yQP0FL2HEssY2EuFh1+MpU73bhMEei1s1XVEOCjFW6h2qH0t1LlUprqC\r\nr6qyD+LyPiS/Btpq9EJscGBVSA7RrTiNsOXMju1086jTIkz4455ZtEdM2glw\r\nv4YzMIIiDNPJLxYTZXbzXit0iY+Ou9mQ1ZxNwFPuWeaWa73ir3jk961oMPx6\r\nB5Iyhg6woCKMxrvmUbGboyp9a5lCjdZ0BDECbqs85c9loX8gLUta/BC/Ypli\r\n7BM1SGxNrIFlvSh996kslyIftHyPq8AUgWpBXwUZxRHser8BOhvqFeoFQ/Ic\r\nuf1Y5EMopaHOK18rknnPePfsbU8WOjxldYvVhULfsRGzhhvC+F/aSz9AvZ4I\r\n6eImK47mibLBoUiUF7Rf4Tt6Q1sO534c/JQSlTT53/idpTGxVUwPHQOI0FWh\r\n/JF0PEn7Jb3TwM54exkRo0M75BdNK8EP6F072qvqLuzWP8YTB83fwStX8N0x\r\n3ecpvUAX26Th17ocz8zOMVKVDLVrWE5tfcs=\r\n=3rTf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "98a833bd4bc0bdcfcee5d4f04c2833400c4e2933", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.5.6", "get-stream": "^6.0.0", "worker-farm": "^1.6.0", "@tsd/typescript": "~4.7.4", "jest-leak-detector": "^29.0.0-alpha.4", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_29.0.0-alpha.4_1659963937495_0.6815103898427783", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.5": {"name": "jest-worker", "version": "29.0.0-alpha.5", "license": "MIT", "_id": "jest-worker@29.0.0-alpha.5", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "72ac6c2c0f157008a11b58ac31d02c951e0fa285", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-29.0.0-alpha.5.tgz", "fileCount": 17, "integrity": "sha512-DM6rCc+fpl49Buun6IRO9g4eEDgYVra3r0xsy/Rm7cb2ycazaGOXZIqzqV3dSDMVe6uaGszlDk5OONn3OwtIRw==", "signatures": [{"sig": "MEUCIFtxDzqjNQI3uxQQe1gGoRCz/cQIotZrYDUGG40sLkW1AiEAorhvkJrgewRlyLxHE99FFEY+xNaEBnm6VfByn2le9BA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84599, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi9QbpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrhsw//ag8DkRZpxK8/MPZrdm6CBIjJtgJz31JExX/g76dAq/epLa+5\r\niDKc3bhrq4Vqlzq9ozsxFoq4NsNFihd+Nx/Du3cLIMXC+HGpVKcyzOi0N0es\r\nc+9Vc+h5W5aav7w/q5iYPwkwauwMkhtWgUlwUPUjNDzwqxuqq9N335uYajJ3\r\nlTXzEtO7HRXP6+qTu1xD0yFqJsluokRUssoa9gCHqldx0D4wUIDLITubsHcz\r\nxpuzCl72VbeyO2j5wFn1WL41nw7lWOsXPb0Fxxeq14EVATqicrEYjVIV7FhL\r\nd1yvL1k/x0KVzoAFfsi9UPbur8zapa8PUuhBAeqUx+RQNzvRg7kGe/MvY079\r\nCBwiQ/yN1DKNQt+F5GDYGu0aI36Cw6MavEsKqKNBJVnsgH/+DSh/ZmC3LeaR\r\n9RHJKipIADuhEVumGGH6ubQfnQqTjg++S8RGPPgW1SggIGFtsJyO7k0Z+cXQ\r\nXBfH++Nl89PEEhHtf+gviglB2f7uav/cz1sAbCcTzEq6JjIUjm0r2AKEQxb9\r\nVhHBXblA+JBBuPVSLSYfLAE+hKAkQ630bihLdeXmphXIpJPtr2BgC6oetPMb\r\nlqdJoMMGWjxtep8/MFAnJA/IgoK/B7GKFGU6sFAW69xU0n5E4E3yJJt8mvgT\r\nI6oRH70JYgmDnozafGWa2RukC2IjJfyyCbk=\r\n=Bhj8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "63e506b5d1558a9132a8fa65151407b0a40be3a5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.5.6", "get-stream": "^6.0.0", "worker-farm": "^1.6.0", "@tsd/typescript": "~4.7.4", "jest-leak-detector": "^29.0.0-alpha.4", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_29.0.0-alpha.5_1660225257154_0.8123488041166989", "host": "s3://npm-registry-packages"}}, "29.0.0-alpha.6": {"name": "jest-worker", "version": "29.0.0-alpha.6", "license": "MIT", "_id": "jest-worker@29.0.0-alpha.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "59d82e84a0da3275d47dc8737ff78c12b86fed93", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-29.0.0-alpha.6.tgz", "fileCount": 29, "integrity": "sha512-zo9vKYze7cvtsum7Nw9l3VohJtqhGk0DS6blJyZTQAtXqHIXV2l6lsIFv8rTBN02GzAHVDqj21Qk3BrWOv+4ug==", "signatures": [{"sig": "MEUCIBbb3vcxVODpDWej4ZUTG1RACl1XyjzS+3ROpBAGdEIRAiEAlXDCLH1frLtJAtDLWAqM80uA9GAFfCohL+358ujxoXA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103866, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi/5bgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrMhQ//ZqY7QKaSeWh7hvZCe3LZUXMBOS8tHU1Iz7Au9CE4StbIKgac\r\nUkQ3fUYPJogEYBqobLGf+phc5tgdW72mAekVqOprqQ+qFLAoTbV4wkQY+B8E\r\n7NruoSn0nPs6p6SwX/6/hIav2A5lt19YVoKtpFwFBpi9z0QJRtkFLQ8WUyYW\r\n0m2U3dtQHYA3HNkY1cl17+9mjdHjkhJxXph9ylOp3WA/U29rS+5o+M7ggpQf\r\nZ2MagMM71g/pO3Nr3vwstZjjnQ++onhDZUxA85lUbB+xI7NSKaqCb6AQjMm6\r\nCNrcBiDvb1lXQaqhqDndN/PvbMUk06ugF260KPBkztVVZSvAqb3eWTAfo9cw\r\ntMdcr6M1wewkRvO7chaQnSRUA9DDj1w0SBLgq6pyVoT2Hv+3Z1+V+yjU0yx3\r\nbxspL22NE5BGyfgEljGMg4VbFStOWPA8Zf6EgCptt4mEEkeAclGWlSkEQqWD\r\nUwoIskPfpHA+GvxErvykL2Bq2yVvxWoS4sDLnTC9o4aRy4jCNo0V09WLLQAz\r\nWlQah8K/FbgOoU/3x3o0/rFBkCZbJh57WzQ751Gk7t0oPCyYtgsYAGOWEM6U\r\nlz2UYOHxjaSUX8Mda8C7eZ58hEry3jEwswtlinzymXJxGrgHBdtFq5WcFdHG\r\naAka5WeL4KojiyvrF/x6SX8WL1ZhmtguBx8=\r\n=ParO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4def94b073cad300e99de378ba900e6ba9b7032f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/1.10.0/node@v16.15.1+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.15.1", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.5.6", "get-stream": "^6.0.0", "worker-farm": "^1.6.0", "@tsd/typescript": "~4.7.4", "jest-leak-detector": "^29.0.0-alpha.6", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_29.0.0-alpha.6_1660917472567_0.6999195194494563", "host": "s3://npm-registry-packages"}}, "29.0.0": {"name": "jest-worker", "version": "29.0.0", "license": "MIT", "_id": "jest-worker@29.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a1c7b67b2d9ce057383999b32d8b71794efcd0b9", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-29.0.0.tgz", "fileCount": 29, "integrity": "sha512-2t9Panx3F9N1wAvRuZT7xLEptRFc1C5G90DOHniIGz1JIgF9uhd5u8jNBsc7wN63lhnaiLeVLnNx21wT7OVFEQ==", "signatures": [{"sig": "MEUCIQDbYfitq6luLxZ9Six9wmS6YdyvhZ6wKX3Zn+Yl+5yqIwIgUXVv5hGa0eMbq4NOQEq5EdU5M4GrgZCXOIE2jvBN+n4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 103850, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjB2wbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmomWw/+NXAL5gaTC75AXjWsjbksPeEvMx0zy+rgaSJ5b9PHUg+NoUCb\r\n7P8SfrjsBPUDhoVq3/KSqRQXiBSDIq8kn7FpG6vfT4/fiGhC091rx9fyBU+3\r\nWBQ4FPzOV7sLgbRyAzicp4/b60lLvwW9As1J7aIAtb9CMUG1jpahA7h+HP8O\r\nRQtIvHqZml1sOVeUa/vNrfcbqHmsc0WmWQ/Ih4cJkIidDwyP2b6lYB93I54q\r\nam2/0UbayRb0lf9uXvg7VR3jVnwSiipnQf/Jui0XES5+YMB0EOqVg85M2BnY\r\nreQy9ySoloBC/u/0vp/4cmH4ijAiZgXe1Ue5/xHQiuAcjrlfU6iHzn1V4SPU\r\n/4CqGtvB43M1p2ufmSFWSR8K+lOPsNZm3TaUxJMhEOiedbmkwrc/AHtgqz/C\r\nIvhDrxpY4W8p2WG2nhRWLekFz07yM4i468Abdy0Ivr5ahugog0xMOeOooLsP\r\nzzBFy98F12HcSdicWjgQjo+CKOtjLDuu30oFVQeva9gnhJujd1xQTiz4uZPJ\r\n2iA8l6qXAQP8/N3naHvUXmxzrL+pqcpmW2UcA60MhopIJFmnzimqDV7Gtbzz\r\n6Ekxw9M7ZbBYm1xaMrG8G+vAPZAjR75neh2sVhq+dm7oSF+kqeWMnUMG9bay\r\nzRVi/1GMo0eCuGhjdExG1cAV4P5rDAeZaRI=\r\n=aoSe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "75006e46c76f6fda14bbc0548f86edb2ba087cd2", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.5.6", "get-stream": "^6.0.0", "worker-farm": "^1.6.0", "@tsd/typescript": "~4.7.4", "jest-leak-detector": "^29.0.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_29.0.0_1661430811197_0.41161831791089076", "host": "s3://npm-registry-packages"}}, "29.0.1": {"name": "jest-worker", "version": "29.0.1", "license": "MIT", "_id": "jest-worker@29.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fb42ff7e05e0573f330ec0cf781fc545dcd11a31", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-29.0.1.tgz", "fileCount": 17, "integrity": "sha512-+B/2/8WW7goit7qVezG9vnI1QP3dlmuzi2W0zxazAQQ8dcDIA63dDn6j4pjOGBARha/ZevcwYQtNIzCySbS7fQ==", "signatures": [{"sig": "MEYCIQCxbhSAJgAYOlhsyDuhXH8q67JnrVyhG4Junp3dRASAHQIhAMxOVjt3EoMIGSED2VOwaMsRTZED0rMMmdwE6WEh7kxC", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84583, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjCMv0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoEQxAAnd1qIChb/1d1S5t9jM0ghM4pC2SXYVw2wOQi+ZGtzJ/nqDDt\r\ndIIT2l9ca3S7XKYmRFcFJQ73ZXCV8UYSNqmK6SOuc99adYVR/2YV1BbWv1lj\r\n9bhw2QQ0+orW/nKr/nfMhbduvhB0JPPF2iP3MWGTNGuAS864g4XzDh6+Womu\r\nqgfd5FypgO/rAh6cRXWTrSE9omzh3B4/cUJ21Bjj+F4Yi7SQ2k7TfefoDHNq\r\n+gSAsdk3ZSGgMBNhnyYGrlVAX91qRNZXmC5T6Cx3ip7K+JTjcRNI5s40rOTN\r\nexKcQaiT6QBQ4vuwf/hY4rFTSRjWcKyDkFRvvx7XHTqAj/rWs0KUzpDX7Id3\r\nYo7Sdwx2sqjwD4mA5G0FsYdzUmQq8szaTCiYQcqMlcRs/Pklg787zUYINb6O\r\n/1gWUv/ItiSYpatFI3iak9bcYeIesG3smIwxlECX2ZvH9ljQ6FCwXZ/fGJ0n\r\nhitaUYIEB/yoBjHU3FQMmtUBfbHhPY4hod8RUMiB1iMcX29aY4s/QxESDkTy\r\nzzD59AHTxdw9qwHD+fkLOHa73HdqvdCja/42j3roorA6YhpPBBAFoRDxZhhe\r\nG9JSRf/iCXAHnnvlFcL11kU3/W7AFkf7d4djjgu4ESFf7gUpmFDXrk4SPPsP\r\nSf5jauJTbDESTNdm0y1t4dzcPU7u+2rpbs0=\r\n=UfIe\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "b959a3d3bdf324ed1c7358f76ab238a8b0b0cf93", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "get-stream": "^6.0.0", "worker-farm": "^1.6.0", "@tsd/typescript": "~4.7.4", "jest-leak-detector": "^29.0.1", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_29.0.1_1661520884711_0.4802079853376138", "host": "s3://npm-registry-packages"}}, "29.0.2": {"name": "jest-worker", "version": "29.0.2", "license": "MIT", "_id": "jest-worker@29.0.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "46c9f2cb9a19663d22babbacf998e4b5d7c46574", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-29.0.2.tgz", "fileCount": 17, "integrity": "sha512-EyvBlYcvd2pg28yg5A3OODQnqK9LI1kitnGUZUG5/NYIeaRgewtYBKB5wlr7oXj8zPCkzev7EmnTCsrXK7V+Xw==", "signatures": [{"sig": "MEUCIQDeQbRCc58yqe7zuj6oqcGRoHy5PkaEmgcw1UXok+sExwIgAJlYzXEHr87mg94vudxWNBAwTGlg3L6J7cU62jwEdW8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84583, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjEzD2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr/hw/+O2dlhcEM3uZKRih1o1SZICs1KcWLrdM98Wyqui1x6Mi8LvKn\r\nutPZn+vzWW9XRVZmiSLIWwX481SNXSUchdY+FxvtRQzzvGCO9sLtClK1o6WA\r\nX7e2rwyhxOpaY9c2pcKcf3vt1CB0I1mcWNwy7qSd40URgE6cWzGxKU+oipn2\r\n0sfyvZBUKQb+px4RPgUQZPMkfkvdDcdsBf5ORe/mwR+fZlyAg0fg31rgxGDy\r\n5Fh2lHTaoBAjVG2I25fT8nbkwS91DtW6ny4VX+KZNfmF2yQQgX8DK5O2A0Iy\r\nATmMvW2rSUXUuxd/DVdOc7jipA0PQzW7xE0UJB+J7z7o5Rr4Tv47eJ00NMmZ\r\ngeS7G3vm6fY1KG41rI0/KryWzLCfcXBIWZClIG6GChlZwrFoaylw6RBYQmkw\r\nRQULt1EOTIE7Jr4J/F3DYfMrTHZYO1Ttl/cqkcCaAcgLFCXmECpi/lXxS1VQ\r\ncyvhXRc/JwKW8vDz28aT1CilbvJSI1dVpVzWsmUOgI0KyW4hVvVBY0H2dAE4\r\nPOdQc10GKp004giXMLb3AqZ1dsWx3aQ3EUSRU+TGfcK2ah5Z5iHZCHlIvA8n\r\nTud/xQm8fZvcUSvYZAEER7RfZls0UEpMBYzKHUXZRiRtYJ1xV/lFHRB3l8KP\r\npN3mQQIBP11OiTgm7l4K6n/Km83WdomIOYE=\r\n=zwMa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "616fcf56bb8481d29ba29cc34be32a92b1cf85e5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "get-stream": "^6.0.0", "worker-farm": "^1.6.0", "@tsd/typescript": "~4.8.2", "jest-leak-detector": "^29.0.2", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_29.0.2_1662202102391_0.2706606058632144", "host": "s3://npm-registry-packages"}}, "29.0.3": {"name": "jest-worker", "version": "29.0.3", "license": "MIT", "_id": "jest-worker@29.0.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "c2ba0aa7e41eec9eb0be8e8a322ae6518df72647", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-29.0.3.tgz", "fileCount": 17, "integrity": "sha512-Tl/YWUugQOjoTYwjKdfJWkSOfhufJHO5LhXTSZC3TRoQKO+fuXnZAdoXXBlpLXKGODBL3OvdUasfDD4PcMe6ng==", "signatures": [{"sig": "MEUCIQCVdJeX7nhNYH2AZSSLDp9TgYVllx5b1yhH0m4bkDshawIgPNogfH1Q9m5TCR71CK3JL6+ZbgN/gDrCH0L7JpVug48=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84583, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjHKIpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoAuA//Q5p4UVy0OEWEEm3LTKk0LhtiGqsjTsWpWyWIO09KFHF37IQj\r\nbRyyi4km2BOp0LHoS1BHXdSjbngpVwkAJ4n42xpo7gYUYSwBUXB+8ke4x46s\r\nTInJmaizhIYw/9KAdYz/i1BFCG1tCOj/7NEhKNFxcG4rHBE3ozxQ/c+xC7tf\r\nPhG7rquf4h7vjtr1O6Ulqo9h0LKXpsnnze9ueLvGqLt5ahw5X3Bq1i/wtZsr\r\n+G/IrOMbKaqKYur0MIyJZBKFc7bZJbRuo19FS+Sq3k1Y0VWuxc08Cv4WW0hj\r\nOodtix4rEaytVbmCeC0ewftoz5WnqE717hDKEgASoEopBjISzxN3DkAJbqPz\r\nZxwTlJ+TECdVKRB8KYxV8t97YGma5CvE51iU/oMcgDAzq/nrT7t4kFDUaXv0\r\nwjTiIHQhXV/cUT8PDHOx+fpTmZwzSR4Wm5dVcxqDumRHv/c4mlW3Dt73aWlP\r\nDUkyvh+4yD6B5MwaEEp66y0BucYdkSOvxHd2l9bLfocMP+8dPUusCyVgEiZO\r\nIZBCJ/+Veo76Mi02HjLK1stAu4SwnTswolCa7oSDZ44IiLFFhTtXnqrAMus2\r\nnO84j02W2zT0pt9+kII/Pis/ebC+21nkQduWGOaDyxXLcbKR0xFEogyK/6re\r\n1WQ9OHCRgyDCL6N5NIyYCS+kEzV1/pcPWGQ=\r\n=3iSS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "77f865da39af5b3e1c114dc347e49257eb3dcfd1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/1.10.0/node@v16.17.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "get-stream": "^6.0.0", "worker-farm": "^1.6.0", "@tsd/typescript": "~4.8.2", "jest-leak-detector": "^29.0.3", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_29.0.3_1662820905549_0.8669018445773511", "host": "s3://npm-registry-packages"}}, "29.1.0": {"name": "jest-worker", "version": "29.1.0", "license": "MIT", "_id": "jest-worker@29.1.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "374d741b976a868e39141d63fb401abacc07a8c9", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-29.1.0.tgz", "fileCount": 17, "integrity": "sha512-yr7RFRAxI+vhL/cGB9B0FhD+QfaWh1qSxurx7gLP16dfmqhG8w75D/CQFU8ZetvhiQqLZh8X0C4rxwsZy6HITQ==", "signatures": [{"sig": "MEQCIFBj09dmJa6+FjYibzkzO3TB+gzUmkz3Rk+cvEK/JNA2AiAnmMPLY1FlLF/T7qKOEnIwyZ+EnuMqFiWGBLpjIcNXjg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84615, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjM/nGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp21A/+KhQIkfBzMYfsQyzucAnSxzRGFRd2d6CgFDflRUYJiWqeppVI\r\n+w2KzYE0bvja+t3D892kER8aJzQDo+cmrdSsFtsaE5xAaGi72kf1gq1AnxUA\r\nBvx786wG7pzqbpuqbu0JIQ50sW0/U9Zig18dnH1j44zcmGpmGoszoWn456ZH\r\nVBW0bzHmXCu2mW4wnG9eQsxVIF4mhFEN9nm+YO6dL6INLN32pJ4anu/0pO15\r\nh40HdyRDMOISNQ1bt6lv0VDmuDBS8poSqoGB1f2Ui4o++viPHkYBzyTMSJ0I\r\nRs2D6ts1+sBdMl7X4uIWZdkFwNLMPfR6/sEiiTfIm138OvWsmaTCfyeXgzt0\r\nB2eVhBCzkyWp7HksAMcppY5qgQDCs6kxnSACq8UQRo1+w4Jp346qurrDadGA\r\nI9SVoT5CzYYQ+QNL7f8Rj60b3Dz40XOQqSL+URRiFUm17ALyy4zUL2wAgYQe\r\ncaVI69HLVwaiLcFpsdjPbozukhlEGT+W1JqwRv2vp7GnCxQ+fJEtG+HHmzm6\r\nErK/C4DPzFJJRu5PW+320Lz8vULGrt2DXHYjSHUE73IbGR6NqZ7RKKXwk/6i\r\nlFMyBtY1XilG3q9IbyvXpjuPW5bC9mMLw8VeOq1J+8EgFdPehEFhSmBZmEyH\r\n7n+H49V5+KWkc1t5T6+uU5FWJ+RFXBO9/LQ=\r\n=RtqP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "51f10300daf90db003a1749ceaed1084c4f74811", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "get-stream": "^6.0.0", "worker-farm": "^1.6.0", "@tsd/typescript": "~4.8.2", "jest-leak-detector": "^29.1.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_29.1.0_1664350662642_0.10641773948634348", "host": "s3://npm-registry-packages"}}, "29.1.2": {"name": "jest-worker", "version": "29.1.2", "license": "MIT", "_id": "jest-worker@29.1.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "a68302af61bce82b42a9a57285ca7499d29b2afc", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-29.1.2.tgz", "fileCount": 17, "integrity": "sha512-AdTZJxKjTSPHbXT/AIOjQVmoFx0LHFcVabWu0sxI7PAy7rFf8c0upyvgBKgguVXdM4vY74JdwkyD4hSmpTW8jA==", "signatures": [{"sig": "MEUCIHpUbsWfBJY+K8E6esUEvz+SNSjswjE759tbMq1/jB6LAiEA/wodqKTUoDIW5TWdM59YRtGYOE7PdlxMO2JbQFGfp7Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84643, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNplKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqqaA/9FjIhcgl2G/WqwXd0UH6L2miMKaW1v811W0FrxHPb2HmpdqGW\r\n5cIQ9P5yFnVOLwdXUz4I7LYKNro77O0MACDRWaehVrxW9aTS/0mfnGyLzBk4\r\nTfYICTOmowaZ8c99KvYr8OV25qvanRfOuKbEN8Hbwq+bIZQe0vFzCVlxKZTa\r\nPH5nq0zbCXl32SwCruWw5V3nZcZkhGwu/hivQ1iIKz4clmuYNKQWOhj/IXy7\r\nL4d38M81k2We7j87X+/+Y2FrAEACVRKhEKEuED7WBdbfImWIuaUJvuhmUDsD\r\npv1bTKbFU5sUZRqlHSl6889w/IA2f6tPq4BPezclDxRG22leTYBdsRBXP84A\r\npHyBoQVt4di43a5iEyKhAfZ9bxQ/ZElb5N0qPSt0UTs/piX8KmG43FMC1Svc\r\nGwJ6k33M+1y3/GJ1T4kWZccvh0R2wmkZ1Y4kq1PQXpWBA/r8sBjf5IPWAgXg\r\np4dGuzCV0hbMto21v+2S5Q4HWc+qEL70CdaqH+IVQ5SKVvYvsH+jXnH4BqB6\r\nyNK79KeOPhAfP61JUqcTNeTvjybHoxWjnI4E6rZoRKZZenaUKVNRbruYbg8Z\r\nOe4tdz9MUQ5ex3CaiZohL4dsSskBY7W0Pe9B1NLYAeB5foYF53dmBPAyTUpJ\r\n99bDs7gjsDzq1Ey7jk+YrzXIXzU8faxvmLE=\r\n=K+I+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "3c31dd619e8c022cde53f40fa12ea2a67f4752ce", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-util": "^29.1.2", "@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "get-stream": "^6.0.0", "worker-farm": "^1.6.0", "@tsd/typescript": "~4.8.2", "jest-leak-detector": "^29.1.2", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_29.1.2_1664522570098_0.49252976126169434", "host": "s3://npm-registry-packages"}}, "29.2.0": {"name": "jest-worker", "version": "29.2.0", "license": "MIT", "_id": "jest-worker@29.2.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "b2bd1a81fc7a1ae79a500b05f5feb0d1c0b1a19e", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-29.2.0.tgz", "fileCount": 17, "integrity": "sha512-mluOlMbRX1H59vGVzPcVg2ALfCausbBpxC8a2KWOzInhYHZibbHH8CB0C1JkmkpfurrkOYgF7FPmypuom1OM9A==", "signatures": [{"sig": "MEUCIFWZZqi46NCqJ25atGwqxxF18+ggKrrlqSIsV7WppCcBAiEAzfqEzLwjDtqEyR5Y9V7iKRGDoSLXjPN7mKOFwW0XTh4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84461, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSShQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp0uhAAlrQTTY0sE8tzpsZdAnsYDbhVEiIEH19Ur0QErf88dAZJ2V/J\r\niDYjrq73Y8Es+/e60bKETiwflyJvm9BfrjKxG9aLQUp+WqOf5aNl5pe/gPB+\r\ny8OB0+gT29/Jyi6NJ0/kL85+el/AdZr3ALGcowj1NNQ0MS2/AMMg2vV3w03a\r\nSkhSRzF9ktzTgIrtJw3qgs44Peh05FX6UtF8RAbeHi5MpIk7ZtjiXdMxPrTJ\r\nJUW80v2LQQVtAcok0Z4iHkK+qhJdWksxtmt913Eg2F9kstNu7aJuYNmBBJXS\r\n4ZI99FdCFFqJrYVdUSOX7q0BzK1B9gPdFvCszJHuqVGyhcCSHgbD0AMmrbCI\r\nXEs5/Xoon9ES0N5rjECJfeBfd+gaK7knhz94B4Txbfo19IM9m8krD5Iwj3KP\r\nSkBOARrMwf1NKBlOyncmGKtLNSnWjDF1tB5DGMhkGNiVZd0AHANpDYZVITek\r\n/6+/VX3ReHByj4vsbXr7ltlX4Y/QOpkGbaNAtY5Kx07kjTRUwl0SdW5yEoVx\r\ndzLxzFHqW5UHG2QJidp20ByWMHvXeE39TQK/A6S4hmEw2uy88ZkKxYQUMIyJ\r\nxyul6/ZJyudR6j8PDR+JJv8GyQHfAUoCXHepeayneH4vQLireGBVDqhMQkXW\r\nhJ6v9e2U8E0IGm2mtjGyg6EMxXYJQxwk/tw=\r\n=77kc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ee5b37a4f4433afcfffb0356cea47739d8092287", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-util": "^29.2.0", "@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "get-stream": "^6.0.0", "worker-farm": "^1.6.0", "@tsd/typescript": "~4.8.2", "jest-leak-detector": "^29.2.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_29.2.0_1665738831765_0.6765740700720222", "host": "s3://npm-registry-packages"}}, "29.2.1": {"name": "jest-worker", "version": "29.2.1", "license": "MIT", "_id": "jest-worker@29.2.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "8ba68255438252e1674f990f0180c54dfa26a3b1", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-29.2.1.tgz", "fileCount": 17, "integrity": "sha512-ROHTZ+oj7sBrgtv46zZ84uWky71AoYi0vEV9CdEtc1FQunsoAGe5HbQmW76nI5QWdvECVPrSi1MCVUmizSavMg==", "signatures": [{"sig": "MEUCIDoEqHHnytzTtHC5gPrxJn7UHHhQ3+pV1VraSsERmusBAiEA+3WjZuwuriTpArDi88tg1Mj4nM9vlRDKEOuHiPytd3I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84461, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTs2OACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp8JQ/+OQ0cCOKx6LDztPxLc0lG3TEGjCBQOReZh2Yt2SFJUxk8Kww5\r\nQ7d3wd+w4Z1SO75cRdgcQfTCj9N6lxG3QP+NKvqPorPsrDsyr5Og/d3puMnu\r\npYHYuynorkj5JxM18K2Ffh91TT0aR9cu9wJTjkG7uL2WlN7lNIDjf3x9hfH1\r\nYjNpbG12YpS8s0nzgS3uAsMjzEzlnCmrd3CTSmZIL/Ou546tqUZQNbm/mbdy\r\nlMWTeob7nEWUO3pEpoe+5BhXr3vzQgFe9g2EbqW6VHubzKPDU6QiLOW+ebMA\r\nCk3m0FgXSVLJfP8e+6KAD6uUm7GwcvoD4V9o8KZQnGGvHqXWJrDJN1cFkrz1\r\nZD+NZsJs9Omq8OZWuvY4Ha8+H1K1CzWe/sP5jIbGPR5lv7SrouOYiJ8wNJ0D\r\nH95QK+0zi9qGFqOHei+6m+9C1G5h5FcoKaUovfRhYzP/4C+wO2aQunUPNHEr\r\nB9/FLtPMjIAprAqXke/1f5RqI/BN29RfRcsC3398EbpsBholpuzoYyOoNDhc\r\nC8TffhClBI2jPox8k+jVaYgSyTscLDAlZi1uJug6lwki5OQpBdQXoBKCYLkp\r\n9ZMjq2UP7eY24ju/8oSOsB7SdoS7Qem6MwJnRdT63qnCouCCG6uCU6ZAkPd6\r\nKOxyPfAOk2Kz8YdaVRrnOur+ofDXWDLopRs=\r\n=PQAF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4551c0fdd4d25b7206824957c7bcc6baf61e63bf", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-util": "^29.2.1", "@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "get-stream": "^6.0.0", "worker-farm": "^1.6.0", "@tsd/typescript": "~4.8.2", "jest-leak-detector": "^29.2.1", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_29.2.1_1666108814626_0.0019998292650091898", "host": "s3://npm-registry-packages"}}, "29.3.0": {"name": "jest-worker", "version": "29.3.0", "license": "MIT", "_id": "jest-worker@29.3.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "240a1cd731c7d6645e8bcc37a3d584f122afb44a", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-29.3.0.tgz", "fileCount": 17, "integrity": "sha512-rP8LYClB5NCWW0p8GdQT9vRmZNrDmjypklEYZuGCIU5iNviVWCZK5MILS3rQwD0FY1u96bY7b+KoU17DdZy6Ww==", "signatures": [{"sig": "MEQCIEmVW2PXDk4fsSb81YtwuI25aqczlFHXkkTYS80a3Ok+AiADsvkopFNqvP2jhPvOmVrWKhXp54aOIKe99TJUIyVZ7g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87330, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjaUaiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqNmw/+JxArrHSZeI9mQCIE3Midub+KuIS6zkXHJJDmgNqksrkF6IAP\r\ngdYFCchRmRrGqu7wmRcJocLMNpWvwyAW+9uIsiuyEsBT55j2+7c/XyCncI66\r\nbNsAU7DRa6/baq8jGCEn3HxV80tm1RkS7mFTHkFMorZqjdzPMYN2XfUyOkgL\r\ncz2a1RmTD9kkO88r/2hlGr79Cz+/VNTH5FThyFz8Ymqnj0iimZXYVslFrZkZ\r\nJH5JVqzYgL5DhHUTlkXVIY9b3c09Pxqux0CMS9L+7D7cRl/xSWIaJ0G2iGW1\r\nXhTzP9KrCtP0naN02LMJgb79l/UqxIlP+vGNsOyrc+U7MT/bp+/dt6Gkvbis\r\nyiPjNu5jTbQnzOWlUtW0jGDBd1EtiH5lwSYlmcZBbE2Ot8qxt4POzpuL/TGB\r\nXNB+Qht/X0nGwXmpPEvzbzr1QW1JWha68KIT3Ozc4L2VRaGRDMEK0rBtZn+6\r\noAKDw8A9KjiUwaM6zAAh5SRYz0i2bZVJA3If88CZWmT8awRJ/eDqz7/R4+o8\r\nvJqb7iHIXpwPLTm7sRMHGs0prV3SkLdMR6BAcZU2NDNioZftHjrJut+4ACZ9\r\nVKt+6JBxfeCl9PYg2PAKBQ9tsiAB4c66xhwqWnY1wWfx6JD6WcnMT9XOy5dB\r\ndPObp6lm72hQH8GamKu8GzS4Q8TuAmbAMv0=\r\n=OqSi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "84b8de987b33e2da20dc833aeb65f23d72a673cd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-util": "^29.2.1", "@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "get-stream": "^6.0.0", "worker-farm": "^1.6.0", "@tsd/typescript": "~4.8.2", "jest-leak-detector": "^29.2.1", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_29.3.0_1667843746191_0.3988249669282702", "host": "s3://npm-registry-packages"}}, "29.3.1": {"name": "jest-worker", "version": "29.3.1", "license": "MIT", "_id": "jest-worker@29.3.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e9462161017a9bb176380d721cab022661da3d6b", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-29.3.1.tgz", "fileCount": 17, "integrity": "sha512-lY4AnnmsEWeiXirAIA0c9SDPbuCBq8IYuDVL8PMm0MZ2PEs2yPvRA/J64QBXuZp7CYKrDM/rmNrc9/i3KJQncw==", "signatures": [{"sig": "MEYCIQD2/vIsO03hAb6kyTFH+pP7iP2MqsHgIkRnSnf84BGycwIhAMirVvyIyWAdqfKCyXdLFCpNaaN2rmzpgii4fEC6z9J5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87330, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjat6YACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqXCRAAiDVNKybBl8sCSJet4anl9JMrK65ooTKNg+4vyxrmV7qxXOVW\r\nGQ5gt5v+Wm3/EYuYOVyMBYAnFX2nN0sy6PYgeAI5eHINtMl0QUfv+yBM8WTH\r\nf/Jjn6gDcm5lK2jRZjDXUFjCWZGGFTwiDIRB7F+bVvG583jSoqixNN+yDUoX\r\nd0HerKcMBGeDaqbyhqLNfAAlUidEEs/jA+/ORch+8a2BY4D2yoZZpGgxzsSd\r\nCyMYs8464A3Ba4b6qchMyJO6vzp6X9E3UTqXyyEGa0FnnDeSfll2wTQbg2YB\r\nXNAPzVQyshg0Pn8TEDzHHaLPJWti/G5J7x4M/dU0ZlJP7bb3H86FKAlMFS+k\r\nVgA+t3lsppDpUDBbY/uGQTzVZQSO+MAunR5Phxcj4ts0JP9pQRWptGxgkwac\r\nAqgCYZEKMcf72UOGXDhPmIpSdlgb0Ckr3nyC5CuqbW4EVoTYDZkhGuVnUVYK\r\nk4nqtACEp4thNtXIpPe9MUqa6qiVUBUglsysW/A1HRKiPVP+ySxk6IFwT5Op\r\naGurfLcvDbRSYa3226A+w14mTNSwBj8PK1qUdcrmqxkutXEp438JFMwcKUI/\r\nnGwXDrk8ZvDW3LI3Btgc0lET3gIro8A6G4D6t8TkkUQ7iA8QV1DevbXQBTT4\r\nmjN8DNiwYrKxryd4rcmlYtj+1jgDsm8mJY4=\r\n=eLOa\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "05deb8393c4ad71e19be2567b704dfd3a2ab5fc9", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/1.11.3/node@v16.17.0+x64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.17.0", "dependencies": {"jest-util": "^29.3.1", "@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "get-stream": "^6.0.0", "worker-farm": "^1.6.0", "@tsd/typescript": "~4.8.2", "jest-leak-detector": "^29.3.1", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_29.3.1_1667948184489_0.21464768734454198", "host": "s3://npm-registry-packages"}}, "29.4.0": {"name": "jest-worker", "version": "29.4.0", "license": "MIT", "_id": "jest-worker@29.4.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "fbf6d700c3366c555765938da75990c1e7fdcdcd", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-29.4.0.tgz", "fileCount": 18, "integrity": "sha512-dICMQ+Q4W0QVMsaQzWlA1FVQhKNz7QcDCOGtbk1GCAd0Lai+wdkQvfmQwL4MjGumineh1xz+6M5oMj3rfWS02A==", "signatures": [{"sig": "MEYCIQC03Xlse4WaALYH4nFqVGJP9XG00d65Ydh2mCY1jBqNEwIhAO3X9n9CeqhZ/7lKLK1GwzLPN2liaFRp7yE/df3WKU3B", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 127594, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjz7k6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqo/xAAow+m5rOws2PhrRrGfctd4JHFPhrfCor8qfLpdEw+G4sB1YqD\r\nV4xpBYQQVVqGQnhGSQlyXokfeqEpz5w3rc9HxKbSWaIVg/eWyJpfoU2sqS7i\r\nLi/VaVQYhOB8SqQ/chIC4cwgXAEdNcmfhDE8J3u/waXGaL48iZXfEn5Rc28O\r\nYGxHkee+pxpxPwJQMiK4ef9I+SFSbq6d1MlN5325zDkIJHBJTX/NcOfYgonX\r\nUs6ucRFVQa3DFyPpf5oM91ji4qdgKUYoSdm7YD0WjKd9CJaYLTJMcmTxabFm\r\nPPGt5Z/bUWP6g47bbuJyBTqgoNI9sE9BpSJRWqq1qlbkm42h0uWHndzkzN5f\r\n53JN5XlKA4A5ADKm/KGDeSmqCJYITF3auryE+hZj7VgN+K0Ert2cuWBDiwA+\r\ngUA+QYb1HmI40icNU/OCcAoBuyjTV6DFYcgJJcypkrV+VdnbY3xbmeo/I+Io\r\njFq5IneURpso2GCfJWLT0kspYchUjKlmW2kK+QJ7pWCd/Yua68SuPXLaA7z9\r\nhGv2eR4GAFAda5CcDTZM/HEgi3zAoRC8tF1TqwGfEE/tcvo31O/t+0K3mUMU\r\nT4GqjzT228FGjyv031SoVX53pAmVED/WY7yzRrPeew/0YUODMpjyFI431EId\r\nNA6Pq+XKgpd1sn/I+K2qR245X5lBrF+sAWg=\r\n=+9at\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4bc0e8acaf990e6618a7bed1dca67760c20bb12a", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"jest-util": "^29.4.0", "@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "get-stream": "^6.0.0", "worker-farm": "^1.6.0", "@tsd/typescript": "^4.9.0", "jest-leak-detector": "^29.4.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_29.4.0_1674557754070_0.2919955672114307", "host": "s3://npm-registry-packages"}}, "29.4.1": {"name": "jest-worker", "version": "29.4.1", "license": "MIT", "_id": "jest-worker@29.4.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "7cb4a99a38975679600305650f86f4807460aab1", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-29.4.1.tgz", "fileCount": 18, "integrity": "sha512-O9doU/S1EBe+yp/mstQ0VpPwpv0Clgn68TkNwGxL6/usX/KUW9Arnn4ag8C3jc6qHcXznhsT5Na1liYzAsuAbQ==", "signatures": [{"sig": "MEUCIHvXLkc/Gn9Mje5kHHYy+XvZNAK3MQxF/LI4A9WZK2MDAiEApASAjr4nlaaWgCJpkoJ8JvFKI8SlJA7KvkqLc/EzQtc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 127594, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0pd2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoAvg//brURW+1bmPhc9vxG9EZ9MqTn11Bgqgk7VV2w4mLiBi+kNZnV\r\naiBftqaOszoDNr6nBUk6aW4Q8eWUo1Qb1VUTY47PBGwvJktXQv9p4i6HMKpV\r\nj1liL2uWfT0x5P8rMdIXunfMatIN4TeOZg2sjaNw18bcJ5jsSvKaaBP8639R\r\n9ygSZekRvxRVUBDvz0k2N2+VBaoIx/vdSXHKB6kRrnXeV8xA9bbBzNEGl3XH\r\n6pi4F35byhSpVIxYcH53Xom/4EXYTOe+qLgOQK/BeawW3IyrsVb2uMq4V/s1\r\nT/Z5ranUHzkTlYp9iZdjT+uLvOVetNSV02EJiv1+SOsJNcqDWWDYbSAj6ZcB\r\n0tE0BUlLIHUfs6LrKhyfxcSnO4xpjW6Zx1sK67wVQpbxSu9vBQ4LUqkAJw8y\r\nbhDuyGwdKCT9uWpVHqyXP7QI97lbwMVjSHMS5Ssqx+RCp3kTXAcobLljMhmK\r\n2ZANK6pNLLJaD8fLhJDaT2At2AjmC1ihWi2gvyxIM44KUhxGUJj/4TLMABFk\r\nFutSJ86YNsCTnHoVRY1aTje7JQcXiBxVV5wVqzy+Gx3xqg50OBjJXTbdOguS\r\n6Nojlc1Hiq5GsjMUK5yF+l+iZ67jJnhRI8YpWhNmp9FcLtszcmfy6xF8Lz1o\r\nLGtq3hFz8+Mad1Qh8y4saQ6lBGhebRGLSS8=\r\n=f/8e\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "bc84c8a15649aaaefdd624dc83824518c17467ed", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"jest-util": "^29.4.1", "@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "get-stream": "^6.0.0", "worker-farm": "^1.6.0", "@tsd/typescript": "^4.9.0", "jest-leak-detector": "^29.4.1", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_29.4.1_1674745718255_0.07169915889953238", "host": "s3://npm-registry-packages"}}, "29.4.2": {"name": "jest-worker", "version": "29.4.2", "license": "MIT", "_id": "jest-worker@29.4.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "d9b2c3bafc69311d84d94e7fb45677fc8976296f", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-29.4.2.tgz", "fileCount": 17, "integrity": "sha512-VIuZA2hZmFyRbchsUCHEehoSf2HEl0YVF8SDJqtPnKorAaBuh42V8QsLnde0XP5F6TyCynGPEGgBOn3Fc+wZGw==", "signatures": [{"sig": "MEQCIFVK30TzO6KnzKoeg9ecrLr0j+MHucwnpsdRbQkLPRDCAiBw+S3B8dRbPI0T57xqoSdOvBoLLOMYowiJWOxvEyrtyA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87649, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj4lX6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpx8xAAolTlWFizwViFjguvpOewk3Qmo8vH3smoEMe4PRE5zgV7C/oB\r\ns0iV0FiT+d+WRzZK9iBJ92H2b1PPngBs7YCzqzMJk2O26etYpp6jXQ6fGT2W\r\nJCEYz7ZikMp+t9C7TnnAkW9YOQO0E0WReMWqH7Yj9JSsIO+z5ut7ukeZxEN9\r\n1zTQNW/8geyBWIMVaQEcGBbYvhnZyWwp/jAtM7FaL+N3YW+Fq2+ueDommmGc\r\nc5g+GiNxRpBEg44eazwCk6U6KEKijOxvoSAzQBOB0cQTfK2UPB/5G3n8Apkh\r\nGcCuxa+IWlgGSxUhNTEv5ERVan/THn5cISxB58sIgkH3s+4R4H51Yl8Igv8I\r\naWxs/U+3g7If5VloDzvSKxcCbOwI3tFhXgIWs+wWAeO7ROZVUw/puSqN6hPI\r\nvLNiu9V+QtYBMMMzR/bcUWr0ccPCdmRcVhOsNS+nWnC/5MFE/SugKp0oinC3\r\n6uyO3Cn12xw+B+NVpSi8l5M4gdUnJi0BU/7zJeArsbZpZsZsjUHcK4IYw87T\r\npIzwZggt3ix3g9BBJhaM/4aJqWeEtCv5cl1OLaTxxjaXu8q+eBatjN0ENWa6\r\n9/JqhrxKQsxPwAv/UWrdEc6XoCP9lsNPQyWCy8aOpsP3SeIdQaakB+1vDb2m\r\ntg7dnVkHtzlgVR3S3Y+CC65tDY+rgA9W2RQ=\r\n=91PI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "f0fc92e8443f09546c7ec0472bf9bce44fe5898f", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/1.13.0/node@v16.19.0+arm64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "16.19.0", "dependencies": {"jest-util": "^29.4.2", "@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "get-stream": "^6.0.0", "@babel/core": "^7.11.6", "worker-farm": "^1.6.0", "@tsd/typescript": "^4.9.0", "jest-leak-detector": "^29.4.2", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_29.4.2_1675777530435_0.32339297870946315", "host": "s3://npm-registry-packages"}}, "29.4.3": {"name": "jest-worker", "version": "29.4.3", "license": "MIT", "_id": "jest-worker@29.4.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "9a4023e1ea1d306034237c7133d7da4240e8934e", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-29.4.3.tgz", "fileCount": 17, "integrity": "sha512-GLHN/GTAAMEy5BFdvpUfzr9Dr80zQqBrh0fz1mtRMe05hqP45+HfQltu7oTBfduD0UeZs09d+maFtFYAXFWvAA==", "signatures": [{"sig": "MEQCIFfZ+znsVQC5pvq1SXcCnE0pFvbf2DSYJAwhLApddn61AiA7+q3iakqudNO5b26TKLGrJFZJe6xT/PqsozaIydsaXQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87385, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj7MikACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmocEg/+P7Rh6lHgoV+VK4SHNsKPWI2wVrYzC8Zia+pA6P5i1DjdQeL1\r\nIqdUEkSm2Mh+yvQEo7sy+x45YnFi3QxKBBRGy0Ro4CmDuaqiqb/RjHCoL0LA\r\nrqiHAJRV4jIXZhygcy+Royv/YVq2mFhQUCwSM4uEypqx3CWg1iI08LvitxBB\r\n0uJrNQ/YBcq/kOwIIX4buYFojcCaHCrteWZqpKSSsY2ZQRt0y4EnYxKZJsEA\r\n9xL1FB5tXC7tQj/CbpABxiErhRuYF9edMAPx7c1vm3nRnD2utY8QMhJOzyDc\r\nx+Eb3bOWxsjvGo+IMOfShcOJTcLfpp/hIKMdYNi/wyKzr1mGgBARGAGDwGsR\r\nEMMXXBPMX+bVv/zOp+0wTTSavp/5UKwNpoF99fo9AVxKNSWqF2G4r2uIaHAf\r\nkZ5I3g0+X7apmvFqhMFoKgnzRzmvnx6q8req5JIF842LV37419JmXcDZ3vfw\r\nUBD9dUm0gv5kMOeD86KfYhF24gO36c2rstlavmbsA4CewYhKUAl3rL8FlqV3\r\n5BpwqJT/SIy3deNSugJz1zMrcdaZ4kvHcuC3THHuwAV4ZGYE7FkUPWigqZ5u\r\neWBM95DRTlri09g3MPcMlrY7yHJ321vTaGq+l7j2fUZRgbRf0rYABQua9KHH\r\nwWwQCRUf4vyHFnKb8PX8gEDD7klL6QgBrrI=\r\n=PwvR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a49c88610e49a3242576160740a32a2fe11161e1", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/1.13.0/node@v18.14.0+arm64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "18.14.0", "dependencies": {"jest-util": "^29.4.3", "@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "get-stream": "^6.0.0", "@babel/core": "^7.11.6", "worker-farm": "^1.6.0", "@tsd/typescript": "^4.9.0", "jest-leak-detector": "^29.4.3", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_29.4.3_1676462244037_0.5269105471236717", "host": "s3://npm-registry-packages"}}, "29.5.0": {"name": "jest-worker", "version": "29.5.0", "license": "MIT", "_id": "jest-worker@29.5.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "bdaefb06811bd3384d93f009755014d8acb4615d", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-29.5.0.tgz", "fileCount": 17, "integrity": "sha512-NcrQnevGoSp4b5kg+akIpthoAFHxPBcb5P6mYPY0fUNT+sSvmtu6jlkEle3anczUKIKEbMxFimk9oTP/tpIPgA==", "signatures": [{"sig": "MEYCIQDDBg9FEQZJhyvNKO7TFY4tTa5q9oyI9Uli5hu8T6zCaQIhAN89/zlDs/tGzGdj9twwmdcFtzY1G9ttk2Nio+LyUEqc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89811, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBeusACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpeahAAicfd+rQ3p3WwY307i/IY1oPbwWVco7Zu36Bc4lsMjl7RgwmR\r\nm9U0hgYyzCE7jVpdPaTYlTxYOyuwIs4BBUbPyI43sc4H8u6iuZf0HKtgDW7f\r\nT7CrjSvfyjwHxjmg5luGs8hEndZ1sLSfZXxVRJQqV59yYUxGXv3R9yDuX9i6\r\n13GdU7P2Q2PQJSIIJMZ3HR/ZOB5LPdOZQXVTqE63KDFk+JFGwr5l1pEUHDLm\r\n7s9MmFROkx1erZHHOzsbdxufAFKD8bvbEHgInXiwZtAhhzxHQSko/HzLU+ss\r\nyh2eU3YtRs+CKFjVJMoITyh5g7oOzePbD4fGaiX/GQ9vtdMvgbnXrbXWLuAc\r\nP1wXdb3CZ5Xl6EtEwlwrrblzoaE+oNFCjun42/7COwZslCpnX20QskOCBhLW\r\nv5bztpTcLBJfJizLXo+7cEN8A811FS8EUNtnH3IeGGhD/0QVOEH9yYNihr53\r\nWvGDwhM7zy7wF6NIe5nh72FBnJOz+myY7zGyd9EMxBbPQkKVlLefOA+MOogD\r\npNBzmkWeXzZeOPBQF4xKLg6FpZ8JxNYLApqIOplpXc2NRqicUCBmRDfcfdgA\r\ngRZnPlN6333jq8cNsUXlenie/50piSKbYo2+8+HMAgsR7DOEgXzLE5Lj5ZGg\r\nzxwqUgJnM6Ay+CS/hAHxVM4GYTDcM06GF9c=\r\n=Gega\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "39f3beda6b396665bebffab94e8d7c45be30454c", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/1.13.0/node@v18.14.2+arm64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "18.14.2", "dependencies": {"jest-util": "^29.5.0", "@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.6.0", "get-stream": "^6.0.0", "@babel/core": "^7.11.6", "worker-farm": "^1.6.0", "@tsd/typescript": "^4.9.0", "jest-leak-detector": "^29.5.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_29.5.0_1678109612094_0.24953782601862895", "host": "s3://npm-registry-packages"}}, "29.6.0": {"name": "jest-worker", "version": "29.6.0", "license": "MIT", "_id": "jest-worker@29.6.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "e0c40226d073fdb8f0dfe87d7f90f8fd987d8ba3", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-29.6.0.tgz", "fileCount": 17, "integrity": "sha512-oiQHH1SnKmZIwwPnpOrXTq4kHBk3lKGY/07DpnH0sAu+x7J8rXlbLDROZsU6vy9GwB0hPiZeZpu6YlJ48QoKcA==", "signatures": [{"sig": "MEUCIQDXANw/85gmQAeFV5mhpL8Ex+Szqk9Msly2JI85Du88CwIgf0jB3TnbKuKu21IK4U+4YVsLDpV3UrfXcITpsp3bWhk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90554}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c1e5b8a38ef54bb138409f89831942ebf6a7a67e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"jest-util": "^29.6.0", "@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.7.0", "get-stream": "^6.0.0", "@babel/core": "^7.11.6", "worker-farm": "^1.6.0", "@tsd/typescript": "^5.0.4", "jest-leak-detector": "^29.6.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_29.6.0_1688484348395_0.45499529964001195", "host": "s3://npm-registry-packages"}}, "29.6.1": {"name": "jest-worker", "version": "29.6.1", "license": "MIT", "_id": "jest-worker@29.6.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "64b015f0e985ef3a8ad049b61fe92b3db74a5319", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-29.6.1.tgz", "fileCount": 17, "integrity": "sha512-U+Wrbca7S8ZAxAe9L6nb6g8kPdia5hj32Puu5iOqBCMTMWFHXuK6dOV2IFrpedbTV8fjMFLdWNttQTBL6u2MRA==", "signatures": [{"sig": "MEQCIFj928a7LFyj+7ITTDOGkLUUYBR0cjQj6zJSlDhsJXU8AiAy/bICbJsxvlepZRhqa6Z0trcocHZ3YRDF4TrMR+8kQA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90554}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "1f019afdcdfc54a6664908bb45f343db4e3d0848", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"jest-util": "^29.6.1", "@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.7.0", "get-stream": "^6.0.0", "@babel/core": "^7.11.6", "worker-farm": "^1.6.0", "@tsd/typescript": "^5.0.4", "jest-leak-detector": "^29.6.1", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_29.6.1_1688653107973_0.12333332132624664", "host": "s3://npm-registry-packages"}}, "29.6.2": {"name": "jest-worker", "version": "29.6.2", "license": "MIT", "_id": "jest-worker@29.6.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/facebook/jest#readme", "bugs": {"url": "https://github.com/facebook/jest/issues"}, "dist": {"shasum": "682fbc4b6856ad0aa122a5403c6d048b83f3fb44", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-29.6.2.tgz", "fileCount": 17, "integrity": "sha512-l3ccBOabTdkng8I/ORCkADz4eSMKejTYv1vB/Z83UiubqhC1oQ5Li6dWCyqOIvSifGjUBxuvxvlm6KGK2DtuAQ==", "signatures": [{"sig": "MEYCIQCFoNuAKPYqZ5qI+JQNET+jY5c86i12q7R6lnpe6fO1rQIhAOvhwoIaIoZKtQalm7nZYHZH8MjlZh0zqepQnHTFe5Bt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90554}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "0fd5b1c37555f485c56a6ad2d6b010a72204f9f6", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/facebook/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/1.13.0/node@v18.16.1+arm64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "18.16.1", "dependencies": {"jest-util": "^29.6.2", "@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.7.0", "get-stream": "^6.0.0", "@babel/core": "^7.11.6", "worker-farm": "^1.6.0", "@tsd/typescript": "^5.0.4", "jest-leak-detector": "^29.6.2", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_29.6.2_1690449692874_0.8784685450080756", "host": "s3://npm-registry-packages"}}, "29.6.3": {"name": "jest-worker", "version": "29.6.3", "license": "MIT", "_id": "jest-worker@29.6.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "7b1a47bbb6559f3c0882d16595938590e63915d5", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-29.6.3.tgz", "fileCount": 17, "integrity": "sha512-wacANXecZ/GbQakpf2CClrqrlwsYYDSXFd4fIGdL+dXpM2GWoJ+6bhQ7vR3TKi3+gkSfBkjy1/khH/WrYS4Q6g==", "signatures": [{"sig": "MEYCIQDA5K6gjwuJ/W9cnH76FCOzzClHUXjP6aUVBid2+y8bggIhAO8jQy6aKLrT270JPbXYjrp4XKdssyMZfmwtoHaNAKmM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90548}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fb7d95c8af6e0d65a8b65348433d8a0ea0725b5b", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"jest-util": "^29.6.3", "@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.7.0", "get-stream": "^6.0.0", "@babel/core": "^7.11.6", "worker-farm": "^1.6.0", "@tsd/typescript": "^5.0.4", "jest-leak-detector": "^29.6.3", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_29.6.3_1692621557655_0.817278616548645", "host": "s3://npm-registry-packages"}}, "29.6.4": {"name": "jest-worker", "version": "29.6.4", "license": "MIT", "_id": "jest-worker@29.6.4", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "f34279f4afc33c872b470d4af21b281ac616abd3", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-29.6.4.tgz", "fileCount": 17, "integrity": "sha512-6dpvFV4WjcWbDVGgHTWo/aupl8/LbBx2NSKfiwqf79xC/yeJjKHT1+StcKy/2KTmW16hE68ccKVOtXf+WZGz7Q==", "signatures": [{"sig": "MEUCIQDTxOUzs3fQ348VbYO5HJDQk+aHI7h8Rh+dgn+HthK7wgIgAVAUcQNqLiwdGJb717UZNNbXvLPCNbEqTReX+s+4kiA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90811}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "55cd6a0aaf6f9178199dfa7af7a00fcaa7c421fd", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/1.13.0/node@v20.5.1+arm64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "20.5.1", "dependencies": {"jest-util": "^29.6.3", "@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.7.0", "get-stream": "^6.0.0", "@babel/core": "^7.11.6", "worker-farm": "^1.6.0", "@tsd/typescript": "^5.0.4", "jest-leak-detector": "^29.6.3", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_29.6.4_1692875426655_0.26543931902486273", "host": "s3://npm-registry-packages"}}, "29.7.0": {"name": "jest-worker", "version": "29.7.0", "license": "MIT", "_id": "jest-worker@29.7.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "acad073acbbaeb7262bd5389e1bcf43e10058d4a", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-29.7.0.tgz", "fileCount": 17, "integrity": "sha512-eIz2msL/EzL9UFTFFx7jBTkeZfku0yUAyZZZmJ93H2TYEiroIx2PQjEXcwYtYl8zXCxb+PAmA2hLIt/6ZEkPHw==", "signatures": [{"sig": "MEYCIQCtil3TQ/akYr5ZgwApm8PF68QXDInaKplZBmxh5kgahQIhAM5FfP/jVGaoWGzEsGt2zCmfzGjbZbSBjOTNUR6OmRUm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90811}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4e56991693da7cd4c3730dc3579a1dd1403ee630", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/1.13.0/node@v18.17.1+arm64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "18.17.1", "dependencies": {"jest-util": "^29.7.0", "@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"tsd-lite": "^0.7.0", "get-stream": "^6.0.0", "@babel/core": "^7.11.6", "worker-farm": "^1.6.0", "@tsd/typescript": "^5.0.4", "jest-leak-detector": "^29.7.0", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_29.7.0_1694501025343_0.425896336942581", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.1": {"name": "jest-worker", "version": "30.0.0-alpha.1", "license": "MIT", "_id": "jest-worker@30.0.0-alpha.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "cb3bf0381f493c5daf789e9c4e183dc5cea82b69", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-30.0.0-alpha.1.tgz", "fileCount": 8, "integrity": "sha512-hi+/wHhToFq4AIdZogc2RZhKo0Hnm1FbcMr3M2ZemjHrk/G5cp+Uhg+cAamrmnT7wkin0TM8wqO9+a2hOc47oQ==", "signatures": [{"sig": "MEUCIQCAx4oDKpJK1p0ioJVWTufIVnW6q/S2ou4sOOJM2zj2ZgIgUr+Z8Y1H69oBwH2i0utIjMtFc6eGEn/1MDRUxHHmS04=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 100096}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "d005cb2505c041583e0c5636d006e08666a54b63", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/1.13.0/node@v20.9.0+arm64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"jest-util": "30.0.0-alpha.1", "@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.8.0", "get-stream": "^6.0.0", "@babel/core": "^7.11.6", "worker-farm": "^1.6.0", "@tsd/typescript": "^5.0.4", "jest-leak-detector": "30.0.0-alpha.1", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_30.0.0-alpha.1_1698672793367_0.8332594434306044", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.2": {"name": "jest-worker", "version": "30.0.0-alpha.2", "license": "MIT", "_id": "jest-worker@30.0.0-alpha.2", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "d8e9dc386b31bb8bc6fdbdcc19dfbb7effccaeca", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-30.0.0-alpha.2.tgz", "fileCount": 8, "integrity": "sha512-ucN1yueK1Kq6gytid2AwsNemTRpq79h61fXgySGhRtlHFcsM4Su6sPFQrWzUcGg9F8fNI4HjLZckqdTFW2tvtg==", "signatures": [{"sig": "MEQCIDQqxDRQaS67kEjhEiPZeW0cJ2H9U3WklLF+FSnginWiAiAONiQyXW+LbRg5LKFgxSzA1K1P5nk45MQJ793CdgmyGQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 100142}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "c04d13d7abd22e47b0997f6027886aed225c9ce4", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/2.7.0/node@v20.9.0+arm64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "20.9.0", "dependencies": {"jest-util": "30.0.0-alpha.2", "@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.8.0", "get-stream": "^6.0.0", "@babel/core": "^7.11.6", "worker-farm": "^1.6.0", "@tsd/typescript": "^5.0.4", "jest-leak-detector": "30.0.0-alpha.2", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_30.0.0-alpha.2_1700126909887_0.4201886936125685", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.3": {"name": "jest-worker", "version": "30.0.0-alpha.3", "license": "MIT", "_id": "jest-worker@30.0.0-alpha.3", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "rubennorte", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "fb", "email": "<EMAIL>"}, {"name": "davidzilburg", "email": "david<PERSON><EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "fb6be6cb4e45cc00dfba739686818701eda97681", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-30.0.0-alpha.3.tgz", "fileCount": 8, "integrity": "sha512-8lS9LxbEjOyBRz0Pdi6m3HYJ3feIi1tv0u7oqxjXvB1lMksq+IcSxaPTCcvJbIqt3WAFFYQnDs5I3NkJiEG5Ow==", "signatures": [{"sig": "MEUCIFoZmb7/Kg/FPREr4ff2mHGBMDIfJj3BQHlBkdgJxnJaAiEA3OPMLb9C7ewf/6IGbrWmOGrwSUyNvmb6NeFvCq07FYE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 100194}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "e267aff33d105399f2134bad7c8f82285104f3da", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.2.1/node@v20.11.1+arm64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"jest-util": "30.0.0-alpha.3", "@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.8.0", "get-stream": "^6.0.0", "@babel/core": "^7.11.6", "worker-farm": "^1.6.0", "@tsd/typescript": "^5.0.4", "jest-leak-detector": "30.0.0-alpha.3", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_30.0.0-alpha.3_1708427350501_0.9253036914515578", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.4": {"name": "jest-worker", "version": "30.0.0-alpha.4", "license": "MIT", "_id": "jest-worker@30.0.0-alpha.4", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "d8b02df59873306ae985b9a8c7c3b0082b749dbd", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-30.0.0-alpha.4.tgz", "fileCount": 8, "integrity": "sha512-01gbBNwDRU3VTZXW8H/TuD0+FkTo/f4huMsbJskL4CkYe6Bylze4aAsezRNZPEcxHn6lnlk+nu8f0FWnnTnGzQ==", "signatures": [{"sig": "MEQCIGV62d8rmHgbbXgjKMyUvaA9KjY1r3V22AURtrjRn/yDAiBoGH2RK4NisCKrPtGjGrqoqjZnFolGh66aG1jHXtezUA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 100238}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "32b966f988d47a7673d2ef4b92e834dab7d66f07", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"jest-util": "30.0.0-alpha.4", "@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"tsd-lite": "^0.8.0", "get-stream": "^6.0.0", "@babel/core": "^7.11.6", "worker-farm": "^1.6.0", "@tsd/typescript": "^5.0.4", "jest-leak-detector": "30.0.0-alpha.4", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_30.0.0-alpha.4_1715550209256_0.11182986983596743", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.5": {"name": "jest-worker", "version": "30.0.0-alpha.5", "license": "MIT", "_id": "jest-worker@30.0.0-alpha.5", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "homepage": "https://github.com/jestjs/jest#readme", "bugs": {"url": "https://github.com/jestjs/jest/issues"}, "dist": {"shasum": "b484f7df00277d0a8f7d77267058fe1fbd9756bd", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-30.0.0-alpha.5.tgz", "fileCount": 8, "integrity": "sha512-BNOP7TMpf4Fw+f7IiVxXS//NroCY/nAzR4yAr5ABkHZDTh3eWIBwGWNCBqK/IBZcaStQYqvgTwjVWgfPAUalPQ==", "signatures": [{"sig": "MEYCIQDSmHxeL3lK9OxIofSlfwZ8yKaDWN08T4808Lmy22w+HwIhAPNa1cjLjVf46wlO0+sepU9e53vOtILOPM5Y9zZ0T3C0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 100179}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "fa24a3bdd6682978d76799265016fb9d5bff135e", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.3.3/node@v20.11.1+arm64 (darwin)", "description": "Module for executing heavy tasks under forked processes in parallel, by providing a `Promise` based interface, minimum overhead, and bound workers.", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"jest-util": "30.0.0-alpha.5", "@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"get-stream": "^6.0.0", "@babel/core": "^7.11.6", "worker-farm": "^1.6.0", "jest-leak-detector": "30.0.0-alpha.5", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_30.0.0-alpha.5_1717073048647_0.7118434966576386", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.6": {"name": "jest-worker", "version": "30.0.0-alpha.6", "license": "MIT", "_id": "jest-worker@30.0.0-alpha.6", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "dist": {"shasum": "6b53ead3e0b5a90ffc892e9be2037def3d27dde1", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-30.0.0-alpha.6.tgz", "fileCount": 8, "integrity": "sha512-qlzX7zFT/QdUV/LWsJwZBlaIBaJ+E2VH3d1gArGVP+9hUHGpJkEzCSBK7yuZrkt+M/U0Jre5+maPRmkinEF4DA==", "signatures": [{"sig": "MEYCIQCCOzP4xPA1BLePZ9BMB0kP1tiBw9Bom/hnPc7olq3BJgIhAOf0CnnNzmFfHPK1b64Q6/o4ErH3eCVblUxGi+/GRK1A", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 108694}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ba74b7de1b9cca88daf33f9d1b46bfe2b7f485a5", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.7.1/node@v20.11.1+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.11.1", "dependencies": {"jest-util": "30.0.0-alpha.6", "@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0", "@ungap/structured-clone": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "@babel/core": "^7.11.6", "worker-farm": "^1.6.0", "jest-leak-detector": "30.0.0-alpha.6", "@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0", "@types/ungap__structured-clone": "^1.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_30.0.0-alpha.6_1723102987181_0.29943837781022786", "host": "s3://npm-registry-packages"}}, "30.0.0-alpha.7": {"name": "jest-worker", "version": "30.0.0-alpha.7", "license": "MIT", "_id": "jest-worker@30.0.0-alpha.7", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "dist": {"shasum": "2c44a929149cd09e0dc224310de0f585e740d325", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-30.0.0-alpha.7.tgz", "fileCount": 8, "integrity": "sha512-kL3vimPjBpN1PKymyD1JYJM5SDTzqtkIs4TcaI4nUIktJEVulnZf0j4YjaAtnYflFohJfgmcPKP6IHqPHcn6dw==", "signatures": [{"sig": "MEYCIQDlaIYkYj3AQeKwOoVEHoDjLXKCsqdsUkXyRt8cx7U6nwIhAJbFBMhKZk7iEK6695+caThCp1swecW6R/ZCd41E6eGC", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 108695}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^16.10.0 || ^18.12.0 || >=20.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "bacb7de30d053cd87181294b0c8a8576632a8b02", "_npmUser": {"name": "simenb", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.11.0/node@v20.18.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "20.18.0", "dependencies": {"jest-util": "30.0.0-alpha.7", "@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0", "@ungap/structured-clone": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "@babel/core": "^7.11.6", "worker-farm": "^1.6.0", "jest-leak-detector": "30.0.0-alpha.7", "@types/merge-stream": "^2.0.0", "@types/supports-color": "^8.1.0", "@types/ungap__structured-clone": "^1.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_30.0.0-alpha.7_1738225714059_0.2183826508098088", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.2": {"name": "jest-worker", "version": "30.0.0-beta.2", "license": "MIT", "_id": "jest-worker@30.0.0-beta.2", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "dist": {"shasum": "5160d8aeb6ae54406654dc1d487cde33f6549c8e", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-30.0.0-beta.2.tgz", "fileCount": 8, "integrity": "sha512-Jaja4GNV4f9gee6rW6Mm/kvfbQOvwK8f6IMZECSIgXOuXMRlmGU9dCm8VYOS8uyG4pCI7ZG4DIiQeVoGY790oA==", "signatures": [{"sig": "MEUCICsSX6oAsEIj5vv6219f7vtw42LEHHxESgl/az7snfscAiEA+x9dATTpwNVe5WTA3KJmDrm0a020b8x/XuOkYawFmaw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 108545}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "53a5635ac9a43099033f6103e179b13a5465e017", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"jest-util": "30.0.0-beta.1", "@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0", "@ungap/structured-clone": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "@babel/core": "^7.11.6", "worker-farm": "^1.6.0", "jest-leak-detector": "30.0.0-beta.2", "@types/merge-stream": "^2.0.0", "@types/supports-color": "^8.1.0", "@types/ungap__structured-clone": "^1.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_30.0.0-beta.2_1748308998917_0.0390875915633917", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.3": {"name": "jest-worker", "version": "30.0.0-beta.3", "license": "MIT", "_id": "jest-worker@30.0.0-beta.3", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "dist": {"shasum": "7c0e60ed2d27261ed2b13e949dc036cf401f9ebf", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-30.0.0-beta.3.tgz", "fileCount": 8, "integrity": "sha512-v17y4Jg9geh3tDm8aU2snuwr8oCJtFefuuPrMRqmC6Ew8K+sLfOcuB3moJ15PHoe4MjTGgsC1oO2PK/GaF1vTg==", "signatures": [{"sig": "MEQCICCjDlKsRBaFVr6LwVDI/IRbz1P4RISRY9a6v7iRJV5vAiABLxVIfZmGAll9axpY3uwqeH6Cwl1DuULm+aVYZBvUWg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 108545}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a123a3b667a178fb988662aaa1bc6308af759017", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"jest-util": "30.0.0-beta.3", "@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0", "@ungap/structured-clone": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "@babel/core": "^7.11.6", "worker-farm": "^1.6.0", "jest-leak-detector": "30.0.0-beta.3", "@types/merge-stream": "^2.0.0", "@types/supports-color": "^8.1.0", "@types/ungap__structured-clone": "^1.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_30.0.0-beta.3_1748309268183_0.9398236119391568", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.4": {"name": "jest-worker", "version": "30.0.0-beta.4", "license": "MIT", "_id": "jest-worker@30.0.0-beta.4", "maintainers": [{"name": "simenb", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}, {"name": "openjs-operations", "email": "<EMAIL>"}], "dist": {"shasum": "4514a36cf682a25e1bda8c14b2aa41116ca5be2c", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-30.0.0-beta.4.tgz", "fileCount": 8, "integrity": "sha512-/v5d0vvj2tVC79FSOQrHZqZIw6dgYluo6ElrZr8BBGycUH8JMndpsW/bVxzA5o5HHlEYvT3uNbliiJi1DzXPgQ==", "signatures": [{"sig": "MEQCIArd2QDVqqZfTULdOMUyOPF/dgdmtf3/f9wZQneYoOwkAiAbWVriYDrWPtRhUl3KCAL2/EDhPHsg0qRCC3XJJaXQ6A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 108545}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || >=22.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "69f0c890c804e6e6b0822adb592cd00372a7c297", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.12.3/node@v23.11.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "23.11.0", "dependencies": {"jest-util": "30.0.0-beta.3", "@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.1.1", "@ungap/structured-clone": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "@babel/core": "^7.27.1", "worker-farm": "^1.7.0", "jest-leak-detector": "30.0.0-beta.3", "@types/merge-stream": "^2.0.0", "@types/supports-color": "^8.1.0", "@types/ungap__structured-clone": "^1.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_30.0.0-beta.4_1748329465410_0.6619251344973143", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.6": {"name": "jest-worker", "version": "30.0.0-beta.6", "license": "MIT", "_id": "jest-worker@30.0.0-beta.6", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "951d88a1b4c9f9a036331b829bc279d53fa95b9f", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-30.0.0-beta.6.tgz", "fileCount": 8, "integrity": "sha512-NfUrwQrUyJl3M7SnQjY1MlezWbcWNheS6GUZav2chT24phSF9xUDtfviqlPVBO3yTE610VejtUfmJjljtSLoMw==", "signatures": [{"sig": "MEQCIB4zgGEGt1iZrKamC1fE9jcwe2kFaO68tR1t6bfDAR40AiB6wM3bofFaA6hzv4ymMuqbwhO7XLrrZ6cIdZwA72hRug==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 108556}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "4f964497dc21c06ce4d54f1349e299a9f6773d52", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/3.12.3/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"jest-util": "30.0.0-beta.6", "@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.1.1", "@ungap/structured-clone": "^1.2.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "@babel/core": "^7.27.1", "worker-farm": "^1.7.0", "jest-leak-detector": "30.0.0-beta.6", "@types/merge-stream": "^2.0.0", "@types/supports-color": "^8.1.0", "@types/ungap__structured-clone": "^1.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_30.0.0-beta.6_1748994649936_0.9471738279968784", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.7": {"name": "jest-worker", "version": "30.0.0-beta.7", "license": "MIT", "_id": "jest-worker@30.0.0-beta.7", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "50f53bb888c232c9ca60bb6fbf7f58976fd3fe54", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-30.0.0-beta.7.tgz", "fileCount": 8, "integrity": "sha512-P7sTQXM9xaUU+XmJoYMCMuZ08AFfk2BF5HR1VK9Z5QFGFRA5ALPW7yX25qBE5DmL+l0qltyDLwPkUglBV32HeQ==", "signatures": [{"sig": "MEUCIHxHT+d2dKEA3TSVBW3CIUouh7ZxYeB0VAlDgk4jmtVMAiEAq/LdcQfvWRSRclYm4rmywvJhxxYRkIG6RXaPXi89Efo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 108556}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "48de6a91368727d853d491df16e7d00c1f323676", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"jest-util": "30.0.0-beta.7", "@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.1.1", "@ungap/structured-clone": "^1.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "@babel/core": "^7.27.4", "worker-farm": "^1.7.0", "jest-leak-detector": "30.0.0-beta.7", "@types/merge-stream": "^2.0.0", "@types/supports-color": "^8.1.3", "@types/ungap__structured-clone": "^1.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_30.0.0-beta.7_1749008143383_0.7362037871868645", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-beta.8": {"name": "jest-worker", "version": "30.0.0-beta.8", "license": "MIT", "_id": "jest-worker@30.0.0-beta.8", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "25b9090ae7b7375e902353a1aeb63ff5f7019993", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-30.0.0-beta.8.tgz", "fileCount": 8, "integrity": "sha512-v9RxYgLR2liTB7xvBz5pBgJDGgohA0cwGl0+QQTY47a/Rujs/C3Iwxt//Ws0eRexjVIDl1PWKibS5TTWZhISyw==", "signatures": [{"sig": "MEQCIEtguPv00Y90sry43xbOcyi7DgEfD74Zz+cjuTo9e98yAiBavno+bpieVSFpUxvDQTPyceogTXT7jYDYnLpK2KnN+g==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 108556}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ac334c0cdf04ead9999f0964567d81672d116d42", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"jest-util": "30.0.0-beta.8", "@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.1.1", "@ungap/structured-clone": "^1.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "@babel/core": "^7.27.4", "worker-farm": "^1.7.0", "jest-leak-detector": "30.0.0-beta.8", "@types/merge-stream": "^2.0.0", "@types/supports-color": "^8.1.3", "@types/ungap__structured-clone": "^1.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_30.0.0-beta.8_1749023591697_0.6126762840691236", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0-rc.1": {"name": "jest-worker", "version": "30.0.0-rc.1", "license": "MIT", "_id": "jest-worker@30.0.0-rc.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "85e9415e8d507e30b93a79f4ebed56eb11e5d460", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-30.0.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-nojG0sKG2jKPTqwQpKv9qUY9X+3Mcj0FbFgQMBuTldW3Xk883fZ9AT3rekYhlRIaVJJEEY5WNOPpBiBBXYaWCw==", "signatures": [{"sig": "MEQCIAnD3r/SRkjw6yCk5tahQhxc9w5EBlHgtGboqJ37tQOuAiAP3asPcgw2jCmFCN/ekGUomSA4CjpaXH1Yxvz4YJ5Zjg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 108550}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "ce14203d9156f830a8e24a6e3e8205f670a72a40", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"jest-util": "30.0.0-rc.1", "@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.1.1", "@ungap/structured-clone": "^1.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "@babel/core": "^7.27.4", "worker-farm": "^1.7.0", "jest-leak-detector": "30.0.0-rc.1", "@types/merge-stream": "^2.0.0", "@types/supports-color": "^8.1.3", "@types/ungap__structured-clone": "^1.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_30.0.0-rc.1_1749430967055_0.6634850632321512", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.0": {"name": "jest-worker", "version": "30.0.0", "license": "MIT", "_id": "jest-worker@30.0.0", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "63f15145e2b2b36db0be2d2d4413d197d0460912", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-30.0.0.tgz", "fileCount": 8, "integrity": "sha512-VZvxfWIybIvwK8N/Bsfe43LfQgd/rD0c4h5nLUx78CAqPxIQcW2qDjsVAC53iUR8yxzFIeCFFvWOh8en8hGzdg==", "signatures": [{"sig": "MEQCIGtJJTCyY5lumrTM5xnpB5nWM4A91kAo0NpWNmRKqzhkAiAq6sRE2p8YehuXAuPiY8PISB1vuiTi5C0k7AXkIzUqJw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 108535}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "a383155cd5af4539b3c447cfa7184462ee32f418", "_npmUser": {"name": "cpojer", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.3.0/node@v24.1.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.1.0", "dependencies": {"jest-util": "30.0.0", "@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.1.1", "@ungap/structured-clone": "^1.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "@babel/core": "^7.27.4", "worker-farm": "^1.7.0", "jest-leak-detector": "30.0.0", "@types/merge-stream": "^2.0.0", "@types/supports-color": "^8.1.3", "@types/ungap__structured-clone": "^1.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_30.0.0_1749521752844_0.28366011799650503", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.1": {"name": "jest-worker", "version": "30.0.1", "license": "MIT", "_id": "jest-worker@30.0.1", "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "dist": {"shasum": "481d72315b02e17db2c45d18f578d2d141496040", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-30.0.1.tgz", "fileCount": 8, "integrity": "sha512-W3zW27LH1+DYwvz5pw4Xw/t83JcWJv24WWp/CtjA2RvQse0k1OViFqUXBAGlUGM6/zTSek/K7EQea+h+SPUKNw==", "signatures": [{"sig": "MEUCIGJMmSpQPk0dJNQbvod0LNIp2RUQ0OyhKqG6b13VN4yZAiEAoAmSyieVSjdVHZvucE+grvCMH8E6jkZnlRLCUPC/s/0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 108535}, "main": "./build/index.js", "types": "./build/index.d.ts", "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "default": "./build/index.js", "require": "./build/index.js"}, "./package.json": "./package.json"}, "gitHead": "5ce865b4060189fe74cd486544816c079194a0f7", "_npmUser": {"name": "cpojer", "actor": {"name": "cpojer", "type": "user", "email": "<EMAIL>"}, "email": "<EMAIL>"}, "repository": {"url": "https://github.com/jestjs/jest.git", "type": "git", "directory": "packages/jest-worker"}, "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "directories": {}, "_nodeVersion": "24.2.0", "dependencies": {"jest-util": "30.0.1", "@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.1.1", "@ungap/structured-clone": "^1.3.0"}, "publishConfig": {"access": "public"}, "_hasShrinkwrap": false, "devDependencies": {"get-stream": "^6.0.0", "@babel/core": "^7.27.4", "worker-farm": "^1.7.0", "jest-leak-detector": "30.0.1", "@types/merge-stream": "^2.0.0", "@types/supports-color": "^8.1.3", "@types/ungap__structured-clone": "^1.2.0"}, "_npmOperationalInternal": {"tmp": "tmp/jest-worker_30.0.1_1750285890543_0.4687797005376577", "host": "s3://npm-registry-packages-npm-production"}}, "30.0.2": {"name": "jest-worker", "version": "30.0.2", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-worker"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "require": "./build/index.js", "import": "./build/index.mjs", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@types/node": "*", "@ungap/structured-clone": "^1.3.0", "jest-util": "30.0.2", "merge-stream": "^2.0.0", "supports-color": "^8.1.1"}, "devDependencies": {"@babel/core": "^7.27.4", "@types/merge-stream": "^2.0.0", "@types/supports-color": "^8.1.3", "@types/ungap__structured-clone": "^1.2.0", "get-stream": "^6.0.0", "jest-leak-detector": "30.0.2", "worker-farm": "^1.7.0"}, "engines": {"node": "^18.14.0 || ^20.0.0 || ^22.0.0 || >=24.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "393acbfac31f64bb38dff23c89224797caded83c", "_nodeVersion": "24.2.0", "_npmVersion": "lerna/4.3.0/node@v24.2.0+arm64 (darwin)", "_id": "jest-worker@30.0.2", "dist": {"integrity": "sha512-RN1eQmx7qSLFA+o9pfJKlqViwL5wt+OL3Vff/A+/cPsmuw7NPwfgl33AP+/agRmHzPOFgXviRycR9kYwlcRQXg==", "shasum": "e67bd7debbc9d8445907a17067a89359acedc8c5", "tarball": "https://registry.npmjs.org/jest-worker/-/jest-worker-30.0.2.tgz", "fileCount": 8, "unpackedSize": 108535, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIGIqATV86jYmGMu3LnOWBYO9/zFiTHLVM2yuZ+llU/zhAiEA89UqOWdxTEMZDiAZB4Wdqptiju7iH96GFoXKZGOwOsg="}]}, "_npmUser": {"name": "cpojer", "email": "<EMAIL>", "actor": {"name": "cpojer", "email": "<EMAIL>", "type": "user"}}, "directories": {}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/jest-worker_30.0.2_1750329980720_0.9087708518590634"}, "_hasShrinkwrap": false}}, "time": {"created": "2017-10-03T11:12:57.530Z", "modified": "2025-06-19T10:46:21.107Z", "0.0.0": "2017-10-03T11:12:57.530Z", "21.3.0-beta.1": "2017-10-04T10:48:35.517Z", "21.3.0-beta.2": "2017-10-13T09:54:05.172Z", "21.3.0-beta.3": "2017-10-25T19:34:04.960Z", "21.3.0-beta.4": "2017-10-26T13:26:54.939Z", "21.2.1": "2017-10-31T23:48:09.890Z", "21.3.0-beta.5": "2017-11-02T13:17:27.968Z", "21.3.0-beta.6": "2017-11-03T16:21:31.236Z", "21.3.0-beta.7": "2017-11-06T09:39:45.821Z", "21.3.0-beta.8": "2017-11-07T17:43:37.761Z", "21.3.0-beta.9": "2017-11-22T13:17:31.520Z", "21.3.0-beta.10": "2017-11-25T12:39:24.685Z", "21.3.0-beta.11": "2017-11-29T14:31:19.120Z", "21.3.0-beta.12": "2017-12-05T18:48:34.326Z", "21.3.0-beta.13": "2017-12-06T14:37:08.932Z", "21.3.0-beta.14": "2017-12-12T10:52:35.650Z", "21.3.0-beta.15": "2017-12-15T13:27:38.769Z", "22.0.0": "2017-12-18T11:03:24.507Z", "22.0.1": "2017-12-18T20:29:25.054Z", "22.0.2": "2017-12-19T13:53:05.012Z", "22.0.3": "2017-12-19T14:58:55.142Z", "22.0.6": "2018-01-11T09:46:45.333Z", "22.1.0": "2018-01-15T11:57:14.301Z", "22.2.0": "2018-02-07T10:26:00.463Z", "22.2.2": "2018-02-09T16:28:13.686Z", "23.0.0-alpha.0": "2018-03-15T14:55:35.747Z", "23.0.0-alpha.1": "2018-03-21T16:00:14.144Z", "22.4.3": "2018-03-21T16:08:08.923Z", "23.0.0-alpha.2": "2018-03-26T10:40:47.204Z", "23.0.0-alpha.4": "2018-03-26T12:31:39.402Z", "23.0.0-alpha.5": "2018-04-10T19:18:18.785Z", "23.0.0-alpha.5r": "2018-04-11T05:52:49.137Z", "23.0.0-alpha.6r": "2018-04-12T07:01:34.764Z", "23.0.0-alpha.7": "2018-04-17T18:55:10.986Z", "23.0.0-beta.0": "2018-04-20T10:10:33.370Z", "23.0.0-beta.1": "2018-04-21T15:44:23.760Z", "23.0.0-beta.2": "2018-04-26T21:17:36.425Z", "23.0.0-alpha.3r": "2018-04-30T13:10:11.879Z", "23.0.0-beta.3r": "2018-04-30T13:14:54.565Z", "23.0.0-charlie.0": "2018-05-02T10:56:23.690Z", "23.0.0-charlie.1": "2018-05-03T12:10:15.132Z", "23.0.0-charlie.2": "2018-05-15T09:51:25.929Z", "23.0.0-charlie.3": "2018-05-22T14:58:59.165Z", "23.0.0-charlie.4": "2018-05-23T10:42:17.185Z", "23.0.0": "2018-05-24T17:26:25.122Z", "23.0.1": "2018-05-27T15:30:55.551Z", "23.2.0": "2018-06-25T14:05:14.564Z", "24.0.0-alpha.0": "2018-10-19T12:12:41.735Z", "24.0.0-alpha.1": "2018-10-22T15:35:48.920Z", "24.0.0-alpha.2": "2018-10-25T10:50:57.440Z", "24.0.0-alpha.4": "2018-10-26T16:33:14.777Z", "24.0.0-alpha.5": "2018-11-09T13:12:45.080Z", "24.0.0-alpha.6": "2018-11-09T17:49:40.873Z", "24.0.0-alpha.7": "2018-12-11T16:07:50.008Z", "24.0.0-alpha.8": "2018-12-13T19:47:52.728Z", "24.0.0-alpha.9": "2018-12-19T14:25:50.456Z", "24.0.0-alpha.10": "2019-01-09T17:04:21.016Z", "24.0.0-alpha.11": "2019-01-10T18:34:58.451Z", "24.0.0-alpha.12": "2019-01-11T15:01:07.739Z", "24.0.0-alpha.13": "2019-01-23T15:15:28.553Z", "24.0.0-alpha.14": "2019-01-24T16:29:18.325Z", "24.0.0-alpha.15": "2019-01-24T17:52:32.469Z", "24.0.0-alpha.16": "2019-01-25T13:42:01.966Z", "24.0.0": "2019-01-25T15:04:54.720Z", "24.2.0-alpha.0": "2019-03-05T14:46:28.590Z", "24.3.0": "2019-03-07T12:59:27.097Z", "24.3.1": "2019-03-07T23:12:18.978Z", "24.4.0": "2019-03-11T14:57:49.233Z", "24.6.0": "2019-04-01T22:26:17.012Z", "24.9.0": "2019-08-16T05:55:46.760Z", "25.0.0": "2019-08-22T03:23:44.463Z", "25.1.0": "2020-01-22T00:59:44.707Z", "25.2.0-alpha.86": "2020-03-25T17:16:11.300Z", "25.2.0": "2020-03-25T17:57:54.571Z", "25.2.1-alpha.1": "2020-03-26T07:54:13.833Z", "25.2.1-alpha.2": "2020-03-26T08:10:21.893Z", "25.2.1": "2020-03-26T09:01:04.701Z", "25.2.6": "2020-04-02T10:29:15.477Z", "25.4.0": "2020-04-19T21:50:19.714Z", "25.5.0": "2020-04-28T19:45:13.275Z", "26.0.0-alpha.0": "2020-05-02T12:12:51.652Z", "26.0.0": "2020-05-04T17:52:57.339Z", "26.1.0": "2020-06-23T15:15:03.908Z", "26.2.0": "2020-07-30T10:11:37.024Z", "26.2.1": "2020-07-30T11:35:19.789Z", "26.3.0": "2020-08-10T11:31:41.814Z", "26.5.0": "2020-10-05T09:28:06.718Z", "26.6.1": "2020-10-23T09:05:35.369Z", "26.6.2": "2020-11-02T12:51:13.056Z", "27.0.0-next.0": "2020-12-05T17:25:07.316Z", "27.0.0-next.2": "2020-12-07T14:35:44.924Z", "27.0.0-next.3": "2021-02-18T22:09:42.345Z", "27.0.0-next.4": "2021-03-08T13:45:07.685Z", "27.0.0-next.5": "2021-03-15T13:03:17.322Z", "27.0.0-next.6": "2021-03-25T19:39:57.140Z", "27.0.0-next.7": "2021-04-02T13:47:48.838Z", "27.0.0-next.8": "2021-04-12T22:42:23.639Z", "27.0.0-next.9": "2021-05-04T06:25:02.153Z", "27.0.0-next.10": "2021-05-20T14:11:12.371Z", "27.0.0-next.11": "2021-05-20T22:28:42.422Z", "27.0.0": "2021-05-25T08:15:04.138Z", "27.0.1": "2021-05-25T10:06:24.186Z", "27.0.2": "2021-05-29T12:07:07.922Z", "27.0.6": "2021-06-28T17:05:32.104Z", "27.1.0": "2021-08-27T09:59:30.624Z", "27.1.1": "2021-09-08T10:12:08.192Z", "27.2.0": "2021-09-13T08:06:35.173Z", "27.2.2": "2021-09-25T13:35:07.823Z", "27.2.3": "2021-09-28T10:11:20.009Z", "27.2.4": "2021-09-29T14:04:46.939Z", "27.2.5": "2021-10-08T13:39:18.778Z", "27.3.0": "2021-10-17T18:34:45.922Z", "27.3.1": "2021-10-19T06:57:31.809Z", "27.4.0": "2021-11-29T13:36:54.917Z", "27.4.1": "2021-11-30T08:37:03.585Z", "27.4.2": "2021-11-30T11:53:33.736Z", "27.4.4": "2021-12-10T04:43:07.062Z", "27.4.5": "2021-12-13T19:36:36.960Z", "27.4.6": "2022-01-04T23:03:31.729Z", "27.5.0": "2022-02-05T09:59:17.982Z", "27.5.1": "2022-02-08T10:52:12.753Z", "28.0.0-alpha.0": "2022-02-10T18:17:26.611Z", "28.0.0-alpha.1": "2022-02-15T21:26:49.427Z", "28.0.0-alpha.2": "2022-02-16T18:11:57.444Z", "28.0.0-alpha.3": "2022-02-17T15:42:20.905Z", "28.0.0-alpha.4": "2022-02-22T12:13:54.683Z", "28.0.0-alpha.5": "2022-02-24T20:57:18.055Z", "28.0.0-alpha.6": "2022-03-01T08:32:22.501Z", "28.0.0-alpha.7": "2022-03-06T10:02:39.790Z", "28.0.0-alpha.8": "2022-04-05T14:59:39.740Z", "28.0.0-alpha.9": "2022-04-19T10:59:14.043Z", "28.0.0-alpha.11": "2022-04-20T13:30:57.931Z", "28.0.0": "2022-04-25T12:08:02.426Z", "28.0.1": "2022-04-26T10:02:33.014Z", "28.0.2": "2022-04-27T07:44:00.336Z", "28.1.0": "2022-05-06T10:48:53.415Z", "28.1.1": "2022-06-07T06:09:35.501Z", "28.1.3": "2022-07-13T14:12:26.179Z", "29.0.0-alpha.0": "2022-07-17T22:07:06.045Z", "29.0.0-alpha.1": "2022-08-04T08:23:29.032Z", "29.0.0-alpha.3": "2022-08-07T13:41:39.262Z", "29.0.0-alpha.4": "2022-08-08T13:05:37.693Z", "29.0.0-alpha.5": "2022-08-11T13:40:57.347Z", "29.0.0-alpha.6": "2022-08-19T13:57:52.731Z", "29.0.0": "2022-08-25T12:33:31.389Z", "29.0.1": "2022-08-26T13:34:44.868Z", "29.0.2": "2022-09-03T10:48:22.574Z", "29.0.3": "2022-09-10T14:41:45.766Z", "29.1.0": "2022-09-28T07:37:42.849Z", "29.1.2": "2022-09-30T07:22:50.232Z", "29.2.0": "2022-10-14T09:13:51.998Z", "29.2.1": "2022-10-18T16:00:14.763Z", "29.3.0": "2022-11-07T17:55:46.322Z", "29.3.1": "2022-11-08T22:56:24.657Z", "29.4.0": "2023-01-24T10:55:54.218Z", "29.4.1": "2023-01-26T15:08:38.552Z", "29.4.2": "2023-02-07T13:45:30.591Z", "29.4.3": "2023-02-15T11:57:24.239Z", "29.5.0": "2023-03-06T13:33:32.250Z", "29.6.0": "2023-07-04T15:25:48.582Z", "29.6.1": "2023-07-06T14:18:28.202Z", "29.6.2": "2023-07-27T09:21:33.081Z", "29.6.3": "2023-08-21T12:39:17.886Z", "29.6.4": "2023-08-24T11:10:26.923Z", "29.7.0": "2023-09-12T06:43:45.568Z", "30.0.0-alpha.1": "2023-10-30T13:33:13.573Z", "30.0.0-alpha.2": "2023-11-16T09:28:30.105Z", "30.0.0-alpha.3": "2024-02-20T11:09:10.670Z", "30.0.0-alpha.4": "2024-05-12T21:43:29.413Z", "30.0.0-alpha.5": "2024-05-30T12:44:08.782Z", "30.0.0-alpha.6": "2024-08-08T07:43:07.350Z", "30.0.0-alpha.7": "2025-01-30T08:28:34.239Z", "30.0.0-beta.2": "2025-05-27T01:23:19.113Z", "30.0.0-beta.3": "2025-05-27T01:27:48.395Z", "30.0.0-beta.4": "2025-05-27T07:04:25.594Z", "30.0.0-beta.6": "2025-06-03T23:50:50.139Z", "30.0.0-beta.7": "2025-06-04T03:35:43.553Z", "30.0.0-beta.8": "2025-06-04T07:53:11.853Z", "30.0.0-rc.1": "2025-06-09T01:02:47.275Z", "30.0.0": "2025-06-10T02:15:53.063Z", "30.0.1": "2025-06-18T22:31:30.737Z", "30.0.2": "2025-06-19T10:46:20.899Z"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jestjs/jest.git", "directory": "packages/jest-worker"}, "maintainers": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "simenb", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "rickhan<PERSON><PERSON>@gmail.com"}, {"name": "openjs-operations", "email": "<EMAIL>"}, {"name": "cpojer", "email": "<EMAIL>"}], "readme": "", "readmeFilename": ""}